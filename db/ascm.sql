--
-- Table structure for table base_battery_project
--
DROP TABLE IF EXISTS base_battery_project;

CREATE TABLE base_battery_project
(
    id               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    pack_num         varchar(50)     NOT NULL DEFAULT '' COMMENT 'PACK国标码',
    pack_num_nine    varchar(20)     NOT NULL DEFAULT '' COMMENT 'PACK国标码（9为）',
    pack_type_num    varchar(20)     NOT NULL DEFAULT '' COMMENT 'PACK型号',
    project          varchar(20)     NOT NULL DEFAULT '' COMMENT '项目',
    create_user_id   varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(20)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(20)     NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 173
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='电池项目维护表';

--
-- Table structure for table base_battery_route_cost
--
DROP TABLE IF EXISTS base_battery_route_cost;

CREATE TABLE base_battery_route_cost
(
    id                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    repair_center_num   varchar(50)     NOT NULL DEFAULT '' COMMENT '维修中心',
    repair_center_name  varchar(50)     NOT NULL DEFAULT '' COMMENT '维修中心描述',
    dispatch_city       varchar(20)     NOT NULL DEFAULT '' COMMENT '启动城市',
    arrival_city        varchar(20)     NOT NULL DEFAULT '' COMMENT '目的城市',
    logistics_provider  varchar(20)     NOT NULL DEFAULT '' COMMENT '物流供应商',
    logistics_type      varchar(20)     NOT NULL DEFAULT '' COMMENT '物流类型',
    route_name          varchar(50)     NOT NULL DEFAULT '' COMMENT '线路名称',
    route_delivery_time double(5, 2)    NOT NULL DEFAULT '0.00' COMMENT '线路时效',
    kilometers_num      int             NOT NULL DEFAULT '0' COMMENT '公里数',
    car_type            int             NOT NULL DEFAULT '0' COMMENT '车型（0-4.2，1-6.8，2-7.2，3-9.6）',
    expense             double(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '费用(元/趟，未税)',
    tax_rates           double(4, 2)    NOT NULL DEFAULT '0.00' COMMENT '税率',
    create_user_id      varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(20)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name    varchar(20)     NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete           int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 115
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='电池线路费用维护表';

--
-- Table structure for table base_carrier
--
DROP TABLE IF EXISTS base_carrier;

CREATE TABLE base_carrier
(
    id               bigint unsigned                                               NOT NULL AUTO_INCREMENT COMMENT '主键id',
    carrier_code     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '承运商编码',
    carrier_name     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '承运商名称',
    address          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '供应商地址',
    contact_person   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '联系人',
    contact_num      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '电话',
    email            varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '电子邮件',
    status           int                                                           NOT NULL DEFAULT '0' COMMENT '状态',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY carrier_code_UNIQUE (carrier_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 13
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='承运商基础数据';

--
-- Table structure for table base_check_standard
--
DROP TABLE IF EXISTS base_check_standard;

CREATE TABLE base_check_standard
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    check_code       varchar(10)      NOT NULL DEFAULT '' COMMENT '校验标准编码',
    check_desc       varchar(255)     NOT NULL DEFAULT '' COMMENT '校验标准描述',
    check_docs       varchar(255)     NOT NULL DEFAULT '' COMMENT '校验标准文本',
    create_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人',
    version          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 231
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='基础校验标准表';

--
-- Table structure for table base_demolition_combine_record
--

DROP TABLE IF EXISTS base_demolition_combine_record;

CREATE TABLE base_demolition_combine_record
(
    id                  bigint                                                       NOT NULL AUTO_INCREMENT COMMENT '主键',
    parent_waybill_code varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '父运单编号',
    action              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '动作',
    child_waybill_code  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '子运单编号',
    item                varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '端',
    create_user_id      varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name    varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete           int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 688
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='拆合记录';

--
-- Table structure for table base_express_transshipment
--

DROP TABLE IF EXISTS base_express_transshipment;

CREATE TABLE base_express_transshipment
(
    id               bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    company_name     varchar(20) NOT NULL DEFAULT '' COMMENT '公司名称',
    company_code     varchar(20) NOT NULL DEFAULT '' COMMENT '公司编码',
    company_type     varchar(20) NOT NULL DEFAULT '' COMMENT '公司类型',
    create_user_id   varchar(60) NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int         NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1605848170472207317
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='快递100快递公司编码表';

--
-- Table structure for table base_file
--
DROP TABLE IF EXISTS base_file;

CREATE TABLE base_file
(
    id               bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
    file_name        varchar(100) NOT NULL DEFAULT '' COMMENT '文件名称',
    file_uuid        varchar(100) NOT NULL DEFAULT '' COMMENT '文件UUID',
    file_save_name   varchar(100) NOT NULL DEFAULT '' COMMENT '保存文件名称',
    project          varchar(20)  NOT NULL DEFAULT '' COMMENT '项目（ASCM，VSCM，OSCM，ASCM）',
    project_path     varchar(255) NOT NULL DEFAULT '' COMMENT '项目路径（ASCM，VSCM，OSCM，ASCM）',
    create_user_id   varchar(60)  NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)  NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)  NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60)  NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY base_file_file_uuid_uindex (file_uuid)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1838144424793919490
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='文件';

--
-- Table structure for table base_materials
--
DROP TABLE IF EXISTS base_materials;

CREATE TABLE base_materials
(
    id               bigint unsigned                                               NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    material_code    varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '物料编号',
    material_desc    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '物料描述',
    pic_left         varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-左',
    pic_right        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-右',
    pic_Stamp        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-钢印',
    pic_before       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-前',
    pic_after        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-左',
    pic_other        varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片-其他',
    pic_complete     varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '图片是否完整',
    unit_package     tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '单元包装 0:否，1:是',
    supplier_package varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '供应商包装',
    material_length  varchar(20)                                                   NOT NULL DEFAULT '0' COMMENT '物料长(mm)',
    material_width   varchar(20)                                                   NOT NULL DEFAULT '0' COMMENT '物料宽(mm)',
    material_height  varchar(20)                                                   NOT NULL DEFAULT '0' COMMENT '物料高(mm)',
    material_volume  varchar(20)                                                   NOT NULL DEFAULT '0' COMMENT '物料体积(mm)',
    check_code_id    bigint                                                        NOT NULL DEFAULT '-1' COMMENT '校验编码ID',
    create_user_id   varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '修改人',
    is_delete        tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    version          tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '乐观锁',
    create_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY material_code_unique (material_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 538
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='物料表';

--
-- Table structure for table base_print_out_task
--
DROP TABLE IF EXISTS base_print_out_task;

CREATE TABLE base_print_out_task
(
    id                  bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键',
    waybill_code        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '运单编号',
    delivery_order_code varchar(500)                                                 NOT NULL DEFAULT '' COMMENT '交货单号',
    order_type          varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '订单类型',
    lgort               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库编码',
    lgobe               varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '仓库名称',
    shop_code           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店编码',
    shop_name           varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '门店名称',
    status              int                                                          NOT NULL DEFAULT '0' COMMENT '打印状态:0 待打印 1 打印中 2 打印完成 3 打印失败',
    count               int                                                          NOT NULL DEFAULT '0' COMMENT '打印次数',
    create_user_id      varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name    varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete           int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 942
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='打印任务';

--
-- Table structure for table base_repair_center
--
DROP TABLE IF EXISTS base_repair_center;

CREATE TABLE base_repair_center
(
    id                     bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    repair_center_num      varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心编码',
    repair_center_name     varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心名称',
    repair_center_remark   varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心简称',
    repair_center_province varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心省份',
    repair_center_city     varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心城市',
    repair_center_address  varchar(60)     NOT NULL DEFAULT '' COMMENT '维修中心地址',
    contact_num            varchar(50)     NOT NULL DEFAULT '' COMMENT '联系电话',
    contact_person         varchar(60)     NOT NULL DEFAULT '' COMMENT '联系人',
    status                 int             NOT NULL DEFAULT '0' COMMENT '状态',
    create_user_id         varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name       varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id         varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name       varchar(60)     NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete              int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time            datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY repair_center_num_UNIQUE (repair_center_num)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1714885167600574467
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='维修中心主数据';

--
-- Table structure for table base_shop
--
DROP TABLE IF EXISTS base_shop;

CREATE TABLE base_shop
(
    id                  bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    region              varchar(10)                                                  NOT NULL DEFAULT '' COMMENT '区域',
    shop_code           varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店编码',
    xl_shop_code        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '骁龙门店编码',
    xl_shop_remark      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '骁龙门店简称',
    shop_name           varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店名称',
    shop_remark         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店简称',
    shop_province       varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店省份',
    shop_city           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店城市',
    shop_address        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店地址',
    contact_num         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系电话',
    contact_person      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系人',
    lng                 double(6, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '经度',
    lat                 double(6, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '纬度',
    fence_range         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '围栏范围',
    status              int                                                          NOT NULL DEFAULT '0' COMMENT '状态',
    create_user_id      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name    varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete           int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    dynamic_update      int                                                          NOT NULL DEFAULT '1' COMMENT '是否动态更新',
    lgort               varchar(100)                                                 NOT NULL DEFAULT '' COMMENT '仓库编码',
    cargo_contact       varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '装货联系人',
    cargo_contact_phone varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '装货联系人电话',
    PRIMARY KEY (id),
    UNIQUE KEY store_code_UNIQUE (shop_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2117
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='门店地址主数据表';

--
-- Table structure for table base_suppliers
--
DROP TABLE IF EXISTS base_suppliers;

CREATE TABLE base_suppliers
(
    id               bigint unsigned                                               NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    supplier_code    varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '供应商编号',
    supplier_name    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '供应商描述',
    create_user_id   varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '更新人',
    version          tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete        tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY supplier_code (supplier_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 277
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='物料供应商表';

--
-- Table structure for table base_transport_timeliness
--
DROP TABLE IF EXISTS base_transport_timeliness;

CREATE TABLE base_transport_timeliness
(
    id               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    warehouse_city   varchar(20)     NOT NULL DEFAULT '' COMMENT '发出城市',
    shop_city        varchar(20)     NOT NULL DEFAULT '' COMMENT '接收城市',
    transport_type   varchar(10)     NOT NULL DEFAULT '' COMMENT '运输方式',
    transport_time   varchar(10)     NOT NULL DEFAULT '' COMMENT '运输时效',
    route_name       varchar(50)     NOT NULL DEFAULT '' COMMENT '线路名称',
    create_user_id   varchar(30)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(30)     NOT NULL DEFAULT '' COMMENT '创建人',
    create_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id   varchar(30)     NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(30)     NOT NULL DEFAULT '' COMMENT '更新人',
    update_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_delete        int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    PRIMARY KEY (id),
    UNIQUE KEY WC_SC_TT_UNIQUE (warehouse_city, shop_city, transport_type)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1696096814535053320
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运输时效';

--
-- Table structure for table base_transport_type
--
DROP TABLE IF EXISTS base_transport_type;

CREATE TABLE base_transport_type
(
    id               bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    transport_type   varchar(10)                                                  NOT NULL DEFAULT '' COMMENT '运输类型',
    status           int                                                          NOT NULL DEFAULT '0' COMMENT '状态',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY transport_type_uq (transport_type)
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运输类型';

--
-- Table structure for table base_warehouse
--
DROP TABLE IF EXISTS base_warehouse;

CREATE TABLE base_warehouse
(
    id                 bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    lgort              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库编码',
    lgobe              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库名称',
    warehouse_type     tinyint                                                      NOT NULL DEFAULT '0' COMMENT '仓库类型',
    warehouse_remark   varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库简称',
    warehouse_province varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库省份',
    warehouse_city     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库城市',
    warehouse_address  varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '仓库地址',
    contact_num        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系电话',
    contact_person     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系人',
    status             int                                                          NOT NULL DEFAULT '0' COMMENT '状态',
    create_user_id     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete          int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time        datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY lgort_UNIQUE (lgort)
) ENGINE = InnoDB
  AUTO_INCREMENT = 21
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='仓库主数据';

--
-- Table structure for table base_waybill_rules
--
DROP TABLE IF EXISTS base_waybill_rules;

CREATE TABLE base_waybill_rules
(
    id               bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    shop_code        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '门店编码',
    path_remark      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '线路描述',
    path             varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '线路',
    path_expiry      int                                                          NOT NULL DEFAULT '0' COMMENT '线路时效',
    order_type       varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '订单类型',
    transport_type   varchar(60)                                                  NOT NULL DEFAULT '' COMMENT '运输类型',
    carrier_code     varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '承运商编码',
    status           int                                                          NOT NULL DEFAULT '0' COMMENT '状态',
    is_specially     int                                                          NOT NULL DEFAULT '0' COMMENT '是否专用:0:否;1:是;',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    lgort            varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '仓库编码',
    shop_city        varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '门店城市',
    shop_province    varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '门店省份',
    PRIMARY KEY (id),
    UNIQUE KEY uq_oslt (order_type, lgort, shop_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 981115
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单规则匹配';

--
-- Table structure for table battery_way_bill
--
DROP TABLE IF EXISTS battery_way_bill;

CREATE TABLE battery_way_bill
(
    id                            bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    repair_center_num             varchar(20)     NOT NULL DEFAULT '' COMMENT '维修中心',
    repair_date                   datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '报修日期',
    waybill_code                  varchar(20)     NOT NULL DEFAULT '' COMMENT '电池运单编号',
    pack_num                      varchar(50)     NOT NULL DEFAULT '' COMMENT 'PACK国标码',
    waybill_status                int             NOT NULL DEFAULT '0' COMMENT '运单状态（0-新建，1-已分配，2-已装运，3-已送达，4-已付款，5-关闭）',
    transportation_type           int             NOT NULL DEFAULT '0' COMMENT '运输类型（0-返厂，1-返店）',
    logistics_type                int             NOT NULL DEFAULT '2' COMMENT '物流类型（0-快递，1-零担）',
    logistics_arrangement_time    datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '物流安排日期',
    logistics_provider            varchar(20)     NOT NULL DEFAULT '' COMMENT '物流供应商',
    xl_shop_code                  varchar(10)     NOT NULL DEFAULT '' COMMENT '骁龙门店编码',
    vin_code                      varchar(25)     NOT NULL DEFAULT '' COMMENT '车架号',
    project                       varchar(20)     NOT NULL DEFAULT '' COMMENT '项目',
    distance_travelled            double(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '行驶里程(km)',
    fault_summary                 varchar(20)     NOT NULL DEFAULT '' COMMENT '故障简述',
    fault_classification          int             NOT NULL DEFAULT '1' COMMENT '故障分类(0-质量，1-非质量)',
    dispatch_city                 varchar(20)     NOT NULL DEFAULT '' COMMENT '启动城市---发出城市名称',
    arrival_city                  varchar(20)     NOT NULL DEFAULT '' COMMENT '目的城市---接收城市名称',
    is_same_package               int             NOT NULL DEFAULT '1' COMMENT '是否同包(0-是，1-否)',
    package_vin                   varchar(300)    NOT NULL DEFAULT '' COMMENT '同包vin号',
    car_type                      varchar(20)     NOT NULL DEFAULT '0' COMMENT '车型（0-4.2，1-6.8，2-7.2，3-9.6）',
    pickup_date                   datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '提货日期',
    route_name                    varchar(30)     NOT NULL DEFAULT '' COMMENT '线路名称',
    route_delivery_time           int             NOT NULL DEFAULT '0' COMMENT '线路时效',
    kilometers_num                int             NOT NULL DEFAULT '0' COMMENT '公里数',
    expense                       double(6, 2)    NOT NULL DEFAULT '0.00' COMMENT '费用(元/趟，未税)',
    driver_name                   varchar(20)     NOT NULL DEFAULT '' COMMENT '司机姓名',
    driver_phone                  varchar(20)     NOT NULL DEFAULT '' COMMENT '司机电话',
    plate_number                  varchar(20)     NOT NULL DEFAULT '' COMMENT '车牌号',
    response_time_tolerance       int             NOT NULL DEFAULT '0' COMMENT '响应时效',
    transportation_time_tolerance int             NOT NULL DEFAULT '0' COMMENT '运输时效',
    actual_payment_amount         double(10, 2)   NOT NULL DEFAULT '0.00' COMMENT '实付费用',
    delivery_time                 datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '送达时间',
    original_waybill              varchar(20)     NOT NULL DEFAULT '' COMMENT '原始运单号',
    remarks                       varchar(120)    NOT NULL DEFAULT '' COMMENT '备注',
    create_user_id                varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name              varchar(20)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id                varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name              varchar(20)     NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete                     int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time                   datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time                   datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    dispatch_address              varchar(100)    NOT NULL DEFAULT '' COMMENT '发运城市地址',
    loading_contact               varchar(30)     NOT NULL DEFAULT '' COMMENT '发运城市联系人',
    loading_phone                 varchar(20)     NOT NULL DEFAULT '' COMMENT '发运城市联系人电话',
    arrival_address               varchar(100)    NOT NULL DEFAULT '' COMMENT '收货城市地址',
    unloading_contact             varchar(30)     NOT NULL DEFAULT '' COMMENT '收货城市联系人',
    unloading_phone               varchar(20)     NOT NULL DEFAULT '' COMMENT '收货城市联系人电话',
    remark                        varchar(200)    NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (id),
    UNIQUE KEY waybill_code_UNIQUE (waybill_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 141
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='电池运单表';

--
-- Table structure for table bz_async_export_log
--
DROP TABLE IF EXISTS bz_async_export_log;

CREATE TABLE bz_async_export_log
(
    id               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    lgort            varchar(40)     NOT NULL DEFAULT '' COMMENT '仓库号',
    name             varchar(40)     NOT NULL DEFAULT '' COMMENT '文件名称',
    operation_code   varchar(40)     NOT NULL DEFAULT '' COMMENT '下载文件码',
    method_path      varchar(200)    NOT NULL DEFAULT '' COMMENT '方法路径：类名.方法名',
    params           text COMMENT '参数集合',
    export_count     int             NOT NULL DEFAULT '0' COMMENT '导出次数 超过3次则不再操作',
    download_count   int             NOT NULL DEFAULT '0' COMMENT '用户下载次数',
    state            int             NOT NULL DEFAULT '0' COMMENT '0：导出中;1: 导出成功;2:导出失败;',
    is_delete        tinyint         NOT NULL DEFAULT '0' COMMENT '删除标记',
    owner_org_id     varchar(40)     NOT NULL DEFAULT '' COMMENT '所属组织ID',
    create_user_id   varchar(40)     NOT NULL DEFAULT '' COMMENT '创建人ID',
    create_user_name varchar(40)     NOT NULL DEFAULT '' COMMENT '创建人姓名',
    create_time      datetime(4)     NOT NULL DEFAULT CURRENT_TIMESTAMP(4) COMMENT '创建时间',
    update_user_id   varchar(40)     NOT NULL DEFAULT '' COMMENT '修改人ID',
    update_user_name varchar(40)     NOT NULL DEFAULT '' COMMENT '修改人姓名',
    update_time      datetime(4)     NOT NULL DEFAULT CURRENT_TIMESTAMP(4) ON UPDATE CURRENT_TIMESTAMP(4) COMMENT '修改时间',
    optimistic_lock  int unsigned    NOT NULL DEFAULT '0' COMMENT '乐观锁',
    out_method_path  varchar(40)     NOT NULL DEFAULT '' COMMENT '异步二进制输出路径',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 265
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='异步导出表';

--
-- Table structure for table bz_delivery_order_material
--
DROP TABLE IF EXISTS bz_delivery_order_material;

CREATE TABLE bz_delivery_order_material
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    order_id         bigint           NOT NULL DEFAULT '-1' COMMENT '物料交货单ID',
    material_id      bigint           NOT NULL DEFAULT '-1' COMMENT '物料ID',
    create_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人',
    version          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 409
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='交货单和物料关联关系表';

--
-- Table structure for table bz_delivery_orders
--
DROP TABLE IF EXISTS bz_delivery_orders;

CREATE TABLE bz_delivery_orders
(
    id                  bigint unsigned                                               NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    delivery_order_code varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '交货单号',
    asn_code            varchar(101) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'ASN号',
    create_user_id      varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name    varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '更新人',
    version             tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete           tinyint unsigned                                              NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 134
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='物料交货单表';

--
-- Table structure for table bz_erp_req_box
--
DROP TABLE IF EXISTS bz_erp_req_box;

CREATE TABLE bz_erp_req_box
(
    id               bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    box_code         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '箱号',
    box_long         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '长(毫米)',
    box_width        varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '宽(毫米)',
    box_height       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '高(毫米)',
    box_volume       double(10, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '包材体积(立方米)',
    box_weight       double(10, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '单箱重量（kg）',
    actual_volume    double(10, 3)                                                NOT NULL DEFAULT '0.000' COMMENT '实际体积(立方米)',
    package_type     varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '包装类型(0-单独包装,1-混合包装,2-虚拟包装)',
    package_code     varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '包装材料编号',
    package_time     datetime                                                     NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '包装日期时间',
    waybill_code     varchar(20)                                                  NOT NULL DEFAULT '' COMMENT '箱号所在运单',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    receive_state    int                                                          NOT NULL DEFAULT '0' COMMENT '收货情况，0-初始化，1-完整，2-缺失',
    PRIMARY KEY (id),
    UNIQUE KEY box_code_UNIQUE (box_code),
    KEY idx_waybill_box_code (waybill_code, box_code),
    KEY idx_waybill_code (waybill_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 14076
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='ERP需求关联箱信息';

--
-- Table structure for table bz_erp_req_material_rel
--
DROP TABLE IF EXISTS bz_erp_req_material_rel;

CREATE TABLE bz_erp_req_material_rel
(
    id                  bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键id',
    delivery_order_code varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '交货单号',
    delivery_order_type varchar(20)                                                  NOT NULL DEFAULT '' COMMENT 'ERP交货单类型描述，对应接口VTEXT',
    line_number         varchar(10)                                                  NOT NULL DEFAULT '' COMMENT '交货单行号',
    box_code            varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '箱号',
    matnr               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '物料号',
    maktx               varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '物料描述',
    quantity            int                                                          NOT NULL DEFAULT '0' COMMENT '数量',
    unit                varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '单位',
    estimated_volume    double(8, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '预估总体积(m³)',
    order_count         int                                                          NOT NULL DEFAULT '0' COMMENT '订单数量',
    package_count       int                                                          NOT NULL DEFAULT '0' COMMENT '包装数量',
    material_long       varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '长(毫米)',
    material_width      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '宽(毫米)',
    material_height     varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '高(毫米)',
    material_volume     double(8, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '物料体积(立方米)',
    create_user_id      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name    varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id      varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name    varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete           int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time         datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    purchase_date       varchar(30)                                                  NOT NULL DEFAULT '' COMMENT '下单日期',
    purchase_time       varchar(30)                                                  NOT NULL DEFAULT '' COMMENT '下单时间',
    delivery_date       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配送日期',
    delivery_time       varchar(30)                                                  NOT NULL DEFAULT '' COMMENT '配送时间',
    delivery_date_time  datetime                                                              DEFAULT NULL COMMENT '配送日期时间',
    purchase_date_time  datetime                                                              DEFAULT NULL COMMENT '下单日期时间',
    plan_shipping_time  datetime                                                              DEFAULT NULL COMMENT '计划发货时间',
    receive_flag        tinyint                                                      NOT NULL DEFAULT '0' COMMENT '是否收货，0：未收货，1：部分收货，2：全部收货',
    receive_count       int                                                          NOT NULL DEFAULT '0' COMMENT '接收数量',
    PRIMARY KEY (id),
    KEY bz_erp_req_material_rel_box_code_IDX (box_code) USING BTREE,
    KEY idx_delivery_order_code (delivery_order_code)
) ENGINE = InnoDB
  AUTO_INCREMENT = 15682
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='ERP需求关联物料';

--
-- Table structure for table bz_supplier_material
--

DROP TABLE IF EXISTS bz_supplier_material;

CREATE TABLE bz_supplier_material
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    supplier_id      bigint           NOT NULL DEFAULT '-1' COMMENT '供应商ID',
    material_id      bigint           NOT NULL DEFAULT '-1' COMMENT '物料ID',
    supplier_snp     varchar(10)      NOT NULL DEFAULT '' COMMENT '供应商snp',
    create_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人',
    version          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    UNIQUE KEY bz_supplier_material_unique (supplier_id, material_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1804
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='供应商和物料关联关系表';

--
-- Table structure for table bz_supplier_package_info
--
DROP TABLE IF EXISTS bz_supplier_package_info;

CREATE TABLE bz_supplier_package_info
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '唯一id',
    supplier_id      bigint           NOT NULL DEFAULT '-1' COMMENT '供应商ID',
    material_id      bigint           NOT NULL DEFAULT '-1' COMMENT '物料ID',
    supplier_snp     varchar(20)      NOT NULL DEFAULT '' COMMENT '供应商snp',
    material_length  varchar(20)      NOT NULL DEFAULT '0' COMMENT '物料长(mm)',
    material_width   varchar(20)      NOT NULL DEFAULT '0' COMMENT '物料宽(mm)',
    material_height  varchar(20)      NOT NULL DEFAULT '0' COMMENT '物料高(mm)',
    stuff            varchar(20)      NOT NULL DEFAULT '' COMMENT '材料',
    stuff_name       varchar(20)      NOT NULL DEFAULT '' COMMENT '材料名称',
    dosage           varchar(20)      NOT NULL DEFAULT '0' COMMENT '用量',
    recycle          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否回收',
    create_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(60)      NOT NULL DEFAULT '' COMMENT '更新人',
    version          tinyint unsigned NOT NULL DEFAULT '0' COMMENT '乐观锁',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 11607
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='供应商物料包装信息表';

--
-- Table structure for table bz_waybill
--
DROP TABLE IF EXISTS bz_waybill;

CREATE TABLE bz_waybill
(
    id                       bigint unsigned                                               NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_code             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '运单编号',
    status                   varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '运单状态',
    lgort                    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '仓库编码',
    lgobe                    varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '仓库名称',
    warehouse_province       varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '仓库省份',
    warehouse_city           varchar(100)                                                  NOT NULL DEFAULT '' COMMENT '仓库城市',
    warehouse_address        varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '仓库地址',
    warehouse_contact_person varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '仓库联系人',
    warehouse_contact_num    varchar(50)                                                   NOT NULL DEFAULT '' COMMENT '仓库联系人电话',
    shop_code                varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '门店编码',
    shop_name                varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '门店名称',
    shop_province            varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '门店省份',
    shop_city                varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '门店城市',
    shop_address             varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '门店地址',
    shop_contact_person      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '门店联系人',
    shop_contact_num         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '门店联系电话',
    werks                    varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '工厂编码',
    factory_desc             varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '工厂描述',
    order_type               varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '订单类型',
    is_abnormal              int                                                           NOT NULL DEFAULT '0' COMMENT '是否异常',
    transport_type           varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '运输类型',
    is_ontime                int                                                           NOT NULL DEFAULT '0' COMMENT '是否准时送达',
    local_update_time        datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '位置更新时间',
    local_info               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '位置信息',
    total_box                int                                                           NOT NULL DEFAULT '0' COMMENT '总箱数量',
    total_volume             double(10, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '总体积(m³)',
    estimated_volume         double(10, 3)                                                 NOT NULL DEFAULT '0.000' COMMENT '预估总体积(m³)',
    carrier_code             varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '承运商编码',
    car_plate                varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '车牌号',
    driver_name              varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '司机名称',
    driver_phone             varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '司机手机号',
    logistics_code           varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '物流单号',
    departure_time           datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '发车时间',
    pda_shipping_time        datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT 'PDA发运时间',
    logistics_company        varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '物流公司',
    sign_time                datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '签收时间',
    waybill_create_time      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '运单创建时间',
    path                     varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '线路',
    path_remark              varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '线路描述',
    path_expiry              int                                                           NOT NULL DEFAULT '0' COMMENT '线路时效',
    expiry_time              datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '过期时间',
    compensate_type          varchar(20)                                                   NOT NULL DEFAULT '' COMMENT '索赔类型',
    compensate_reason        varchar(60)                                                   NOT NULL DEFAULT '' COMMENT '索赔原因',
    is_devanning             int                                                           NOT NULL DEFAULT '0' COMMENT '是否拆箱',
    combine_count            int                                                           NOT NULL DEFAULT '0' COMMENT '合单次数',
    is_temp                  int                                                           NOT NULL DEFAULT '0' COMMENT '是否为临时运单（0-否，1-是）',
    is_hang_up               int                                                           NOT NULL DEFAULT '0' COMMENT '是否挂起',
    hang_up_time             datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '挂起时间',
    create_user_id           varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id           varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name         varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci  NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete                int                                                           NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time              datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time              datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_complete_packing      int                                                           NOT NULL DEFAULT '0' COMMENT '是否包装完成',
    demolition_count         int                                                           NOT NULL DEFAULT '0' COMMENT '拆单次数',
    abnormal_cause           varchar(40)                                                   NOT NULL DEFAULT '' COMMENT '运单异常原因',
    received_time            datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '接收时间（WMS下发时间）',
    circulation_time         datetime                                                      NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '下发时间（推送给pda的时间)',
    route                    varchar(30)                                                   NOT NULL DEFAULT '' COMMENT '路线',
    status_code              int                                                           NOT NULL DEFAULT '0' COMMENT '运单状态码（0-未完成，1-已完成）',
    sort                     tinyint                                                       NOT NULL DEFAULT '0' COMMENT '排序规则。越大排序越高',
    sort_num                 tinyint                                                       NOT NULL DEFAULT '0' COMMENT '排序规则。越大排序越高',
    update_status            int                                                           NOT NULL DEFAULT '0' COMMENT '运单时效更新状态',
    PRIMARY KEY (id),
    UNIQUE KEY waybill_code_UNIQUE (waybill_code),
    KEY bz_waybill_is_delete_IDX (is_delete, waybill_code, lgort, carrier_code, shop_code) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 2282
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单表';

--
-- Table structure for table bz_waybill_box_rel
--
DROP TABLE IF EXISTS bz_waybill_box_rel;

CREATE TABLE bz_waybill_box_rel
(
    id               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_code     varchar(20)     NOT NULL DEFAULT '' COMMENT '运单编号',
    box_code         varchar(60)     NOT NULL DEFAULT '' COMMENT '箱号',
    status           int             NOT NULL DEFAULT '0' COMMENT '箱号状态（0-未扫描，1-已扫描）',
    is_devanning     int             NOT NULL DEFAULT '0' COMMENT '是否拆箱（0-正常，1-已拆离）',
    is_delete        int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_user_id   varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60)     NOT NULL DEFAULT '' COMMENT '更新人',
    create_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY bz_waybill_box_rel_waybill_code_IDX (waybill_code) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 14987
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单箱子关联表';

--
-- Table structure for table bz_waybill_export_record
--
DROP TABLE IF EXISTS bz_waybill_export_record;

CREATE TABLE bz_waybill_export_record
(
    id               bigint unsigned                                              NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    status           int                                                          NOT NULL DEFAULT '0' COMMENT '导出状态（0-导出中，1-已完成）',
    file_id          varchar(100)                                                 NOT NULL DEFAULT '' COMMENT '导出文件Id',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 9
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单导出记录表';

--
-- Table structure for table bz_waybill_file
--
DROP TABLE IF EXISTS bz_waybill_file;

CREATE TABLE bz_waybill_file
(
    id               int unsigned                                                 NOT NULL AUTO_INCREMENT,
    waybill_code     varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '运单编号',
    file_Id          varchar(45)                                                  NOT NULL DEFAULT '' COMMENT 'fileId',
    upload_type      varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '文件上传类型（1为签收图片，2异常图片，3电池运单图片）',
    upload_time      varchar(45)                                                  NOT NULL DEFAULT '' COMMENT '文件上传时间',
    create_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id   varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
    is_delete        int                                                          NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time      datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 262
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单文件地址表';

--
-- Table structure for table bz_waybill_info_sync
--
DROP TABLE IF EXISTS bz_waybill_info_sync;

CREATE TABLE bz_waybill_info_sync
(
    id                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_id         bigint          NOT NULL COMMENT '运单ID',
    waybill_code       varchar(20)     NOT NULL DEFAULT '' COMMENT '运单编号',
    dispatch_time_sync tinyint         NOT NULL DEFAULT '0' COMMENT '发运时间同步 0:未同步 1:已同步',
    signed_time_sync   tinyint         NOT NULL DEFAULT '0' COMMENT '签收时间同步 0:未同步 1:已同步',
    is_delete          tinyint         NOT NULL DEFAULT '0' COMMENT '是否删除（1:已删除 0:正常）',
    create_user_id     varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name   varchar(20)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id     varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name   varchar(20)     NOT NULL DEFAULT '' COMMENT '更新人',
    create_time        datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1815271193827927625
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单信息同步表';

--
-- Table structure for table bz_waybill_retry
--
DROP TABLE IF EXISTS bz_waybill_retry;

CREATE TABLE bz_waybill_retry
(
    id                 bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_code       varchar(20)     NOT NULL DEFAULT '' COMMENT '运单编号',
    warehouse_number   varchar(10)     NOT NULL DEFAULT '' COMMENT '仓库编号',
    interface_number   varchar(10)     NOT NULL DEFAULT '' COMMENT '接口编码',
    interface_describe varchar(10)     NOT NULL DEFAULT '' COMMENT '接口描述',
    shop_code          varchar(10)     NOT NULL DEFAULT '' COMMENT '门店编码',
    shop_describe      varchar(30)     NOT NULL DEFAULT '' COMMENT '门店描述',
    target_system      varchar(10)     NOT NULL DEFAULT '' COMMENT '目标系统',
    first_send_date    datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '首次发送日期',
    first_send_state   tinyint         NOT NULL DEFAULT '0' COMMENT '首次发送状态(0:未知，1:成功，2:失败)',
    last_send_date     datetime        NOT NULL DEFAULT '1970-01-01 08:00:00' COMMENT '最后一次发送日期',
    last_send_name     varchar(30)     NOT NULL DEFAULT '' COMMENT '最后一次发送人',
    retry_return_msg   tinyint         NOT NULL DEFAULT '0' COMMENT '重发返回信息(0:未知，2:成功，2:失败)',
    payload_content    longtext        NOT NULL COMMENT '报文',
    request_count      tinyint         NOT NULL DEFAULT '0' COMMENT '请求次数',
    is_show            tinyint(1)      NOT NULL DEFAULT '0' COMMENT '是否展示（0：否 1：是）',
    send_source        varchar(3)      NOT NULL DEFAULT '00' COMMENT '发送源 (00：默认 10：pda 20：小程序)',
    create_user_id     varchar(60)     NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name   varchar(30)     NOT NULL DEFAULT '' COMMENT '创建人',
    update_user_id     varchar(60)     NOT NULL DEFAULT '' COMMENT '修改人id',
    update_user_name   varchar(30)     NOT NULL DEFAULT '' COMMENT '更新人',
    create_time        datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time        datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 4471
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单重试表';

--
-- Table structure for table bz_waybill_rules_rel
--
DROP TABLE IF EXISTS bz_waybill_rules_rel;

CREATE TABLE bz_waybill_rules_rel
(
    id          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    rules_id    bigint          NOT NULL DEFAULT '0' COMMENT '正向运单规则id',
    waybill_id  bigint          NOT NULL DEFAULT '0' COMMENT '正向运单id',
    is_delete   int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='正向运单规则关联表';

--
-- Table structure for table bz_waybill_track_history
--
DROP TABLE IF EXISTS bz_waybill_track_history;

CREATE TABLE bz_waybill_track_history
(
    id           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_code varchar(20)     NOT NULL DEFAULT '' COMMENT '运单编码',
    province     varchar(20)     NOT NULL DEFAULT '' COMMENT '省',
    city         varchar(20)     NOT NULL DEFAULT '' COMMENT '城市',
    address      varchar(200)    NOT NULL DEFAULT '' COMMENT '详细地址',
    latitude     double          NOT NULL DEFAULT '0' COMMENT '纬度',
    longitude    double          NOT NULL DEFAULT '0' COMMENT '经度',
    track_status int             NOT NULL DEFAULT '0' COMMENT '状态(0-初始点，1-转交，2-在途点，3-交付）',
    driver_name  varchar(20)     NOT NULL DEFAULT '' COMMENT '司机名称',
    driver_phone varchar(60)     NOT NULL DEFAULT '' COMMENT '司机手机号',
    is_delete    int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time  datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 34956
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='运单追踪历史表';

--
-- Table structure for table bz_waybill_track_truck_driver
--
DROP TABLE IF EXISTS bz_waybill_track_truck_driver;

CREATE TABLE bz_waybill_track_truck_driver
(
    id                   bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
    waybill_code         varchar(20)     NOT NULL DEFAULT '' COMMENT '运单编码',
    province             varchar(20)     NOT NULL DEFAULT '' COMMENT '省',
    city                 varchar(20)     NOT NULL DEFAULT '' COMMENT '城市',
    address              varchar(200)    NOT NULL DEFAULT '' COMMENT '详细地址',
    receive_driver_name  varchar(20)     NOT NULL DEFAULT '' COMMENT '接收方司机名称',
    receive_driver_phone varchar(60)     NOT NULL DEFAULT '' COMMENT '接收方司机手机号',
    send_driver_name     varchar(20)     NOT NULL DEFAULT '' COMMENT '送出方司机名称',
    send_driver_phone    varchar(60)     NOT NULL DEFAULT '' COMMENT '送出方司机手机号',
    is_delete            int             NOT NULL DEFAULT '0' COMMENT '是否删除（1 - 已删除，0 - 正常）',
    create_time          datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time          datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 597
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='司机转交历史表';

--
-- Table structure for table mbp_org_info
--
DROP TABLE IF EXISTS mbp_org_info;

CREATE TABLE mbp_org_info
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    code             varchar(64)      NOT NULL DEFAULT '' COMMENT '组织编码',
    name             varchar(64)      NOT NULL DEFAULT '' COMMENT '组织名称',
    parent_id        bigint unsigned  NOT NULL DEFAULT '0' COMMENT '父组织ID',
    level            int unsigned     NOT NULL DEFAULT '0' COMMENT '组织层级',
    type_id          bigint unsigned  NOT NULL DEFAULT '0' COMMENT '组织类型ID',
    status           tinyint unsigned NOT NULL DEFAULT '0' COMMENT '组织状态 0-正常 1-禁用',
    remark           varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    sort             int unsigned     NOT NULL DEFAULT '0' COMMENT '排序',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0:否;1:是;',
    create_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人名称',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人名称',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_parent_id (parent_id),
    KEY idx_type_id (type_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 35
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='组织信息表';

--
-- Table structure for table mbp_org_type
--
DROP TABLE IF EXISTS mbp_org_type;


CREATE TABLE mbp_org_type
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    code             varchar(64)      NOT NULL DEFAULT '' COMMENT '组织类型编码',
    name             varchar(64)      NOT NULL DEFAULT '' COMMENT '组织类型名称',
    remark           varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    sort             int unsigned     NOT NULL DEFAULT '0' COMMENT '排序',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0:否;1:是;',
    create_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人名称',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人名称',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 11
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='组织类型表';

--
-- Table structure for table mbp_user_org_rel
--
DROP TABLE IF EXISTS mbp_user_org_rel;

CREATE TABLE mbp_user_org_rel
(
    id               bigint unsigned  NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    user_id          varchar(32)      NOT NULL DEFAULT '' COMMENT '用户ID',
    org_id           bigint unsigned  NOT NULL DEFAULT '0' COMMENT '组织ID',
    is_default       tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否默认 0:否;1:是;',
    is_delete        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0:否;1:是;',
    create_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人id',
    create_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '创建人名称',
    create_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_user_id   varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人id',
    update_user_name varchar(32)      NOT NULL DEFAULT '' COMMENT '更新人名称',
    update_time      datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id),
    KEY idx_user_id (user_id),
    KEY idx_org_id (org_id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 53
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='用户组织关系表';