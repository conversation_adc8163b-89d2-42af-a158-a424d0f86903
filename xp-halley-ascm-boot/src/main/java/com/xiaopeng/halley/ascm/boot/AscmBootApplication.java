package com.xiaopeng.halley.ascm.boot;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@MapperScan({"com.xiaopeng.**.mapper"})
@EnableScheduling
@Slf4j
@EnableAsync
public class AscmBootApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(AscmBootApplication.class);
        application.run(args);
    }

}