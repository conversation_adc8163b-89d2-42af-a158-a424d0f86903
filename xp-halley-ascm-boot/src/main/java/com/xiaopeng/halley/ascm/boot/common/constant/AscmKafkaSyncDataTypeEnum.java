package com.xiaopeng.halley.ascm.boot.common.constant;

/**
 * @Author：huqizhi
 * @Date：2024/1/8 11:22
 */
public enum AscmKafkaSyncDataTypeEnum {

    KUAIDI100_DATA_SYNC("kuaidi100_data_sync", "快递100快递信息同步"),
    CREATE_WAYBILL_RETRY_SYNC("create_waybill_retry_sync", "创建运单重试条目"),
    SYNC_DATA_API_DETAILS("sync_data_api_details", "同步数据接口详情"),
    ASCM0084_DATA_SYNC("ascm0084_data_sync", "ascm0084接口信息同步");
    final String key;
    final String des;

    AscmKafkaSyncDataTypeEnum(String key, String des) {
        this.key = key;
        this.des = des;
    }

    public String getKey() {
        return key;
    }

    public String getDes() {
        return des;
    }
}
