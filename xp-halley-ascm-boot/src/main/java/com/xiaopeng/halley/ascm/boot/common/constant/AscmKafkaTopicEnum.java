package com.xiaopeng.halley.ascm.boot.common.constant;

import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2024/1/8 11:06
 */
@Getter
public enum AscmKafkaTopicEnum {

    DATA_INSTALL(TopicNames.DATA_SYNC, Collections.singletonList(KafkaConstant.EVENT_DATA_SYNC));

    private final String topicName;
    private final List<String> eventKeys;

    AscmKafkaTopicEnum(String topicName, List<String> eventKeys) {
        this.topicName = topicName;
        this.eventKeys = eventKeys;
    }

    public static AscmKafkaTopicEnum getTopicByKey(String key) {
        for (AscmKafkaTopicEnum topicEnum : values()) {
            if (topicEnum.eventKeys.contains(key)) {
                return topicEnum;
            }
        }
        throw new IllegalArgumentException("不存在key[" + key + "]关联的Topic");
    }

}
