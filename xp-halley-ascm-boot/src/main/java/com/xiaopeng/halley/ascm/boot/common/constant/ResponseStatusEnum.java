package com.xiaopeng.halley.ascm.boot.common.constant;

/**
 * 响应状态码
 */
@SuppressWarnings("unused")
public enum ResponseStatusEnum {

    /**
     * 通用响应
     */
    RESPONSE_TEXT(100, "请把信息反馈给用户"),
    RESPONSE_LINK(101, "建议跳转至新地址"),
    RESPONSE_IMAGE(102, "建议显示图片"),
    RESPONSE_SUCCESS(200, "success"),
    RESPONSE_NOT_MODIFIED(304, "nothing change."),
    RESPONSE_BAD_REQUEST(400, "非法请求"),
    RESPONSE_NOT_AUTHORIZED(401, "授权未通过."),
    RESPONSE_NOT_AUTHORIZED_TIMEOUT(402, "授权签名超时."),
    RESPONSE_FORBIDDEN(403, "禁止访问."),
    RESPONSE_NOT_FOUND(404, "请求的资源不存在"),
    RESPONSE_STATUS_ERROR(500, "内部错误"),

    RESPONSE_SF_BIZ_ERROR(21001, "SF业务异常"),
    RESPONSE_SF_BIZ_DATA_EMPTY(21002, "SF响应异常"),
    RESPONSE_SF_BIZ_TOKEN_EMPTY(21003, "SF token为空"),
    RESPONSE_SF_DECRYPT_ERROR(21004, "SF解密异常"),
    RESPONSE_SF_ENCRYPT_ERROR(21004, "SF加密异常"),
    RESPONSE_SF_TOKEN_ERROR(21401, "SF toke 错误"),
    RESPONSE_NOT_BIND_SF_ACCOUNT(21404, "未绑定SF账号"),
    RESPONSE_SF_TIMEOUT(21501, "SF接口超时"),

    RESPONSE_OCR_SERVER_ERROR(1101, "OCR服务异常."),
    RESPONSE_OCR_DISCERN_ERROR(1102, "图片不清晰, OCR无法识别"),

    RESPONSE_USER_CENTER_NOT_REGISTER(22001, "用户未注册APP"),
    RESPONSE_USER_CENTER_NOT_LOGIN_IM(22002, "用户未使用IM"),

    RESPONSE_IM_HANDLE(30001, "线索模块正在处理群"),

    RESPONSE_SF_CAR_NON_IDLE(31001, "车辆处于非空闲，请检查车辆状态"),
    RESPONSE_SF_CAR_NON_EXIST(31002, "车辆不存在"),
    RESPONSE_SF_TD_FINISH(31003, "试驾录入已经完成"),
    RESPONSE_SF_EXPERIENCE_NON_EXIST(31004, "试驾体验不存在"),
    RESPONSE_SF_RESULT_NON_EXIST(31005, "试驾结果未出或已通过"),
    RESPONSE_SF_EXAMINE_EXIST(31006, "试驾审批已经存在"),
    RESPONSE_SF_RESULT_OVER_TIME(31007, "试驾申诉已过期"),
    RESPONSE_SF_APPEAL_NEED_OWN(31007, "试驾申诉仅支持本人发起"),
    RESPONSE_SF_NOT_START(31008, "试驾未开始，无法完成，请先点击开始试驾"),

    BOXVERIFY_BV_NOT_FOUND(41001, "找不到箱号信息"),
    BOXVERIFY_BV_DIFFERENT_WAYBILL(41002, "来自不同运单,无法完成扫描"),
    BOXVERIFY_BV_WAYBILL_NOT_EXIST(41003, "运单不存在！"),
    BOXVERIFY_BV_WAYBILL_NOT_BOXVERIFY(41004, "当前运单状态不能核箱！"),
    BOXVERIFY_BV_BOXVERIFY_FAIL(41005, "核箱失败"),
    BOXVERIFY_BV_FAIL(41006, "状态异常！");

    private final int code;

    private final String message;

    ResponseStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据状态码获取反馈
     *
     * @param code 状态码
     * @return 反馈体
     */
    static public ResponseStatusEnum get(int code) {
        ResponseStatusEnum[] responseStatuses = values();
        for (ResponseStatusEnum responseStatus : responseStatuses) {
            if (responseStatus.getCode() == code) {
                return responseStatus;
            }
        }
        return null;
    }

    /**
     * 根据CODE获取消息
     *
     * @param code 状态码
     * @return 文案
     */
    static public String getMessage(int code) {
        ResponseStatusEnum[] responseStatuses = values();
        for (ResponseStatusEnum responseStatus : responseStatuses) {
            if (responseStatus.getCode() == code) {
                return responseStatus.getMessage();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
