package com.xiaopeng.halley.ascm.boot.common.constant;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.constant
 * @Date 2024/7/2 9:40
 */
public interface WaybillStatusConstants {
    // 运单状态常量
    /**
     * 待发布
     */
    String PENDING_RELEASE = "待发布"; // 运单状态 --- 待发布
    /**
     * 已发布
     */
    String PUBLISHED = "已发布"; // 运单状态 --- 已发布
    /**
     * 待提货
     */
    String PENDING_PICKUP = "待提货"; // 运单状态 --- 待提货
    /**
     * 待运输
     */
    String PENDING_TRANSIT = "待运输"; // 运单状态 --- 待运输
    /**
     * 运输中
     */
    String IN_TRANSIT = "运输中"; // 运单状态 --- 运输中
    /**
     * 异常中
     */
    String EXCEPTION_OCCURRED = "异常中"; // 运单状态 --- 异常中
    /**
     * 已取消
     */
    String CANCELED = "已取消"; // 运单状态 --- 已取消
    /**
     * 已完成
     */
    String COMPLETED = "已完成"; // 运单状态 --- 已完成
}
