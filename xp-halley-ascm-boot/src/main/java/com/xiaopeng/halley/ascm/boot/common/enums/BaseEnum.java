package com.xiaopeng.halley.ascm.boot.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 通用枚举接口
 *
 * @param <E>
 */
@SuppressWarnings("all")
public interface BaseEnum<E extends Enum<?>> extends IEnum<Integer> {
	BaseEnum UNKOWN_ENUM = new BaseEnum() {
		@Override
		public String getLabel() {
			return "未知";
		}

		@Override
		public Integer getValue() {
			return -1;
		}
	};

	static BaseEnum<?>[] values(Class<?> clazz) {
		Class<? extends BaseEnum<?>> enumClass = (Class<? extends BaseEnum<?>>) clazz;
		return enumClass.getEnumConstants();
	}

	static <E> E valueOf(Class<?> clazz, Integer value) {
		try {
			Class<? extends BaseEnum<?>> enumClass = (Class<? extends BaseEnum<?>>) clazz;
			for (BaseEnum<?> baseEnum : enumClass.getEnumConstants()) {
				if (baseEnum.getValue().equals(value)) {
					return (E) baseEnum;
				}
			}
		} catch (Exception e) {
			throw new IllegalArgumentException("无法将value转换为对应的BaseEnum类型");
		}
		return (E) UNKOWN_ENUM;
	}

	static <E> E labelOf(Class<?> clazz, String label) {
		try {
			Class<? extends BaseEnum<?>> enumClass = (Class<? extends BaseEnum<?>>) clazz;
			for (BaseEnum<?> baseEnum : enumClass.getEnumConstants()) {
				if (baseEnum.getLabel().equals(label)) {
					return (E) baseEnum;
				}
			}
		} catch (Exception e) {
			throw new IllegalArgumentException("无法将label转换为对应的BaseEnum类型");
		}
		return (E) UNKOWN_ENUM;
	}

	String getLabel();

	@JsonValue
	Integer getValue();
}
