package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.enums
 * @Date 2024/4/25 11:13
 */
@Getter
public enum BusinessTypeEnum {
    WMS(1, "10"),

    <PERSON><PERSON><PERSON>(2, "30"),

    <PERSON><PERSON>(3, "20"),

    <PERSON><PERSON><PERSON>(5, "50"),

    XL(4, "40");

    private final Integer num;
    private final String type;

    BusinessTypeEnum(Integer num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getBusinessType(Integer num) {
        BusinessTypeEnum[] sendSystemEnums = BusinessTypeEnum.values();
        for (BusinessTypeEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getType();
            }
        }
        return "50";
    }
}
