package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.enums
 * @Date 2024/5/10 14:17
 */
@Getter
public enum InterfaceConstantEnum {
    ASCM0083(1, "ASCM0083"),

    ASCM0086(2, "ASCM0086");

    private final Integer num;
    private final String name;

    InterfaceConstantEnum(Integer num, String name) {
        this.num = num;
        this.name = name;
    }

    public static String getInterfaceConstant(Integer num) {
        InterfaceConstantEnum[] sendSystemEnums = InterfaceConstantEnum.values();
        for (InterfaceConstantEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getName();
            }
        }
        return "-";
    }
}
