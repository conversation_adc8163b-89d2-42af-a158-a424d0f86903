package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.enums
 * @Date 2024/4/22 16:33
 */
@Getter
public enum InterfaceDescribeEnum {

    WMS(1, "计划发运信息"),

    JF<PERSON>(2, "实际发运信息"),

    JD(3, "实际发运信息"),

    XL(4, "实际发运信息"),

    XL_WMS(5, "计划发运信息")
    ;

    private final Integer num;
    private final String type;

    InterfaceDescribeEnum(Integer num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getInterfaceDescribe(Integer num) {
        InterfaceDescribeEnum[] sendSystemEnums = InterfaceDescribeEnum.values();
        for (InterfaceDescribeEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getType();
            }
        }
        return "-";
    }
}
