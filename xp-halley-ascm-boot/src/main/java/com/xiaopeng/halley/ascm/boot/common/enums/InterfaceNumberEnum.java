package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.enums
 * @Date 2024/4/22 16:33
 */
@Getter
public enum InterfaceNumberEnum {

    WMS(1, "ASCM0086"),

    <PERSON><PERSON><PERSON>(2, "ASCM0083"),

    <PERSON><PERSON>(3, "ASCM0083"),

    XL(4, "ASCM0083"),

    XL_WMS(5, "ASCM0083");
    ;

    private final Integer num;
    private final String type;

    InterfaceNumberEnum(Integer num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getInterfaceNumber(Integer num) {
        InterfaceNumberEnum[] sendSystemEnums = InterfaceNumberEnum.values();
        for (InterfaceNumberEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getType();
            }
        }
        return "-";
    }
}
