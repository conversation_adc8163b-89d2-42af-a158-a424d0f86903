package com.xiaopeng.halley.ascm.boot.common.enums;

/**
 * @Author：huqizhi
 * @Date：2023/7/7 9:51
 */
public enum OrderTypeEnum {
    URGENT(1, "紧急"),
    ROUTINE(2, "常规");

    private final Integer num;
    private final String type;

    OrderTypeEnum(Integer num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getTypeValue(Integer num) {
        switch (num) {
            case 1:
                return URGENT.getType();
            case 2:
                return ROUTINE.getType();
            default:
                return "订单类型不存在！";
        }
    }

    public Integer getNum() {
        return num;
    }

    public String getType() {
        return type;
    }
}
