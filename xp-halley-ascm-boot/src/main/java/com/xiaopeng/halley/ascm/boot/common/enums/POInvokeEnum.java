package com.xiaopeng.halley.ascm.boot.common.enums;

public enum POInvokeEnum {
    SI_ASCM0087_Syn_Out("SI_ASCM0087_Syn_Out", "ASCM0087(ASCM发ERP实际发运签收数据) ", "/BS_VSCM/SI_ASCM0087_Syn_Out"),
    SI_ASCM0086_Syn_Out("SI_ASCM0086_Syn_Out", "ASCM0086(ASCM发WMS实际发运数据) ", "/BS_VSCM/SI_ASCM0086_Syn_Out"),
    SI_ASCM0083_Syn_Out("SI_ASCM0083_Syn_Out ", "ASCM0083(ASCM发到分仓WMS运单收货数据) ", "/BS_VSCM/SI_ASCM0083_Syn_Out"),
    SI_ASCM0040_Syn_Out("SI_ASCM0040_Syn_Out ", "打印装箱清单运输交接单 ", "/BS_VSCM/SI_ASCM0040_Syn_Out"),
    SI_ASCM0088_Syn_Out("SI_ASCM0088_Syn_Out", "查询WMS箱号数量", "/BS_VSCM/SI_ASCM0088_Syn_Out");

    String apiCode;
    String apiName;
    String suffixUrl;

    POInvokeEnum(String apiCode, String apiName, String suffixUrl) {
        this.apiCode = apiCode;
        this.apiName = apiName;
        this.suffixUrl = suffixUrl;
    }

    public static POInvokeEnum getDescriptor(String apiCode) {
        POInvokeEnum[] POInvokeEnums = values();
        POInvokeEnum[] var2 = POInvokeEnums;
        int var3 = POInvokeEnums.length;

        for (int var4 = 0; var4 < var3; ++var4) {
            POInvokeEnum item = var2[var4];
            if (item.getApiCode().equals(apiCode)) {
                return item;
            }
        }

        return null;
    }

    public String getApiCode() {
        return this.apiCode.trim();
    }

    public void setApiCode(String apiCode) {
        this.apiCode = apiCode;
    }

    public String getApiName() {
        return this.apiName.trim();
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public String getSuffixUrl() {
        return this.suffixUrl.trim();
    }

    public void setSuffixUrl(String suffixUrl) {
        this.suffixUrl = suffixUrl;
    }
}