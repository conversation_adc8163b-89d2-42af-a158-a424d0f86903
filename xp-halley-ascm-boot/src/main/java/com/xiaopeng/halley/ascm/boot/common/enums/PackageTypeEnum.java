package com.xiaopeng.halley.ascm.boot.common.enums;

public enum PackageTypeEnum {
    //包装类型(0-单独包装,1-混合包装,2-虚拟包装)
    ONE("0", "单独包装"),
    MORE("1", "混合包装"),
    VIRTUAL("2", "虚拟包装");

    private final String num;
    private final String type;

    PackageTypeEnum(String num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getPackageType(String num) {
        PackageTypeEnum[] packageTypeEnumList = PackageTypeEnum.values();
        for (PackageTypeEnum item : packageTypeEnumList) {
            if (item.getNum().equals(num)) {
                return item.getType();
            }
        }
        return "状态未知";
    }

    public String getNum() {
        return num;
    }

    public String getType() {
        return type;
    }
}
