package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用于统一标识系统与外部交互的接口。
 */
@Getter
@AllArgsConstructor
public enum RemoteApiEnum {
	ASCM0030("WMS运单信息传ASCM", ApiType.INCOMING, System.WMS),
	ASCM0050("WMS包装完成传ASCM", ApiType.INCOMING, System.WMS),
	ASCM0084("ASCM0084（收货情况）", ApiType.INCOMING, System.JD),
	ASCM0085("ASCM0085（在途跟踪）", ApiType.INCOMING, System.JD),
	ASCM0085_1("ASCM0085-1（出库结果）", ApiType.INCOMING, System.JD),
	ASCM0089("WMS箱子物料信息传ASCM", ApiType.INCOMING, System.WMS),

	ASCM0040("打印装箱清单运输交接单", ApiType.OUTGOING, System.XL),
	ASCM0083("ASCM发到分仓WMS运单收货数据", ApiType.OUTGOING, System.XL),
	ASCM0086("ASCM发WMS实际发运数据", ApiType.OUTGOING, System.XL),
	ASCM0087("ASCM发ERP实际发运签收数据", ApiType.OUTGOING, System.XL),
	ASCM0088("查询WMS箱号数量", ApiType.OUTGOING, System.XL),
	;

	/**
	 * 接口场景
	 */
	private final String name;

	/**
	 * 定义接口的类型
	 */
	private final ApiType type;

	/**
	 * 目标系统
	 */
	private final System[] targetSystem;

	RemoteApiEnum(String name, ApiType type, System system) {
		this.name = name;
		this.type = type;
		this.targetSystem = new System[]{system};
	}

	enum System {
		WMS, JD, XL
	}

	/**
	 * 接口方向枚举
	 */
	enum ApiType {
		/**
		 * 表示该接口是接收来自外部系统的请求。
		 * 你的系统作为服务端。
		 */
		INCOMING,

		/**
		 * 表示该接口是向外部系统发送请求。
		 * 你的系统作为客户端。
		 */
		OUTGOING
	}
}