package com.xiaopeng.halley.ascm.boot.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.common.enums
 * @Date 2024/4/22 16:33
 */
@Getter
public enum SendSystemEnum {

    WMS(1, "WMS", "WMS"),

    JF<PERSON>(2, "SMGL", "捷富凯"),

    JD(3, "ECLP", "京东"),

    XL(4, "XDRAGON", "骁龙"),

    WMS_XL(5, "XDRAGON", "自营骁龙")
    ;

    private final Integer num;
    private final String type;
    private final String name;

    SendSystemEnum(Integer num, String type, String name) {
        this.num = num;
        this.type = type;
        this.name = name;
    }

    public static String getSystemType(Integer num) {
        SendSystemEnum[] sendSystemEnums = SendSystemEnum.values();
        for (SendSystemEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getType();
            }
        }
        return "系统未知";
    }

    public static String getSystemName(Integer num) {
        SendSystemEnum[] sendSystemEnums = SendSystemEnum.values();
        for (SendSystemEnum item : sendSystemEnums) {
            if (item.getNum().equals(num)) {
                return item.getName();
            }
        }
        return "系统未知";
    }
}
