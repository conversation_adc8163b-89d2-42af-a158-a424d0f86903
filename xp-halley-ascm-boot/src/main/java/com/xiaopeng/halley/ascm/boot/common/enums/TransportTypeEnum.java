package com.xiaopeng.halley.ascm.boot.common.enums;

/**
 * @Author：huqizhi
 * @Date：2023/7/7 9:51
 */
public enum TransportTypeEnum {
    EXPRESS(1, "快运"),
    CAR(2, "专车"),
    EXPRESS_DELIVERY(3, "快递");

    private final Integer num;
    private final String type;

    TransportTypeEnum(Integer num, String type) {
        this.num = num;
        this.type = type;
    }

    public static String getTypeValue(Integer num) {
        switch (num) {
            case 1:
                return EXPRESS.getType();
            case 2:
                return CAR.getType();
            case 3:
                return EXPRESS_DELIVERY.getType();
            default:
                return "订单类型不存在！";
        }
    }

    public Integer getNum() {
        return num;
    }

    public String getType() {
        return type;
    }
}
