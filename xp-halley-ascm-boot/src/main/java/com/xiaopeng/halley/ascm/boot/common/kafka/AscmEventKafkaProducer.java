package com.xiaopeng.halley.ascm.boot.common.kafka;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaTopicEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author：huqizhi
 * @Date：2024/1/8 11:05
 */
@Slf4j
@Component
public class AscmEventKafkaProducer {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 业务发起kafka消息通知
     */
    public void pushEvent(String eventKey, Object message) {
        AscmKafkaTopicEnum topicEnum = AscmKafkaTopicEnum.getTopicByKey(eventKey);
        log.info("topic -> {}, message -> {}", topicEnum.getTopicName(), message);
        kafkaTemplate.send(topicEnum.getTopicName(), JSONUtil.toJsonStr(MapUtil.builder()
                .put("eventKey", eventKey)
                .put("eventData", message)
                .map())
        ).addCallback(success -> log.info("kafka消息推送完成！ metadata:" + JSON.toJSONString(message)),
                failure -> log.error("kafka消息推送失败！ metadata:" + JSON.toJSONString(message), failure));

    }

}
