package com.xiaopeng.halley.ascm.boot.common.kafka;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaSyncDataTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.constant.KafkaConstant;
import com.xiaopeng.halley.ascm.boot.common.constant.TopicNames;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillRetryService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.service.sync.ThirdPartySyncWaybillService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * @Author：huqizhi
 * @Date：2024/1/8 10:00
 */
@Slf4j
@Component
public class KafkaSyncEventListener {

    @Resource
    private BzWaybillService bzWaybillService;

    @Resource
    private BzWaybillRetryService bzWaybillRetryService;

    @Resource
    private ThirdPartySyncWaybillService thirdPartySyncWaybillService;

    @KafkaListener(topics = {TopicNames.DATA_SYNC})
    public void syncKuaidiData(ConsumerRecord<?, ?> record, Acknowledgment ack) {
        Optional.ofNullable(record.value())
                .map(String::valueOf)
                .filter(StringUtils::isNotBlank)
                .ifPresent(message -> {
                    try {
                        Map<String, Object> value = JSONUtil.toBean(message, new TypeReference<Map<String, Object>>() {
                        }, true);
                        String eventKey = BeanUtil.getProperty(value, "eventKey");
                        Map<String, Object> eventData = BeanUtil.getProperty(value, "eventData");

                        log.info("成功获取kafka推送事件，eventKey->{}，eventData->{}", eventKey, eventData);

                        if (StrUtil.isBlank(eventKey) || CollUtil.isEmpty(eventData)) {
                            log.error("kafka推送事件数据异常，eventKey->{}，eventData->{}", eventKey, eventData);
                            return;
                        }
                        if (KafkaConstant.EVENT_DATA_SYNC.equals(eventKey)) {
                            log.info("成功获取数据同步推送事件，准备更新数据库，eventData->{}", eventData);
                            String data = BeanUtil.getProperty(eventData, "data");
                            String key = BeanUtil.getProperty(eventData, "key");
                            Long ts = 0L;

                            try {
                                ts = BeanUtil.getProperty(eventData, "ts");
                            } catch (Exception e) {
                                log.info("解析出错，Exception ->{}", e.getMessage());
                            }

                            if (StrUtil.isBlank(key)) {
                                log.error("kafka推送事件数据异常，data->{}，key->{}，ts->{}", data, key, ts);
                                return;
                            }
                            if (AscmKafkaSyncDataTypeEnum.KUAIDI100_DATA_SYNC.getKey().equals(key)) {
                                log.info("成功获取快递100同步快递数据推送事件，准备更新数据库，eventData->{}", eventData);
                                if (StrUtil.isBlank(data)) {
                                    data = "";
                                }
                                bzWaybillService.kuaidiAsyncSaveData(data);
                            }
                            if (AscmKafkaSyncDataTypeEnum.ASCM0084_DATA_SYNC.getKey().equals(key)) {
                                log.info("成功获取ascm0084接口推送事件，准备更新数据库，eventData->{}", eventData);
                                if (StrUtil.isBlank(data)) {
                                    data = "";
                                }
                                if (ObjectUtil.isNull(ts) || 0 == ts) {
                                    ts = System.currentTimeMillis();
                                }
                                thirdPartySyncWaybillService.saveWaybillData(data, ts.toString());
                            }
                            if (AscmKafkaSyncDataTypeEnum.CREATE_WAYBILL_RETRY_SYNC.getKey().equals(key)) {
                                log.info("成功获取新建重推条目推送事件，准备更新数据库，eventData->{}", eventData);
                                if (StrUtil.isBlank(data)) {
                                    data = "";
                                }
                                bzWaybillRetryService.createRetryItem(data);
                            }
                            if (AscmKafkaSyncDataTypeEnum.SYNC_DATA_API_DETAILS.getKey().equals(key)) {
                                log.info("成功获取接口详情推送事件，准备更新数据库，eventData->{}", eventData);
                                bzWaybillRetryService.apiDetails(eventData);
                            }
                        }
                    } catch (Throwable throwable) {
                        log.error("提取事件数据异常：" + throwable.getMessage(), throwable);
                    }
                });

        // 手动提交offset
        ack.acknowledge();
    }
}
