package com.xiaopeng.halley.ascm.boot.common.redis;

import lombok.Getter;

@Getter
public enum RedisKeyManager {
    WAYBILL_CODE_SERIAL_NUMBER("waybill:code:serial:number:%s", "运单编号流水号"),
    PDA_ROUTE_CACHE("ascm:pda:route:%s:%s", "PDA路线缓存"),
    ASCM_USER_CACHE("ascm:user:cache:%s", "用户缓存"),

    ADDRESS_IN_SUCCESS_EXCEL("ascm:address:in:success:excel:%s", "地址数据导入校验成功数据"),
    ADDRESS_IN_FAIL_EXCEL("ascm:address:in:fail:excel:%s", "地址数据导入校验错误数据"),
    PRINT_OUT_TASK("ascm:print:out:task:%s", "ASCM打印任务"),
    ROUND_IN_SUCCESS_EXCEL("ascm:round:in:success:excel:%s", "轮次数据导入校验成功数据"),
    ROUND_IN_FAIL_EXCEL("ascm:round:in:fail:excel:%s", "轮次数据导入校验错误数据"),
    PATH_IN_SUCCESS_EXCEL("ascm:path:in:success:excel:%s", "线路数据导入校验成功数据"),
    PATH_IN_FAIL_EXCEL("ascm:path:in:fail:excel:%s", "线路数据导入校验错误数据"),
    CREATE_TODAY_ASCM_INDEX("ascm:erp:today:ascm:%s", "当天ASCM编码"),
    PDI_ERP_SYNC_ERPORDER("ascm:erp:sync:erporder:%s", "erp交货单同步"),
    CREATE_TODAY_INDEX("ascm:create:today:index:%s", "生成当天流水号"),
    CREATE_TODAY_REPAIR_INDEX("ascm:create:today:repair:index:%s:%s", "生成当天流水号"),
    IMPORT_WAYBILL_RULES("ascm:import:waybill:rules:%s:%s", "运单匹配规则导入"),
    IMPORT_TRANSPORT_TIMELINESS("ascm:import:transport:timeliness:%s:%s", "运输时效导入"),
    IMPORT_BASE_BATTERY_PROJECT("ascm:import:base:battery:project:%s:%s", "电池项目维护导入"),
    IMPORT_BASE_CHECK_STANDARD("ascm:import:base:check:standard:%s:%s", "物料校验标准导入"),
    IMPORT_UNIT_PACKAGE_INFO("ascm:import:unit:package:info:%s:%s", "物料单元包装信息导入"),
    IMPORT_CHECK_RULES_INFO("ascm:import:check:rules:info:%s:%s", "物料校验标准匹配规则信息导入"),
    IMPORT_BASE_BATTERY_ROUTE_COST("ascm:import:base:battery:route:cost:%s:%s", "电池线路费用维护导入"),
    IMPORT_THE_RETURN_BATTERY_WAYBILL("ascm:import:the:return:battery:waybill:%s:%s", "导入返厂电池运单"),
    IMPORT_BASE_SHOP_BATTERY_DATA("ascm:import:base:shop:battery:data:%s:%s", "门店电池搬运人导入"),
    IMPORT_ACTUAL_TIME("ascm:import:actual:time:%s:%s", "实际到达时间导入"),
    SPARE_PARTS_PIC_INFO("ascm:spare:parts:pic:info:%s", "缓存备件图片访问地址"),
    SPARE_PARTS_RAW_PIC_INFO("ascm:spare:parts:raw:pic:info:%s", "缓存备件原图片访问地址"),
    CREATE_APPOINTMENT_TODAY_INDEX("ascm:create:appointment:today:index:%s", "生成预约模块当天流水号后缀");

    private final String key;
    private final String descriptor;

    RedisKeyManager(String key, String descriptor) {
        this.key = key;
        this.descriptor = descriptor;
    }

    public static void main(String[] args) {
        System.out.println(PDI_ERP_SYNC_ERPORDER.buildKey("99999"));
    }

    public String buildKey(Object... args) {
        return String.format(this.key, args);
    }

}
