package com.xiaopeng.halley.ascm.boot.component;

import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 流水号生成器
 */
@Component
public class SerialNumberGenerator {
	@Resource
	private RedissonClient redissonClient;

	public String generateWaybillCode(String lgort) {
		String key = RedisKeyManager.WAYBILL_CODE_SERIAL_NUMBER.buildKey(lgort);
		return lgort + generateSerialNumber(key, "yyyyMMdd", 3);
	}

	/**
	 * 生成序列号
	 *
	 * @param keyPrefix   Redis key前缀
	 * @param datePattern 日期格式 如：yyyyMMdd
	 * @param length      序列号长度
	 * @return 生成的序列号
	 */
	public String generateSerialNumber(String keyPrefix, String datePattern, int length) {
		LocalDate today = LocalDate.now();
		String dateStr = today.format(DateTimeFormatter.ofPattern(datePattern));
		String key = keyPrefix + ":" + dateStr;

		// 获取或创建当天的原子计数器
		RAtomicLong dailyCounter = redissonClient.getAtomicLong(key);
		// 若不存在，设置过期时间，精确到当天的最后一秒
		if (!dailyCounter.isExists()) {
			dailyCounter.set(1);
			LocalDateTime tomorrowStart = today.plusDays(1).atStartOfDay();
			Instant expiryInstant = tomorrowStart.atZone(ZoneId.systemDefault()).toInstant();
			dailyCounter.expire(expiryInstant);
		}
		// 格式化流水号
		String serialNumberStr = String.format("%0" + length + "d", dailyCounter.getAndIncrement());
		// 组合日期和流水号
		return dateStr + serialNumberStr;
	}
}
