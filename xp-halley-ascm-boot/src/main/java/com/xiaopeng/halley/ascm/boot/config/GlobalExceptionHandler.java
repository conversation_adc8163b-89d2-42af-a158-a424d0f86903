package com.xiaopeng.halley.ascm.boot.config;

import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.stream.Collectors;

@RestControllerAdvice
@Order(1)
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler({BindException.class})
    public Result<String> BindExceptionHandler(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        // 多个参数校验时，默认返回所有的错误结果
        String messages = fieldErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(","));
        return ResultUtil.failed(messages);
    }

    @ExceptionHandler(value = ResultException.class)
    public Result<String> handleResultException(ResultException ex) {
        log.error("自定义异常", ex);
        return ResultUtil.failed(ex.getMessage() == null || "null".equals(ex.getMessage()) ? "空指针异常" : ex.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public Result<String> handleException(Exception ex) {
        log.error("未知异常", ex);
        return ResultUtil.failed(ex.getMessage() == null || "null".equals(ex.getMessage()) ? "空指针异常" : ex.getMessage());
    }

    @ExceptionHandler(value = RuntimeException.class)
    public Result<String> handleRuntimeException(RuntimeException ex) {
        log.error("运行时异常", ex);
        return ResultUtil.failed(ex.getMessage() == null || "null".equals(ex.getMessage()) ? "空指针异常" : ex.getMessage());
    }

}
