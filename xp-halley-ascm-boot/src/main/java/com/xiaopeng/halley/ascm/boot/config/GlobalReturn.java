package com.xiaopeng.halley.ascm.boot.config;

import cn.hutool.core.util.ObjectUtil;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.ExcludeResponseBody;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.lang.reflect.Method;

@RestControllerAdvice
public class GlobalReturn implements ResponseBodyAdvice<Object> {

    @Override
    @SuppressWarnings("all")
    public boolean supports(MethodParameter methodParameter,
                            Class<? extends HttpMessageConverter<?>> aClass) {

        if (methodParameter.getMethod().getReturnType().isAssignableFrom(Result.class)) {
            // 已经是包装类型，不包装
            return false;
        } else {
            // 如果是controller就都统一拦截加对象
            if (methodParameter.getDeclaringClass().isAnnotationPresent(GlobalResponseBody.class)) {
                // 包装
                return true;
            } else {
                // 不包装
                return false;
            }
        }
    }

    @Override
    @SuppressWarnings("all")
    public Object beforeBodyWrite(Object object, MethodParameter methodParameter, MediaType mediaType,
                                  Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest,
                                  ServerHttpResponse serverHttpResponse) {

        //如果有@ExcludeGlobalReturn就不用包装
        Method method = methodParameter.getMethod();
        boolean annotationPresent = method.isAnnotationPresent(ExcludeResponseBody.class);
        if (annotationPresent) {
            return object;
        }
        // 来到这里一定是需要包装的
        if (ObjectUtil.isNotNull(object)) {
            return ResultUtil.success(object);
        } else {
            return ResultUtil.success();
        }
    }
}