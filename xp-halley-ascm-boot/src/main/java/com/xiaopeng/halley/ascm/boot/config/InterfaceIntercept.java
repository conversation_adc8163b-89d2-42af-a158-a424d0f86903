package com.xiaopeng.halley.ascm.boot.config;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaSyncDataTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.constant.KafkaConstant;
import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.entity.EgressGatewayRequest;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.config
 * @Date 2024/5/6 9:58
 */
@Slf4j
@Aspect // FOR AOP
@Component
public class InterfaceIntercept {

    @Resource
    private AscmEventKafkaProducer ascmEventKafkaProducer;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    @Around("execution(* com.xiaopeng.halley.ascm.boot.service.PoRequestHelp.invoke(com.xiaopeng.halley.ascm.boot.entity.EgressGatewayRequest))")
    public Object aroundInterception(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法参数
        joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        log.info("执行调用三方接口方法 参数：{}", JSON.toJSONString(args));
        Object proceed = null;

        // 判断接口是否需要发送
        EgressGatewayRequest egressGatewayRequest = (EgressGatewayRequest) args[0];
        String apiCode = egressGatewayRequest.getApiCode();
        List<String> apiCodes = new ArrayList<>();
        apiCodes.add(POInvokeEnum.SI_ASCM0083_Syn_Out.getApiCode());
        apiCodes.add(POInvokeEnum.SI_ASCM0086_Syn_Out.getApiCode());
        boolean needSend = apiCodes.contains(apiCode);

        try {
            proceed = joinPoint.proceed(args);
            if (needSend) {
                sendKafka(args, proceed);
            }
        } catch (Throwable e) {
            if (needSend) {
                sendKafka(args, Throwables.getStackTraceAsString(e));
            }
            log.error("记录接口报文信息异常 {}, EgressGatewayRequest: {}", e.getMessage(), JSON.toJSONString(egressGatewayRequest));
        }
        return proceed;
    }

    private void sendKafka(Object[] args, Object proceed) {
        // 封装内容
        Map<String, Object> map = new HashMap<>();
        map.put("args", args[0]);
        map.put("result", proceed);
        map.put("name", ObjectUtil.isNull(ascmLoginUserHelper.getLoginUser()) ? " " : ascmLoginUserHelper.getLoginUser().getName());

        // 发kafka异步处理
        ascmEventKafkaProducer.pushEvent(KafkaConstant.EVENT_DATA_SYNC, MapUtil.builder()
                .put("data", JSONObject.toJSONString(map))
                .put("key", AscmKafkaSyncDataTypeEnum.SYNC_DATA_API_DETAILS.getKey())
                .put("ts", System.currentTimeMillis()).map());
    }

}
