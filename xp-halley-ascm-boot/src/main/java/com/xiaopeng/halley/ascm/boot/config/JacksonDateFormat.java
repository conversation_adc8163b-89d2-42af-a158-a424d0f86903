package com.xiaopeng.halley.ascm.boot.config;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.util.StdDateFormat;

import java.text.FieldPosition;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class JacksonDateFormat extends StdDateFormat {
    public static final JacksonDateFormat instance = new JacksonDateFormat();
    private static final long serialVersionUID = -3201781773655300201L;

    public JacksonDateFormat() {
    }

    @Override
    public Date parse(String dateStr, ParsePosition pos) {
        return this.getDate(dateStr, pos);
    }

    @Override
    public Date parse(String dateStr) {
        ParsePosition pos = new ParsePosition(0);
        return this.getDate(dateStr, pos);
    }

    private Date getDate(String dateStr, ParsePosition pos) {
        SimpleDateFormat sdf = null;
        TimeZone tz = TimeZone.getTimeZone("Asia/Shanghai");

        boolean useTimezone = true;
        if (StrUtil.isBlank(dateStr)) {
            return null;
        } else {
            if (dateStr.matches("^\\d{4}-\\d{1,2}$")) {
                sdf = new SimpleDateFormat("yyyy-MM");
            } else if (dateStr.matches("^\\d{4}-\\d{1,2}-\\d{1,2}$")) {
                sdf = new SimpleDateFormat("yyyy-MM-dd");
            } else if (dateStr.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}$")) {
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            } else if (dateStr.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}:\\d{1,2}$")) {
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            } else if (dateStr.length() == 23) {
                sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
            } else {
                sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                useTimezone = false;
            }

            if (sdf != null) {
                if (useTimezone && tz != null) {
                    sdf.setTimeZone(tz);
                }

                return sdf.parse(dateStr, pos);
            } else {
                return super.parse(dateStr, pos);
            }
        }
    }

    @Override
    public StringBuffer format(Date date, StringBuffer toAppendTo, FieldPosition fieldPosition) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

        return sdf.format(date, toAppendTo, fieldPosition);
    }

    @Override
    public JacksonDateFormat clone() {
        JacksonDateFormat clone = new JacksonDateFormat();
        return clone;
    }
}
