package com.xiaopeng.halley.ascm.boot.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaopeng.halley.ascm.boot.config.interceptor.GetLoginTypeInterceptor;
import com.xiaopeng.halley.ascm.boot.config.interceptor.WechatAuthInterceptor;
import com.xiaopeng.halley.ascm.boot.config.interceptor.WechatAuthProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ConversionServiceFactoryBean;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Configuration
@EnableConfigurationProperties(WechatAuthProperties.class)
public class MvcConfig implements WebMvcConfigurer {

    @Resource
    private WechatAuthInterceptor wechatAuthInterceptor;

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new MappingJackson2HttpMessageConverter());
    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        configurer.favorPathExtension(false);
    }

    /**
     * 添加拦截器，给线程添加参数，用于判读是哪个端登入的请求
     *
     * @return
     */
    @Bean
    public GetLoginTypeInterceptor getLoginTypeInterceptor() {
        return new GetLoginTypeInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(wechatAuthInterceptor)
                .addPathPatterns("/**");
        registry.addInterceptor(this.getLoginTypeInterceptor())
                .addPathPatterns("/**");
    }

    @Bean
    public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter() {
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = converter.getObjectMapper();
        JacksonDateFormat cdf = new JacksonDateFormat();
        objectMapper.setDateFormat(cdf);
        objectMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        converter.setObjectMapper(objectMapper);
        return converter;
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        converter.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.ALL));
        return converter;
    }

    @Bean
    public SpringDateConverter springDateConverter() {
        return new SpringDateConverter();
    }

    @Bean
    @Resource
    public ConversionService getConversionService(SpringDateConverter converter) {
        ConversionServiceFactoryBean factoryBean = new ConversionServiceFactoryBean();
        Set<Converter> converters = new HashSet();
        converters.add(converter);
        factoryBean.setConverters(converters);
        return factoryBean.getObject();
    }
}
