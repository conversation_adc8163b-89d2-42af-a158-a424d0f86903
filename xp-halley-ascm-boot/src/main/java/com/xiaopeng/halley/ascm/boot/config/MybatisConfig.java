package com.xiaopeng.halley.ascm.boot.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.InterceptorConfig.MyTenantLineInnerInterceptor;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.MyTenantLineHandler;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.BaseFeeStandardService;
import com.xiaopeng.halley.ascm.boot.service.BzAsyncExportLogService;
import com.xpeng.athena.sdk.mbp.web.service.MbpService;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Configuration
public class MybatisConfig {

    //所有表中对应的租户字段 key表名 value字段
    // 这个可以理解为，规则，这些规则是如果需要匹配的时候的人勇的
    static ConcurrentHashMap<String, List<String>> concurrentHashMap = new ConcurrentHashMap<>();
    static List<String> ignoreTable = new ArrayList<>();

    //加载租户字段 可自定义
    static {
        ignoreTable.add("mbp_org_info");
        ignoreTable.add("mbp_org_type");
        ignoreTable.add("mbp_user_org_rel");
        ignoreTable.add("base_materials");
        ignoreTable.add("base_suppliers");
        ignoreTable.add("bz_supplier_material");
        ignoreTable.add("bz_supplier_package_info");
        ignoreTable.add("bz_delivery_orders");
        ignoreTable.add("bz_delivery_order_material");
        ignoreTable.add("base_check_standard");

        List<String> defaultList = new ArrayList<>();
        defaultList.add("lgort");
        defaultList.add("shop_code");

        log.info("-------------------------开始加载多租户的表字段默认关系----------------------");
        //注册自定义权限字段
        //运单
        concurrentHashMap.put("bz_waybill", defaultList);

        concurrentHashMap.put("base_print_config", Collections.singletonList("lgort"));

        // 导出日志
        // List<String> bzAsyncExportLogList = new ArrayList<>();
        // bzAsyncExportLogList.add("lgort");
        // concurrentHashMap.put("bz_async_export_log",bzAsyncExportLogList);

        //运单匹配规则
        concurrentHashMap.put("base_waybill_rules", defaultList);

        //打印任务
        concurrentHashMap.put("base_print_out_task", defaultList);

        List<String> newList = new ArrayList<>();
        newList.add("repair_center_num");
        //电池运单和维修中心
        concurrentHashMap.put("battery_way_bill", newList);
        concurrentHashMap.put("base_repair_center", newList);
        concurrentHashMap.put("base_battery_route_cost", newList);

    }

    @Resource
    private ApplicationContext context;

    public static List<String> getConcurrentHashMap(String tableName) {

        return CollUtil.isNotEmpty(concurrentHashMap.get(tableName)) ? concurrentHashMap.get(tableName) : new ArrayList<>();
    }

    @Bean
    @Primary
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        log.info("ascm MybatisAutoConfig running");

        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();

        mybatisPlusInterceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());

        mybatisPlusInterceptor.addInnerInterceptor(new MyTenantLineInnerInterceptor(new MyTenantLineHandler() {

            @Override
            public List<Expression> getTenantId() {
                // 这里需要获取到用户，然后判断用户的组织类型，然后通过组织类型去区分不同的仓库
                AscmLoginUserHelper ascmLoginUserHelper = context.getBean(AscmLoginUserHelper.class);

                AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

                try {
                    // 这一步是为了过滤掉定时器的情况
                    if (ObjectUtil.isNotNull(ascmLoginUser) && StrUtil.isNotBlank(ascmLoginUser.getUserId()) &&
                            Boolean.TRUE.equals(BaseFeeStandardService.threadLocal.get())) {
                        MbpService mbpService = context.getBean(MbpService.class);
                        List<Expression> expressions = mbpService.getLoginInfo().getOrganizationInfo().getOrganizationRelatedList()
                                .stream()
                                .filter(e -> StrUtil.contains(e.getRemark(), "="))
                                .map(e -> new StringValue(e.getRemark().split("=")[1]))
                                .collect(Collectors.toList());
                        log.info("expressions: {}", expressions);
                        return expressions;
                    }
                } catch (Exception e) {
                    log.warn("发生异常", e);
                }

                if (ObjectUtil.isNotNull(ascmLoginUser)) {
                    log.info("BootApplication AppletUserResponse 开始运行 [{}]", JSONObject.toJSONString(ascmLoginUser));
                }

                if (ObjectUtil.isNotNull(ascmLoginUser) && StrUtil.isNotBlank(ascmLoginUser.getUserId())) {

                    log.info("BootApplication AppletUserResponse 进来 0");

                    if (ObjectUtil.isNotNull(ascmLoginUser) && StrUtil.isNotBlank(ascmLoginUser.getAuth())) {

                        List<Expression> list = new ArrayList<>();
                        try {
                            for (String s : ascmLoginUser.getAuth().split(",")) {
                                list.add(new StringValue(s));
                            }
                        } catch (Exception e) {
                            //发生异常就啥也不给看
                            list = new ArrayList<>();
                            list.add(new StringValue("default"));
                        }
                        //如果什么都没填写 就啥也看不到
                        if (CollUtil.isEmpty(list)) {
                            list.add(new StringValue("default"));
                        }

                        log.info("BootApplication AppletUserResponse return 进来 1 [{}]", JSONObject.toJSONString(list));

                        return list;
                    }

                    log.info("BootApplication AppletUserResponse 进来 2");

                    return Collections.singletonList(new StringValue("default"));
                }

                try {

                    log.info("BootApplication AppletUserResponse 进来 3 0");

                    Map map = (Map) BzAsyncExportLogService.threadLocalUser.get();

                    log.info("BootApplication AppletUserResponse 进来 3 1 [{}]", JSONObject.toJSONString(map));

                    if (map.containsKey("lgort")) {

                        List<Expression> list = new ArrayList<>();

                        for (String s : map.get("lgort").toString().split(",")) {
                            list.add(new StringValue(s));
                        }

                        log.info("BootApplication AppletUserResponse 进来 3 2 [{}]", JSONObject.toJSONString(list));

                        return list;

                    }

                } catch (Exception e) {
                    log.info("BootApplication AppletUserResponse 进来 4");
                    return Collections.singletonList(new StringValue("default"));
                }

                // 由于ignoreTable中已经设置了如果没有用户，就会直接忽略，来到这里的都是有用户的
                // 如果用户可以执行到这句，证明组织有问题，弄个default出去，查不到数据即可
                log.info("BootApplication AppletUserResponse 进来 5");
                return Collections.singletonList(new StringValue("default"));
            }

            @Override
            public boolean ignoreTable(String tableName) {
                if (true) {
                    return true;
                }
                log.info("BootApplication ignoreTable [{}]", tableName);
                // 这里如果碰到是没有用户的，就直接跳过，不用多租户，但是那个更新是有问题的，如果有多租户的情况下

                // 如果是组织相关直接跳过
                log.info("BootApplication ignoreTable ignoreTableList [{}]", JSONObject.toJSONString(ignoreTable));
                if (ignoreTable.contains(tableName)) {
                    log.info("BootApplication ignoreTable ignoreTableList 跳过 [{}]", JSONObject.toJSONString(ignoreTable));
                    return true;
                }

                log.info("BootApplication ignoreTable 获取用户");

                AscmLoginUserHelper ascmLoginUserHelper = context.getBean(AscmLoginUserHelper.class);

                AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

                log.info("BootApplication LoginUser ignoreTable 开始运行 123 [" + JSONObject.toJSONString(ascmLoginUser) + "]");

                log.info("BootApplication AppletUserResponse 多租户进来");

                if (ObjectUtil.isNotNull(ascmLoginUser) && StrUtil.isNotBlank(ascmLoginUser.getUserId())) {
                    log.info("BootApplication AppletUserResponse 多租户进来 1");
                    log.info("多租户启用");
                    // 如果是有用户的调用，启动多商户插件
                    if (ascmLoginUser.getUserId().contains("wx-")) {
                        log.info("BootApplication AppletUserResponse wechat user 用户为微信用户 [{}]", JSONObject.toJSONString(ascmLoginUser));
                        // 如果是来自微信，则不用多租户
                        return true;
                    }
                    return false;
                } else {

                    log.info("BootApplication AppletUserResponse 多租户进来 2");
                    log.info("不启用");

                    // 如果是没有用户，证明是feign调用，不打开多商户插件，一律忽略

                    try {
                        Map map = (Map) BzAsyncExportLogService.threadLocalUser.get();
                        if (map.containsKey("lgort")) {
                            return false;
                        }

                    } catch (Exception e) {
                        return true;
                    }

                    log.info("不启用 这里");

                    return true;
                }
            }

        }));

        mybatisPlusInterceptor.addInnerInterceptor(new
                PaginationInnerInterceptor(DbType.MYSQL));

        return mybatisPlusInterceptor;
    }

}
