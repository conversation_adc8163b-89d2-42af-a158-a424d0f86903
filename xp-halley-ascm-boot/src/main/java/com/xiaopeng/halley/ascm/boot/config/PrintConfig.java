package com.xiaopeng.halley.ascm.boot.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "print")
public class PrintConfig {
	@Schema(description = "二维码url")
	private String url;

	@Schema(description = "需要远程打印的仓库类型")
	private List<Integer> warehouseTypes = new ArrayList<>();
}
