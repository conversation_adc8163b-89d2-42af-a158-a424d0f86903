package com.xiaopeng.halley.ascm.boot.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "shop-code")
public class ShopCodeMapperConfig {
	@Schema(description = "是否启用")
	private boolean enable;

	@Schema(description = "映射列表")
	private List<MapperInfo> mapperInfos;

	@Data
	public static class MapperInfo {
		@Schema(description = "门店城市")
		private String shopCity;

		@Schema(description = "原始门店编码")
		private String originShopCode;

		@Schema(description = "映射门店编码")
		private String mapperShopCode;
	}
}
