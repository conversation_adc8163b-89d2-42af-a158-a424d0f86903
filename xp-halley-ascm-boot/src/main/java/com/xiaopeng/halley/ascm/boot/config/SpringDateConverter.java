package com.xiaopeng.halley.ascm.boot.config;

import cn.hutool.core.util.StrUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.core.convert.converter.Converter;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

public class SpringDateConverter implements Converter<String, Date> {

    private static final Logger log = LogManager.getLogger(SpringDateConverter.class);
    private static final List<String> formarts = new ArrayList(4);

    static {
        formarts.add("yyyy-MM");
        formarts.add("yyyy-MM-dd");
        formarts.add("yyyy-MM-dd HH:mm");
        formarts.add("yyyy-MM-dd HH:mm:ss");
        formarts.add("yyyy-MM-dd HH:mm:ss.SSS");
        formarts.add("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
    }

    public SpringDateConverter() {
    }

    @Override
    public Date convert(String source) {

        TimeZone tz = TimeZone.getTimeZone("Asia/Shanghai");

        if (StrUtil.isBlank(source)) {
            return null;
        } else if (source.matches("^\\d{4}-\\d{1,2}$")) {
            return this.parseDate(source, formarts.get(0), tz);
        } else if (source.matches("^\\d{4}-\\d{1,2}-\\d{1,2}$")) {
            return this.parseDate(source, formarts.get(1), tz);
        } else if (source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}$")) {
            return this.parseDate(source, formarts.get(2), tz);
        } else if (source.matches("^\\d{4}-\\d{1,2}-\\d{1,2} {1}\\d{1,2}:\\d{1,2}:\\d{1,2}$")) {
            return this.parseDate(source, formarts.get(3), tz);
        } else if (source.length() == 23) {
            return this.parseDate(source, formarts.get(4), tz);
        } else if (source.length() == 29 && source.contains("T")) {
            return this.parseDate(source, formarts.get(5), null);
        } else {
            throw new IllegalArgumentException("Invalid date value '" + source + "'");
        }
    }

    private Date parseDate(String dateStr, String format, TimeZone timeZone) {
        Date date = null;

        try {
            DateFormat dateFormat = new SimpleDateFormat(format);
            if (timeZone != null) {
                dateFormat.setTimeZone(timeZone);
            }

            date = dateFormat.parse(dateStr);
        } catch (Exception var6) {
            log.error("date parse error.", var6);
        }

        return date;
    }
}
