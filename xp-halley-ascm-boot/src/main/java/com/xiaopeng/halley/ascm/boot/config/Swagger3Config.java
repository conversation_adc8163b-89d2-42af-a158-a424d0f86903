/*
 * Company:  广州小鹏汽车科技有限公司
 * Author:   Primus 自动生成
 * Date:     2022-07-07 07:22:43
 */
package com.xiaopeng.halley.ascm.boot.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger3的配置<br>
 * <br>
 *
 * <AUTHOR> 自动生成
 * @date 2022-07-07 07:22:43
 */
@Configuration
public class Swagger3Config {
    @Bean
    public OpenAPI springShopOpenApi() {
        return new OpenAPI()
                .info(new Info().title("Primus自动生成项目")
                        .description("项目描述信息")
                        .version("1.0.0-SNAPSHOT")
                        .contact(new Contact().name("Primus").email("<EMAIL>"))
                );
    }
}
