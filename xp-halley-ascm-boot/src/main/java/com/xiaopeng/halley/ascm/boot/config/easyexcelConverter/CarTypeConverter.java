package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/9/25 15:43
 */
public class CarTypeConverter implements Converter<Integer> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        double carType = cellData.getNumberValue().doubleValue();
        if (4.2 == carType) {
            return 0;
        } else if (6.8 == carType) {
            return 1;
        } else if (7.2 == carType) {
            return 2;
        } else if (9.6 == carType) {
            return 3;
        } else {
            return 4;
        }
    }

    @Override
    public WriteCellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == 0) {
            return new WriteCellData<>("4.2");
        } else if (value == 1) {
            return new WriteCellData<>("6.8");
        } else if (value == 2) {
            return new WriteCellData<>("7.2");
        } else if (value == 3) {
            return new WriteCellData<>("9.6");
        } else {
            return new WriteCellData<>("");
        }
    }
}
