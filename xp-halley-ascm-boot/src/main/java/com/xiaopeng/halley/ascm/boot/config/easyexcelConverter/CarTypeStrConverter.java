package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/10/10 9:52
 */
public class CarTypeStrConverter implements Converter<String> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public WriteCellData<String> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isEmpty(value)) {
            return new WriteCellData<>("4.2");
        } else if ("1".equals(value)) {
            return new WriteCellData<>("6.8");
        } else if ("2".equals(value)) {
            return new WriteCellData<>("7.2");
        } else if ("3".equals(value)) {
            return new WriteCellData<>("9.6");
        } else {
            return new WriteCellData<>("");
        }
    }
}
