package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/10/9 17:07
 */
public class LogisticsTypeConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isBlank(cellData.getStringValue())) {
            return 0;
        } else if ("快递".equals(cellData.getStringValue())) {
            return 0;
        } else if ("零担".equals(cellData.getStringValue())) {
            return 1;
        } else if ("专车".equals(cellData.getStringValue())) {
            return 2;
        } else {
            return 0;
        }
    }

    @Override
    public WriteCellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == 0) {
            return new WriteCellData<>("快递");
        } else if (value == 1) {
            return new WriteCellData<>("零担");
        } else if (value == 2) {
            return new WriteCellData<>("专车");
        } else {
            return new WriteCellData<>("-");
        }
    }
}
