package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/10/8 14:08
 */
public class SamePackageConverter implements Converter<Integer> {

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<String> convertToExcelData(Integer integer, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) throws Exception {
        if (integer == 0) {
            return new WriteCellData<>("是");
        } else if (integer == 1) {
            return new WriteCellData<>("否");
        } else {
            return new WriteCellData<>("");
        }

    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                     GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isBlank(cellData.getStringValue())) {
            return 0;
        } else if ("是".equals(cellData.getStringValue())) {
            return 0;
        } else if ("否".equals(cellData.getStringValue())) {
            return 1;
        } else {
            return 0;
        }
    }

}
