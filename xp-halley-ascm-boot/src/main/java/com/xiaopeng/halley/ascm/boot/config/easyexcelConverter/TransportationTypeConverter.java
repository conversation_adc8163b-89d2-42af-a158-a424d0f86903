package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/9/20 9:42
 */
public class TransportationTypeConverter implements Converter<Integer> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isBlank(cellData.getStringValue())) {
            return 0;
        } else if ("返厂".equals(cellData.getStringValue())) {
            return 0;
        } else if ("返店".equals(cellData.getStringValue())) {
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public WriteCellData<String> convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == 0) {
            return new WriteCellData<>("返厂");
        } else if (value == 1) {
            return new WriteCellData<>("返店");
        } else {
            return new WriteCellData<>("");
        }
    }

}
