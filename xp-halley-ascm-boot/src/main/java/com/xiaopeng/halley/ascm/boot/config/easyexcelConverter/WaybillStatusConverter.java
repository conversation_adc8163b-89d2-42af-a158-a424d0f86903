package com.xiaopeng.halley.ascm.boot.config.easyexcelConverter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

/**
 * @Author：huqizhi
 * @Date：2023/9/28 14:54
 */
public class WaybillStatusConverter implements Converter<Integer> {
    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<String> convertToExcelData(Integer integer, ExcelContentProperty contentProperty,
                                                    GlobalConfiguration globalConfiguration) throws Exception {
        if (integer == 0) {
            return new WriteCellData<>("新建");
        } else if (integer == 1) {
            return new WriteCellData<>("已分配");
        } else if (integer == 2) {
            return new WriteCellData<>("已装运");
        } else if (integer == 3) {
            return new WriteCellData<>("已送达");
        } else if (integer == 4) {
            return new WriteCellData<>("已付款");
        } else if (integer == 5) {
            return new WriteCellData<>("关闭");
        } else {
            return new WriteCellData<>("");
        }

    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty,
                                     GlobalConfiguration globalConfiguration) throws Exception {
        if (StrUtil.isBlank(cellData.getStringValue())) {
            return 0;
        } else if ("新建".equals(cellData.getStringValue())) {
            return 0;
        } else if ("已分配".equals(cellData.getStringValue())) {
            return 1;
        } else if ("已装运".equals(cellData.getStringValue())) {
            return 2;
        } else if ("已送达".equals(cellData.getStringValue())) {
            return 3;
        } else if ("已付款".equals(cellData.getStringValue())) {
            return 4;
        } else if ("关闭".equals(cellData.getStringValue())) {
            return 5;
        } else {
            return 0;
        }
    }
}
