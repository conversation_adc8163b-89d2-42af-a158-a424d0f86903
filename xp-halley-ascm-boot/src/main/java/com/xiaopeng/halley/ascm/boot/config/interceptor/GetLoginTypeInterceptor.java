package com.xiaopeng.halley.ascm.boot.config.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.service.AscmRedisHelper;
import com.xiaopeng.mqi.common.LoginUser;
import com.xiaopeng.mqi.helper.LoginUserHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author：huqizhi
 * @Date：2023/8/18 10:30
 */
@Slf4j
public class GetLoginTypeInterceptor implements HandlerInterceptor {

    public static ThreadLocal<String> threadLocal = new ThreadLocal<>();

    @Resource
    private AscmRedisHelper ascmRedisHelper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        String header = request.getHeader("Client-Type");
        log.info("{}请求进来了!", header);
        if (header != null) {
            if ("android".equals(header)) {
                threadLocal.set(header);
            }
        }

        try {
            HttpServletRequest theRequest = (HttpServletRequest) request;
            if (ObjectUtil.isNotNull(theRequest) && StrUtil.isNotBlank(theRequest.getRequestURI())) {
                if (theRequest.getRequestURI().endsWith("/mbp/web/switchOrg")) {
                    log.info("命中切换组织，删除当前用户缓存");
                    // 删除缓存
                    LoginUser loginUser = LoginUserHelper.getLoginUser();
                    if (ObjectUtil.isNotNull(loginUser) && StrUtil.isNotBlank(loginUser.getUsername())) {
                        log.info("命中切换组织，删除当前用户缓存 [{}]", loginUser.getUsername());
                        ascmRedisHelper.delete(RedisKeyManager.ASCM_USER_CACHE.buildKey(loginUser.getUsername()));
                    }
                }
            }
        } catch (Exception e) {
            log.info("缓存清理失败 [{}]", e.getMessage());
        }

        return true;
    }

    //移除线程中携带的用户信息
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 必须在请求结束后移除，不然后续的查询依然会有问题
        threadLocal.remove();
        log.info("移除成功！");
    }
}
