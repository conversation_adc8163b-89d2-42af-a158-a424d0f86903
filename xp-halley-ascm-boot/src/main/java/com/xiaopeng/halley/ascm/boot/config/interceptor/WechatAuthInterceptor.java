package com.xiaopeng.halley.ascm.boot.config.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaopeng.logan.commons.constant.HeaderConstants;
import com.xiaopeng.mqi.auth.custom.LoginHandler;
import com.xpeng.athena.sdk.mbp.constants.MbpConstants;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 微信小程序接口权限拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatAuthInterceptor implements HandlerInterceptor {
    /**
     * 路径匹配器
     */
    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
    /**
     * 微信小程序接口白名单
     */
    @Resource
    private WechatAuthProperties properties;

    @Resource
    private LoginHandler loginHandler;

    /**
     * 在请求处理之前进行拦截
     *
     * @param request  HTTP请求对象
     * @param response HTTP响应对象
     * @param handler  请求处理者
     * @return boolean 表示是否继续执行其他拦截器和目标方法
     * @throws Exception 抛出异常时
     */
    @Override
    public boolean preHandle(HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        // 非小程序用户无需校验
        String clientType = request.getHeader(HeaderConstants.CLIENT_TYPE);
        log.info("clientType: {}", clientType);
        if (!MbpConstants.WECHAT_CLIENT_NAME.equals(clientType) && !isWechatUser(request, response)) {
            return true;
        }
        // 获取请求URI，用于日志记录和白名单匹配
        String requestURI = request.getRequestURI();
        log.info("开始对微信用户进行接口校验 requestURI: {}", requestURI);
        List<String> whiteList = properties.getWhiteList();
        log.info("白名单: {}", whiteList);
        if (CollUtil.isNotEmpty(whiteList)) {
            // 遍历白名单，检查请求URI是否匹配
            for (String path : whiteList) {
                if (antPathMatcher.match(path, requestURI)) {
                    return true;
                }
            }
        }
        // 请求URI未在白名单中，记录错误日志并抛出异常
        log.error("校验不通过！requestURI: {}", requestURI);
        throw new RuntimeException("权限不足");
    }

    /**
     * 判断用户是否非微信用户
     * 通过用户名称的前缀来判断用户是否为微信用户微信用户的用户名以"wx-"开头
     * 此方法用于识别并区分微信用户和非微信用户
     *
     * @param request  HTTP请求对象，用于获取用户信息
     * @param response HTTP响应对象，未使用
     * @return boolean 如果用户是微信用户，则返回false；否则返回true如果用户未登录或用户名获取失败，也返回false
     */
    private boolean isWechatUser(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取用户名称
            String userName = loginHandler.getUserName(request, response);
            log.info("userName: {}", userName);
            // 检查用户名是否为空或以"wx-"开头，以判断用户是否为微信用户
            if (StrUtil.isBlank(userName)) {
                return false;
            }
            return userName.startsWith("wx-");
        } catch (Exception e) {
            return false;
        }
    }
}

