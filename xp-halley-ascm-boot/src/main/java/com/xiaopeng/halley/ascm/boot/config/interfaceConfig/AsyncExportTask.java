package com.xiaopeng.halley.ascm.boot.config.interfaceConfig;

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
/**
 * @deprecated Wang bin jie
 * 创建异步导出任务
 */
public @interface AsyncExportTask {
    /**
     * 文件名称
     *
     * @return
     */
    String name() default "";

    /**
     * 方法路径
     *
     * @return
     */
    String methodPath() default "";

    /**
     * 导出最大数据
     *
     * @return
     */
    String maxExportCount() default "";

    /**
     * 0:异步 或者 1:同步
     */
    int syncFlag() default 0;

}
