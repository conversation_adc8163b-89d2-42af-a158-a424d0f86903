package com.xiaopeng.halley.ascm.boot.config.interfaceConfig.InterceptorConfig;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.entity.BzAsyncExportLog;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.AscmRedisHelper;
import com.xiaopeng.halley.ascm.boot.service.BzAsyncExportLogService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect // FOR AOP
@Order
@Component
public class AsyncExportConfig {

    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private BzAsyncExportLogService bzAsyncExportLogService;
    @Resource
    private Environment environment;

    @Before("@annotation(com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask)")
    public void beforeTest(JoinPoint joinPoint) throws ResultException {
        String key = "ascm:async:export:time" + ascmLoginUserHelper.getLoginUser().getUserId();
        if (ascmRedisHelper.hasKey(key)) {
            throw new ResultException(500, "导出已经执行，请前往导出日志记录下载！");
        }
        ascmRedisHelper.set(key, ascmLoginUserHelper.getLoginUser().getName(), 6, TimeUnit.SECONDS);
        //执行
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        AsyncExportTask apiLog = null;
        if (method != null) {
            //拿到自定义注解参数
            apiLog = method.getAnnotation(AsyncExportTask.class);
        } else {
            throw new ResultException(500, "AsyncExportTask: 未找到该方法");
        }
        // 匹配导出数量

        String maxExportCount = environment.getProperty("export." + apiLog.maxExportCount());
        log.info("AsyncExportTask maxExportCount [" + maxExportCount + "]");

        //4. 获取方法的参数 一一对应
        Object[] args = joinPoint.getArgs();
        boolean flag = false;
        //同步导出 异步导出 处理方式不同
        if (apiLog.syncFlag() == 1) {
            BzAsyncExportLog bzAsyncExportLog = new BzAsyncExportLog();
            HttpServletResponse response = null;
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof PageQuery) {
                    PageQuery pageQuery = (PageQuery) args[0];
                    //同步情况
                    bzAsyncExportLog = new BzAsyncExportLog();
                    if (ObjectUtil.isNotNull(pageQuery.getParam())) {
                        bzAsyncExportLog.setParams(JSONObject.toJSONString(pageQuery.getParam()));
                    }
                    bzAsyncExportLog.setName(apiLog.name());
                    bzAsyncExportLog.setMethodPath(apiLog.methodPath());
                    bzAsyncExportLog.setSyncFlag(apiLog.syncFlag());
                } else if (args[i] instanceof HttpServletResponse) {
                    response = (HttpServletResponse) args[i];
                }
            }
            try {
                if (ObjectUtil.isNotNull(bzAsyncExportLog) && ObjectUtil.isNotNull(response)) {
                    bzAsyncExportLogService.openMain(bzAsyncExportLog, response);
                    flag = true;
                }

            } catch (Exception e) {
                e.printStackTrace();
                throw new ResultException(500, "导出异常！");
            }
        } else {
            for (int i = 0; i < args.length; i++) {
                if (args[i] instanceof PageQuery) {
                    PageQuery pageQuery = (PageQuery) args[0];

                    if (StrUtil.isNotBlank(maxExportCount) && pageQuery.getTotal() > Long.parseLong(maxExportCount)) {
                        throw new ResultException(500, "导出条目过多，当前限制[" + maxExportCount + "]");
                    }

                    BzAsyncExportLog bzAsyncExportLog = bzAsyncExportLogService.insert(pageQuery, apiLog.name(), apiLog.methodPath());
                    flag = true;
                    break;
                }
            }
        }
        if (!flag) {
            throw new ResultException(500, "当前方法不适用于该注解！类型应为：PageQuery");
        }

    }
}