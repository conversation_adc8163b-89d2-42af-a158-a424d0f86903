package com.xiaopeng.halley.ascm.boot.config.interfaceConfig.InterceptorConfig;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.ClassUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.baomidou.mybatisplus.extension.toolkit.PropertyMapper;
import com.xiaopeng.halley.ascm.boot.config.MybatisConfig;
import com.xiaopeng.halley.ascm.boot.config.interceptor.GetLoginTypeInterceptor;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.MyTenantLineHandler;
import com.xiaopeng.halley.ascm.boot.service.BzAsyncExportLogService;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 3.4.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Slf4j
@SuppressWarnings({"rawtypes"})
public class MyTenantLineInnerInterceptor extends JsqlParserSupport implements InnerInterceptor {
    private MyTenantLineHandler tenantLineHandler;

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        if (InterceptorIgnoreHelper.willIgnoreTenantLine(ms.getId())) {
            return;
        }
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        mpBs.sql(parserSingle(mpBs.sql(), null));
    }

    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        PluginUtils.MPStatementHandler mpSh = PluginUtils.mpStatementHandler(sh);
        MappedStatement ms = mpSh.mappedStatement();
        SqlCommandType sct = ms.getSqlCommandType();
        if (sct == SqlCommandType.INSERT || sct == SqlCommandType.UPDATE || sct == SqlCommandType.DELETE) {
            if (InterceptorIgnoreHelper.willIgnoreTenantLine(ms.getId())) {
                return;
            }
            PluginUtils.MPBoundSql mpBs = mpSh.mPBoundSql();
            mpBs.sql(parserMulti(mpBs.sql(), null));
        }
    }

    @Override
    protected void processSelect(Select select, int index, String sql, Object obj) {
        processSelectBody(select.getSelectBody());
        List<WithItem> withItemsList = select.getWithItemsList();
        if (!CollUtil.isEmpty(withItemsList)) {
            withItemsList.forEach(this::processSelectBody);
        }
    }

    protected void processSelectBody(SelectBody selectBody) {
        if (selectBody == null) {
            return;
        }
        if (selectBody instanceof PlainSelect) {
            //TODO:进入这个方法时判断是PC还是PDA
            processPlainSelect((PlainSelect) selectBody);
        } else if (selectBody instanceof WithItem) {
            WithItem withItem = (WithItem) selectBody;
            processSelectBody(withItem.getSubSelect().getSelectBody());
        } else {
            SetOperationList operationList = (SetOperationList) selectBody;
            List<SelectBody> selectBodyList = operationList.getSelects();
            if (CollUtil.isNotEmpty(selectBodyList)) {
                selectBodyList.forEach(this::processSelectBody);
            }
        }
    }

    @Override
    protected void processInsert(Insert insert, int index, String sql, Object obj) {
        //如果未注册 不予执行
        if (tenantLineHandler.ignoreTable(insert.getTable().getName()) || CollUtil.isEmpty(MybatisConfig.getConcurrentHashMap(insert.getTable().getName()))) {
            // 过滤退出执行

            return;
        }
        List<Column> columns = insert.getColumns();
        if (CollUtil.isEmpty(columns)) {
            // 针对不给列名的insert 不处理
            return;
        }

    }

    /**
     * update 语句处理
     */
    @Override
    protected void processUpdate(Update update, int index, String sql, Object obj) {
        final Table table = update.getTable();
        //如果未注册 不予执行
        if (tenantLineHandler.ignoreTable(table.getName()) || CollUtil.isEmpty(MybatisConfig.getConcurrentHashMap(update.getTable().getName()))) {
            // 过滤退出执行
            return;
        }
        update.setWhere(this.andExpression(table, update.getWhere()));
    }

    /**
     * delete 语句处理
     */
    @Override
    protected void processDelete(Delete delete, int index, String sql, Object obj) {
        //如果未注册 不予执行
        if (tenantLineHandler.ignoreTable(delete.getTable().getName()) || CollUtil.isEmpty(MybatisConfig.getConcurrentHashMap(delete.getTable().getName()))) {
            // 过滤退出执行
            return;
        }
        delete.setWhere(this.andExpression(delete.getTable(), delete.getWhere()));
    }

    /**
     * delete update 语句 where 处理
     */
    protected BinaryExpression andExpression(Table table, Expression where) {
        //获得where条件表达式
        //更改逻辑
        List<Column> aliasColumn = this.getAliasColumn(table);
        List<InExpression> inExpressions = new ArrayList<>();
            /*for (int j = 0; j < aliasColumn.size(); j++) {
                inExpressions.add(new InExpression(aliasColumn.get(j), new ExpressionList(tenantLineHandler.getTenantId())));
            }*/

        inExpressions.add(new InExpression(aliasColumn.get(0), new ExpressionList(tenantLineHandler.getTenantId())));

        if (CollUtil.isEmpty(inExpressions)) {
            return (BinaryExpression) where;
        }

        Expression injectExpression = inExpressions.get(0);
        // 如果有多表，则用 and 连接（更改）
        // 用or链接
        /*if (inExpressions.size() > 1) {
            for (int i = 1; i < inExpressions.size(); i++) {
                injectExpression = new OrExpression(injectExpression, inExpressions.get(i));
            }
        }*/

        if (null != where) {
            if (where instanceof OrExpression) {
                return new AndExpression(new Parenthesis(injectExpression), new Parenthesis(where));
            } else {
                return new AndExpression(new Parenthesis(injectExpression), where);
            }
        }
        return (BinaryExpression) injectExpression;
    }

    /**
     * 处理 insert into select
     * <p>
     * 进入这里表示需要 insert 的表启用了多租户,则 select 的表都启动了
     *
     * @param selectBody SelectBody
     */
//    protected void processInsertSelect(SelectBody selectBody) {
//        PlainSelect plainSelect = (PlainSelect) selectBody;
//        FromItem fromItem = plainSelect.getFromItem();
//        if (fromItem instanceof Table) {
//            // fixed gitee pulls/141 duplicate update
//            processPlainSelect(plainSelect);
//            appendSelectItem(plainSelect.getSelectItems());
//        } else if (fromItem instanceof SubSelect) {
//            SubSelect subSelect = (SubSelect) fromItem;
//            appendSelectItem(plainSelect.getSelectItems());
//            processInsertSelect(subSelect.getSelectBody());
//        }
//    }

    /**
     * 追加 SelectItem
     *
     * @param selectItems SelectItem
     */
//    protected void appendSelectItem(List<SelectItem> selectItems) {
//        if (CollUtil.isEmpty(selectItems)) {
//            return;
//        }
//        if (selectItems.size() == 1) {
//            SelectItem item = selectItems.get(0);
//            if (item instanceof AllColumns || item instanceof AllTableColumns) {
//                return;
//            }
//        }
//        List<String> tenantIdColumn = tenantLineHandler.getTenantIdColumn();
//        for (int i = 0; i < tenantIdColumn.size(); i++) {
//            String item = tenantIdColumn.get(i);
//            selectItems.add(new SelectExpressionItem(new Column(item)));
//        }
//    }

    /**
     * 处理 PlainSelect
     */
    protected void processPlainSelect(PlainSelect plainSelect) {
        //#3087 github
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (CollUtil.isNotEmpty(selectItems)) {
            selectItems.forEach(this::processSelectItem);
        }

        // 处理 where 中的子查询
        Expression where = plainSelect.getWhere();
        processWhereSubSelect(where);

        // 处理 fromItem
        FromItem fromItem = plainSelect.getFromItem();
        List<Table> list = processFromItem(fromItem);
        List<Table> mainTables = new ArrayList<>(list);

        // 处理 join
        List<Join> joins = plainSelect.getJoins();
        if (CollUtil.isNotEmpty(joins)) {
            mainTables = processJoins(mainTables, joins);
        }

        // 当有 mainTable 时，进行 where 条件追加
        // TODO:在这里判断进入pda的方法还是进入pc的方法
        if (CollUtil.isNotEmpty(mainTables)) {
            plainSelect.setWhere(builderExpression(where, mainTables));
        }
    }

    private List<Table> processFromItem(FromItem fromItem) {
        // 处理括号括起来的表达式
        while (fromItem instanceof ParenthesisFromItem) {
            fromItem = ((ParenthesisFromItem) fromItem).getFromItem();
        }

        List<Table> mainTables = new ArrayList<>();
        // 无 join 时的处理逻辑
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            mainTables.add(fromTable);
        } else if (fromItem instanceof SubJoin) {
            // SubJoin 类型则还需要添加上 where 条件
            List<Table> tables = processSubJoin((SubJoin) fromItem);
            mainTables.addAll(tables);
        } else {
            // 处理下 fromItem
            processOtherFromItem(fromItem);
        }
        return mainTables;
    }

    /**
     * 处理where条件内的子查询
     * <p>
     * 支持如下:
     * 1. in
     * 2. =
     * 3. >
     * 4. <
     * 5. >=
     * 6. <=
     * 7. <>
     * 8. EXISTS
     * 9. NOT EXISTS
     * <p>
     * 前提条件:
     * 1. 子查询必须放在小括号中
     * 2. 子查询一般放在比较操作符的右边
     *
     * @param where where 条件
     */
    protected void processWhereSubSelect(Expression where) {
        if (where == null) {
            return;
        }
        if (where instanceof FromItem) {
            processOtherFromItem((FromItem) where);
            return;
        }
        if (where.toString().indexOf("SELECT") > 0) {
            // 有子查询
            if (where instanceof BinaryExpression) {
                // 比较符号 , and , or , 等等
                BinaryExpression expression = (BinaryExpression) where;
                processWhereSubSelect(expression.getLeftExpression());
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof InExpression) {
                // in
                InExpression expression = (InExpression) where;
                Expression inExpression = expression.getRightExpression();
                if (inExpression instanceof SubSelect) {
                    processSelectBody(((SubSelect) inExpression).getSelectBody());
                }
            } else if (where instanceof ExistsExpression) {
                // exists
                ExistsExpression expression = (ExistsExpression) where;
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof NotExpression) {
                // not exists
                NotExpression expression = (NotExpression) where;
                processWhereSubSelect(expression.getExpression());
            } else if (where instanceof Parenthesis) {
                Parenthesis expression = (Parenthesis) where;
                processWhereSubSelect(expression.getExpression());
            }
        }
    }

    protected void processSelectItem(SelectItem selectItem) {
        if (selectItem instanceof SelectExpressionItem) {
            SelectExpressionItem selectExpressionItem = (SelectExpressionItem) selectItem;
            if (selectExpressionItem.getExpression() instanceof SubSelect) {
                processSelectBody(((SubSelect) selectExpressionItem.getExpression()).getSelectBody());
            } else if (selectExpressionItem.getExpression() instanceof Function) {
                processFunction((Function) selectExpressionItem.getExpression());
            }
        }
    }

    /**
     * 处理函数
     * <p>支持: 1. select fun(args..) 2. select fun1(fun2(args..),args..)<p>
     * <p> fixed gitee pulls/141</p>
     *
     * @param function
     */
    protected void processFunction(Function function) {
        ExpressionList parameters = function.getParameters();
        if (parameters != null) {
            parameters.getExpressions().forEach(expression -> {
                if (expression instanceof SubSelect) {
                    processSelectBody(((SubSelect) expression).getSelectBody());
                } else if (expression instanceof Function) {
                    processFunction((Function) expression);
                }
            });
        }
    }

    /**
     * 处理子查询等
     */
    protected void processOtherFromItem(FromItem fromItem) {
        // 去除括号
        while (fromItem instanceof ParenthesisFromItem) {
            fromItem = ((ParenthesisFromItem) fromItem).getFromItem();
        }

        if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody());
            }
        } else if (fromItem instanceof ValuesList) {
            logger.debug("Perform a subQuery, if you do not give us feedback");
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                SubSelect subSelect = lateralSubSelect.getSubSelect();
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody());
                }
            }
        }
    }

    /**
     * 处理 sub join
     *
     * @param subJoin subJoin
     * @return Table subJoin 中的主表
     */
    private List<Table> processSubJoin(SubJoin subJoin) {
        List<Table> mainTables = new ArrayList<>();
        if (subJoin.getJoinList() != null) {
            List<Table> list = processFromItem(subJoin.getLeft());
            mainTables.addAll(list);
            mainTables = processJoins(mainTables, subJoin.getJoinList());
        }
        return mainTables;
    }

    /**
     * 处理 joins
     *
     * @param mainTables 可以为 null
     * @param joins      join 集合
     * @return List<Table> 右连接查询的 Table 列表
     */
    private List<Table> processJoins(List<Table> mainTables, List<Join> joins) {
        // join 表达式中最终的主表
        Table mainTable = null;
        // 当前 join 的左表
        Table leftTable = null;

        if (mainTables == null) {
            mainTables = new ArrayList<>();
        } else if (mainTables.size() == 1) {
            mainTable = mainTables.get(0);
            leftTable = mainTable;
        }

        //对于 on 表达式写在最后的 join，需要记录下前面多个 on 的表名
        Deque<List<Table>> onTableDeque = new LinkedList<>();
        for (Join join : joins) {
            // 处理 on 表达式
            FromItem joinItem = join.getRightItem();

            // 获取当前 join 的表，subJoint 可以看作是一张表
            List<Table> joinTables = null;
            if (joinItem instanceof Table) {
                joinTables = new ArrayList<>();
                joinTables.add((Table) joinItem);
            } else if (joinItem instanceof SubJoin) {
                joinTables = processSubJoin((SubJoin) joinItem);
            }

            if (joinTables != null) {

                // 如果是隐式内连接
                if (join.isSimple()) {
                    mainTables.addAll(joinTables);
                    continue;
                }

                // 当前表是否忽略
                Table joinTable = joinTables.get(0);

                List<Table> onTables = null;
                // 如果不要忽略，且是右连接，则记录下当前表
                if (join.isRight()) {
                    mainTable = joinTable;
                    if (leftTable != null) {
                        onTables = Collections.singletonList(leftTable);
                    }
                } else if (join.isLeft()) {
                    onTables = Collections.singletonList(joinTable);
                } else if (join.isInner()) {
                    if (mainTable == null) {
                        onTables = Collections.singletonList(joinTable);
                    } else {
                        onTables = Arrays.asList(mainTable, joinTable);
                    }
                    mainTable = null;
                }

                mainTables = new ArrayList<>();
                if (mainTable != null) {
                    mainTables.add(mainTable);
                }

                // 获取 join 尾缀的 on 表达式列表
                Collection<Expression> originOnExpressions = join.getOnExpressions();
                // 正常 join on 表达式只有一个，立刻处理
                if (originOnExpressions.size() == 1 && onTables != null) {
                    List<Expression> onExpressions = new LinkedList<>();
                    onExpressions.add(builderExpression(originOnExpressions.iterator().next(), onTables));
                    join.setOnExpressions(onExpressions);
                    leftTable = joinTable;
                    continue;
                }
                // 表名压栈，忽略的表压入 null，以便后续不处理
                onTableDeque.push(onTables);
                // 尾缀多个 on 表达式的时候统一处理
                if (originOnExpressions.size() > 1) {
                    Collection<Expression> onExpressions = new LinkedList<>();
                    for (Expression originOnExpression : originOnExpressions) {
                        List<Table> currentTableList = onTableDeque.poll();
                        if (CollUtil.isEmpty(currentTableList)) {
                            onExpressions.add(originOnExpression);
                        } else {
                            onExpressions.add(builderExpression(originOnExpression, currentTableList));
                        }
                    }
                    join.setOnExpressions(onExpressions);
                }
                leftTable = joinTable;
            } else {
                processOtherFromItem(joinItem);
                leftTable = null;
            }
        }

        return mainTables;
    }

    /**
     * 处理条件
     */
    //TODO: 此处添加where添加，需要在此处区分是pc还是pda
    protected Expression builderExpression(Expression currentExpression, List<Table> tables) {
        // 没有表需要处理直接返回
        if (CollUtil.isEmpty(tables)) {
            return currentExpression;
        }
        // 构造每张表的条件
        List<Table> tempTables = tables.stream()
                .filter(x -> //如果未注册 不予执行
                        !CollUtil.isEmpty(MybatisConfig.getConcurrentHashMap(x.getName())))
                .filter(x -> !tenantLineHandler.ignoreTable(x.getName()))
                .collect(Collectors.toList());

        // 没有表需要处理直接返回
        if (CollUtil.isEmpty(tempTables)) {
            return currentExpression;
        }

        //逻辑变更 批量处理 且更改为 c  = ? and （a in (?) or b in (?)）
        List<Expression> tenantIdList = tenantLineHandler.getTenantId();
        ItemsList itemsList = new ExpressionList(tenantIdList);

        List<InExpression> inExpressions = new ArrayList<>();
        for (int i = 0; i < tempTables.size(); i++) {
            Table item = tempTables.get(i);
            List<Column> aliasColumn = getAliasColumn(item);

            for (int j = 0; j < aliasColumn.size(); j++) {
                inExpressions.add(new InExpression(aliasColumn.get(j), itemsList));
            }

        }

        //在这里做判断，如果是pda的请求，就把之前添加的清空，换拼接的内容
        String loginType = GetLoginTypeInterceptor.threadLocal.get();
        log.info("获取到线程携带的参数{}", loginType);
        if (loginType != null) {
            if ("android".equals(loginType)) {
                inExpressions.removeAll(inExpressions);
                log.info("前集合中的东西：{}", JSON.toJSONString(inExpressions));
                for (int i = 0; i < tempTables.size(); i++) {
                    Table item = tempTables.get(i);
                    List<Column> aliasColumn = getAliasColumn(item);
                    inExpressions.add(new InExpression(aliasColumn.get(0), itemsList));
                    log.info("后集合中的东西：{}", JSON.toJSONString(inExpressions));
                }
            }
        }

        //在这里做判断，如果是下载任务的请求，就把之前添加的清空，拼接发货仓库就行
        Map map = (Map) BzAsyncExportLogService.threadLocalUser.get();
        log.info("获取到线程携带的参数{}", JSON.toJSONString(map));
        if (map != null) {
            //只要这个线程可以断定就是定时任务，就可以直接在这里先删除了这个数据
            //有finally所以不用急着干了它
            //BzAsyncExportLogService.threadLocalUser.remove();
            inExpressions.removeAll(inExpressions);
            log.info("前集合中的东西：{}", JSON.toJSONString(inExpressions));
            for (int i = 0; i < tempTables.size(); i++) {
                Table item = tempTables.get(i);
                List<Column> aliasColumn = getAliasColumn(item);
                for (int j = 0; j < aliasColumn.size(); j++) {
                    inExpressions.add(new InExpression(aliasColumn.get(j), itemsList));
                }
                log.info("后集合中的东西：{}", JSON.toJSONString(inExpressions));
            }

        }

        if (CollUtil.isEmpty(inExpressions)) {
            return currentExpression;
        }

        // 注入的表达式
        Expression injectExpression = inExpressions.get(0);
        // 如果有多表，则用 and 连接（更改）
        // 用or链接
        if (inExpressions.size() > 1) {
            for (int i = 1; i < inExpressions.size(); i++) {
                injectExpression = new OrExpression(injectExpression, inExpressions.get(i));
            }
        }

        // 外围要是小括号
        if (currentExpression == null) {
            return new Parenthesis(injectExpression);
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), new Parenthesis(injectExpression));
        } else {
            return new AndExpression(currentExpression, new Parenthesis(injectExpression));
        }
    }

    /**
     * 租户字段别名设置
     * <p>tenantId 或 tableAlias.tenantId</p>
     *
     * @param table 表对象
     * @return 字段
     */
    protected List<Column> getAliasColumn(Table table) {

        List<Column> columnList = new ArrayList<>();

        for (int i = 0; i < MybatisConfig.getConcurrentHashMap(table.getName()).size(); i++) {
            String item = MybatisConfig.getConcurrentHashMap(table.getName()).get(i);

            StringBuilder column = new StringBuilder();
            // 为了兼容隐式内连接，没有别名时条件就需要加上表名
            if (table.getAlias() != null) {
                column.append(table.getAlias().getName());
            } else {
                column.append(table.getName());
            }
            column.append(StringPool.DOT).append(item);
            columnList.add(new Column(column.toString()));
        }
        return columnList;

    }

    @Override
    public void setProperties(Properties properties) {
        PropertyMapper.newInstance(properties).whenNotBlank("tenantLineHandler",
                ClassUtils::newInstance, this::setTenantLineHandler);
    }

}
