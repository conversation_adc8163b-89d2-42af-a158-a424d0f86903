package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.dto.LoactionRequestDTO;
import com.xiaopeng.halley.ascm.boot.service.AscmBaseService;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 基础接口可以放这里
 */
@Slf4j
@RestController
@RequestMapping("/ascmbase")
public class AscmBaseController {


    @Resource
    private AscmBaseService ascmBaseService;


    // 逆地址解析
    @PostMapping("/geocodeLocation")
    public Result<Object> geocodeLocation(@RequestBody @Validated LoactionRequestDTO reqDTO) {
        log.info("逆地址解析");
        JSONObject returnVO = ascmBaseService.geocodeLocation(reqDTO);
        return ResultUtil.success(returnVO);
    }
}
