package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.AccountSuccessRequestDTO;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectVo;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.service.BaseBatteryProjectService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author：huqizhi
 * @Date：2023/9/13 11:01
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseBatteryProject")
@Tag(name = "电池项目维护相关接口")
public class BaseBatteryProjectController {

    @Resource
    private BaseBatteryProjectService baseBatteryProjectService;

    @PostMapping("/mbp/test")
    @Operation(summary = "电池项目维护分页查询", description = "电池项目维护分页查询")
    public JSONObject test(@RequestBody JSONObject jsonObject) {
        log.info("BaseBatteryProjectController test {}", jsonObject.toJSONString());
        return jsonObject;
    }

    /**
     * 电池项目维护分页查询
     *
     * @param page 查询参数
     * @return 查询结果
     */
    @PostMapping("/getPage")
    @Operation(summary = "电池项目维护分页查询", description = "电池项目维护分页查询")
    public Page<BaseBatteryProjectVo> getPage(@RequestBody PageQuery<BaseBatteryProjectDto> page) {
        log.info("BaseBatteryProjectController getPage {}", JSON.toJSONString(page));
        return baseBatteryProjectService.getPage(page);
    }

    /**
     * 新增或更新
     *
     * @param dto 新增或更新的实体
     * @return 新增或更新结果
     */
    @PostMapping("/updateOrAdd")
    @Operation(summary = "电池项目维护新增或更新", description = "电池项目维护新增或更新")
    public Result update(@RequestBody BaseBatteryProjectDto dto) {
        log.info("BaseBatteryProjectController update {}", JSON.toJSONString(dto));
        return baseBatteryProjectService.updateOrAdd(dto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @Operation(summary = "电池项目维护删除", description = "电池项目维护删除")
    public Result delete(@RequestParam String id) {
        log.info("BaseBatteryProjectController delete {}", id);
        return baseBatteryProjectService.delete(id);
    }

    /**
     * 电池项目维护导出
     *
     * @param page 导出条件
     */
    @PostMapping("/downloadFile")
    @Operation(summary = "电池项目维护导出", description = "电池项目维护导出")
    @AsyncExportTask(name = "电池项目维护表导出", methodPath = "BaseBatteryProjectService.getPage")
    public Result downloadFile(@RequestBody PageQuery<BaseBatteryProjectDto> page) {
        return ResultUtil.success();
    }

    /**
     * 电池项目维护模板下载
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/tempFile")
    @Operation(summary = "电池项目维护模板下载", description = "电池项目维护模板下载")
    public ImageResponseDTO tempFile() {
        log.info("BaseBatteryProjectController tempFile 开始执行");
        return baseBatteryProjectService.tempFile();
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "电池项目维护导入数据参数校验", description = "电池项目维护导入数据参数校验")
    public ImportResponseDto importFile(@RequestParam("file") MultipartFile file) {
        log.info("BaseBatteryProjectController importFile 开始执行");
        return baseBatteryProjectService.importFile(file);
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "电池项目维护获取校验成功的数据分页", description = "电池项目维护获取校验成功的数据分页")
    public Page<BaseBatteryProjectVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BaseBatteryProjectController getSuccessPage {}", JSON.toJSONString(page));
        return baseBatteryProjectService.getSuccessPage(page);
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "电池项目维护下载错误的文件", description = "电池项目维护下载错误的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BaseBatteryProjectController downloadFailFile {}", JSON.toJSONString(dto));
        baseBatteryProjectService.downloadFailFile(dto, response);
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @PostMapping("/importData")
    @Operation(summary = "电池项目维护保存导入校验成功的规则", description = "电池项目维护保存导入校验成功的规则")
    public Result importData(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BaseBatteryProjectController importData {}", JSON.toJSONString(operationCode));
        return baseBatteryProjectService.importData(operationCode.getOperationCode());
    }

}
