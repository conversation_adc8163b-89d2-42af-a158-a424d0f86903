package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.service.BaseBatteryRouteCostService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 10:54
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseBatteryRouteCost")
@Tag(name = "电池线路费用维护相关接口")
public class BaseBatteryRouteCostController {

    @Resource
    private BaseBatteryRouteCostService baseBatteryRouteCostService;

    @PostMapping("/getPage")
    @Operation(summary = "电池线路费用维护分页查询", description = "电池线路费用维护分页查询")
    public Page<BaseBatteryRouteCostVo> getPage(@RequestBody PageQuery<BaseBatteryRouteCostDto> page) {
        log.info("BaseBatteryRouteCostController getPage {}", JSON.toJSONString(page));
        return baseBatteryRouteCostService.getPage(page);
    }

    /**
     * 新增或更新
     *
     * @param dto 新增或更新的实体
     * @return 新增或更新结果
     */
    @PostMapping("/updateOrAdd")
    @Operation(summary = "电池线路费用维护新增或更新", description = "电池线路费用维护新增或更新")
    public Result update(@RequestBody BaseBatteryRouteCostDto dto) {
        log.info("BaseBatteryRouteCostController update {}", JSON.toJSONString(dto));
        return baseBatteryRouteCostService.updateOrAdd(dto);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    @Operation(summary = "电池线路费用维护删除", description = "电池线路费用维护删除")
    public Result delete(@RequestParam String id) {
        log.info("BaseBatteryRouteCostController delete {}", id);
        return baseBatteryRouteCostService.delete(id);
    }

    /**
     * 电池项目维护导出
     *
     * @param page 导出条件
     */
    @PostMapping("/downloadFile")
    @Operation(summary = "电池线路费用维护导出", description = "电池线路费用维护导出")
    @AsyncExportTask(name = "电池线路费用维护表导出", methodPath = "BaseBatteryRouteCostService.getPage")
    public Result downloadFile(@RequestBody PageQuery<BaseBatteryRouteCostDto> page) {
        return ResultUtil.success();
    }

    /**
     * 电池项目维护模板下载
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/tempFile")
    @Operation(summary = "电池线路费用维护模板下载", description = "电池线路费用维护模板下载")
    public ImageResponseDTO tempFile() {
        log.info("BaseBatteryRouteCostController tempFile 开始执行");
        return baseBatteryRouteCostService.tempFile();
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "电池线路费用维护导入数据参数校验", description = "电池线路费用维护导入数据参数校验")
    public ImportResponseDto importFile(@RequestParam("file") MultipartFile file) {
        log.info("BaseBatteryRouteCostController importFile 开始执行");
        return baseBatteryRouteCostService.importFile(file);
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "电池线路费用维护获取校验成功的数据分页", description = "电池线路费用维护获取校验成功的数据分页")
    public Page<BaseBatteryRouteCostExportVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BaseBatteryRouteCostController getSuccessPage {}", JSON.toJSONString(page));
        return baseBatteryRouteCostService.getSuccessPage(page);
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "电池线路费用维护下载错误的文件", description = "电池线路费用维护下载错误的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BaseBatteryRouteCostController downloadFailFile {}", JSON.toJSONString(dto));
        baseBatteryRouteCostService.downloadFailFile(dto, response);
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @PostMapping("/importData")
    @Operation(summary = "电池线路费用维护保存导入校验成功的规则", description = "电池线路费用维护保存导入校验成功的规则")
    public Result importData(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BaseBatteryRouteCostController importData {}", JSON.toJSONString(operationCode));
        return baseBatteryRouteCostService.importData(operationCode.getOperationCode());
    }
}
