package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseCheckStandard;
import com.xiaopeng.halley.ascm.boot.service.BaseCheckStandardService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.controller.v1
 * @Date 2024/9/9 11:14
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseCheckStandard")
public class BaseCheckStandardController {

    @Resource
    private BaseCheckStandardService baseCheckStandardService;

    @PostMapping("/page")
    public Page<BaseCheckStandardVo> page(@RequestBody PageQuery<BaseCheckStandardDto> page) {
        return baseCheckStandardService.getPage(page);
    }

    /**
     * 删除校验文本
     *
     * @param param 删除的参数
     * @return 删除结果
     */
    @DeleteMapping("/deleteItem")
    public Result<String> deleteItem(@RequestBody BaseCheckStandardDto param) {
        return baseCheckStandardService.deleteItem(param);
    }

    /**
     * 编辑校验标准
     *
     * @param param 编辑的参数
     * @return 编辑的结果
     */
    @PostMapping("/editItem")
    public Result<String> editItem(@RequestBody BaseCheckStandardDto param) {
        return baseCheckStandardService.editItem(param);
    }

    /**
     * 导出校验文本
     *
     * @param page 导出查询参数
     * @return 导出结果
     */
    @PostMapping("/exportItem")
    @AsyncExportTask(name = "分配校验标准导出", methodPath = "BaseCheckStandardService.getPage")
    public Result<String> exportItem(@RequestBody PageQuery<BaseCheckStandardDto> page) {
        return ResultUtil.success();
    }

    /**
     * 导入校验文本
     *
     * @param file 导入的文件信息
     * @return 导入的结果
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResponseDto importFile(@RequestParam("file") MultipartFile file) {
        return baseCheckStandardService.importFile(file);
    }

    /**
     * 导出校验失败的文件
     *
     * @param operationCode
     * @param response
     */
    @GetMapping(value = "/exportFailFile/{operationCode}")
    public void exportFailFile(@PathVariable(value = "operationCode") String operationCode, HttpServletResponse response) {
        baseCheckStandardService.exportFailFile(operationCode, response);
    }

    /**
     * 提交校验成功的文件
     *
     * @param operationCode
     */
    @PostMapping(value = "/submitFile")
    public Result<String> submitFile(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        return baseCheckStandardService.submitFile(operationCode.getOperationCode());
    }

    /**
     * 备件检验信息导入模板下载
     *
     * @return 结果
     */
    @GetMapping("/tempFile")
    public ImageResponseDTO tempFile() {
        log.info("BaseCheckStandardController tempFile 开始执行");
        return baseCheckStandardService.tempFile();
    }

    /**
     * 获取校验成功的分页查询
     *
     * @param page 查询参数
     * @return 结果
     */
    @PostMapping("/getSuccessPage")
    public Page<BaseCheckStandardImportVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        return baseCheckStandardService.getSuccessPage(page);
    }

    /**
     * 详情页校验编码选择
     *
     * @param dto 查询条件
     * @return 结果
     */
    @PostMapping("/getCheckCodeInfo")
    public List<BaseCheckStandard> getCheckCodeInfo(@RequestBody BaseCheckStandardDto dto) {
        return baseCheckStandardService.getCheckCodeInfo(dto.getCheckDesc());
    }

}
