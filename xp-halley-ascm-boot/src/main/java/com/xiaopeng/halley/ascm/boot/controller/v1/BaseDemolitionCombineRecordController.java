package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.DemolitionCombineRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.DemolitionCombineResponseDto;
import com.xiaopeng.halley.ascm.boot.service.BaseDemolitionCombineRecordService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/BaseDemolitionCombineRecord")
@Tag(name = "拆合记录")
public class BaseDemolitionCombineRecordController {

    @Resource
    private BaseDemolitionCombineRecordService baseDemolitionCombineRecordService;

    @PostMapping("/page")
    @Operation(summary = "拆合记录-列表展示", description = "拆合记录-列表展示")
    public Page<DemolitionCombineResponseDto> page(@RequestBody PageQuery<DemolitionCombineRequestDto> page) throws ResultException {
        return baseDemolitionCombineRecordService.getPage(page);
    }

}
