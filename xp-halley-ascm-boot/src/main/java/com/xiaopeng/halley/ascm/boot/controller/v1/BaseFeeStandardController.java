package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseFeeStandard;
import com.xiaopeng.halley.ascm.boot.service.BaseFeeStandardService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import com.xpeng.athena.starter.excel.annotation.ExcelParameter;
import com.xpeng.athena.starter.excel.annotation.ExcelResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api("费用标准接口")
@RestController
@RequestMapping("/baseFeeStandard")
public class BaseFeeStandardController {
	@Resource
	private BaseFeeStandardService baseFeeStandardService;

	@PostMapping("/querySummary")
	@ApiOperation("查询日度费用汇总")
	public Result<Page<BaseFeeStandardSummary>> querySummary(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success(baseFeeStandardService.querySummary(request));
	}

	@PostMapping("/exportSummary")
	@ApiOperation("导出日度费用汇总")
	@AsyncExportTask(name = "日度费用汇总导出", methodPath = "BaseFeeStandardService.querySummary")
	public Result<String> exportSummary(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success();
	}

	@PostMapping
	@ApiOperation("新增费用标准")
	public Result<String> add(@RequestBody BaseFeeStandard baseFeeStandard) {
		try {
			boolean success = baseFeeStandardService.save(baseFeeStandard);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping
	@ApiOperation("删除费用标准")
	public Result<String> delete(@RequestBody List<Long> ids) {
		try {
			boolean success = baseFeeStandardService.removeByIds(ids);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PutMapping
	@ApiOperation("修改费用标准")
	public Result<String> update(@RequestBody BaseFeeStandard baseFeeStandard) {
		try {
			boolean success = baseFeeStandardService.updateById(baseFeeStandard);
			return success ? ResultUtil.success("修改成功") : ResultUtil.failed("修改失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("修改失败：" + e.getMessage());
		}
	}

	@GetMapping("/{id}")
	@ApiOperation("根据ID查询费用标准")
	public Result<BaseFeeStandard> getById(@PathVariable Long id) {
		BaseFeeStandard baseFeeStandard = baseFeeStandardService.getById(id);
		return baseFeeStandard != null ? ResultUtil.success(baseFeeStandard) : ResultUtil.failed("未找到该费用标准");
	}

	@PostMapping("/page")
	@ApiOperation("分页查询费用标准")
	public Result<Page<?>> page(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success(baseFeeStandardService.page(request));
	}

	@PostMapping("/export")
	@ApiOperation("导出零担快递费用标准")
	@AsyncExportTask(name = "零担快递费用标准导出", methodPath = "BaseFeeStandardService.pageKd")
	public Result<String> export(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success();
	}

	@PostMapping("/pageView")
	@ApiOperation("分页查询费用信息")
	public Result<Page<BaseFeeStandardView>> pageView(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success(baseFeeStandardService.query(request));
	}

	@PostMapping("/exportView")
	@ApiOperation("导出查询零担快递费用信息")
	@AsyncExportTask(name = "查询零担快递费用信息导出", methodPath = "BaseFeeStandardService.view")
	public Result<String> exportView(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success();
	}

	@GetMapping("/getExcelTemplate")
	@ApiOperation("获取零担快递费用标准Excel模版")
	public void getExcelTemplate(HttpServletResponse response) throws Exception {
		String name = "零担快递费用标准导入模板.xlsx";
		ClassPathResource classPathResource = new ClassPathResource("/file/" + name);
		response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(name, "UTF-8"));
		response.setContentType("application/vnd.ms-excel;charset=UTF-8");
		ServletUtil.write(response, classPathResource.getInputStream());
	}

	@PostMapping("/exportSpecialCar")
	@ApiOperation("导出专车费用标准")
	@AsyncExportTask(name = "专车费用标准导出", methodPath = "BaseFeeStandardService.pageSpecialCar")
	public Result<String> exportSpecialCar(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success();
	}

	@PostMapping("/exportSpecialCarView")
	@ApiOperation("导出专车查询费用标准")
	@AsyncExportTask(name = "专车费用查询导出", methodPath = "BaseFeeStandardService.specialCarView")
	public Result<String> exportSpecialCarView(@RequestBody PageQuery<BaseFeeStandardQuery> request) {
		return ResultUtil.success();
	}

	@GetMapping("/getSpecialCarExcelTemplate")
	@ApiOperation("获取专车费用标准Excel模版")
	public void getSpecialCarExcelTemplate(HttpServletResponse response) throws Exception {
		String name = "专车费用标准导入模板.xlsx";
		ClassPathResource classPathResource = new ClassPathResource("/file/" + name);
		response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(name, "UTF-8"));
		response.setContentType("application/vnd.ms-excel;charset=UTF-8");
		ServletUtil.write(response, classPathResource.getInputStream());
	}

	@PostMapping("/importVerify")
	@ApiOperation("校验导入费用标准")
	public Result<ImportResponseDto> importVerify(@RequestParam("file") MultipartFile file, Integer type) throws Exception {
		Map<String, Object> params = new HashMap<>();
		params.put("type", ObjectUtil.defaultIfNull(type, 1));
		return ResultUtil.success(baseFeeStandardService.importVerify(file.getInputStream(), BaseFeeStandardImport.class, params));
	}

	@PostMapping("/downloadFailFile/{fileCode}")
	@ApiOperation("下载费用标准导入失败列表")
	@ExcelResponse(filename = "零担快递费用标准导入失败列表", parameter = @ExcelParameter(writeHandler = LongestMatchColumnWidthStyleStrategy.class))
	public List<?> downloadFailFile(@PathVariable String fileCode, Integer type) {
		List<BaseFeeStandardImport> failList = baseFeeStandardService.getFailList(fileCode);
		if (CollUtil.isEmpty(failList)) {
			throw new RuntimeException("错误文件不存在");
		}
		if (Integer.valueOf(2).equals(type)) {
			return BeanUtil.copyToList(failList, BaseFeeStandardSpecialCarImportVO.class);
		}
		return BeanUtil.copyToList(failList, BaseFeeStandardImportVO.class);
	}

	@PostMapping("/importData/{fileCode}")
	@ApiOperation("导入费用标准数据")
	public Result<String> importData(@PathVariable String fileCode) {
		return ResultUtil.success(baseFeeStandardService.importData(fileCode));
	}
}