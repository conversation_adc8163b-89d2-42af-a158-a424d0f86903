package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.service.BaseMaterialsService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:47
 * @Description:
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseMaterials")
@Tag(name = "物料相关接口")
public class BaseMaterialController {

    @Resource
    private BaseMaterialsService baseMaterialsService;

    @PostMapping("/queryPic")
    @Operation(summary = "查询物料-照片(骁龙抓取)")
    public List<MaterialPicVO> queryPic(@RequestBody List<String> materialCodeList) throws ResultException {
        return baseMaterialsService.queryPic(materialCodeList);
    }

    @PostMapping("/page")
    @Operation(summary = "查询物料-列表展示", description = "查询物料-列表展示")
    public Page<GetMaterialPageResonseVo> page(@RequestBody PageQuery<GetMaterialPageRequestDto> page) throws ResultException {
        return baseMaterialsService.getPage(page);
    }

    @PostMapping("/detail")
    @Operation(summary = "查询物料详情", description = "查询物料详情")
    public Result<GetMaterialDetailResponseVo> detail(@RequestBody GetMaterialDetailRequestVo param) throws ResultException {
        return baseMaterialsService.detail(param);
    }

    @PostMapping("/querySupplierPackage")
    @Operation(summary = "查询供应商包装信息", description = "查询供应商包装信息")
    public Result<List<PackageInfoVo>> querySupplierPackage(@RequestBody QuerySupplierPackageDto param) {
        return baseMaterialsService.querySupplierPackage(param);
    }

    @PostMapping("/uploadPic")
    @Operation(summary = "导入物料图片", description = "导入物料图片")
    public Result<String> importMaterialPicture(@RequestBody @Validated MaterialPictureFile param) {
        return baseMaterialsService.importMaterialPicture(param);
    }

    @PostMapping("/save")
    @Operation(summary = "保存物料图片", description = "保存物料图片")
    public Result<String> save(@RequestBody @Validated SaveMaterialCodeDto param) {
        return baseMaterialsService.submit(param);
    }

    @PostMapping("/deletePics")
    @Operation(summary = "删除物料图片", description = "删除物料图片")
    public Result<String> deletePics(@RequestBody @Validated DeletePicsDto param) {
        return baseMaterialsService.deletePics(param);
    }

    @PostMapping("/export")
    @Operation(summary = "查看页面-导出物料", description = "导出物料")
    @AsyncExportTask(name = "导出物料", methodPath = "BaseMaterialsService.exportItem")
    public Result<String> viewExport(@RequestBody PageQuery<GetMaterialPageRequestDto> page) {
        return ResultUtil.success();
    }

    /**
     * 校验标准分页查询
     *
     * @param page
     * @return
     */
    @PostMapping("/getCheckPage")
    public Page<GetMaterialCheckPageResponseVo> getCheckPage(@RequestBody PageQuery<GetMaterialCheckPageRequestDto> page) {
        return baseMaterialsService.getCheckPage(page);
    }

    /**
     * 删除匹配关系
     *
     * @return 结果
     */
    @DeleteMapping("/delMatchingTies")
    public Result<String> delMatchingTies(@RequestBody GetMaterialCheckPageRequestDto dto) {
        return baseMaterialsService.delMatchingTies(dto);
    }

    /**
     * 检验标准导出
     *
     * @param page 导出查询参数
     * @return 结果
     */
    @PostMapping("/exportCheckPage")
    @AsyncExportTask(name = "备件分配检验标准导出", methodPath = "BaseMaterialsService.getCheckPage")
    public Result<String> exportCheckPage(@RequestBody PageQuery<GetMaterialCheckPageRequestDto> page) {
        return ResultUtil.success();
    }

    /**
     * 导入单元包装
     *
     * @param file 导入文件
     */
    @PostMapping(value = "/importUnitPackage", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResponseDto importUnitPackage(@RequestParam("file") MultipartFile file) {
        return baseMaterialsService.importUnitPackage(file);
    }

    /**
     * 单元包装获取校验成功的分页查询
     *
     * @param page 查询参数
     * @return 结果
     */
    @PostMapping("/getSuccessPage")
    public Page<UnitPackageImportVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        return baseMaterialsService.getSuccessPage(page);
    }

    /**
     * 单元包装下载校验失败的数据
     *
     * @param operationCode 文件编码
     * @param response      响应失败的结果
     */
    @PostMapping("/exportFailUnitPackage/{operationCode}")
    public void exportFailUnitPackage(@PathVariable(value = "operationCode") String operationCode, HttpServletResponse response) {
        baseMaterialsService.exportFailUnitPackage(operationCode, response);
    }

    /**
     * 提交校验成功的文件
     *
     * @param operationCode
     */
    @PostMapping(value = "/submitUnitPackage")
    public Result<String> submitUnitPackage(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        return baseMaterialsService.submitUnitPackage(operationCode.getOperationCode());
    }

    /**
     * 编辑页修改校验标准提交
     *
     * @return 结果
     */
    @PutMapping(value = "/editCheckInfo/{materialId}/{checkCodeId}")
    public Result<String> editCheckInfo(@PathVariable(value = "materialId") String materialId, @PathVariable(value = "checkCodeId") String checkCodeId) {
        return baseMaterialsService.editCheckInfo(materialId, checkCodeId);
    }

    /**
     * 物料单元包装导入模板下载
     *
     * @return 结果
     */
    @GetMapping("/packageTempFile")
    public ImageResponseDTO packageTempFile() {
        return baseMaterialsService.packageTempFile();
    }

    /**
     * 物料规则匹配导入模板下载
     *
     * @return 结果
     */
    @GetMapping("/rulesTempFile")
    public void rulesTempFile(HttpServletResponse response) {
        baseMaterialsService.rulesTempFile(response);
    }

    /**
     * 物料规则匹配导入校验
     *
     * @param file 文件
     * @return 结果
     */
    @PostMapping(value = "/rulesImportCheck", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ImportResponseDto rulesImportCheck(@RequestParam("file") MultipartFile file) {
        return baseMaterialsService.rulesImportCheck(file);
    }

    /**
     * 获取校验成功的规则分页
     *
     * @param page 分页参数
     * @return 结果
     */
    @PostMapping("/getSuccessRulesPage")
    public Page<ValidationRulesImportVo> getSuccessRulesPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        return baseMaterialsService.getSuccessRulesPage(page);
    }

    /**
     * 导出校验失败的规则
     *
     * @param operationCode 导出文件编码
     */
    @PostMapping("/exportFailRules/{operationCode}")
    public void exportFailRules(@PathVariable(value = "operationCode") String operationCode, HttpServletResponse response) {
        baseMaterialsService.exportFailRules(operationCode, response);
    }

    /**
     * 提交校验成功的数据
     *
     * @param operationCode 文件编码
     * @return 结果
     * @throws ResultException 异常
     */
    @PostMapping(value = "/submitSuccessRules")
    public Result<String> submitSuccessRules(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        return baseMaterialsService.submitSuccessRules(operationCode.getOperationCode());
    }

}
