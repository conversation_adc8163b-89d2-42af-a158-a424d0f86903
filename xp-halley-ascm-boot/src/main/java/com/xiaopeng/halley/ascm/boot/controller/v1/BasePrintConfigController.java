package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintConfig;
import com.xiaopeng.halley.ascm.boot.service.BasePrintConfigService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api("远程打印配置表接口")
@RestController
@RequestMapping("/basePrintConfig")
public class BasePrintConfigController {
	@Resource
	private BasePrintConfigService basePrintConfigService;

	@PostMapping
	@ApiOperation("新增远程打印配置表")
	public Result<String> add(@RequestBody BasePrintConfig basePrintConfig) {
		try {
			boolean success = basePrintConfigService.create(basePrintConfig);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping
	@ApiOperation("删除远程打印配置表")
	public Result<String> delete(@RequestBody List<Long> ids) {
		try {
			boolean success = basePrintConfigService.removeByIds(ids);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PutMapping
	@ApiOperation("修改远程打印配置表")
	public Result<String> update(@RequestBody BasePrintConfig basePrintConfig) {
		try {
			boolean success = basePrintConfigService.updateById(basePrintConfig);
			return success ? ResultUtil.success("修改成功") : ResultUtil.failed("修改失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("修改失败：" + e.getMessage());
		}
	}

	@GetMapping("/{id}")
	@ApiOperation("根据ID查询远程打印配置表")
	public Result<BasePrintConfig> getById(@PathVariable Long id) {
		BasePrintConfig basePrintConfig = basePrintConfigService.getById(id);
		return basePrintConfig != null ? ResultUtil.success(basePrintConfig) : ResultUtil.failed("未找到该远程打印配置表");
	}

	@PostMapping("/page")
	@ApiOperation("分页查询远程打印配置表")
	public Result<Page<BasePrintConfig>> page(@RequestBody PageQuery<BasePrintConfig> request) {
		LambdaQueryWrapper<BasePrintConfig> wrapper = new LambdaQueryWrapper<>(request.getParam());
		wrapper.orderByDesc(BasePrintConfig::getId);
		return ResultUtil.success(basePrintConfigService.page(request.toPage(), wrapper));
	}

}