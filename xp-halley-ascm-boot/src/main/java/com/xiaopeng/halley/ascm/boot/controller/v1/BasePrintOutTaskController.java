package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.common.enums.BaseEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.PrintTaskStatusEnum;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintOutTask;
import com.xiaopeng.halley.ascm.boot.entity.BzPrintLog;
import com.xiaopeng.halley.ascm.boot.service.BaseFileService;
import com.xiaopeng.halley.ascm.boot.service.BasePrintOutTaskService;
import com.xiaopeng.halley.ascm.boot.service.BzPrintLogService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.utils.PageUtils;
import com.xiaopeng.halley.ascm.boot.xxlJob.MatchPublishingJob;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:51
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/basePrintOutTask")
@Tag(name = "打印任务")
public class BasePrintOutTaskController {
    @Resource
    private BaseFileService baseFileService;
    @Resource
    private BzPrintLogService bzPrintLogService;
    @Resource
    private BasePrintOutTaskService basePrintOutTaskService;
    @Resource
    private MatchPublishingJob matchPublishingJob;

    @PostMapping("/createPdf")
    @Operation(summary = "重新生成pdf")
    public Boolean createPdf(@RequestBody List<Long> ids) {
        for (BasePrintOutTask basePrintOutTask : basePrintOutTaskService.listByIds(ids)) {
            matchPublishingJob.createPdfToOss(basePrintOutTask);
        }
        return Boolean.TRUE;
    }

    @PostMapping("/submit")
    @Operation(summary = "更新打印任务状态")
    public Boolean submit(@RequestBody BasePrintOutTaskSubmit param) {
        PrintTaskStatusEnum printStatus = BaseEnum.valueOf(PrintTaskStatusEnum.class, param.getStatus());
        BasePrintOutTask basePrintOutTask = basePrintOutTaskService.getById(param.getId());
        if (basePrintOutTask == null) {
            throw new RuntimeException("任务不存在");
        }

        // 打印成功，增加打印次数
        if (printStatus == PrintTaskStatusEnum.SUCCESS) {
            basePrintOutTask.setCount(basePrintOutTask.getCount() + 1);
        }
        basePrintOutTask.setStatus(param.getStatus());
        basePrintOutTaskService.updateById(basePrintOutTask);

        if (printStatus == PrintTaskStatusEnum.FAIL) {
            BzPrintLog bzPrintLog = BeanUtil.copyProperties(basePrintOutTask, BzPrintLog.class, "id");
            bzPrintLog.setState(param.getState());
            bzPrintLog.setMessage(param.getMessage());
            bzPrintLogService.save(bzPrintLog);
        }
        return true;
    }

    @PostMapping("/remotePage")
    @Operation(summary = "查询待打印的远程打印任务")
    public Page<BasePrintResponseDto> remotePage(@RequestBody PageQuery<BasePrintOutTaskQuery> pageQuery) {
        BasePrintOutTaskQuery param = pageQuery.getParam();
        LambdaQueryWrapper<BasePrintOutTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(param.getPrintList()), BasePrintOutTask::getPrinterName, param.getPrintList());
        wrapper.ge(param.getStartTime() != null, BasePrintOutTask::getUpdateTime, param.getStartTime());
        wrapper.le(param.getEndTime() != null, BasePrintOutTask::getUpdateTime, param.getEndTime());
        // 已经生成了pdf的打印任务才会被查询
        wrapper.isNotNull(BasePrintOutTask::getBaseFileId);
        wrapper.eq(BasePrintOutTask::getStatus, PrintTaskStatusEnum.UN_PRINTED.getValue());
        // 先创建的优先给客户端进行打印
        wrapper.orderByAsc(BasePrintOutTask::getCreateTime);

        Page<BasePrintResponseDto> pageResult = PageUtils.copyToPage(basePrintOutTaskService.page(pageQuery.toPage(), wrapper), BasePrintResponseDto.class);
        baseFileService.setOssUrl(pageResult.getRecords(), BasePrintResponseDto::getBaseFileId, (row, file) -> {
            row.setFileName(file.getFileName());
            row.setFileUrl(file.getUrl());
        });
        return pageResult;
    }

    @PostMapping("/page")
    @Operation(summary = "列表查询", description = "列表查询")
    public Page<BasePrintResponseDto> page(@RequestBody PageQuery<BasePrintRequestDto> page) throws ResultException {
        Page<BasePrintResponseDto> pageResult = basePrintOutTaskService.page(page);
        baseFileService.setOssUrl(pageResult.getRecords(), BasePrintResponseDto::getBaseFileId, (row, file) -> {
            row.setFileName(file.getFileName());
            row.setFileUrl(file.getUrl());
        });
        return pageResult;
    }

    @PostMapping("/cancelPrint")
    @Operation(summary = "取消打印", description = "取消打印")
    public void cancelPrint(@RequestBody BasePrintCancelRequestDto cancelDto) throws ResultException {
        basePrintOutTaskService.cancelPrint(cancelDto);
    }

    @PostMapping("/print")
    @Operation(summary = "开始打印", description = "开始打印")
    public Result<String> print(@RequestBody BasePrintActionRequestDto actionDto) throws ResultException {
        basePrintOutTaskService.print(actionDto);
        return ResultUtil.success();
    }

}
