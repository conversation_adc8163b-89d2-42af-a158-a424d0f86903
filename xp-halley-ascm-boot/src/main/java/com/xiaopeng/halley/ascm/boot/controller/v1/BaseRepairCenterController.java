package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterVo;
import com.xiaopeng.halley.ascm.boot.service.BaseRepairCenterService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 11:07
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseRepairCenter")
@Tag(name = "维修中心主数据相关接口")
public class BaseRepairCenterController {

    @Resource
    private BaseRepairCenterService baseRepairCenterService;

    /**
     * 新增或更新
     *
     * @param dto 新增或更新的实体
     * @return 新增或更新结果
     */
    @PostMapping("/updateOrAdd")
    @Operation(summary = "维修中心新增或更新", description = "维修中心新增或更新")
    public Result update(@RequestBody BaseRepairCenterDto dto) {
        log.info("BaseRepairCenterController update {}", JSON.toJSONString(dto));
        return baseRepairCenterService.updateOrAdd(dto);
    }

    /**
     * 维修中心分页查询
     *
     * @param page
     * @return
     */
    @PostMapping("/getPage")
    @Operation(summary = "维修中心分页查询", description = "维修中心分页查询")
    public Page<BaseRepairCenterVo> getPage(@RequestBody PageQuery<BaseRepairCenterDto> page) {
        log.info("BaseRepairCenterController getPage {}", JSON.toJSONString(page));
        return baseRepairCenterService.getPage(page);
    }

    /**
     * 字段校验
     *
     * @param code
     * @return
     */
    @GetMapping("/getRepairCenterNum")
    @Operation(summary = "获取维修中心编码校验", description = "获取维修中心编码校验")
    public Result getRepairCenterNum(@RequestParam String code) {
        log.info("BaseRepairCenterController getRepairCenterNum {}", code);
        return baseRepairCenterService.getRepairCenterNum(code);
    }
}
