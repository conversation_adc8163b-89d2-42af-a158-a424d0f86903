package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.AccountSuccessRequestDTO;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopVo;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.dto.importExport.BaseShopBatteryExport;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.service.BaseShopService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:51
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseShop")
@Tag(name = "门店数据相关接口")
public class BaseShopController {
    @Resource
    private BaseShopService baseShopService;

    /**
     * 门店数据表模板下载
     *
     * @return
     * @throws ResultException
     * @throws UnsupportedEncodingException
     */
    @GetMapping("/tempFile")
    @Operation(summary = "门店信息模板文件下载", description = "门店信息模板文件下载")
    public ImageResponseDTO tempFile() {
        log.info("BaseCarExWarehouseTaskController tempFile 开始执行");
        return baseShopService.tempFile();
    }

    /**
     * 门店数据表导出
     *
     * @param response
     */
    @GetMapping("/shopDataExport")
    @Operation(summary = "门店数据表导出", description = "门店数据表导出")
    public void shopExport(HttpServletResponse response) {
        baseShopService.shopExport(response);
    }

    /**
     * 门店数据表导入
     *
     * @param file
     * @return
     * @throws ResultException
     */
    @PostMapping(value = "/shopDataImport", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "门店数据导入", description = "门店数据导入")
    public Result<ImportResponseDto> shopImport(@RequestParam("file") MultipartFile file) throws ResultException {
        ImportResponseDto responseDTO = baseShopService.shopImport(file);
        return ResultUtil.success(responseDTO);
    }

    /**
     * 门店数据分页查询
     *
     * @param page 查询参数
     * @return 查询结果
     */
    @PostMapping("/getPage")
    @Operation(summary = "门店数据分页查询", description = "门店数据分页查询")
    public Page<BaseShopVo> getPage(@RequestBody PageQuery<BaseShopDto> page) {
        log.info("BaseShopController getPage {}", JSON.toJSONString(page));
        return baseShopService.getPage(page);
    }

    /**
     * 更新门店状态
     *
     * @param id
     * @param status
     * @return
     */
    @PutMapping("/updateStatus")
    @Operation(summary = "门店数据更新状态", description = "门店数据更新状态(0-启用，1-停用)")
    public Result updateStatus(@RequestParam String id, @RequestParam Integer status) {
        log.info("BaseShopController updateStatus {} {}", id, status);
        return baseShopService.updateStatus(id, status);
    }

    /**
     * 导入电池装运联系人模板下载
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/batteryTempFile")
    @Operation(summary = "导入电池装运联系人模板下载", description = "导入电池装运联系人模板下载")
    public ImageResponseDTO batteryTempFile() {
        log.info("BaseShopController batteryTempFile 开始执行");
        return baseShopService.batteryTempFile();
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/batteryImportFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "导入电池装运联系人导入数据参数校验", description = "导入电池装运联系人导入数据参数校验")
    public ImportResponseDto batteryImportFile(@RequestParam("file") MultipartFile file) throws Exception {
        log.info("BaseShopController batteryImportFile 开始执行");
        return baseShopService.batteryImportFile(file);
    }

    /**
     * 基础门店数据新增或更新
     *
     * @param dto
     * @return
     */
    @PostMapping("updateOrAdd")
    @Operation(summary = "基础门店数据新增或更新", description = "基础门店数据新增或更新")
    public Result updateOrAdd(@RequestBody BaseShopDto dto) {
        log.info("BaseShopController updateOrAdd");
        return baseShopService.updateOrAdd(dto);
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "导入电池装运联系人校验成功的数据分页", description = "导入电池装运联系人校验成功的数据分页")
    public Page<BaseShopBatteryExport> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BaseShopController getSuccessPage {}", JSON.toJSONString(page));
        return baseShopService.getSuccessPage(page);
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "导入电池装运联系人下载错误的文件", description = "导入电池装运联系人下载错误的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BaseShopController downloadFailFile {}", JSON.toJSONString(dto));
        baseShopService.downloadFailFile(dto, response);
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @PostMapping("/importData")
    @Operation(summary = "导入电池装运联系人导入校验成功的规则", description = "导入电池装运联系人导入校验成功的规则")
    public Result importData(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BaseShopController importData {}", JSON.toJSONString(operationCode));
        return baseShopService.importData(operationCode.getOperationCode());
    }

}
