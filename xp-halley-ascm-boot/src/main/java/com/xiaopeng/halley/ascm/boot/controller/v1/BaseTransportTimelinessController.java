package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.AccountSuccessRequestDTO;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessVo;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.service.BaseTransportTimelinessService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.mqi.basic.security.common.login.OrganizationInfo;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import com.xpeng.athena.sdk.mbp.web.service.MbpService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author：huqizhi
 * @Date：2023/8/7 9:22
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseTransportTimeliness")
@Tag(name = "运输时效相关接口")
public class BaseTransportTimelinessController {
    @Resource
    private MbpService mbpService;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
    @Resource
    private BaseTransportTimelinessService baseTransportTimelinessService;

    /**
     * 运输时效分页查询
     *
     * @param page 查询参数
     * @return 查询结果
     */
    @PostMapping("/getPage")
    @Operation(summary = "运输时效分页查询", description = "运输时效分页查询")
    public Page<BaseTransportTimelinessVo> getPage(@RequestBody PageQuery<BaseTransportTimelinessDto> page) {
        log.info("BaseTransportTimelinessController getPage {}", JSON.toJSONString(page));
        BaseTransportTimelinessDto param = page.getParam();
        if (param != null && param.isIsolation()) {
            OrganizationInfo.Organization currentOrganization = mbpService.getLoginInfo().getOrganizationInfo().getCurrentOrganization();
            if (StrUtil.contains(currentOrganization.getRemark(), "=")) {
                String lgort = currentOrganization.getRemark().split("=")[1];
                BaseWarehouse baseWarehouse = baseWarehouseMapper.selectByLgort(lgort);
                if (baseWarehouse != null) {
                    param.setWarehouseCity(baseWarehouse.getWarehouseCity());
                }
            }
        }
        return baseTransportTimelinessService.getPage(page);
    }

    /**
     * 新增或添加
     *
     * @param dto 新增或添加的实体
     * @return 新增或添加结果
     */
    @PostMapping("/updateOrAdd")
    @Operation(summary = "运输时效新增或添加", description = "运输时效新增或添加")
    public Result update(@RequestBody BaseTransportTimelinessDto dto) {
        log.info("BaseTransportTimelinessController update {}", dto);
        return baseTransportTimelinessService.updateOrAdd(dto);
    }

    /**
     * 运输时效导出
     *
     * @param page 导出条件
     */
    @PostMapping("/downloadFile")
    @Operation(summary = "运输时效导出", description = "运输时效导出")
    @AsyncExportTask(name = "运输时效导出", methodPath = "BaseTransportTimelinessService.getPage")
    public Result downloadFile(@RequestBody PageQuery<BaseTransportTimelinessDto> page) {
        return ResultUtil.success();
    }

    /**
     * 运输时效模板下载
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/tempFile")
    @Operation(summary = "运输时效模板下载", description = "运输时效模板下载")
    public ImageResponseDTO tempFile() {
        log.info("BaseTransportTimelinessController tempFile 开始执行");
        return baseTransportTimelinessService.tempFile();
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "运输时效导入数据参数校验", description = "运输时效导入数据参数校验")
    public ImportResponseDto importFile(@RequestParam("file") MultipartFile file) {
        log.info("BaseTransportTimelinessController importList 开始执行");
        return baseTransportTimelinessService.importFile(file);
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "运输时效获取校验成功的数据分页", description = "运输时效获取校验成功的数据分页")
    public Page<BaseTransportTimelinessVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BaseTransportTimelinessController getSuccessPage {}", JSON.toJSONString(page));
        return baseTransportTimelinessService.getSuccessPage(page);
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "运输时效下载错误的文件", description = "运输时效下载错误的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BaseTransportTimelinessController downloadFailFile {}", JSON.toJSONString(dto));
        baseTransportTimelinessService.downloadFailFile(dto, response);
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @PostMapping("/importData")
    @Operation(summary = "运输时效保存校验成功的规则", description = "运输时效保存校验成功的规则")
    public Result importData(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BaseTransportTimelinessController importData {}", JSON.toJSONString(operationCode));
        return baseTransportTimelinessService.importData(operationCode.getOperationCode());
    }

}
