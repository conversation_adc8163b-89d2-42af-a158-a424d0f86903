package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.entity.BaseTransportType;
import com.xiaopeng.halley.ascm.boot.service.BaseTransportTypeService;
import com.xpeng.athena.common.core.domain.PageRequest;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 运输类型表
 */
@RestController
@GlobalResponseBody
@RequestMapping("/baseTransportType")
@Tag(name = "运输类型表")
public class BaseTransportTypeController {
    @Resource
    private BaseTransportTypeService baseService;

    @PostMapping("/create")
    @Operation(summary = "新增运输类型")
    public Result<String> create(@RequestBody BaseTransportType request) {
        this.baseService.checkExists(request);
        this.baseService.save(request);
        return ResultUtil.success();
    }

    @PostMapping("/update")
    @Operation(summary = "修改运输类型")
    public Result<String> update(@RequestBody BaseTransportType request) {
        this.baseService.checkExists(request);
        this.baseService.updateById(request);
        return ResultUtil.success();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除运输类型")
    public Result<String> delete(@PathVariable String id) {
        this.baseService.removeById(id);
        return ResultUtil.success();
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询运输类型")
    public Result<Page<BaseTransportType>> page(@RequestBody PageRequest<BaseTransportType> request) {
        Page<BaseTransportType> page = new Page<>(request.getPage(), request.getSize());
        return ResultUtil.success(this.baseService.page(page, new LambdaQueryWrapper<>(request.getParam())));
    }

    @PostMapping("/enable/{id}")
    @Operation(summary = "启用运输类型")
    public Result<Page<BaseTransportType>> enable(@PathVariable String id) {
        this.baseService.lambdaUpdate().eq(BaseTransportType::getId, id)
                .set(BaseTransportType::getStatus, 0).update();
        return ResultUtil.success();
    }

    @PostMapping("/disable/{id}")
    @Operation(summary = "禁用运输类型")
    public Result<Page<BaseTransportType>> disable(@PathVariable String id) {
        this.baseService.lambdaUpdate().eq(BaseTransportType::getId, id)
                .set(BaseTransportType::getStatus, 1).update();
        return ResultUtil.success();
    }

    @GetMapping("/getTypeAll")
    @Operation(summary = "获取所有类型", description = "获取所有类型")
    public Result<List<String>> getTypeAll() {
        return ResultUtil.success(this.baseService.getTypeAll());
    }

}
