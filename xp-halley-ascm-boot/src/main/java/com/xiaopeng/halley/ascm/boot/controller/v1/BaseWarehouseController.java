package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.service.BaseWarehouseService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:51
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseWarehouse")
@Tag(name = "仓库页面相关接口")
public class BaseWarehouseController {

    @Resource
    private BaseWarehouseService baseWarehouseService;

    @PostMapping("/page")
    @Operation(summary = "列表查询", description = "列表查询")
    public Page<BaseWarehouseResponseDto> page(@RequestBody PageQuery<BaseWarehouseRequestDto> page) throws ResultException {
        return baseWarehouseService.getPage(page);
    }

    @PostMapping("/stopAndOpen")
    @Operation(summary = "停用启用", description = "停用启用")
    public Result<String> stopAndOpen(@RequestBody BaseWarehouseStopAndOpenResponseDto update) throws ResultException {
        baseWarehouseService.stopAndOpen(update);
        return ResultUtil.success();
    }

    @PostMapping("/insertItem")
    @Operation(summary = "新增", description = "新增")
    public Result<String> insertItem(@RequestBody BaseWarehouseInsertResponseDto insert) throws ResultException {
        baseWarehouseService.insertItem(insert);
        return ResultUtil.success();
    }

    @PostMapping("/updateItem")
    @Operation(summary = "更新", description = "更新")
    public Result<String> updateItem(@RequestBody BaseWarehouseUpdateResponseDto update) {
        baseWarehouseService.updateItem(update);
        return ResultUtil.success();
    }

}
