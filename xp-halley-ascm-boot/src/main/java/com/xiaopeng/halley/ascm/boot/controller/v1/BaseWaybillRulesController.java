package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseWaybillRules;
import com.xiaopeng.halley.ascm.boot.service.AscmLockHelper;
import com.xiaopeng.halley.ascm.boot.service.BaseWaybillRulesService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.mqi.basic.security.common.login.LoginInfo;
import com.xiaopeng.mqi.common.LoginUser;
import com.xiaopeng.mqi.helper.LoginUserHelper;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import com.xpeng.athena.sdk.mbp.web.service.MbpService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/baseWaybillRules")
@Tag(name = "运单匹配规则表")
public class BaseWaybillRulesController {

    @Resource
    private BaseWaybillRulesService baseService;

    @Resource
    private AscmLockHelper ascmLockHelper;

    @Resource
    private MbpService mbpService;

    @PostMapping("/page")
    @Operation(summary = "分页", description = "分页")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Page<BaseWaybillRulesDTO> page(@RequestBody PageQuery<BaseWaybillRulesRequestDTO> page) {

        LoginInfo loginInfo = mbpService.getLoginInfo();
        log.info("loginInfo [{}]", JSONObject.toJSONString(loginInfo));

        LoginUser loginUser = LoginUserHelper.getLoginUser();
        log.info("loginUser BaseWaybillRulesController abc [{}]", JSONObject.toJSONString(loginUser));

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            log.info("loginUser BaseWaybillRulesController CCCC [{}]", JSONObject.toJSONString(authentication));
            Object principal = authentication.getPrincipal();
            if (principal instanceof LoginUser) {
                loginUser = (LoginUser) principal;
            }
        } else {
            log.info("loginUser BaseWaybillRulesController DDDD");
        }

        return this.baseService.page(page);
    }

    @PostMapping("/export")
    @Operation(summary = "导出", description = "导出")
    @AsyncExportTask(name = "运单匹配规则导出", methodPath = "BaseWaybillRulesService.page")
    public Result export(@RequestBody PageQuery<BaseWaybillRulesRequestDTO> page) {
        return ResultUtil.success();
    }

    @PostMapping("/getByCode")
    @Operation(summary = "根据编码获取对应信息", description = "根据编码获取对应信息 1:门店 2:仓库 3:承运商")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Object getByCode(@RequestBody BaseWaybillRulesGetByCodeDTO param) throws ResultException {
        return this.baseService.getByCode(param);
    }

    @PostMapping("/createRule")
    @Operation(summary = "创建规则", description = "创建规则")
    public boolean createRule(@RequestBody BaseWaybillRules param) throws ResultException {
        return this.baseService.createRule(param);
    }

    @PostMapping("/deleteByIds")
    @Operation(summary = "批量删除", description = "批量删除")
    public boolean deleteByIds(@RequestBody List<Long> ids) throws ResultException {
        return this.baseService.deleteByIds(ids);
    }

    @GetMapping("/tempFile")
    @Operation(summary = "运单匹配规则导入模版下载", description = "运单匹配规则导入模版下载")
    public ImageResponseDTO tempFile() {
        return this.baseService.tempFile();
    }

    @PostMapping(value = "/importList", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "导入校验", description = "导入校验")
    public ImportResponseDto importList(@RequestParam("file") MultipartFile file) throws ResultException {
        return this.baseService.importList(file);
    }

    @PostMapping("/getAccountPage")
    @Operation(summary = "获取成功条目分页", description = "获取成功条目分页")
    public Page<BaseWayBillRulesImportDTO> getAccountPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) throws ResultException {
        return baseService.getAccountPage(page);
    }

    @PostMapping("/importAccount")
    @Operation(summary = "导入任务", description = "导入任务")
    public ImportResponseDto importAccount(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        return this.baseService.importAccount(operationCode.getOperationCode());
    }

    @PostMapping("/failDownload")
    @Operation(summary = "下载错误文件", description = "下载错误文件")
    @AsyncExportTask(name = "下载错误文件", methodPath = "BaseWaybillRulesService.failDownload", syncFlag = 1)
    public void failDownload(@RequestBody PageQuery<AccountSuccessRequestDTO> pageQuery, HttpServletResponse response) {
    }

    @PostMapping("/updateRules")
    @Operation(summary = "更新规则", description = "更新规则")
    public boolean updateRules(@RequestBody BaseWaybillRules baseWaybillRules) throws ResultException {
        return this.baseService.updateRules(baseWaybillRules);
    }

    @GetMapping("/generateRules")
    @Operation(summary = "刷新数据", description = "刷新数据")
    public void generateRules() throws ResultException {

        //大锁
        String buildKey = "ascm:base:waybill:rule:********";
        if (ascmLockHelper.tryLockComboKey(buildKey)) {
            try {
                this.baseService.generateRules();
            } finally {
                ascmLockHelper.unlockComboKey(buildKey);
            }

        }

    }

}
