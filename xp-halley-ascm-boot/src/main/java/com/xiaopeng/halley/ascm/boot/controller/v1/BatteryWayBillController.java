package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.mapper.BaseBatteryRouteCostMapper;
import com.xiaopeng.halley.ascm.boot.service.BatteryWayBillService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillTrackHistoryService;
import com.xiaopeng.halley.ascm.boot.service.ImageService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayImageVO;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 14:56
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/batteryWayBill")
@Tag(name = "电池运单相关接口")
public class BatteryWayBillController {

    @Resource
    private BatteryWayBillService batteryWayBillService;

    @Resource
    private ImageService imageService;

    @Resource
    private BzWaybillTrackHistoryService bzWaybillTrackHistoryService;

    @Resource
    private BaseBatteryRouteCostMapper baseBatteryRouteCostMapper;

    /**
     * 返厂电池运单导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "返厂电池运单导入数据参数校验", description = "返厂电池运单导入数据参数校验")
    public ImportResponseDto importFile(@RequestParam("file") MultipartFile file) {
        log.info("BatteryWayBillController importFile 开始执行");
        return batteryWayBillService.importFile(file);
    }

    /**
     * 返厂电池运单导入数据获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "返厂电池运单导入数据获取校验成功的数据分页", description = "返厂电池运单导入数据获取校验成功的数据分页")
    public Page<BatteryWayBillImportVo> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BatteryWayBillController getSuccessPage {}", JSON.toJSONString(page));
        return batteryWayBillService.getSuccessPage(page);
    }

    /**
     * 返厂电池运单导入数据下载错误数据的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "返厂电池运单导入数据下载错误数据的文件", description = "返厂电池运单导入数据下载错误数据的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BatteryWayBillController downloadFailFile {}", JSON.toJSONString(dto));
        batteryWayBillService.downloadFailFile(dto, response);
    }

    /**
     * 保存校验成功的数据
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @PostMapping("/importData")
    @Operation(summary = "电池运单导入数据-保存校验成功的数据", description = "电池运单导入数据-保存校验成功的数据")
    public Result importData(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BatteryWayBillController importData {}", JSON.toJSONString(operationCode));
        return batteryWayBillService.importData(operationCode.getOperationCode());
    }

    /**
     * 电池运单明细页面分页查询
     *
     * @param page
     * @return
     */
    @PostMapping("/getPage")
    @Operation(summary = "电池运单分页查询", description = "电池运单分页查询")
    public Page<BatteryWayBillVo> getPage(@RequestBody PageQuery<BatteryWayBillDto> page) {
        log.info("BatteryWayBillController getPage {}", JSON.toJSONString(page));
        return batteryWayBillService.getPage(page);
    }

    /**
     * 关闭运单
     *
     * @param waybill
     * @return
     */
    @PutMapping("/closeWaybill")
    @Operation(summary = "电池运单关闭运单", description = "电池运单关闭运单")
    public Result closeWaybill(@RequestParam String waybill) {
        log.info("BatteryWayBillController closeWaybill {}", waybill);
        return batteryWayBillService.closeWaybill(waybill);
    }

    /**
     * 电池运单明细页面导出
     *
     * @param page 导出条件
     */
    @PostMapping("/downloadFile")
    @Operation(summary = "电池运单明细页面导出", description = "电池运单明细页面导出")
    @AsyncExportTask(name = "电池运单明细表导出", methodPath = "BatteryWayBillService.getPage")
    public Result downloadFile(@RequestBody PageQuery<BatteryWayBillDto> page) {
        return ResultUtil.success();
    }

    /**
     * 电池运单上传图片
     *
     * @param imagesfile  图片集合
     * @param waybillCode 运单编号
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/imagesUpload")
    @Operation(summary = "电池运单上传图片", description = "电池运单上传图片")
    public Result imagesUpload(@RequestParam("imagesfile") MultipartFile imagesfile, @RequestParam("waybillCode") String waybillCode) throws IOException {
        return batteryWayBillService.imagesUpload(imagesfile, waybillCode);
    }

    /**
     * 电池运单创建返回运单
     *
     * @param waybillCode 运动编号
     * @return
     */
    @PostMapping("/createReverseWaybill")
    @Operation(summary = "电池运单创建返回运单", description = "电池运单创建返回运单")
    public Result createReverseWaybill(@RequestBody List<String> waybillCode) {
        return batteryWayBillService.createReverseWaybill(waybillCode);
    }

    /**
     * 电池运单编辑运单
     *
     * @param batteryWayBillDto
     * @return
     */
    @PutMapping("/editWaybill")
    @Operation(summary = "电池运单编辑运单", description = "电池运单编辑运单")
    public Result editWaybill(@RequestBody BatteryWayBillDto batteryWayBillDto) {
        return batteryWayBillService.editWaybill(batteryWayBillDto);
    }

    /**
     * 校验输入城市是否有误
     *
     * @param dispatchCity       发运城市
     * @param arrivalCity        接收城市
     * @param transportationType 运输类型
     * @return
     */
    @GetMapping("/cityVerification")
    @Operation(summary = "电池运单编辑发运城市校验", description = "电池运单编辑发运城市校验")
    public Result cityVerification(@RequestParam("dispatchCity") String dispatchCity, @RequestParam("arrivalCity") String arrivalCity, @RequestParam("transportationType") Integer transportationType) {
        return batteryWayBillService.cityVerification(dispatchCity, arrivalCity, transportationType);
    }

    /**
     * 电池运单页面导入运单
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/tempFile")
    @Operation(summary = "电池运单页面下载导入导入运单模板", description = "电池运单页面下载导入导入运单模板")
    public ImageResponseDTO tempFile() {
        log.info("BatteryWayBillController tempFile 开始执行");
        return batteryWayBillService.tempFile();
    }

    @GetMapping("/getImages")
    @Operation(summary = "电池运单编辑页面获取上报的图片", description = "电池运单编辑页面获取上报的图片")
    public List<BzWayImageVO> getImageList(@RequestParam String waybillCode) {
        log.info("BatteryWayBillController getImageList {}", waybillCode);
        List<BzWayImageVO> imageList = bzWaybillTrackHistoryService.getImageList(waybillCode);
        imageList.forEach(imageItem -> {
            ImageResponseDTO tempURL = imageService.getTempURL(imageItem.getFileId());
            if (ObjectUtil.isNotNull(tempURL) && ObjectUtil.isNotNull(tempURL.getUrl())) {
                imageItem.setFileShowURL(tempURL.getUrl());
            }
        });
        return imageList;
    }

    /**
     * 下载运单打印pdf文件
     *
     * @param waybillCodes 多个运单编号
     * @param response
     */
    @PostMapping("/getPrintFile")
    @Operation(summary = "电池运单编辑页面下载打印文件", description = "电池运单编辑页面下载打印文件")
    public void getPrintFile(@RequestBody List<String> waybillCodes, HttpServletResponse response) throws Exception {
        log.info("BatteryWayBillController getPrintFile {}", JSON.toJSONString(waybillCodes));
        batteryWayBillService.getPrintFile(waybillCodes, response);
    }

    @GetMapping("/getLogisticsProvider")
    public List<String> getLogisticsProvider(@RequestParam String dispatchCity, @RequestParam String arrivalCity, @RequestParam String repairCenterNum) {
        log.info("BatteryWayBillController getLogisticsProvider {} {} {}", dispatchCity, arrivalCity, repairCenterNum);
        return baseBatteryRouteCostMapper.getLogisticsProvider(dispatchCity, arrivalCity, repairCenterNum);
    }

}
