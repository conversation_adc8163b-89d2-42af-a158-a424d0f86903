package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.BaseAsyncExportGetURLRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseAsyncExportGetURLResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseAsyncExportRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseAsyncExportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BzAsyncExportLog;
import com.xiaopeng.halley.ascm.boot.service.BzAsyncExportLogService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 异步导出表Controller
 *
 * <AUTHOR> 自动生成
 * @date 2023-01-09 06:17:47
 */
@RestController
@GlobalResponseBody
@RequestMapping("/bzAsyncExportLog")
@Tag(name = "异步导出表接口")
public class BzAsyncExportLogController {

    @Resource
    private BzAsyncExportLogService baseService;

    @PostMapping("/newPage")
    @Operation(summary = "列表查询", description = "列表查询")
    public Page<BaseAsyncExportResponseDto> newPage(@RequestBody PageQuery<BaseAsyncExportRequestDto> page) {
        return baseService.newPage(page);
    }

    @PostMapping("/getDownloadURL")
    @Operation(summary = "获取下载URL", description = "获取下载URL")
    public BaseAsyncExportGetURLResponseDto getDownloadURL(@RequestBody BaseAsyncExportGetURLRequestDto requestDto) throws ResultException {
        return baseService.getDownloadURL(requestDto);
    }

    /**
     * 更新
     *
     * @param id
     * @param bzAsyncExportLogVo
     * @return
     */
    @PostMapping("{id}")
    @Operation(summary = "更新", description = "更新")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public BzAsyncExportLog update(@PathVariable(value = "id") Long id, @RequestBody @Validated BzAsyncExportLog bzAsyncExportLogVo) throws ResultException {
        bzAsyncExportLogVo.setId(id);
        return this.baseService.update(bzAsyncExportLogVo);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    @Operation(summary = "删除", description = "删除")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Boolean delete(@PathVariable(value = "id") Long id) throws ResultException {
        return this.baseService.delete(id);
    }

    /**
     * 分页
     *
     * @param page
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "分页", description = "分页")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Page<BzAsyncExportLog> page(@RequestBody PageQuery<BzAsyncExportLog> page) {
        return this.baseService.page(page);
    }

    /**
     * 列表查询
     *
     * @param bzAsyncExportLogVo
     * @return
     */
    @PostMapping("/list")
    @Operation(summary = "查询list", description = "查询list")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public List<BzAsyncExportLog> list(@RequestBody BzAsyncExportLog bzAsyncExportLogVo) {
        return this.baseService.list(bzAsyncExportLogVo);
    }

    @PostMapping("/export")
    @Operation(summary = "触发异步导出", description = "触发异步导出")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public void export() {
        this.baseService.export();
    }

}
