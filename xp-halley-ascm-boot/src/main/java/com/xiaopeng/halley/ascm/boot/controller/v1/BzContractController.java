package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BzContractQuery;
import com.xiaopeng.halley.ascm.boot.entity.BzContract;
import com.xiaopeng.halley.ascm.boot.entity.BzContractFile;
import com.xiaopeng.halley.ascm.boot.service.BaseWarehouseService;
import com.xiaopeng.halley.ascm.boot.service.BzContractFileService;
import com.xiaopeng.halley.ascm.boot.service.BzContractService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Api("合同接口")
@RestController
@RequestMapping("/bzContract")
public class BzContractController {
	@Resource
	private BzContractService bzContractService;
	@Resource
	private BaseWarehouseService baseWarehouseService;
	@Resource
	private BzContractFileService bzContractFileService;

	@PostMapping
	@ApiOperation("新增合同")
	public Result<String> add(@RequestBody BzContract bzContract) {
		try {
			Assert.checkBetween(bzContract.getContractCode().length(), 0, 50, "合同编号不能超过50");
			Assert.checkBetween(bzContract.getPartners().length(), 0, 50, "合作伙伴不能超过50");
			if (bzContractService.lambdaQuery().eq(BzContract::getContractCode, bzContract.getContractCode()).exists()) {
				return ResultUtil.failed("合同编码已存在");
			}
			bzContractService.checkUnique(bzContract);
			boolean success = bzContractService.save(bzContract);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping("/{id}")
	@ApiOperation("删除合同")
	public Result<String> delete(@PathVariable Long id) {
		if (bzContractFileService.lambdaQuery().eq(BzContractFile::getContractId, id).exists()) {
			return ResultUtil.failed("合同有文档，不能进行删除");
		}
		try {
			boolean success = bzContractService.removeById(id);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PutMapping
	@ApiOperation("修改合同")
	public Result<String> update(@RequestBody BzContract bzContract) {
		try {
			bzContractService.checkUnique(bzContract);
			boolean success = bzContractService.updateById(bzContract);
			return success ? ResultUtil.success("修改成功") : ResultUtil.failed("修改失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("修改失败：" + e.getMessage());
		}
	}

	@GetMapping("/{id}")
	@ApiOperation("根据ID查询合同")
	public Result<BzContract> getById(@PathVariable Long id) {
		BzContract bzContract = bzContractService.getById(id);
		return bzContract != null ? ResultUtil.success(bzContract) : ResultUtil.failed("未找到该合同");
	}

	@PostMapping("/page")
	@ApiOperation("分页查询合同")
	public Result<Page<BzContract>> page(@RequestBody PageQuery<BzContractQuery> request) {
		BzContractQuery param = ObjectUtil.defaultIfNull(request.getParam(), new BzContractQuery());
		LambdaQueryWrapper<BzContract> wrapper = new LambdaQueryWrapper<>();
		wrapper.like(StrUtil.isNotBlank(param.getContractCode()), BzContract::getContractCode, param.getContractCode());
		wrapper.like(StrUtil.isNotBlank(param.getPartners()), BzContract::getPartners, param.getPartners());
		if (param.getEffectiveTime() != null && param.getEndTime() != null) {
			// 查询在给定时间范围内有效的合同（有重叠）
			wrapper.le(BzContract::getEffectiveTime, param.getEndTime())
					.ge(BzContract::getEndTime, param.getEffectiveTime());
		} else {
			if (param.getEffectiveTime() != null) {
				// 查询在开始日期之后仍然有效的合同
				wrapper.le(BzContract::getEffectiveTime, param.getEffectiveTime())
						.ge(BzContract::getEndTime, param.getEffectiveTime());
			}
			if (param.getEndTime() != null) {
				// 查询在结束日期之前已经生效的合同
				wrapper.le(BzContract::getEffectiveTime, param.getEndTime())
						.ge(BzContract::getEndTime, param.getEndTime());
			}
		}
		wrapper.orderByDesc(BzContract::getId);
		Map<String, String> codeMapName = baseWarehouseService.getCodeMapName();
		Page<BzContract> page = bzContractService.page(Page.of(request.getPage(), request.getSize()), wrapper);
		page.getRecords().forEach(e -> e.setOperationWarehouseStr(codeMapName.get(e.getOperationWarehouse())));
		return ResultUtil.success(page);
	}
}