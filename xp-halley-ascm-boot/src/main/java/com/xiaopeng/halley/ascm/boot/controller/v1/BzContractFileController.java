package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BzContractQuery;
import com.xiaopeng.halley.ascm.boot.dto.BzContractFileRequest;
import com.xiaopeng.halley.ascm.boot.dto.BzContractFileTimeVO;
import com.xiaopeng.halley.ascm.boot.dto.MaterialPictureItemVo;
import com.xiaopeng.halley.ascm.boot.entity.BzContractFile;
import com.xiaopeng.halley.ascm.boot.service.BzContractFileService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Api("合同文件接口")
@RestController
@RequestMapping("/bzContractFile")
public class BzContractFileController {
	@Resource
	private BzContractFileService bzContractFileService;

	@PostMapping
	@ApiOperation("新增合同文件")
	public Result<String> add(@RequestBody @Validated BzContractFileRequest request) {
		try {
			DateUtil.parse(request.getTime(), BzContractFileService.TIME_FORMAT_PATTERN);
			List<String> filenames = request.getFileItems().stream().map(MaterialPictureItemVo::getFileName).collect(Collectors.toList());
			if (new HashSet<>(filenames).size() != filenames.size()) {
				return ResultUtil.failed("文件名不能重复");
			}
			bzContractFileService.create(request);
			return ResultUtil.success("新增成功");
		} catch (DateException e) {
			return ResultUtil.failed("所属年月格式不正确，应为" + BzContractFileService.TIME_FORMAT_PATTERN);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping("/{id}")
	@ApiOperation("删除合同文件")
	public Result<String> delete(@PathVariable Long id) {
		try {
			boolean success = bzContractFileService.removeById(id);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PostMapping("/page")
	@ApiOperation("分页查询合同文件")
	public Result<Page<BzContractFileTimeVO>> page(@RequestBody PageQuery<BzContractQuery> request) {
		BzContractQuery param = ObjectUtil.defaultIfNull(request.getParam(), BzContractQuery::new);
		Assert.notNull(param.getContractId(), "合同ID不能为空");
		return ResultUtil.success(bzContractFileService.pageView(request));
	}
}