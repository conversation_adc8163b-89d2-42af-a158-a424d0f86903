package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationVO;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqMaterialRelDto;
import com.xiaopeng.halley.ascm.boot.service.BzErpReqMaterialRelService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:17
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/bzErpReqMaterialRel")
@Tag(name = "运单箱号信息相关接口")
public class BzErpReqMaterialRelController {
    @Resource
    private BzErpReqMaterialRelService bzErpReqMaterialRelService;

    /**
     * 运单箱号信息分页查询
     *
     * @param pageQuery
     * @return
     */
    @PostMapping("/deliveryOrderPage")
    @Operation(summary = "运单箱号信息", description = "运单箱号信息")
    public Page<BzWaybillBoxInformationVO> deliveryOrderPage(@RequestBody PageQuery<BzWaybillBoxInformationDto> pageQuery) {
        return bzErpReqMaterialRelService.deliveryOrderPage(pageQuery);
    }

    /**
     * 运单详情页-箱子中物料详情
     *
     * @param boxCode 箱子编号
     * @return 结果
     */
    @GetMapping("/getMaterialDetail")
    @Operation(summary = "运单详情页-箱子中物料详情", description = "运单详情页-箱子中物料详情")
    public List<BzErpReqMaterialRelDto> getMaterialDetail(@RequestParam String boxCode) {
        log.info("BzErpReqMaterialRelController getMaterialDetail {}", boxCode);
        return bzErpReqMaterialRelService.getBaseMapper().findAllByBoxCode(boxCode);
    }
}
