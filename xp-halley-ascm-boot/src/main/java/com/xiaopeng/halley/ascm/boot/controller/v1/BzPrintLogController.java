package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BzPrintLogQuery;
import com.xiaopeng.halley.ascm.boot.entity.BzPrintLog;
import com.xiaopeng.halley.ascm.boot.service.BzPrintLogService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api("远程打印日志表接口")
@RestController
@RequestMapping("/bzPrintLog")
public class BzPrintLogController {
	@Resource
	private BzPrintLogService bzPrintLogService;

	@PostMapping
	@ApiOperation("新增远程打印日志表")
	public Result<String> add(@RequestBody BzPrintLog bzPrintLog) {
		try {
			boolean success = bzPrintLogService.save(bzPrintLog);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping
	@ApiOperation("删除远程打印日志表")
	public Result<String> delete(@RequestBody List<Long> ids) {
		try {
			boolean success = bzPrintLogService.removeByIds(ids);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PostMapping("/page")
	@ApiOperation("分页查询远程打印日志表")
	public Result<Page<BzPrintLog>> page(@RequestBody PageQuery<BzPrintLogQuery> request) {
		BzPrintLogQuery param = request.getParam();
		LambdaQueryWrapper<BzPrintLog> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(StrUtil.isNotBlank(param.getLgort()), BzPrintLog::getLgort, param.getLgort());
		wrapper.in(CollUtil.isNotEmpty(param.getState()), BzPrintLog::getState, param.getState());
		wrapper.in(CollUtil.isNotEmpty(param.getStatus()), BzPrintLog::getStatus, param.getStatus());
		wrapper.eq(StrUtil.isNotBlank(param.getWaybillCode()), BzPrintLog::getWaybillCode, param.getWaybillCode());
		wrapper.orderByDesc(BzPrintLog::getId);
		return ResultUtil.success(bzPrintLogService.page(request.toPage(), wrapper));
	}
}