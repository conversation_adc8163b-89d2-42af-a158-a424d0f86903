package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaidi100.sdk.response.SubscribeResp;
import com.kuaidi100.sdk.response.SubscribeWithMapPushParamResp;
import com.xiaopeng.halley.ascm.boot.common.enums.SelfWaybillStatus;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillStatusDto;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import com.xiaopeng.halley.ascm.boot.mp.DefaultQueryCondition;
import com.xiaopeng.halley.ascm.boot.mp.WrapperUtils;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.BzSelfWaybillService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.utils.PageUtils;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Api("自建运单接口")
@RestController
@RequestMapping("/bzSelfWaybill")
public class BzSelfWaybillController {
	@Resource
	private AscmLoginUserHelper ascmLoginUserHelper;
	@Resource
	private BzSelfWaybillService bzSelfWaybillService;

	@PostMapping("/assignDriver")
	@ApiOperation("分配取货任务给司机")
	public Result<String> assignDriver(@RequestBody BzSelfWaybillAssignRequest request) {
		List<Long> ids = request.getIds();
		if (ids.isEmpty()) {
			return ResultUtil.failed("请选择需要分配的运单");
		}
		List<BzSelfWaybill> bzSelfWaybills = bzSelfWaybillService.listByIds(ids);
		bzSelfWaybills.forEach(e -> {
			BeanUtil.copyProperties(request, e);
			e.setAssignTime(new Date());
			e.setAssignUserName(ascmLoginUserHelper.getLoginUser().getName());
		});
		bzSelfWaybillService.updateBatchById(bzSelfWaybills);
		return ResultUtil.success();
	}

	@PostMapping("/listMy")
	@ApiOperation("查询我的运单")
	public Result<List<BzSelfWaybill>> listMy(@RequestBody PageQuery<BzSelfWaybillQuery> request) {
		LambdaQueryWrapper<BzSelfWaybill> wrapper = WrapperUtils.buildLamda(request.getParam());
		wrapper.eq(BzSelfWaybill::getDriverPhone, ascmLoginUserHelper.getLoginUser().getPhone());
		wrapper.in(BzSelfWaybill::getStatus, SelfWaybillStatus.START.getValue(), SelfWaybillStatus.TRANSPORT.getValue());
		return ResultUtil.success(bzSelfWaybillService.list(wrapper));
	}

	@PostMapping("/ship")
	@ApiOperation("司机发运")
	public Result<String> ship(@RequestBody WaybillStatusDto request) {
		AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
		if (!StrUtil.equals(loginUser.getPhone(), request.getDriverPhone())) {
			return ResultUtil.failed("非本人的运单");
		}
		BzSelfWaybill bzSelfWaybill = bzSelfWaybillService.getByWaybillCode(request.getWaybillCode());
		bzSelfWaybill.setActualCarType(request.getCarType());
		bzSelfWaybill.setActualTranTime(new Date());
		bzSelfWaybill.setEstimatedArriveTime(request.getExpiryTime());
		bzSelfWaybill.setStatus(SelfWaybillStatus.TRANSPORT.getValue());
		bzSelfWaybillService.updateById(bzSelfWaybill);
		return ResultUtil.success();
	}

	@PostMapping("/sign")
	@ApiOperation("司机签收")
	public Result<String> sign(@RequestBody SignSelfWaybillRequest request) {
		bzSelfWaybillService.sign(request);
		return ResultUtil.success();
	}

	@PostMapping("/callBackMethod")
	@ApiOperation("快递100回调")
	public SubscribeResp callBackMethod(@RequestParam String param) {
		SubscribeWithMapPushParamResp resp = JSON.parseObject(param, SubscribeWithMapPushParamResp.class);
		return bzSelfWaybillService.callBackMethod(resp);
	}

	@GetMapping("/{id}")
	@ApiOperation("根据ID查询自建运单")
	public Result<BzSelfWaybillDetail> getById(@PathVariable Long id) {
		return ResultUtil.success(bzSelfWaybillService.getSelfWaybillDetail(id));
	}

	@PostMapping
	@ApiOperation("新增自建运单")
	public Result<String> add(@RequestBody BzSelfWaybill bzSelfWaybill) {
		try {
			boolean success = bzSelfWaybillService.save(bzSelfWaybill);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping
	@ApiOperation("删除自建运单")
	public Result<String> delete(@RequestBody List<Long> ids) {
		try {
			boolean success = bzSelfWaybillService.removeByIds(ids);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PutMapping
	@ApiOperation("修改自建运单")
	public Result<String> update(@RequestBody BzSelfWaybill bzSelfWaybill) {
		try {
			boolean success = bzSelfWaybillService.lambdaUpdate().eq(BzSelfWaybill::getId, bzSelfWaybill.getId())
					.set(BzSelfWaybill::getTransportRequire, bzSelfWaybill.getTransportRequire())
					.set(BzSelfWaybill::getTransportType, bzSelfWaybill.getTransportType())
					.update();
			return success ? ResultUtil.success("修改成功") : ResultUtil.failed("修改失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("修改失败：" + e.getMessage());
		}
	}

	@PostMapping("/pageCreated")
	@ApiOperation("分页查询自建运单【创建运单页面】")
	public Result<Page<BzSelfWaybillCreateVO>> pageCreated(@RequestBody PageQuery<BzSelfWaybillQuery> request) {
		BzSelfWaybillQuery param = Assert.notNull(request.getParam(), "参数不能为空");
		LambdaQueryWrapper<BzSelfWaybill> wrapper = WrapperUtils.buildLamda(param, new DefaultQueryCondition("a"));
		wrapper.eq(BzSelfWaybill::getIsDelete, 0);
		wrapper.orderByDesc(BzSelfWaybill::getId);
		return ResultUtil.success(bzSelfWaybillService.getBaseMapper().pageCreated(request.toPage(), wrapper));
	}

	@PostMapping("/exportCreated")
	@ApiOperation("导出自建运单【创建运单页面】")
	@AsyncExportTask(name = "自建运单导出", methodPath = "BzSelfWaybillService.pageCreated")
	public Result<String> exportCreated(@RequestBody PageQuery<BzSelfWaybill> request) {
		return ResultUtil.success();
	}

	@PostMapping("/page")
	@ApiOperation("分页查询自建运单")
	public Result<Page<BzSelfWaybillVO>> page(@RequestBody PageQuery<BzSelfWaybillQuery> request) {
		BzSelfWaybillQuery param = Assert.notNull(request.getParam(), "参数不能为空");
		LambdaQueryWrapper<BzSelfWaybill> wrapper = WrapperUtils.buildLamda(param);
		wrapper.orderByDesc(BzSelfWaybill::getId);
		Page<BzSelfWaybill> page = bzSelfWaybillService.page(request.toPage(), wrapper);
		return ResultUtil.success(PageUtils.copyToPage(page, BzSelfWaybillVO.class));
	}

	@PostMapping("/export")
	@ApiOperation("导出自建运单")
	@AsyncExportTask(name = "自建运单导出", methodPath = "BzSelfWaybillService.page")
	public Result<String> export(@RequestBody PageQuery<BzSelfWaybill> request) {
		return ResultUtil.success();
	}

}