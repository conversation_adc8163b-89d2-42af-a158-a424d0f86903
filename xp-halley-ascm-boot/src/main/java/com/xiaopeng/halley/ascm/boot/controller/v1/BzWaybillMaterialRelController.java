package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillMaterialRel;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillMaterialRelService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api("自建运单物料关联接口")
@RestController
@RequestMapping("/bzWaybillMaterialRel")
public class BzWaybillMaterialRelController {
	@Resource
	private BzWaybillMaterialRelService bzWaybillMaterialRelService;

	@PostMapping
	@ApiOperation("新增自建运单物料关联")
	public Result<String> add(@RequestBody BzWaybillMaterialRel bzWaybillMaterialRel) {
		try {
			boolean success = bzWaybillMaterialRelService.save(bzWaybillMaterialRel);
			return success ? ResultUtil.success("新增成功") : ResultUtil.failed("新增失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("新增失败：" + e.getMessage());
		}
	}

	@DeleteMapping
	@ApiOperation("删除自建运单物料关联")
	public Result<String> delete(@RequestBody List<Long> ids) {
		try {
			boolean success = bzWaybillMaterialRelService.removeByIds(ids);
			return success ? ResultUtil.success("删除成功") : ResultUtil.failed("删除失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("删除失败：" + e.getMessage());
		}
	}

	@PutMapping
	@ApiOperation("修改自建运单物料关联")
	public Result<String> update(@RequestBody BzWaybillMaterialRel bzWaybillMaterialRel) {
		try {
			boolean success = bzWaybillMaterialRelService.updateById(bzWaybillMaterialRel);
			return success ? ResultUtil.success("修改成功") : ResultUtil.failed("修改失败");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ResultUtil.failed("修改失败：" + e.getMessage());
		}
	}

	@GetMapping("/{id}")
	@ApiOperation("根据ID查询自建运单物料关联")
	public Result<BzWaybillMaterialRel> getById(@PathVariable Long id) {
		BzWaybillMaterialRel bzWaybillMaterialRel = bzWaybillMaterialRelService.getById(id);
		return bzWaybillMaterialRel != null ? ResultUtil.success(bzWaybillMaterialRel) : ResultUtil.failed("未找到该自建运单物料关联");
	}

	@PostMapping("/page")
	@ApiOperation("分页查询自建运单物料关联")
	public Result<Page<BzWaybillMaterialRel>> page(@RequestBody PageQuery<BzWaybillMaterialRel> request) {
		LambdaQueryWrapper<BzWaybillMaterialRel> wrapper = new LambdaQueryWrapper<>();
		return ResultUtil.success(bzWaybillMaterialRelService.page(request.toPage(), wrapper));
	}

	@PostMapping("/export")
	@ApiOperation("导出自建运单物料关联")
	@AsyncExportTask(name = "自建运单物料关联导出", methodPath = "BzWaybillMaterialRelService.page")
	public Result<String> export(@RequestBody PageQuery<BzWaybillMaterialRel> request) {
		return ResultUtil.success();
	}

}