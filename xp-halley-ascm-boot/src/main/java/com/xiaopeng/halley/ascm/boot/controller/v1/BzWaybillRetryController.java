package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryVo;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillRetryService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.controller.v1
 * @Date 2024/4/23 17:20
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/bzWaybillRetry")
@Tag(name = "运单重推相关接口")
public class BzWaybillRetryController {

    @Resource
    private BzWaybillRetryService bzWaybillRetryService;

    /**
     * 分页查询
     *
     * @param page 查询参数
     * @return 结果
     */
    @PostMapping("/getPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public Page<BzWaybillRetryVo> getPage(@RequestBody PageQuery<BzWaybillRetryDto> page) {
        log.info("BzWaybillRetryController getPage {}", JSON.toJSONString(page));
        return bzWaybillRetryService.getPage(page);
    }

    /**
     * 获取报文
     *
     * @param id 条目id
     * @return 请求报文
     */
    @GetMapping("/getPayload/{id}")
    @Operation(summary = "查看报文", description = "查看报文")
    @Parameter(name = "条目id", in = ParameterIn.PATH, required = true)
    public Result<String> getPayload(@PathVariable(value = "id") Long id) {
        log.info("BzWaybillRetryController getPayload {}", id);
        return bzWaybillRetryService.getPayload(id);
    }

    /**
     * 重试
     *
     * @param id 条目id
     * @return 重试结果
     */
    @GetMapping("/retry/{id}")
    @Operation(summary = "重发", description = "重发")
    @Parameter(name = "条目id", in = ParameterIn.PATH, required = true)
    public Result<String> retry(@PathVariable(value = "id") Long id) {
        log.info("BzWaybillRetryController retry {}", id);
        return bzWaybillRetryService.retry(id);
    }

}
