package com.xiaopeng.halley.ascm.boot.controller.v1;

import cn.hutool.core.util.ObjectUtil;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillTrackHistoryService;
import com.xiaopeng.halley.ascm.boot.service.ImageService;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayImageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/8/10 9:20
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/bzWaybillTrackHistory")
@Tag(name = "运单轨迹追踪相关接口")
public class BzWaybillTrackHistoryController {

    @Resource
    private BzWaybillTrackHistoryService bzWaybillTrackHistoryService;
    @Resource
    private ImageService imageService;

    /**
     * 运输详情页——获取运输轨迹坐标点
     *
     * @param waybillCode
     * @return
     */
    @GetMapping("/getMap")
    @Operation(summary = "获取运输轨迹坐标点", description = "获取运输轨迹坐标点")
    public List<BzWayBillTrackHistoryVO> getTransportationList(@RequestParam String waybillCode) {
        log.info("BzWaybillTrackHistoryController getTransportationList {}", waybillCode);
        return bzWaybillTrackHistoryService.getTransportationList(waybillCode);
    }

    /**
     * 运输详情页——获取上报的图片
     *
     * @param waybillCode
     * @return
     */
    @GetMapping("/getImages")
    @Operation(summary = "获取上报的图片", description = "获取上报的图片")
    public List<BzWayImageVO> getImageList(@RequestParam String waybillCode) {
        log.info("BzWaybillTrackHistoryController getImageList {}", waybillCode);
        List<BzWayImageVO> imageList = bzWaybillTrackHistoryService.getImageList(waybillCode);
        imageList.forEach(imageItem -> {
            ImageResponseDTO tempURL = imageService.getTempURL(imageItem.getFileId());
            if (ObjectUtil.isNotNull(tempURL) && ObjectUtil.isNotNull(tempURL.getUrl())) {
                imageItem.setFileShowURL(tempURL.getUrl());
            }
        });
        return imageList;
    }
}
