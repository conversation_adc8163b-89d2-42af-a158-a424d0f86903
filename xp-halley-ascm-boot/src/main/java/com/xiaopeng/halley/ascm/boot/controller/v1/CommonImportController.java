package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.excel.AbstractExcelImportHandler;
import com.xiaopeng.halley.ascm.boot.excel.ExcelImportProcessor;
import com.xiaopeng.halley.ascm.boot.excel.ExcelUtils;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import com.xpeng.athena.starter.excel.annotation.ExcelParameter;
import com.xpeng.athena.starter.excel.annotation.ExcelResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Api("通用导入接口")
@RestController
@RequestMapping("/import")
public class CommonImportController {
	@Resource
	private ExcelImportProcessor excelImportProcessor;

	@ApiOperation("获取导入模板")
	@GetMapping("/getExcelTemplate")
	public void getTemplate(@RequestParam String businessKey, HttpServletResponse response) throws Exception {
		AbstractExcelImportHandler<?> handler = excelImportProcessor.getHandler(businessKey);
		Class<?> clazz = excelImportProcessor.resolveExcelClass(handler);
		ExcelUtils.createTemplate(response, clazz, handler.getSheetName() + "导入模版");
	}

	@ApiOperation("导入校验")
	@PostMapping("/importVerify")
	public Result<ImportResponseDto> importVerify(@RequestParam String businessKey, Map<String, Object> params, @RequestParam("file") MultipartFile file) throws Exception {
		AbstractExcelImportHandler<?> handler = excelImportProcessor.getHandler(businessKey);
		return ResultUtil.success(handler.importVerify(file.getInputStream(), excelImportProcessor.resolveExcelClass(handler), params));
	}

	@ApiOperation("下载失败数据")
	@PostMapping("/downloadFailFile")
	@ExcelResponse(filename = "导入失败列表", parameter = @ExcelParameter(writeHandler = LongestMatchColumnWidthStyleStrategy.class))
	public List<?> downloadFailFile(@RequestParam String businessKey, @RequestParam String fileCode) {
		AbstractExcelImportHandler<?> handler = excelImportProcessor.getHandler(businessKey);
		return handler.getFailListOrThrowEx(fileCode);
	}

	@ApiOperation("导入数据")
	@PostMapping("/importData")
	public Result<String> importData(@RequestParam String businessKey, @RequestParam String fileCode) {
		AbstractExcelImportHandler<?> handler = excelImportProcessor.getHandler(businessKey);
		return ResultUtil.success(handler.importData(fileCode));
	}
}
