package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.aliyuncs.exceptions.ClientException;
import com.xiaopeng.halley.ascm.boot.service.ImageService;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: tuyb
 * @Date: 2024-8-5 17:32
 * @Description:
 */
@RestController
@RequestMapping("/ossAuth")
public class StsController {

    @Resource
    private ImageService imageService;

    @GetMapping("getAliTempKey")
    public Result<Map<String, Object>> getAliTempKey() throws ClientException, ResultException {
        return ResultUtil.success(imageService.generateTempCredentials());
    }
}
