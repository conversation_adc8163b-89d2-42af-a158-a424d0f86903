package com.xiaopeng.halley.ascm.boot.controller.v1;

import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * Controller
 *
 * <AUTHOR> 自动生成
 * @date 2022-10-08 06:33:41
 */
@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/pda/user")
@Tag(name = "接口")
public class UserController {

    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    @Operation(summary = "获取登录信息")
    @GetMapping("loginInfo")
    public Object loginInfo() {
        return ascmLoginUserHelper.getLoginUser();
    }

    @Operation(summary = "样例")
    @PostMapping("demopost")
    public String demopost() {
        return "hello world post !!!!";
    }

    @GetMapping("demoget")
    public String demoget() {
        return "hello world get !!!!";
    }

    @GetMapping("getTime")
    public void getTime() {
        log.info("UserController getTime" + new Date());
        log.info("UserController getTime" + (new Date()).getTime());
    }

}
