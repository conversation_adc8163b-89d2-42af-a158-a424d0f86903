package com.xiaopeng.halley.ascm.boot.controller.v1.miniProgram.waybill;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.common.constant.ResponseStatusEnum;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.OrderDeliveryRequestDTO;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.dto.waybill.PDAWaybillListPageRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillStatusDto;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillFileService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillTrackHistoryService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.WayBillShopVO;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@GlobalResponseBody
@RequestMapping("/miniProgram/waybill")
@Tag(name = "小程序")
public class MiniProgramController {
    @Resource
    private BzWaybillService bzWaybillService;
    @Resource
    private BzWaybillTrackHistoryService bzWaybillTrackHistoryService;
    @Resource
    private BzWaybillFileService bzWaybillFileService;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Value("#{'${car-type-list}'.split(',')}")
    private List<String> carTypeList;

    @GetMapping("/getCarTypeList")
    @Operation(summary = "获取车型列表信息")
    public Result<List<String>> getCarTypeList(){
        return ResultUtil.success(carTypeList);
    }

    @GetMapping("/getDriverInfo")
    @Operation(summary = "获取登录用户司机信息")
    public Result<WaybillStatusDto> getDriverInfo() {
        String phone = ascmLoginUserHelper.getLoginUser().getPhone();
        WaybillStatusDto waybillStatusDto = new WaybillStatusDto();
        waybillStatusDto.setDriverPhone(phone);
        BzWaybill waybill = bzWaybillService.lambdaQuery()
                .eq(BzWaybill::getDriverPhone, phone)
                .ne(BzWaybill::getCarPlate, "")
                .ne(BzWaybill::getDriverName, "")
                .orderByDesc(BzWaybill::getCreateTime)
                .last("limit 1").one();
        if (waybill != null) {
            waybillStatusDto.setCarType(waybill.getCarType());
            waybillStatusDto.setCarPlate(waybill.getCarPlate());
            waybillStatusDto.setDriverName(waybill.getDriverName());
        }
        return ResultUtil.success(waybillStatusDto);
    }

    /**
     * 获取首页运单总状态
     * <p>
     * 该函数通过当前登录用户的手机号，调用运单服务获取运单状态的总数，并返回结果。
     *
     * @return Result 包含运单状态总数的结果对象，操作成功时返回成功状态及数据。
     */
    @PostMapping("/statusNum")
    @Operation(summary = "运单状态", description = "运单状态")
    public Result statusNum() {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用运单服务，根据用户手机号获取运单状态总数，并返回成功结果
        return ResultUtil.success(bzWaybillService.statuNum(ascmLoginUser.getPhone()));
    }

    /**
     * 扫描二维码并检查运单信息
     * <p>
     * 该函数通过接收到的运单号，调用服务层的方法来检查运单信息，并返回处理结果。
     *
     * @param waybillCode 运单号，用于标识需要检查的运单
     * @return 返回处理结果，包含运单检查的结果信息
     * @throws ResultException 如果在处理过程中发生错误，抛出ResultException异常
     */
    @PostMapping("/scanWaybill")
    @Operation(summary = "扫描二维码", description = "扫描二维码")
    public Result checkWaybill(@RequestParam("waybillCode") @Parameter(description = "运单号") String waybillCode) throws ResultException {
        // 调用服务层方法检查运单信息，并返回处理结果
        return ResultUtil.success(bzWaybillService.checkWaybill(waybillCode));
    }

    /**
     * 修改运单司机
     * <p>
     * 该函数用于处理修改运单司机的请求。首先会检查用户是否已登录，如果未登录则返回错误信息。
     * 如果用户已登录，则调用服务层方法更新运单信息，并返回操作结果。
     *
     * @param dto 包含运单状态信息的DTO对象，用于传递修改运单司机所需的数据
     * @return 返回操作结果，包含成功或失败的信息
     * @throws ResultException 如果操作过程中发生异常，则抛出ResultException
     */
    @PostMapping("/beDriver")
    @Operation(summary = "修改运单司机", description = "修改运单司机")
    public Result updateDriver(@RequestBody WaybillStatusDto dto) throws ResultException {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 检查用户是否已登录，未登录则返回错误信息
        if (null == ascmLoginUser) {
            return ResultUtil.failed("请登录");
        }

        // 调用服务层方法更新运单信息，并返回操作结果
        return ResultUtil.success(bzWaybillService.updateWaybillInfo(dto, ascmLoginUser.getPhone()));
    }

    /**
     * 获取构建JSON对象
     * <p>
     * 该接口用于根据运单号获取构建JSON对象。通常用于修改运单司机的操作。
     *
     * @param waybill 运单号，用于标识需要获取构建JSON的运单
     * @return JSONObject 返回与运单相关的构建JSON对象
     */
    @PostMapping("/getBuildJson")
    @Operation(summary = "修改运单司机", description = "修改运单司机")
    public JSONObject getBuildJson(@RequestParam("waybill") @Parameter(description = "运单号") String waybill) {
        return bzWaybillService.getBuildJson(waybill);
    }

    /**
     * 渲染地图
     * <p>
     * 该函数用于根据运单号获取地图位置点信息，并返回渲染结果。
     *
     * @param waybillCode 运单号，用于查询对应的地图位置点信息
     * @return 返回包含地图位置点信息的Result对象，表示操作成功
     */
    @PostMapping("/mapAddress")
    @Operation(summary = "地图位置点", description = "地图位置点")
    public Result getMapAddress(@RequestParam("waybillCode") @Parameter(description = "运单号") String waybillCode) {
        // 调用服务层方法获取地图数据，并封装为Result对象返回
        return ResultUtil.success(bzWaybillTrackHistoryService.getMapData(waybillCode));
    }

    /**
     * 获取前端上报地点
     * <p>
     * 该函数用于处理前端上报的地点信息，并将该信息传递给服务层进行处理。
     *
     * @param bzWaybillTrackHistory 前端上报的运单轨迹历史信息，包含地点等详细信息。
     * @return 返回处理结果，通常包含操作成功或失败的状态信息以及相关数据。
     */
    @PostMapping("/locationReporting")
    @Operation(summary = "获取前端上报地点", description = "获取前端上报地点")
    public Result locationReporting(@RequestBody BzWaybillTrackHistory bzWaybillTrackHistory) {
        // 调用服务层方法处理前端上报的地点信息
        return bzWaybillTrackHistoryService.locationReporting(bzWaybillTrackHistory);
    }

    /**
     * 查询运输信息Tab-运输信息
     * <p>
     * 该函数用于根据运单号查询运输信息，并返回查询结果。
     *
     * @param waybillCode 运单号，用于查询对应的运输信息。
     * @return 返回包含运输信息的Result对象，ResultUtil.success用于包装查询结果。
     */
    @PostMapping("/waybillTransportation")
    @Operation(summary = "运输信息", description = "运输信息")
    public Result waybillTransportationDTO(@RequestParam("waybillCode") @Parameter(description = "运单号") String waybillCode) {
        // 调用bzWaybillTrackHistoryService的getTransportationList方法，根据运单号获取运输信息列表，并返回成功结果
        return ResultUtil.success(bzWaybillTrackHistoryService.getTransportationList(waybillCode));
    }

    /**
     * 运单详情
     * <p>
     * 该函数用于根据运单号数组获取运单详情信息。通过调用 `bzWaybillService.getWayBillListByCode` 方法，
     * 获取运单列表，并将结果封装在 `Result` 对象中返回。
     *
     * @param waybillCode 运单号数组，用于查询对应的运单详情。可以传入一个或多个运单号。
     * @return 返回一个 `Result` 对象，其中包含查询到的运单详情列表。如果查询成功，`Result` 对象的状态为成功。
     */
    @PostMapping("/wayBillDetail")
    @Operation(summary = "运单详情", description = "运单详情")
    public Result wayBillDetail(@RequestParam("waybillCode") @Parameter(description = "运单号数组") String... waybillCode) {
        List<WayBillShopVO> wayBillListByCode = bzWaybillService.getWayBillListByCode(waybillCode);
        for (WayBillShopVO wayBillShopVO : wayBillListByCode) {
            wayBillShopVO.setShopContactNum(DesensitizedUtil.mobilePhone(wayBillShopVO.getShopContactNum()));
        }
        return ResultUtil.success(wayBillListByCode);
    }

    /**
     * 文件上传
     * <p>
     * 该函数用于处理文件上传请求，接收一个MultipartFile类型的文件参数，并返回上传文件的唯一标识符。
     *
     * @param file 用户上传的文件，类型为MultipartFile，包含文件的二进制数据及元信息。
     * @return 返回一个Result对象，其中包含上传文件的唯一标识符。Result对象通常用于封装操作结果，包含状态码、消息和数据。
     * @throws IOException 如果文件处理过程中发生I/O错误，则抛出此异常。
     */
    @PostMapping("/fileUpload")
    @Operation(summary = "文件上传", description = "文件上传")
    public Result fileUpload(@RequestParam("file") MultipartFile file) throws IOException {
        // 调用bzWaybillFileService的getPictureFileId方法处理文件上传，并返回文件的唯一标识符
        return ResultUtil.success(bzWaybillFileService.getPictureFileId(file));
    }

    /**
     * 文件上传固定下载名称
     * <p>
     * 该函数用于处理文件上传请求，并返回文件ID。上传的文件和指定的文件名将作为参数传递给服务层进行处理。
     *
     * @param file     上传的文件，类型为MultipartFile，表示用户上传的文件内容。
     * @param fileName 用户指定的文件名，类型为String，表示上传文件后保存的文件名。
     * @return 返回一个Result对象，包含文件上传操作的结果。如果操作成功，Result对象中将包含文件ID。
     * @throws IOException 如果文件上传过程中发生IO异常，将抛出该异常。
     */
    @PostMapping("/fileUploadByOrgName")
    @Operation(summary = "文件上传", description = "文件上传")
    public Result putFileGetFileId(@RequestParam("file") MultipartFile file, @RequestParam("fileName") String fileName) throws IOException {
        // 调用服务层方法处理文件上传，并返回文件ID
        return ResultUtil.success(bzWaybillFileService.putFileGetFileId(file, fileName));
    }

    /**
     * 文件上传并加入水印的接口
     * <p>
     * 该接口用于接收用户上传的文件，并在文件上添加指定的水印文字。
     *
     * @param file      用户上传的文件，类型为MultipartFile，包含文件的二进制数据。
     * @param waterText 需要添加到文件上的水印文字，类型为String。
     * @return 返回一个Result对象，包含文件上传并添加水印后的处理结果。ResultUtil.success()用于包装成功的结果。
     */
    @PostMapping("/fileUploadWater")
    @Operation(summary = "文件上传(加入水印)", description = "文件上传")
    public Result fileUploadWater(@RequestParam("file") MultipartFile file, @RequestParam("waterText") String waterText) {
        // 调用服务层方法处理文件上传并添加水印，返回处理结果
        return ResultUtil.success(bzWaybillFileService.fileUploadWater(file, waterText));
    }

    /**
     * 处理订单交付确认请求。
     * <p>
     * 该函数接收一个包含交付信息的请求体，提取其中的文件ID列表和运单号列表，
     * 并将运单号列表转换为数组格式。然后获取当前登录用户信息，并调用服务层
     * 方法进行交付确认操作。
     *
     * @param orderDeliveryRequestDTO 包含交付信息的请求体，包括文件ID列表和运单号列表。
     * @return 返回交付确认操作的结果，通常包含操作成功或失败的信息。
     * @throws ResultException 如果交付确认过程中发生错误，抛出此异常。
     */
    @PostMapping("/deliveryConfirm")
    @Operation(summary = "我要交付", description = "我要交付")
    public Result deliveryConfirm(@RequestBody OrderDeliveryRequestDTO orderDeliveryRequestDTO) throws ResultException {

        // 从请求体中提取文件ID列表和运单号列表
        List fileIdList = orderDeliveryRequestDTO.getFileIdList();
        List waybillCodeList = orderDeliveryRequestDTO.getWaybillCodes();

        // 将运单号列表转换为数组格式
        String[] waybillCodes = new String[waybillCodeList.size()];
        waybillCodeList.toArray(waybillCodes);

        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用服务层方法进行交付确认操作
        return bzWaybillFileService.deliveryConfirm(ascmLoginUser, fileIdList, waybillCodes);
    }

    /**
     * 获取司机的历史运输记录
     * <p>
     * 该接口用于查询指定司机的历史运输记录，支持分页查询。
     *
     * @param drivePhone 司机的手机号，用于标识司机身份
     * @param page       当前页码，用于分页查询
     * @param pageSize   每页显示的记录数，用于分页查询
     * @return 返回查询结果，包含司机的历史运输记录信息
     */
    @PostMapping("/driverDeliveryHistory")
    @Operation(summary = "历史运输记录", description = "历史运输记录")
    public Result driverDeliveryHistory(@RequestParam("driverPhone") @Parameter(description = "司机手机号") String drivePhone, @RequestParam("page") Integer page, @RequestParam("pageSize") Integer pageSize) {
        // 调用服务层方法，获取司机的历史运输记录
        return bzWaybillService.driverDeliveryHistory(drivePhone, page, pageSize);
    }

    /**
     * 获取运单列表
     * <p>
     * 该接口用于分页查询运单列表，适用于小程序端。通过传入的分页查询参数和当前登录用户信息，返回对应的运单列表。
     *
     * @param page 分页查询参数，包含查询条件和分页信息，类型为PageQuery<PDAWaybillListPageRequestDto>
     * @return 返回运单列表查询结果，封装在Result对象中。如果获取手机号失败，则返回错误信息。
     */
    @PostMapping(value = "/getWaybillList")
    @Operation(summary = "运单列表", description = "运单列表")
    public Result waybillList(@RequestBody PageQuery<PDAWaybillListPageRequestDto> page) {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 检查用户手机号是否为空，若为空则返回错误信息
        if (StrUtil.isBlank(ascmLoginUser.getPhone())) {
            return ResultUtil.failed(ResponseStatusEnum.RESPONSE_STATUS_ERROR.getCode(), "获取手机号失败");
        }

        // 调用服务层方法，获取运单列表并返回成功结果
        return ResultUtil.success(bzWaybillService.waybillMiniProgramList(page, ascmLoginUser));
    }

    /**
     * 处理重试请求的接口。
     * 该接口通过HTTP GET请求接收运单号，并调用服务层进行重试操作。
     *
     * @param waybillCode 运单号，作为请求参数传入，用于标识需要重试的运单。
     * @return 无返回值，该接口仅执行重试操作，不返回任何数据。
     */
    @GetMapping("/retry")
    public void retry(@RequestParam String waybillCode) {
        // 调用服务层方法，根据运单号执行重试操作
        bzWaybillService.retry(waybillCode);
    }
}
