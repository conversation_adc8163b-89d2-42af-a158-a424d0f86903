package com.xiaopeng.halley.ascm.boot.controller.v1.miniProgram.wechat;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.miniProgram.WeChatGetPhoneNumDto;
import com.xiaopeng.halley.ascm.boot.dto.miniProgram.WeChatSetCacheDto;
import com.xiaopeng.halley.ascm.boot.service.AscmRedisHelper;
import com.xiaopeng.halley.ascm.boot.service.weChat.WeChatService;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 微信相关接口
 *
 * <AUTHOR>
 * @Date 2022/10/8 2:07 PM
 */
@SuppressWarnings("rawtypes")
@RestController
@GlobalResponseBody
@RequestMapping("/miniProgram/wechat")
@Tag(name = "微信相关接口")
public class WeChatController {

    @Resource
    private WeChatService weChatService;

    @Resource
    private AscmRedisHelper ascmRedisHelper;

    /**
     * 通过微信小程序提供的Code获取用户手机号
     *
     * @param weChatGetPhoneNumDto 包含微信小程序提供的Code的DTO对象
     * @return 返回包含手机号信息的Result对象。如果获取微信Token失败或解析返回结果时发生错误，则返回失败的Result对象。
     */
    @PostMapping("/getPhoneNumber")
    @Operation(summary = "通过Code获取手机号", description = "通过Code获取手机号")
    public Result getPhoneNumber(@RequestBody WeChatGetPhoneNumDto weChatGetPhoneNumDto) {
        // 获取微信的Access Token
        String token = weChatService.getAccessToken();
        if (StrUtil.isEmpty(token)) {
            // 如果获取Token失败，返回错误信息
            return ResultUtil.failed("获取微信Token失败");
        }

        try {
            // 使用Code和Token获取用户手机号
            Map result = weChatService.getPhoneNumber(weChatGetPhoneNumDto.getCode(), token);
            // 构建成功的返回结果，并将手机号信息设置到返回结果中
            Result resp = ResultUtil.success();
            resp.setData(result);
            return resp;
        } catch (Throwable t) {
            // 如果解析返回结果时发生错误，返回错误信息
            return ResultUtil.failed("返回解析错误");
        }
    }

    /**
     * 设置缓存
     * <p>
     * 该函数用于将指定的键值对存储到Redis缓存中。键的前缀为"WeChat-"，值由传入的DTO对象提供。
     *
     * @param weChatSetCacheDto 包含缓存键值对的DTO对象，其中key为缓存键，value为缓存值
     * @return 返回操作结果，包含成功信息
     * @throws ResultException 如果操作过程中发生异常，则抛出ResultException
     */
    @PostMapping("/setCache")
    @Operation(summary = "设置缓存", description = "设置缓存")
    public Result setCache(@RequestBody WeChatSetCacheDto weChatSetCacheDto) throws ResultException {
        // 将键值对存储到Redis缓存中，键的前缀为"WeChat-"
        ascmRedisHelper.set("WeChat-" + weChatSetCacheDto.getKey(), weChatSetCacheDto.getValue(), 2, TimeUnit.HOURS);

        // 返回操作成功的结果
        return ResultUtil.success("保存成功!");
    }

    /**
     * 获取缓存
     * <p>
     * 该函数通过传入的WeChatSetCacheDto对象中的key值，从Redis缓存中获取对应的缓存数据。
     * 如果缓存中存在该key对应的数据，则返回成功结果；否则返回失败结果。
     *
     * @param weChatSetCacheDto 包含缓存key的DTO对象，用于指定要获取的缓存数据
     * @return Result 包含获取缓存结果的对象，成功时返回缓存数据，失败时返回失败信息
     * @throws ResultException 如果获取缓存过程中发生异常，则抛出ResultException
     */
    @PostMapping("/getCache")
    @Operation(summary = "获取缓存", description = "获取缓存")
    public Result getCache(@RequestBody WeChatSetCacheDto weChatSetCacheDto) throws ResultException {
        // 检查Redis中是否存在指定key的缓存数据
        if (ObjectUtil.isNotNull(ascmRedisHelper.get("WeChat-" + weChatSetCacheDto.getKey()))) {
            // 如果存在，返回成功结果并包含缓存数据
            return ResultUtil.success(ascmRedisHelper.get("WeChat-" + weChatSetCacheDto.getKey()));
        }
        // 如果不存在，返回失败结果
        return ResultUtil.failed();
    }

}
