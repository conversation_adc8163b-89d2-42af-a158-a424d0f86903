package com.xiaopeng.halley.ascm.boot.controller.v1.pda.waybill;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.DemolitionVO;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.dto.waybill.PDAWaybillListPageRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillStatusDto;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.BusinessException;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * pad端正向运单相关接口
 *
 * <AUTHOR>
 * @Date 2022/7/15 5:18 PM
 */
@Slf4j
@SuppressWarnings({"rawtypes", "unchecked"})
@RestController
@GlobalResponseBody
@RequestMapping("/pda/waybill")
@Tag(name = "正向运单相关接口")
public class PDABzWaybillController {

    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    @Resource
    private BzWaybillService bzWaybillService;

    @GetMapping("/verifyRoute")
    @Operation(summary = "校验路线", description = "PDA扫箱跳转详情前的校验")
    public Result verifyRoute(@RequestParam String boxCode) {
        Map map = bzWaybillService.getBaseMapper().verifyStatus(null, boxCode, null, null);
        if (ObjectUtil.isEmpty(map)) {
            return ResultUtil.success("箱号不存在");
        }
        if ("1".equals(MapUtil.getStr(map, "status"))) {
            return ResultUtil.success("已扫描");
        }
        // pda扫描路线检查
        String route = bzWaybillService.getRoute();
        if (StrUtil.isNotBlank(route) && !route.equals(MapUtil.getStr(map, "path"))) {
            throw new BusinessException("扫描的线路不一致！");
        }
        return ResultUtil.success();
    }

    @PostMapping("/setRoute")
    @Operation(summary = "设置PDA线路")
    public Result setRoute(String route) {
        bzWaybillService.setRoute(route);
        return ResultUtil.success();
    }

    @GetMapping("/getRoute")
    @Operation(summary = "获取PDA线路")
    public Result<String> getRoute() {
        return ResultUtil.success(bzWaybillService.getRoute());
    }

    /**
     * 获取运单详情信息
     * <p>
     * 该接口用于根据运单号、类型、页码和每页大小获取运单的详细信息。如果运单号以"JD"开头，则返回无需扫箱的提示。
     *
     * @param waybillCode 运单号，用于查询对应的运单详情
     * @param type        查询类型，1表示PDA查询，2表示PC查询
     * @param page        当前页码，用于分页查询
     * @param pageSize    每页大小，用于分页查询
     * @return 返回包含运单详情的Result对象，如果运单号以"JD"开头，则返回错误提示
     * @throws ResultException 如果查询过程中发生异常，则抛出ResultException
     */
    @GetMapping("/detail")
    @Operation(summary = "运单详情", description = "运单详情: type:1 pda 2pc")
    public Result detail(@RequestParam String waybillCode, @RequestParam int type, @RequestParam int page, @RequestParam int pageSize) throws ResultException {
        Result response = ResultUtil.success();

        // 检查运单号是否以"JD"开头，如果是则返回无需扫箱的提示
        String substring = waybillCode.substring(0, 2);
        if ("JD".equals(substring)) {
            return ResultUtil.failed("您无需扫箱！");
        }

        // 根据运单号、类型、页码和每页大小查询运单详情，并将结果设置到response中
        response.setData(bzWaybillService.waybillDetailByPDA(waybillCode, type, page, pageSize));
        return response;
    }

    /**
     * 核箱盲扫接口
     * 该接口用于根据箱号获取与该箱相关的所有运单信息。
     *
     * @param boxCode 箱号，用于查询与该箱相关的运单信息
     * @return 返回一个包含所有相关运单信息的Result对象
     * @throws ResultException 如果查询过程中发生错误，抛出ResultException异常
     */
    @GetMapping("/detailBox")
    @Operation(summary = "核箱盲扫", description = "核箱盲扫")
    public Result detailBox(@RequestParam String boxCode) throws ResultException {
        // 根据箱号查询所有相关的运单信息
        List<BzErpReqBoxRelDto> boxRelDtos = bzWaybillService.getBoxAllByCode(boxCode);

        // 将查询结果封装为Result对象并返回
        return ResultUtil.success(boxRelDtos);
    }

    /**
     * 获取运单状态
     * <p>
     * 该接口用于根据箱号查询运单的当前状态。如果运单不存在，则抛出异常。
     *
     * @param boxCode 箱号，用于查询运单状态的唯一标识
     * @return Result 包含运单状态信息的响应对象，若运单不存在则返回错误信息
     * @throws ResultException 当运单号未找到时抛出异常，状态码为500，错误信息为"运单号未找到！"
     */
    @GetMapping("/getWaybillStatus")
    @Operation(summary = "获取运单状态", description = "获取运单状态")
    public Result getWaybillStatus(@RequestParam String boxCode) throws ResultException {
        Result response = ResultUtil.success();

        // 根据箱号查询运单状态
        WaybillStatusDto waybillStatusDto = bzWaybillService.getWaybillStatus(boxCode);

        // 如果运单状态为空，抛出异常
        if (ObjectUtil.isNull(waybillStatusDto)) {
            throw new ResultException(500, "运单号未找到！");
        }

        // 将运单状态信息设置到响应对象中
        response.setData(waybillStatusDto);
        return response;
    }

    /**
     * 根据运单号获取运单状态信息
     *
     * @param waybillCode 运单号，用于查询运单状态
     * @return Result 包含运单状态信息的响应结果
     * @throws ResultException 如果运单号未找到，抛出500异常
     */
    @GetMapping("/getWaybillStatusByNO")
    @Operation(summary = "获取运单状态", description = "获取运单状态")
    public Result getWaybillStatusByNO(@RequestParam String waybillCode) throws ResultException {
        Result response = ResultUtil.success();

        // 根据运单号查询运单状态信息
        WaybillStatusDto waybillStatusDto = bzWaybillService.getWaybillStatusByNO(waybillCode);

        // 如果运单状态信息为空，抛出异常
        if (ObjectUtil.isNull(waybillStatusDto)) {
            throw new ResultException(500, "运单号未找到！");
        }

        waybillStatusDto.setDriverPhone(DesensitizedUtil.mobilePhone(waybillStatusDto.getDriverPhone()));
        // 如果运单状态中包含路径过期时间，计算并设置过期时间
        if (ObjectUtils.isNotEmpty(waybillStatusDto.getPathExpiry())) {
            DateTime expiryTime = DateUtil.offsetHour(new Date(), waybillStatusDto.getPathExpiry());
            waybillStatusDto.setExpiryTime(expiryTime);
        }

        // 将运单状态信息设置到响应结果中
        response.setData(waybillStatusDto);
        return response;
    }

    /**
     * 核箱提货接口
     * <p>
     * 该接口用于根据运单号和箱号进行核箱提货操作。通过调用业务服务层的方法，验证运单和箱子的状态，
     * 并返回操作结果。
     *
     * @param waybillCode 运单号，用于标识具体的运单
     * @param boxCode     箱号，用于标识具体的箱子
     * @return 返回操作结果，包含核箱提货的状态信息
     * @throws ResultException 如果核箱提货过程中发生错误，抛出此异常
     */
    @GetMapping("/verifyStatus")
    @Operation(summary = "核箱提货", description = "核箱提货")
    public Result verifyStatus(@RequestParam String waybillCode, @RequestParam String boxCode) throws ResultException {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用业务服务层方法进行核箱提货操作，并返回结果
        return bzWaybillService.verifyStatus(waybillCode, boxCode, ascmLoginUser);
    }

    /**
     * 处理扫描退回请求。
     * 该函数接收运单号和箱号作为参数，调用服务层方法执行退回操作，并返回操作结果。
     *
     * @param waybillCode 运单号，标识需要退回的运单。
     * @param boxCode     箱号，标识需要退回的箱子。
     * @return 返回操作结果，包含退回操作的成功或失败信息。
     */
    @GetMapping("/sendBack")
    @Operation(summary = "扫描退回", description = "扫描退回")
    public Result sendBack(@RequestParam String waybillCode, @RequestParam String boxCode) {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用服务层方法执行退回操作，并返回结果
        return bzWaybillService.sendBack(waybillCode, boxCode, ascmLoginUser);
    }

    /**
     * 拆箱操作
     * <p>
     * 该函数用于处理拆箱请求，根据传入的拆箱信息执行拆箱操作，并返回操作结果。
     *
     * @param demolitionVO 拆箱操作的相关信息，包含拆箱的箱子列表、是否打印、物料信息、运输类型等。
     * @return 返回拆箱操作的结果，如果拆箱成功返回true，否则返回false并附带错误信息。
     */
    @PostMapping("/demolitionBox")
    @Operation(summary = "拆箱操作", description = "拆箱操作")
    public Result demolitionBox(@RequestBody DemolitionVO demolitionVO) {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 从请求体中获取拆箱所需的参数
        List<String> boxList = demolitionVO.getBoxList();
        Boolean isPrint = demolitionVO.getIsPrint();
        String item = demolitionVO.getItem();
        String transportType = demolitionVO.getTransportType();

        // 调用拆箱服务执行拆箱操作，并根据操作结果返回相应的响应
        if (bzWaybillService.demolitionBox(boxList, isPrint, item, ascmLoginUser, transportType)) {
            return ResultUtil.success(true);
        } else {
            return ResultUtil.failed("拆箱失败！").setData(false);
        }
    }

    /**
     * 确认快递单号真实
     * <p>
     * 该接口用于验证指定快递公司的快递单号是否真实有效。
     *
     * @param company 快递公司名称，用于指定需要验证的快递公司。
     * @param number  快递单号，需要验证的快递单号。
     * @return 返回验证结果，通常包含验证是否成功的状态信息。
     */
    @GetMapping("/confirmDeliveryCode")
    @Operation(summary = "确认快递单号真实", description = "确认快递单号真实")
    public Result confirmDeliveryCode(@RequestParam String company, @RequestParam String number) {
        return bzWaybillService.confirmDeliveryCode(company, number);
    }

    /**
     * 回填转运信息
     * <p>
     * 该接口用于回填指定运单的转运信息，包括转运公司和转运单号。
     *
     * @param waybillCode 运单号，用于标识需要回填转运信息的运单
     * @param company     转运公司名称，表示负责转运的公司
     * @param number      转运单号，表示转运公司提供的唯一标识符
     * @return 返回操作结果，包含回填转运信息的成功或失败状态
     */
    @GetMapping("/backFillExpress")
    @Operation(summary = "回填转运信息", description = "回填转运信息")
    public Result backFillExpress(@RequestParam String waybillCode, @RequestParam String company, @RequestParam String number) {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用服务层方法，回填转运信息并返回操作结果
        return bzWaybillService.backFillExpress(waybillCode, company, number, ascmLoginUser);
    }

    /**
     * 完成运单
     * <p>
     * 该函数用于处理运单的完成操作。通过传入的运单编码和运输类型，调用服务层完成运单的处理。
     *
     * @param waybillCode   运单编码，用于唯一标识一个运单
     * @param transportType 运输类型，用于指定运单的运输方式
     * @return 返回操作结果，包含成功或失败的信息
     * @throws ResultException 如果操作过程中发生错误，抛出ResultException异常
     */
    @GetMapping("/completeWaybill")
    @Operation(summary = "完成运单", description = "完成运单")
    public Result completeWaybill(@RequestParam String waybillCode, @RequestParam String transportType) throws ResultException {
        // 获取当前登录用户信息
        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        // 调用服务层完成运单操作
        return bzWaybillService.completeWaybill(waybillCode, ascmLoginUser, transportType);
    }

    /**
     * 根据分页参数和关键字搜索转运快递公司列表。
     * <p>
     * 该函数处理GET请求，路径为"/searchDeliveryCompany"，用于查询转运快递公司的列表。
     * 支持分页查询，并且可以根据关键字进行模糊搜索。
     *
     * @param page     当前页码，表示请求的页数，从1开始计数。
     * @param pageSize 每页显示的记录数，表示每页返回的快递公司数量。
     * @param keyWord  搜索关键字，可选参数，用于模糊匹配快递公司名称或其他相关信息。
     * @return 返回一个包含查询结果的Result对象，通常包括快递公司列表、分页信息等。
     */
    @GetMapping("/searchDeliveryCompany")
    @Operation(summary = "转运快递列表", description = "转运快递列表")
    public Result searchDeliveryCompany(@RequestParam Integer page, @RequestParam Integer pageSize, @RequestParam(required = false) String keyWord) {
        return bzWaybillService.searchDeliveryCompany(page, pageSize, keyWord);
    }

    /**
     * 获取状态数量
     * <p>
     * 该函数用于获取当前系统中不同状态的数量，并将其封装在Result对象中返回。
     *
     * @return Result 包含状态数量的响应对象，其中data字段为状态数量的具体数据。
     */
    @GetMapping("/statusNum")
    @Operation(summary = "获取状态数量", description = "获取状态数量")
    public Result statusNum() {
        // 创建一个成功的Result对象
        Result response = ResultUtil.success();

        // 调用bzWaybillService的statusNum方法获取状态数量，并设置到Result对象的data字段中
        response.setData(bzWaybillService.statusNum());

        // 返回包含状态数量的Result对象
        return response;
    }

    /**
     * 获取运单列表
     * <p>
     * 该接口用于分页查询运单列表信息。通过传入的分页查询参数，调用运单服务获取相应的运单数据，并返回给前端。
     *
     * @param page 分页查询参数，包含分页信息和运单列表查询条件
     * @return 返回包含运单列表数据的响应结果，其中数据部分为分页查询后的运单列表
     */
    @PostMapping(value = "/getWaybillList")
    @Operation(summary = "运单列表", description = "运单列表")
    public Result waybillList(@RequestBody PageQuery<PDAWaybillListPageRequestDto> page) {
        // 初始化响应结果对象
        Result response = ResultUtil.success();

        // 调用运单服务获取运单列表数据，并设置到响应结果中
        response.setData(bzWaybillService.waybillList(page));

        // 返回包含运单列表数据的响应结果
        return response;
    }

    /**
     * 获取运单号下所有已扫描的箱号
     * <p>
     * 该函数通过传入的运单号，调用业务服务层的方法，获取该运单号下所有已扫描的箱号信息。
     *
     * @param waybillCode 运单号，用于查询已扫描的箱号
     * @return 返回包含查询结果的Result对象，其中包含运单号下所有已扫描的箱号信息
     */
    @GetMapping("/getDemolitionBoxList")
    @Operation(summary = "获取运单号下所有已扫描的箱号", description = "获取运单号下所有已扫描的箱号")
    public Result getDemolitionBoxList(@RequestParam String waybillCode) {
        return bzWaybillService.getDemolitionBoxList(waybillCode);
    }

    /**
     * 快递100订阅接口
     * <p>
     * 该接口用于订阅快递100的物流信息。通过传入快递单号、快递公司编码以及收件人手机号（可选），
     * 调用服务层方法进行订阅操作，并根据返回结果判断订阅是否成功。
     *
     * @param deliveryCode  快递单号，用于标识具体的物流信息
     * @param companyCode   快递公司编码，用于标识快递公司
     * @param deliveryPhone 收件人手机号（可选），用于接收物流信息通知
     * @return 返回订阅结果，成功返回true，失败返回错误信息
     * @throws Exception 如果订阅过程中发生异常，则抛出异常
     */
    @GetMapping(value = "/subscribeDelivery")
    @Operation(summary = "快递100订阅接口", description = "快递100订阅接口")
    public Result subscribeDelivery(@RequestParam("deliveryCode") String deliveryCode, @RequestParam("companyCode") String companyCode, @RequestParam(value = "deliveryPhone", required = false) String deliveryPhone) throws Exception {
        // 调用服务层方法进行快递订阅
        Map map = bzWaybillService.subscribeDelivery(deliveryCode, companyCode, deliveryPhone, null);

        // 根据返回的订阅结果进行处理
        if ("200".equals(map.get("returnCode"))) {
            // 订阅成功，返回成功结果
            return ResultUtil.success(true);
        } else {
            // 订阅失败，返回错误信息
            return ResultUtil.failed(map.get("message").toString());
        }
    }
}
