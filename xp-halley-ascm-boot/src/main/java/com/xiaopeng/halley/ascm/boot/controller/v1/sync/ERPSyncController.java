package com.xiaopeng.halley.ascm.boot.controller.v1.sync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApi;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApiEnum;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.ErpSyncShopDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.*;
import com.xiaopeng.halley.ascm.boot.dto.syn.SynReturnDTo;
import com.xiaopeng.halley.ascm.boot.dto.wms.ResultWMS;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant.WarehouseTypeEnum;
import com.xiaopeng.halley.ascm.boot.service.BaseWarehouseService;
import com.xiaopeng.halley.ascm.boot.service.sync.AscmErpWmsSyncNewService;
import com.xiaopeng.halley.ascm.boot.service.sync.ThirdPartySyncWaybillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * com.xiaopeng.ascm.boot.controller.v1.sync
 *
 * <AUTHOR>
 * @Date 2022/7/8 11:21 AM
 */
@Slf4j
@GlobalResponseBody
@RestController
@RequestMapping(path = "/erp_sync")
@Tag(name = "ERP同步接口")
public class ERPSyncController {
    @Resource
    private BaseWarehouseService baseWarehouseService;
    @Resource
    private AscmErpWmsSyncNewService erpWmsSyncNewService;
    @Resource
    private ThirdPartySyncWaybillService thirdPartySyncWaybillService;

    @RemoteApi(RemoteApiEnum.ASCM0089)
    @PostMapping("/receiveBoxMaterial")
    @Operation(summary = "WMS箱子物料信息传ASCM（0089接口）")
    public SynReturnDTo receiveBoxMaterial(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController receiveBoxMaterial 开始执行 {}", JSONObject.toJSONString(resultWMS));
        List<BoxMaterialRequestDto> materialRequestDtos = JSON.parseArray(JSON.toJSONString(resultWMS.getDATA()), BoxMaterialRequestDto.class);
	    try {
		    erpWmsSyncNewService.receiveBoxMaterial(materialRequestDtos);
            return SynReturnDTo.getSuccess("success");
	    } catch (Exception e) {
            log.error(e.getMessage(), e);
            return SynReturnDTo.getError("error", e.getMessage());
	    }
    }

    @RemoteApi(RemoteApiEnum.ASCM0030)
    @PostMapping("/wmsWaybill")
    @Operation(summary = "WMS运单信息传ASCM（0030接口）", description = "WMS运单信息传ASCM")
    public SynReturnDTo wmsWaybill(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController orderStatus 开始执行 erpOrderStatusDto [{}]", JSONObject.toJSONString(resultWMS));

        String dataString = JSON.toJSONString(resultWMS.getDATA());
        JSONArray returnObject = JSONObject.parseArray(dataString);
        List<WmsWaybillDto> wmsWaybillDtoList = new ArrayList<>();

        try {
            wmsWaybillDtoList = JSONObject.parseArray(returnObject.toJSONString(), WmsWaybillDto.class);
        } catch (Exception e) {
            WmsWaybillDto wmsWaybillDto = JSONObject.parseObject(returnObject.toJSONString(), WmsWaybillDto.class);
            wmsWaybillDtoList.add(wmsWaybillDto);
        }
        if (wmsWaybillDtoList.isEmpty()) {
            new SynReturnDTo();
            return SynReturnDTo.getError("error", "error");
        }

        JSONObject params = new JSONObject();
        params.put("DATA", wmsWaybillDtoList);
        params.put("HEADER", JSONObject.parseObject(JSONUtil.toJsonStr(resultWMS.getHEADER())));

        return erpWmsSyncNewService.wmsWaybillSyn(params);
    }

    @RemoteApi(RemoteApiEnum.ASCM0050)
    @PostMapping("/packCompleted")
    @Operation(summary = "WMS包装完成传ASCM（0050接口）", description = "WMS包装完成传ASCM")
    public SynReturnDTo packCompleted(@RequestBody ResultWMS resultWMS) {

        log.info("BzErpReqController packCompleted 开始执行 传入数据 [{}]", JSONObject.toJSONString(resultWMS));

        String dataString = JSON.toJSONString(resultWMS.getDATA());
        List list = JSON.parseObject(dataString, List.class);

        if (ObjectUtil.isEmpty(list)) {
            new SynReturnDTo();
            return SynReturnDTo.getError("error", "error");
        }

        for (Object item : list) {
            Map map = (Map) item;
            if (StrUtil.isBlank(map.get("ZYDBH") == null ? "" : map.get("ZYDBH").toString())) {
                new SynReturnDTo();
                return SynReturnDTo.getError("error", "运单号为空");
            }

            if (StrUtil.isBlank(map.get("ZPACSTATUS") == null ? "" : map.get("ZPACSTATUS").toString())) {
                new SynReturnDTo();
                return SynReturnDTo.getError("error", "取消状态为空");
            }
            SynReturnDTo synReturnDTo = erpWmsSyncNewService.packCompleted(map);
            if ("S".equals(synReturnDTo.getZSTATUS())) {
                continue;
            } else {
                log.info("packCompleted 返回值->{}", synReturnDTo);
                return synReturnDTo;
            }
        }
        return SynReturnDTo.getSuccess("success");
    }

    @PostMapping("/shopDataSync")
    @Operation(summary = "ERP同步门店数据", description = "ERP同步门店数据")
    public SynReturnDTo shopDataSync(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController shopDataSync 开始执行 [{}]", JSONObject.toJSONString(resultWMS));
        String dataString = JSON.toJSONString(resultWMS.getDATA());
        JSONArray returnObject = JSONObject.parseArray(dataString);
        List<ErpSyncShopDto> erpSyncShopDto = new ArrayList<>();

        try {
            erpSyncShopDto = JSONObject.parseArray(returnObject.toJSONString(), ErpSyncShopDto.class);
        } catch (Exception e) {
            log.error("BzErpReqController shopDataSync 转换时发生异常:{}", e.getMessage());
            ErpSyncShopDto orderStatusDto = JSONObject.parseObject(returnObject.toJSONString(), ErpSyncShopDto.class);
            erpSyncShopDto.add(orderStatusDto);
        }
        if (CollUtil.isEmpty(erpSyncShopDto)) {
            return SynReturnDTo.getError("error", "error");
        }
        JSONObject params = new JSONObject();
        params.put("DATA", erpSyncShopDto);
        params.put("HEADER", JSONObject.parseObject(JSONUtil.toJsonStr(resultWMS.getHEADER())));

        erpWmsSyncNewService.shopDataSync(params);

        return SynReturnDTo.getSuccess("success");
    }

    @PostMapping("/purchaseTimeAndDeliveryTimeSync")
    @Operation(summary = "ERP同步交货单下单及配货时间数据", description = "ERP同步交货单下单及配货时间数据")
    public SynReturnDTo pTAndDTSync(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController pTAndDTSync 开始执行 [{}]", JSONObject.toJSONString(resultWMS));
        String dataString = JSON.toJSONString(resultWMS.getDATA());
        JSONArray returnObject = JSONObject.parseArray(dataString);
        List<ErpSyncPTAndDTDto> erpSyncShopDto = new ArrayList<>();

        try {
            erpSyncShopDto = JSONObject.parseArray(returnObject.toJSONString(), ErpSyncPTAndDTDto.class);
        } catch (Exception e) {
            log.error("BzErpReqController pTAndDTSync 转换时发生异常:{}", e.getMessage());
            ErpSyncPTAndDTDto erpSyncPTAndDTDto = JSONObject.parseObject(returnObject.toJSONString(), ErpSyncPTAndDTDto.class);
            erpSyncShopDto.add(erpSyncPTAndDTDto);
        }
        if (CollectionUtil.isEmpty(erpSyncShopDto)) {
            return SynReturnDTo.getError("error", "error");
        }
        JSONObject params = new JSONObject();
        params.put("DATA", erpSyncShopDto);
        params.put("HEADER", JSONObject.parseObject(JSONUtil.toJsonStr(resultWMS.getHEADER())));

        return erpWmsSyncNewService.pTAndDTSync(params);
    }

    @RemoteApi(RemoteApiEnum.ASCM0085)
    @PostMapping("/syncWaybillforJD")
    @Operation(summary = "ASCM0085（在途跟踪）", description = "ASCM0085（在途跟踪）")
    public SynReturnDTo syncWaybillforJD(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController syncWaybillforJD 开始执行 [{}]", JSONObject.toJSONString(resultWMS));

        String jsonString = JSON.toJSONString(resultWMS.getDATA());
        SyncWaybillForJDDto syncWaybillForJDDto = JSON.parseObject(jsonString, SyncWaybillForJDDto.class);

        log.info("运单信息：[{}]", JSON.toJSONString(syncWaybillForJDDto));
        List<BaseWarehouse> baseWarehouses = baseWarehouseService.lambdaQuery()
                .eq(BaseWarehouse::getLgort, syncWaybillForJDDto.getLgort()).list();
        if (baseWarehouses.size() == 1) {
            BaseWarehouse baseWarehouse = baseWarehouses.get(0);
            if (WarehouseTypeEnum.JD.getValue().equals(baseWarehouse.getWarehouseType())) {
                return erpWmsSyncNewService.syncWaybillforJD(syncWaybillForJDDto);
            }
        }
        return SynReturnDTo.getError("500", "同步数据有误！");
    }

    @RemoteApi(RemoteApiEnum.ASCM0085_1)
    @PostMapping("/syncWaybillDetailforJD")
    @Operation(summary = "ASCM0085-1（出库结果）", description = "ASCM0085-1（出库结果）")
    public SynReturnDTo syncWaybillDetailforJD(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController syncWaybillDetailforJD 开始执行 [{}]", JSONObject.toJSONString(resultWMS));

        String jsonString = JSON.toJSONString(resultWMS.getDATA());
        syncWaybillDetailforJDDto syncWaybillDetailforJDDto = JSON.parseObject(jsonString, syncWaybillDetailforJDDto.class);

        log.info("BzErpReqController syncWaybillDetailforJD 解析结果：[{}]", JSON.toJSONString(syncWaybillDetailforJDDto));

        return thirdPartySyncWaybillService.syncWaybillDetailforJD(syncWaybillDetailforJDDto);
    }

    @RemoteApi(RemoteApiEnum.ASCM0084)
    @PostMapping("/syncWaybillResult")
    @Operation(summary = "ASCM0084（收货情况）", description = "ASCM0084（收货情况）")
    public SynReturnDTo syncWaybillResult(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController syncWaybillResult 开始执行 [{}]", JSONObject.toJSONString(resultWMS));

        String jsonString = JSON.toJSONString(resultWMS.getDATA());
        SyncWaybillResultDto syncWaybillResultDto = JSON.parseObject(jsonString, SyncWaybillResultDto.class);

        log.info("BzErpReqController syncWaybillDetailforJD 解析结果：[{}]", JSON.toJSONString(syncWaybillResultDto));

        return thirdPartySyncWaybillService.syncWaybillResult(syncWaybillResultDto);
    }

    @PostMapping("/materialInfo")
    @Operation(summary = "erp下发物流和供应商信息", description = "erp下发物流和供应商信息")
    public SynReturnDTo materialInfo(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController materialInfo 开始执行 [{}]", JSONObject.toJSONString(resultWMS));

        String jsonString = JSON.toJSONString(resultWMS.getDATA());
        MaterialInfoDto materialInfoDto = JSON.parseObject(jsonString, MaterialInfoDto.class);
        log.info("BzErpReqController materialInfo 解析结果：[{}]", JSON.toJSONString(materialInfoDto));

        return thirdPartySyncWaybillService.materialInfo(materialInfoDto);
    }

    @PostMapping("/materialDeliveryNote")
    @Operation(summary = "ERP下发物料和入库订单信息", description = "ERP下发物料和入库订单信息")
    public SynReturnDTo materialDeliveryNote(@RequestBody ResultWMS resultWMS) {
        log.info("BzErpReqController materialDeliveryNote 开始执行 [{}]", JSONObject.toJSONString(resultWMS));

        String jsonString = JSON.toJSONString(resultWMS.getDATA());
        MaterialDeliveryNoteDto materialDeliveryNoteDto = JSON.parseObject(jsonString, MaterialDeliveryNoteDto.class);
        log.info("BzErpReqController materialDeliveryNote 解析结果：[{}]", JSON.toJSONString(materialDeliveryNoteDto));

        return thirdPartySyncWaybillService.materialDeliveryNote(materialDeliveryNoteDto);
    }
}
