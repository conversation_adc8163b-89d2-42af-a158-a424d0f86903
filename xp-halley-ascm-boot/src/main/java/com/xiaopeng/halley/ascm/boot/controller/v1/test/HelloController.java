package com.xiaopeng.halley.ascm.boot.controller.v1.test;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 调试接口
 *
 * <AUTHOR>
 * @Date 2022/6/15 3:58 PM
 */
@RestController
@RequestMapping("/hello")
@Tag(name = "调试接口")
public class HelloController {

    @GetMapping("/test")
    public String test(HttpServletResponse response) throws IOException {
        int i = 1 / 0;
        return "hello xp";
    }
}
