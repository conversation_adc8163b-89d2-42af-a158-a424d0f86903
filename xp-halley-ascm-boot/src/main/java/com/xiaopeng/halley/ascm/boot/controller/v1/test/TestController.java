package com.xiaopeng.halley.ascm.boot.controller.v1.test;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.swing.clipboard.ClipboardUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.component.SerialNumberGenerator;
import com.xiaopeng.halley.ascm.boot.dto.PORequest;
import com.xiaopeng.halley.ascm.boot.dto.WaybillBoxListInfoDto;
import com.xiaopeng.halley.ascm.boot.dto.WaybillInfoSyncErpVO;
import com.xiaopeng.halley.ascm.boot.dto.erp.WmsWaybillDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.WmsWaybillItemDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseShop;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillBoxRel;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillBoxRelMapper;
import com.xiaopeng.halley.ascm.boot.service.*;
import com.xiaopeng.halley.ascm.boot.service.sync.AscmErpWmsSyncNewService;
import com.xiaopeng.halley.ascm.boot.service.third.ThirdPartyService;
import com.xiaopeng.halley.ascm.boot.service.weChat.WeChatService;
import com.xiaopeng.halley.ascm.boot.utils.POReqUtil;
import com.xiaopeng.halley.ascm.boot.xxlJob.BasePrintTaskJob;
import com.xiaopeng.halley.ascm.boot.xxlJob.MatchPublishingJob;
import com.xiaopeng.xtiger.ssoauthsdk.common.config.SsoDefaultLoginConfig;
import com.xiaopeng.xtiger.ssoauthsdk.common.config.SsoSdkConfig;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.SneakyThrows;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.Test;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 调试接口
 *
 * <AUTHOR>
 * @Date 2022/6/15 3:58 PM
 */
@SuppressWarnings("all")
@RestController
@RequestMapping("/test")
@Tag(name = "调试接口")
public class TestController {
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private WeChatService weChatService;

	@GetMapping("/service")
	public Object service(Long id) throws Exception {
		SsoSdkConfig config = SpringUtil.getBean(SsoSdkConfig.class);
		System.err.println(SpringUtil.getBean(SsoDefaultLoginConfig.class).getDefaultFrontRedirect());
		return ResultUtil.success();
	}

	public static void main(String[] args) throws Exception {
		System.err.println(ClipboardUtil.getStr().replace("&#x20;", " "));
	}

	@Test
	public void excel() {
		// Input and output file paths
		String inputFilePath = "/Users/<USER>/Downloads/物流信息导入模板 .xlsx";
		String outputFilePath = "/Users/<USER>/Downloads/output.xlsx";

		int columnIndexToModify = 0; // Assuming you want to replace the 2nd column
		String newValue = "AT250626235608E-2";

		List<String> outputHeaders = Arrays.asList(
				"运单编号", "司机姓名", "联系电话", "城市", "位置更新时间",
				"物流单号", "物流公司", "省份", "具体位置"
		);
		List<Map<Integer, String>> rawData = EasyExcelFactory.read(inputFilePath).sheet(0).headRowNumber(1).doReadSync();

		List<List<String>> processedDataForWrite = new ArrayList<>();

		for (Map<Integer, String> rowMap : rawData) {
			List<String> rowList = new ArrayList<>();
			for (int i = 0; i < outputHeaders.size(); i++) {
				String cellValue = rowMap.get(i); // Get value by column index

				if (i == columnIndexToModify) {
					rowList.add(newValue); // Replace the value in the specified column
				} else {
					rowList.add(cellValue != null ? cellValue : ""); // Add original value or empty string
				}
			}
			processedDataForWrite.add(rowList);
		}

		// --- Write Updated Data to New Excel File ---
		// Define the headers for writing. EasyExcel uses these to create the header row.
		// It's good practice to make this match outputHeaders for clarity.
		List<List<String>> writeHead = new ArrayList<>();
		for (String head : outputHeaders) {
			writeHead.add(Arrays.asList(head)); // EasyExcel expects List<List<String>> for dynamic headers
		}

		EasyExcel.write(outputFilePath).sheet(0).head(writeHead).doWrite(processedDataForWrite);

		System.out.println("Excel processing complete. Output written to: " + outputFilePath);
	}

	@Test
	public void mysqlIn() {
		Set<String> set = new HashSet<>();
		for (String s : ClipboardUtil.getStr().split("\n")) {
			set.add(StrUtil.wrap(s, "'"));
		}
		String collect = set.stream().collect(Collectors.joining(","));
		System.err.println(collect);
	}

	@Test
	public void set() {
		System.err.println(ClipboardUtil.getStr().length());
	}

	@Test
	public void name() {
		Object collect = JSONUtil.parseObj(ClipboardUtil.getStr()).getByPath("DATA.MATNR", List.class).stream()
				.map(e -> StrUtil.wrap(e.toString(), "'")).collect(Collectors.joining(","));
		System.out.println(collect);
	}

	@Test
	public void test() {
		String str = "{\"childWaybillId\":[507182,507487,507183],\"parentWaybillId\":\"509039\"}";
		JSON json = JSONUtil.parse(str);
		List<Integer> res = new ArrayList<>();
		res.add(json.getByPath("parentWaybillId", Integer.class));
		res.addAll(json.getByPath("childWaybillId", List.class));

		String collect = res.stream().map(e -> StrUtil.wrap(e.toString(), "'"))
				.collect(Collectors.joining(","));
		ClipboardUtil.setStr(StrUtil.format("select id, waybill_code from bz_waybill where id in ({}) ORDER BY FIELD(id, {});", collect));
	}

	@Test
	public void extra() {
		List res1 = JSONUtil.parseObj(FileUtil.readUtf8String("1.json"))
				.getByPath("results.waybill_code", List.class);
		List res2 = JSONUtil.parseObj(FileUtil.readUtf8String("2.json"))
				.getByPath("results.child_waybill_code", List.class);
		System.out.println(CollUtil.subtract(res1, res2));
		System.out.println(CollUtil.subtract(res2, res1));
	}

	@PostMapping("/generateWaybillCode")
	public Result<String> generateWaybillCode(String lgort) throws Exception {
		return ResultUtil.success(SpringUtil.getBean(SerialNumberGenerator.class).generateWaybillCode(lgort));
	}

	@PostMapping("/dispatchTimeSync")
	public Result<String> dispatchTimeSync(String waybillCode) throws Exception {
		SpringUtil.getBean(BzWaybillInfoSyncService.class).dispatchTimeSync();
		return ResultUtil.success();
	}

	@PostMapping("/checkBoxMaterialCount")
	public Result<String> checkBoxMaterialCount(String waybillCode) throws Exception {
		SpringUtil.getBean(ThirdPartyService.class).checkBoxMaterialCount(waybillCode);
		return ResultUtil.success();
	}

	@Data
	static class WaybillRequest {
		private String lgort;
		private String shopCode;
		private int boxCount = 1;
	}

	@PostMapping("/saveWaybill")
	public Object saveWaybill(@RequestBody WaybillRequest request) throws Exception {
		String format = DateUtil.format(new Date(), "yyyyMMddHHmm");
		String waybillCode = format.substring(2) + RandomUtil.randomNumbers(3);

		BaseShopService baseShopService = SpringUtil.getBean(BaseShopService.class);
		BaseWarehouseService baseWarehouseService = SpringUtil.getBean(BaseWarehouseService.class);
		BaseShop baseShop = baseShopService.getOne(new LambdaQueryWrapper<BaseShop>().eq(BaseShop::getShopCode, request.getShopCode()));
		BaseWarehouse baseWarehouse = baseWarehouseService.getOne(new LambdaQueryWrapper<BaseWarehouse>().eq(BaseWarehouse::getLgort, request.getLgort()));

		WmsWaybillDto wmsWaybillDto = new WmsWaybillDto();
		wmsWaybillDto.setZYDBH(waybillCode);
		wmsWaybillDto.setStatusValue("10");
		wmsWaybillDto.setWerks("2200");
		wmsWaybillDto.setName1("小鹏汽车销售有限公司工厂");

		wmsWaybillDto.setKunnr(request.getShopCode());
		wmsWaybillDto.setNameOrg1(baseShop.getShopName());
		wmsWaybillDto.setZsdsf(baseShop.getShopProvince());
		wmsWaybillDto.setZsdcty(baseShop.getShopCity());
		wmsWaybillDto.setZscadd(baseShop.getShopAddress());
		wmsWaybillDto.setZjsr(baseShop.getContactPerson());
		wmsWaybillDto.setZjcrnum(baseShop.getContactNum());

		wmsWaybillDto.setLgort(request.getLgort());
		wmsWaybillDto.setLgobe(baseWarehouse.getLgobe());
		wmsWaybillDto.setZfhr(baseWarehouse.getContactPerson());
		wmsWaybillDto.setZfhnum(baseWarehouse.getContactNum());
		wmsWaybillDto.setZfhsf(baseWarehouse.getWarehouseProvince());
		wmsWaybillDto.setZfhcty(baseWarehouse.getWarehouseCity());
		wmsWaybillDto.setZfhadd(baseWarehouse.getWarehouseAddress());
		wmsWaybillDto.setItems(Lists.newArrayList());

		int boxCode = Integer.parseInt(RandomUtil.randomNumbers(3));
		for (int i = 0; i < request.getBoxCount(); i++) {
			WmsWaybillItemDto item = new WmsWaybillItemDto();
			wmsWaybillDto.getItems().add(item);
			item.setCarton("N22200240723" + String.valueOf(boxCode + i));
			item.setZdxzl(49.500);
			item.setPosnr("000010");
			item.setZzvbeln("62072301A");
			item.setZYL02("62072301A");
			item.setWave("7200014311");
			item.setLprio("00");
			item.setVtext("补充交货(UB-备件)");
			item.setLfimg(10);
			item.setQty(5);
			item.setZbzrq("20240103");
			item.setZbzsj("085805");
			item.setZsjtj(0.396);
			item.setZcd("1100.000");
			item.setZkd("800.000");
			item.setZgd("450.000");
			item.setPmat("ZX5004");
			item.setProductno("8225009DB1-00-01");
			item.setMaktx("外后视镜调节开关");
			item.setCd("100.000");
			item.setKd("100.000");
			item.setGd("100.000");
			item.setZwltj(0.005);
		}

		String res = SpringUtil.getBean(AscmErpWmsSyncNewService.class)
				.saveWaybill(Collections.singletonList(wmsWaybillDto), new Date());
		System.err.println(res);
		return ResultUtil.success(waybillCode);
	}

	@PostMapping("/retry")
	public void retry(@RequestBody JSONObject request) {
		// 1. 解析输入数据
		List<JSONObject> dataList = request.getByPath("results", List.class);
		List<WaybillInfoSyncErpVO> list = waybillTimeInfoSyncErp(dataList);
		System.err.println(list.size());
		int i = 0;
		for (WaybillInfoSyncErpVO result : list) {
			//进行数据封装
			String sendId = UUID.fastUUID().toString(true);
			com.alibaba.fastjson.JSONObject headParams = POReqUtil.getHeadParams("ASCM", "ERP", "ASCM0087", sendId);
			// 封装DATA的数据
			com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
			List<WaybillBoxListInfoDto> boxList = result.getBoxList();
			com.alibaba.fastjson.JSONArray boxJsonArr = new com.alibaba.fastjson.JSONArray();
			for (WaybillBoxListInfoDto waybillBoxListInfoDto : boxList) {
				com.alibaba.fastjson.JSONObject boxInfo = new com.alibaba.fastjson.JSONObject();
				boxInfo.put("BOXCODE", waybillBoxListInfoDto.getBoxCode());
				List<String> deliveryOrderCodeList = waybillBoxListInfoDto.getDeliveryOrderCodeList();
				com.alibaba.fastjson.JSONArray deliveryOrderCodeListJson = new JSONArray();
				for (String deliveryOrderCode : deliveryOrderCodeList) {
					com.alibaba.fastjson.JSONObject deliveryOrderCodeJson = new com.alibaba.fastjson.JSONObject();
					deliveryOrderCodeJson.put("DELIVERYORDERCODE", deliveryOrderCode);
					deliveryOrderCodeListJson.add(deliveryOrderCodeJson);
				}
				boxInfo.put("DELIVERYORDERLIST", deliveryOrderCodeListJson);
				boxJsonArr.add(boxInfo);
			}
			jsonObject.put("BOXLIST", boxJsonArr);
			jsonObject.put("WAYBILLCODE", result.getWaybillCode());
//			jsonObject.put("ZCLASS", "50");
			Date signTime = result.getSignTime();
			if (null != signTime) {
				jsonObject.put("ZSJQSDT", DateUtil.format(signTime, "yyyyMMdd"));// 实际签收日期
				jsonObject.put("ZSJQSTM", DateUtil.format(signTime, "HHmmss"));// 实际签收时间
			}
			com.alibaba.fastjson.JSONObject params = new com.alibaba.fastjson.JSONObject();
			params.put("HEADER", headParams);
			params.put("DATA", jsonObject);
			System.err.println(++i);
			send(params);
		}
	}

	@SneakyThrows
	private void send(com.alibaba.fastjson.JSONObject req) {
		OkHttpClient client = new OkHttpClient().newBuilder().build();
		MediaType mediaType = MediaType.parse("application/json");
		okhttp3.RequestBody body = okhttp3.RequestBody.create(mediaType, req.toString());
		Request request = new Request.Builder()
				.url("https://poprd.xiaopeng.com/RESTAdapter/BS_VSCM/SI_ASCM0087_Syn_Out")
				.method("POST", body)
				.addHeader("Authorization", "Basic dG1zdXNlcjpQb1BSRFRNUzY=")
				.addHeader("Client-Type", "web")
				.addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
				.addHeader("Content-Type", "application/json")
				.addHeader("Accept", "*/*")
				.addHeader("Host", "poprd.xiaopeng.com")
				.addHeader("Connection", "keep-alive")
				.addHeader("Cookie", "saplb_*=(J2EE1568520)1568553; JSESSIONID=nmyUEleCcVEPUAr53_rFzKqhzkHTlgEo7xcA_SAPIFjEg2JfFIgxpP521taUfKvY")
				.build();
		Response response = client.newCall(request).execute();
		System.err.println(response);
		response.close();
	}

	public List<WaybillInfoSyncErpVO> waybillTimeInfoSyncErp(List<JSONObject> dataList) {
		List<WaybillInfoSyncErpVO> list = new ArrayList<>();

		for (JSONObject first : dataList) {
			WaybillInfoSyncErpVO result = new WaybillInfoSyncErpVO();
			list.add(result);
			// 获取运单基本信息(取第一条记录即可)
			result.setWaybillCode(first.getStr("waybillCode"));
			result.setSignTime(first.getDate("signTime"));

			// 按箱码分组处理
			Map<String, List<JSONObject>> boxGroupMap = dataList.stream()
					.collect(Collectors.groupingBy(json -> json.getStr("boxCode")));

			List<WaybillBoxListInfoDto> boxList = new ArrayList<>();

			// 处理每个箱的信息
			boxGroupMap.forEach((boxCode, boxRecords) -> {
				WaybillBoxListInfoDto boxInfo = new WaybillBoxListInfoDto();
				boxInfo.setBoxCode(boxCode);

				// 获取该箱的所有订单号
				List<String> deliveryOrderCodes = boxRecords.stream()
						.map(record -> record.getStr("deliveryOrderCode"))
						.distinct()
						.collect(Collectors.toList());

				boxInfo.setDeliveryOrderCodeList(deliveryOrderCodes);
				boxList.add(boxInfo);
			});

			result.setBoxList(boxList);
		}
		return list;
	}

	@PostMapping("/printTask")
	public void printTask() {
		SpringUtil.getBean(MatchPublishingJob.class).printTask();
	}

	@PostMapping("/refreshPrintTaskStatus")
	public void refreshPrintTaskStatus() {
		SpringUtil.getBean(BasePrintTaskJob.class).refreshPrintTaskStatus();
	}

	@PostMapping("/checkBoxCount")
	public Integer checkBoxCount() {
		PORequest poRequest = PORequest.builder()
				.data(Lists.newArrayList(
						new JSONObject().set("CARTON", "N23100020250227001"),
						new JSONObject().set("CARTON", "N23100020250227003")
				))
				.busId("ASCM0088")
				.receiver("SAP")
				.poInvokeEnum(POInvokeEnum.SI_ASCM0088_Syn_Out)
				.build();
		return Convert.toInt(SpringUtil.getBean(PoRequestHelp.class).invoke(poRequest, "ZTOTAL"));
	}

	@GetMapping("/createPdf")
	public void createPdf(String waybillCode, HttpServletResponse response) throws Exception {
		InputStream inputStream = SpringUtil.getBean(BasePrintOutTaskService.class).createPdf(waybillCode, new Date());
		ServletUtil.write(response, inputStream);
	}

	@GetMapping("/getURLContent")
	public Object getURLContent(Long id) throws Exception {
		for (int i = 0; i < 1; i++) {
			Map<String, Object> map = SpringUtil.getBean(BzWaybillService.class).getURLContent("清远市清城区新城B28号区奇鸿汽车城（1F11-01 号）小鹏汽车广清大道店");
			System.err.println(map);
		}
		return ResultUtil.success();
	}

	@GetMapping("/copy")
	public Object copy(String waybillCode, String newWaybillCode) throws IOException {
		BzWaybillService bzWaybillService = SpringUtil.getBean(BzWaybillService.class);
		BzWaybillBoxRelMapper boxRelMapper = SpringUtil.getBean(BzWaybillBoxRelMapper.class);

		BzWaybill waybill = bzWaybillService.getOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, waybillCode));
		BzWaybill newWaybill = BeanUtil.copyProperties(waybill, BzWaybill.class, "id");
		newWaybill.setWaybillCode(newWaybillCode);
		bzWaybillService.save(newWaybill);

		for (BzWaybillBoxRel bzWaybillBoxRel : boxRelMapper.selectList(new LambdaQueryWrapper<>(BzWaybillBoxRel.class).eq(BzWaybillBoxRel::getWaybillCode, waybillCode))) {
			bzWaybillBoxRel.setId(null);
			bzWaybillBoxRel.setWaybillCode(newWaybillCode);
			boxRelMapper.insert(bzWaybillBoxRel);
		}
		return newWaybill;
	}

	/**
	 * 设置段落样式
	 *
	 * @param paragraph        段落
	 * @param alignment        对齐方式
	 * @param indentationLeft  左缩进
	 * @param indentationRight 右缩进
	 * @param firstLineIndent  首行缩进
	 * @param fixedLeading     行间距
	 * @param spacingBefore    段落上空白
	 * @param spacingAfter     段落下空白
	 */
	public static void setParagraphStyle(Paragraph paragraph,
	                                     int alignment,
	                                     float indentationLeft,
	                                     float indentationRight,
	                                     float firstLineIndent,
	                                     float fixedLeading,
	                                     float spacingBefore,
	                                     float spacingAfter
	) {
		paragraph.setAlignment(alignment);// 对齐方式
		paragraph.setIndentationLeft(indentationLeft);// 左缩进
		paragraph.setIndentationRight(indentationRight);// 右缩进
		paragraph.setFirstLineIndent(firstLineIndent);// 首行缩进
		paragraph.setLeading(fixedLeading);// 行间距
		paragraph.setSpacingBefore(spacingBefore);// 设置上空白
		paragraph.setSpacingAfter(spacingAfter);// 设置段落下空白
	}

	/**
	 * 支持中文 设置字体，字体颜色、大小等
	 *
	 * @param fonts 字体路径
	 * @param size  大小
	 * @param style 字体样式（比如加粗/斜体等）
	 * @param color 字体颜色
	 * @return
	 */
	public static Font getChineseFont(String fonts, float size, int style, BaseColor color) {
		BaseFont simpChinese;
		Font ChineseFont = null;
		try {
			simpChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
			ChineseFont = new Font(simpChinese, size, style, color);
		} catch (DocumentException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return ChineseFont;
	}

	@GetMapping("/test")
	public Map test(HttpServletResponse response) throws IOException {
		String token = weChatService.getAccessToken();
		Map result = weChatService.getPhoneNumber("e31968a7f94cc5ee25fafc2aef2773f0bb8c3937b22520eb8ee345274d00c144", token);
		return result;
	}

	@GetMapping("/testPDF")
	public Result testPDF(HttpServletResponse response) throws Exception {

		Document document = new Document(PageSize.A4, 50, 50, 30, 20); //页面大小-左边距-右边距-上边距-下边距。

		//第二步，创建Writer实例
		PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream("hello.pdf"));

		//创建中文字体
		BaseFont bfchinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
		Font topfont = new Font(bfchinese, 10, Font.BOLD);

		//页眉页尾
//        HeaderFooterPageEvent event = new HeaderFooterPageEvent();
//        writer.setPageEvent(event);

		//第三步，打开文档
		document.open();
		//第四步，写入内容

//        Paragraph pt=new Paragraph("名称",topfont);
//        //设置字体样式pt.setAlignment(1);//设置文字居中 0靠左 1，居中 2，靠右
//        pt.setIndentationLeft(12);// 左缩进
//        pt.setIndentationRight(12);// 右缩进
//        pt.setFirstLineIndent(24);// 首行缩进
//        pt.setLeading(20f);// 行间距
//        pt.setSpacingBefore(5f);// 设置上空白
//        pt.setSpacingAfter(10f);// 设置段落下空白
//        document.add(pt);
//
//        Paragraph pt2=new Paragraph("小鹏交货单",topfont);
//        document.add(pt2);
//
//        document.add( new Phrase("\n") ); //

		//创建一列的格子
		PdfPTable goodTable = new PdfPTable(13);
		goodTable.setWidthPercentage(100);

		//第一行
		{
			//设置图片
			Image image = Image.getInstance(new DefaultResourceLoader().getResource("classpath:image/logo.png").getURL());
			image.scalePercent(20);

			PdfPCell cell = new PdfPCell(image, false);
			//格子横跨2个格子
			cell.setColspan(3);
			//格子高度35px
			cell.setMinimumHeight(35);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			PdfPCell cell = new PdfPCell();
			//格子横跨2个格子
			cell.setColspan(2);
			//格子高度35px
			cell.setMinimumHeight(35);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			PdfPCell cell = new PdfPCell(new Phrase("小鹏交接单", new Font(bfchinese, 20, Font.BOLD)));
			//格子横跨2个格子
			cell.setColspan(3);
			//格子高度35px
			cell.setMinimumHeight(35);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			PdfPCell cell = new PdfPCell();
			//格子横跨2个格子
			cell.setColspan(2);
			//格子高度35px
			cell.setMinimumHeight(35);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			//设置图片
			Image image = Image.getInstance(new DefaultResourceLoader().getResource("classpath:image/QRCode.png").getURL());
			image.scaleAbsolute(80, 80);

			PdfPCell cell = new PdfPCell(image, false);
			//格子横跨2个格子
			cell.setColspan(3);
			//格子高度35px
			cell.setMinimumHeight(35);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_CENTER);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			cell.setPadding(5);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		//第二行
		{
			goodTable.addCell(getCell("运单编号", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("AZ22072719040", 9, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("运输方式", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("零担", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));

		}

		//第三行
		{
			goodTable.addCell(getCell("收方信息", 10, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("打印时间", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("2022.01.15 15:23:32", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第四行
		{
			goodTable.addCell(getCell("客户名称", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("无锡小鹏骑车销售服务有限公司", 9, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("客户代码", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("2222", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第五行
		{
			goodTable.addCell(getCell("收件信息", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("江苏省无锡市锡山区先锋中路25-6-2号 小鹏骑车售后配件仓库", 12, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第六行
		{
			goodTable.addCell(getCell("目的地", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("江苏省无锡市区", 4, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("联系人", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("张三", 4, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("联系电话", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("12323215574", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));

		}

		//第七行
		{
			goodTable.addCell(getCell("寄方信息", 13, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第八行
		{
			goodTable.addCell(getCell("发运仓库", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("小鹏售后备件物流中心", 12, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第九行
		{
			goodTable.addCell(getCell("寄方信息", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("广东省肇庆市四会市大旺高新技术技术开房去亚铝大街唯品会物流园2号库", 12, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第十行
		{
			goodTable.addCell(getCell("联系人", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("李四", 9, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("联系电话", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("12312321312312", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第十一行
		{
			goodTable.addCell(getCell("订单信息", 13, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第十二行
		{
			goodTable.addCell(getCell("交货单号", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("81234567，81234567，81234567，81234567，81234567，", 12, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		//第十三行
		{
			goodTable.addCell(getCell("序号", 1, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("箱号(箱)", 2, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("尺寸(长宽高)", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("体积(立方米)", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("装箱类别", 1, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("备注", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
		}

		for (int i = 0; i < 100; i++) {
			goodTable.addCell(getCell(String.valueOf(i + 1), 1, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell(String.valueOf(2201040650L + 1), 2, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell(" ", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell(String.valueOf(i + 1), 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("混合箱", 1, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
		}

		{
			goodTable.addCell(getCell("合计", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("20", 12, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		{
			goodTable.addCell(getCell("签收信息", 13, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		{
			goodTable.addCell(getCell("发货仓库确认", 3, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("物流运输商确认", 6, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("门店签收确认", 4, 1, 15, Element.ALIGN_MIDDLE, Element.ALIGN_MIDDLE));
		}

		{
			goodTable.addCell(getCell("", 3, 1, 30, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("", 6, 1, 30, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("", 4, 1, 30, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		{
			goodTable.addCell(getCell("异常备注", 1, 1, 30, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
			goodTable.addCell(getCell("", 12, 1, 30, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
		}

		{
			PdfPCell cell = new PdfPCell(new Phrase("[未来出行探索者 EXPLORER OF FUTURE MOBITY]", new Font(bfchinese, 7, Font.BOLD)));
			//格子横跨2个格子
			cell.setColspan(13);
			//格子高度35px
			cell.setMinimumHeight(15);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorBottom(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			PdfPCell cell = new PdfPCell(new Phrase("广州小鹏汽车科技有限公司|广州市天河区岑村长兴街道松岗大街8号|邮编510640", new Font(bfchinese, 7, Font.NORMAL)));
			//格子横跨2个格子
			cell.setColspan(13);
			//格子高度35px
			cell.setMinimumHeight(15);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorBottom(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		{
			PdfPCell cell = new PdfPCell(new Phrase("电话 +8620-66806680|www.xiaopeng.com", new Font(bfchinese, 7, Font.NORMAL)));
			//格子横跨2个格子
			cell.setColspan(13);
			//格子高度35px
			cell.setMinimumHeight(15);
			//格子纵跨1个格子
			cell.setRowspan(1);
			//格子内容左右居中
			cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
			//格子内容上下居中
			cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

			//设置边框颜色
			cell.setUseVariableBorders(true);
			cell.setBorderColorTop(BaseColor.WHITE);
			cell.setBorderColorBottom(BaseColor.WHITE);
			cell.setBorderColorLeft(BaseColor.WHITE);
			cell.setBorderColorRight(BaseColor.WHITE);

			goodTable.addCell(cell);
		}

		document.add(goodTable);
		//第五步，关闭文档
		document.close();

		return ResultUtil.success();
	}

	public PdfPCell getCell(String content, int colspan, int rowspan, Integer minHeight, int horizontalAlignment, int verticalAlignment) throws Exception {
		BaseFont bfchinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
		PdfPCell cell = new PdfPCell(new Phrase(content, new Font(bfchinese, 7, Font.BOLD)));
		cell.setColspan(colspan);
		cell.setMinimumHeight(minHeight);
		cell.setRowspan(rowspan);
		cell.setHorizontalAlignment(horizontalAlignment);
		cell.setVerticalAlignment(verticalAlignment);
		return cell;
	}

}
