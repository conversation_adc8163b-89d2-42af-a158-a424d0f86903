package com.xiaopeng.halley.ascm.boot.controller.v1.waybill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaidi100.sdk.response.SubscribeResp;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.AsyncExportTask;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.ExcludeResponseBody;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.importExport.TempResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.waybill.*;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import com.xiaopeng.halley.ascm.boot.feign.AssApiFeign;
import com.xiaopeng.halley.ascm.boot.service.BzErpReqMaterialRelService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.utils.excel.CheckSizeUtil;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.BusinessException;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Slf4j
@GlobalResponseBody
@RestController
@RequestMapping("/bzWaybill")
@Tag(name = "正向运单相关接口")
public class BzWaybillController {

    @Resource
    private CheckSizeUtil checkSizeUtil;

    @Resource
    private BzWaybillService bzWaybillService;

    @Resource
    private AssApiFeign assApiFeign;

    @Resource
    private DragonService dragonService;

    @Operation(summary = "同步运单信息到骁龙")
    @PostMapping("/syncWaybill")
    public Result<String> syncWaybill(@RequestBody List<AsmWayBillReq> reqList) {
        return assApiFeign.syncWaybill(reqList);
    }

    @Operation(summary = "同步运单物流信息到骁龙")
    @PostMapping("/freshWaybill")
    public Result<String> freshWaybill(@RequestBody AsmWaybillFreshReq freshReq) {
        return assApiFeign.freshWaybill(freshReq);
    }

    @Operation(summary = "骁龙拉取查询运单信息", description = "属于门店且未完成的运单")
    @PostMapping("/queryStoreWaybill")
    public Result<Map<String, List<AsmWayBillReq>>> queryStoreWaybill(@RequestBody List<String> waybillCodes) {
        return ResultUtil.success(dragonService.queryStoreWaybill(waybillCodes));
    }

    @Operation(summary = "骁龙拉取查询运单更新信息", description = "属于门店且未完成的运单")
    @PostMapping("/queryFreshWaybill")
    public Result<List<AsmWaybillFreshReq>> queryFreshWaybill(@RequestBody List<String> waybillCodes) {
        return ResultUtil.success(dragonService.queryFreshWaybill(waybillCodes));
    }

    @Operation(summary = "计算运单总体积总重量")
    @PostMapping("/calVolumeWeight")
    public Result<List<BzWaybillDetailDto>> calVolumeWeight(@RequestBody List<String> waybillCodes, boolean update) {
        return ResultUtil.success(bzWaybillService.calVolumeWeight(waybillCodes, update));
    }

    @Operation(summary = "修改运单包装数量")
    @PostMapping("/updateCount")
    public Result<String> updateCount(@RequestBody UpdateWaybillCountDto request) {
        Assert.notNull(request.getId(), "ERP需求关联箱ID不能为空");
        BzErpReqMaterialRelService service = SpringUtil.getBean(BzErpReqMaterialRelService.class);
        boolean update = service.lambdaUpdate().eq(BzErpReqMaterialRel::getId, request.getId())
                .set(BzErpReqMaterialRel::getQuantity, request.getPackageCount())
                .set(BzErpReqMaterialRel::getPackageCount, request.getPackageCount())
                .set(BzErpReqMaterialRel::getReceiveCount, request.getReceiveCount())
                .update();
        if (!update) {
            return ResultUtil.failed("修改运单包装数量失败");
        }
        return ResultUtil.success();
    }

    @PostMapping("/finishWaybillList")
    @Operation(summary = "完成运单", description = "完成运单")
    public Result<Object> finishWaybill(@RequestBody WaybillSuccessDto requestVO) {
        log.info("BzWaybillController finishWaybill 开始执行");
        try {
            bzWaybillService.finishWaybill(requestVO);
        } catch (BusinessException e) {
            return ResultUtil.failed(e.getMessage());
        }
        return ResultUtil.success();
    }

    /**
     * 下载模板
     */
    @GetMapping("/tempFile")
    @Operation(summary = "下载导入物流信息模板", description = "下载导入物流信息模板")
    public void tempFile(HttpServletResponse response) throws IOException {
        log.info("BaseAddressController tempFile 开始执行");
        String name = "运单配送信息导入.xlsx";
        ClassPathResource classPathResource = new ClassPathResource("/file/" + name);
        response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(name, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        IoUtil.copy(classPathResource.getInputStream(), response.getOutputStream());
    }

    /**
     * 下载模板
     */
    @GetMapping("/tempFileDelivery")
    @Operation(summary = "下载导入快递信息模板", description = "下载导入快递信息模板")
    public Result<TempResponseDto> tempFileDelivery() {
        log.info("BaseAddressController tempFileDelivery 开始执行");
        return ResultUtil.success(this.bzWaybillService.tempFileDelivery());
    }

    /**
     * 导入物流校验
     */
    @PostMapping(value = "/importCheckList", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "物流信息导入校验", description = "物流信息导入校验")
    public Result<ImportResponseDto> importCheckList(@RequestParam("file") MultipartFile file) {
        log.info("BzWaybillController importCheckList 开始执行");
        checkSizeUtil.checkExcelSize(file);
        return ResultUtil.success(bzWaybillService.importCheckList(file));
    }

    /**
     * 导入快递校验
     */
    @PostMapping(value = "/importCheckDeliveryList", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "物流信息导入校验", description = "物流信息导入校验")
    public Result<ImportResponseDto> importCheckDeliveryList(@RequestParam("file") MultipartFile file) {
        log.info("BzWaybillController importCheckDeliveryList 开始执行");
        checkSizeUtil.checkExcelSize(file);
        return ResultUtil.success(bzWaybillService.importCheckDeliveryList(file));
    }

    /**
     * 下载错误物流文件
     */
    @GetMapping("/failDownload/{operationCode}")
    @Operation(summary = "物流信息下载错误文件", description = "物流信息下载错误文件")
    @Parameters(value = {
            @Parameter(name = "operationCode", description = "失败文件获取代码")
    })
    public void failDownload(@PathVariable(value = "operationCode") String operationCode, HttpServletResponse response) {
        log.info("BzWaybillController failDownload 开始执行 [{}]", operationCode);
        this.bzWaybillService.failDownload(operationCode, response);
    }

    /**
     * 下载错误快递文件
     */
    @GetMapping("/failDeliveryDownload/{operationCode}")
    @Operation(summary = "快递信息下载错误文件", description = "快递信息下载错误文件")
    @Parameters(value = {
            @Parameter(name = "operationCode", description = "失败文件获取代码")
    })
    public void failDeliveryDownload(@PathVariable(value = "operationCode") String operationCode, HttpServletResponse response) {
        log.info("BzWaybillController failDeliveryDownload 开始执行 [{}]", operationCode);
        this.bzWaybillService.failDeliveryDownload(operationCode, response);
    }

    /**
     * 导入
     */
    @GetMapping(value = "/importList")
    @Operation(summary = "导入成功数据", description = "地址数据导入校验")
    public Result<ImportResponseDto> importList(@RequestParam String operationCode) {
        log.info("BaseAddressController importList 开始执行 operationCode:{}", operationCode);
        return ResultUtil.success(this.bzWaybillService.importList(operationCode));
    }

    /**
     * 导入
     */
    @GetMapping(value = "/importDeliveryList")
    @Operation(summary = "导入快递信息订阅", description = "导入快递信息订阅")
    public Result<ImportResponseDto> importDeliveryList(@RequestParam String operationCode) throws Exception {
        log.info("BaseAddressController importDeliveryList 开始执行 operationCode:{}", operationCode);
        return ResultUtil.success(this.bzWaybillService.importDeliveryList(operationCode));
    }

    @Operation(summary = "运单导出", description = "运单导出")
    private Result export(@RequestBody WaybillExportSearchDto exportDto) throws IOException {
        log.info("BzWaybillController export 导出开始执行 exportDto:{}", exportDto);
        if (CollUtil.isNotEmpty(exportDto.getWaybillCreateTime())) {
            try {
                exportDto.setCreateTimeStart(exportDto.getWaybillCreateTime().get(0));
                exportDto.setCreateTimeEnd(exportDto.getWaybillCreateTime().get(1));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        this.bzWaybillService.export(exportDto);
        return ResultUtil.success("导出中");
    }

    /**
     * 导入
     */
    @GetMapping(value = "/checkExpiryTime")
    @Operation(summary = "检查是否过期", description = "检查是否过期")
    public Result checkBzWaybillExpiryTime() {
        return this.bzWaybillService.checkBzWaybillExpiryTime();
    }

    @PostMapping(value = "/getURLContent")
    @Operation(summary = "根据位置获取经纬度信息", description = "根据位置获取经纬度信息")
    public Result getURLContent(@RequestParam String address) {
        return ResultUtil.success(this.bzWaybillService.getURLContent(address));
    }

    @ExcludeResponseBody
    @PostMapping(value = "/callBackMethod")
    @Operation(summary = "快递信息回调接口", description = "快递信息回调接口")
    public SubscribeResp callBackMethod(HttpServletRequest request) {
        return bzWaybillService.callBackMethod(request);
    }

    @PostMapping(value = "/boxesByWaybill")
    @Operation(summary = "总箱数查询", description = "总箱数查询")
    public Result boxesByWaybill(@RequestBody BoxParamVo vo) {
        if (null == vo.getBzWaybillId()) {
            return ResultUtil.failed("运单ID不能为空！");
        }
        if (StrUtil.isNotBlank(vo.getBoxStatus())) {
            if ("已扫描".equals(vo.getBoxStatus())) {
                vo.setBoxStatus("1");
            } else {
                vo.setBoxStatus("0");
            }
        }
        if (StrUtil.isNotBlank(vo.getIsDevanning())) {
            if ("已拆离".equals(vo.getIsDevanning())) {
                vo.setIsDevanning("1");
            } else {
                vo.setIsDevanning("0");
            }
        }
        return ResultUtil.success(bzWaybillService.boxesByWaybill(vo));
    }

    @PostMapping(value = "/getWaybillStatus")
    @Operation(summary = "获取所有运单状态", description = "获取所有运单状态")
    public Result getWaybillStatus() {
        return bzWaybillService.getWaybillAllStatusCountList();
    }

    /**
     * 运单总览分页查询
     *
     * @param page
     * @return
     */
    @PostMapping("/page")
    @Operation(summary = "运单总览分页查询", description = "运单总览分页查询")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Page<BzWayBillPageVo> getPage(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return bzWaybillService.getPage(page);
    }

    /**
     * 运单总览导出
     *
     * @param page
     * @return
     */
    @PostMapping("/wayBillOverviewExport")
    @Operation(summary = "运单总览导出", description = "运单总览导出")
    @AsyncExportTask(name = "运单总览导出", methodPath = "BzWaybillService.getNewPage")
    public Result wayBillOverviewExport(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    @PostMapping("/wayBillOverviewExportForKD")
    @Operation(summary = "在途跟踪-快递", description = "在途跟踪-快递")
    @AsyncExportTask(name = "在途跟踪-快递", methodPath = "BzWaybillService.getPage")
    public Result wayBillOverviewExportForKD(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    @PostMapping("/wayBillOverviewExportForZC")
    @Operation(summary = "在途跟踪-专车", description = "在途跟踪-专车")
    @AsyncExportTask(name = "在途跟踪-专车", methodPath = "BzWaybillService.getPage")
    public Result wayBillOverviewExportForZC(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    @PostMapping("/wayBillOverviewExportForLD")
    @Operation(summary = "在途跟踪-零担", description = "在途跟踪-专车")
    @AsyncExportTask(name = "在途跟踪-零担", methodPath = "BzWaybillService.getPage")
    public Result wayBillOverviewExportForLD(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    /**
     * 分仓调拨导出
     *
     * @param page
     * @return
     */
    @PostMapping("/warehouseAllocateExport")
    @Operation(summary = "分仓调拨导出", description = "分仓调拨导出")
    @AsyncExportTask(name = "分仓调拨导出", methodPath = "BzWaybillService.getPage")
    public Result export(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    /**
     * 批量导入实际到达时间
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    @PostMapping(value = "/importActualTime", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "批量导入实际到达时间", description = "批量导入实际到达时间")
    public ImportResponseDto importActualTime(@RequestParam("file") MultipartFile file) {
        log.info("BzWaybillController importActualTime 开始执行");
        return bzWaybillService.importActualTime(file);
    }

    /**
     * 实际时间模板下载
     *
     * @return 模板的下载路径名称等
     */
    @GetMapping("/getTempFile")
    @Operation(summary = "批量导入实际到达时间模板下载", description = "批量导入实际到达时间模板下载")
    public ImageResponseDTO getTempFile() {
        log.info("BzWaybillController getTempFile 开始执行");
        return this.bzWaybillService.getTempFile();
    }

    /**
     * 下载错误的文件
     *
     * @param page     错误文件的UUID
     * @param response 响应文件
     */
    @PostMapping("/downloadFailFile")
    @Operation(summary = "批量导入实际到达时间下载错误的文件", description = "批量导入实际到达时间下载错误的文件")
    public void downloadFailFile(@RequestBody PageQuery<AccountSuccessRequestDTO> page, HttpServletResponse response) throws ResultException {
        AccountSuccessRequestDTO dto = page.getParam();
        log.info("BzWaybillController downloadFailFile {}", JSON.toJSONString(dto));
        this.bzWaybillService.downloadFailFile(dto, response);
    }

    /**
     * 批量导入更新运单状态
     *
     * @param operationCode 校验成功的文件UUID
     * @return 更新的结果
     */
    @PostMapping("/updateActualTime")
    @Operation(summary = "批量导入实际到达时间更新运单状态", description = "批量导入实际到达时间更新运单状态")
    public Result updateActualTime(@RequestBody AccountSuccessRequestDTO operationCode) throws ResultException {
        log.info("BzWaybillController updateActualTime {}", JSON.toJSONString(operationCode));
        return bzWaybillService.updateActualTime(operationCode.getOperationCode());
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    @PostMapping("/getSuccessPage")
    @Operation(summary = "批量导入实际到达时间验成功的数据分页", description = "批量导入实际到达时间验成功的数据分页")
    public Page<ActualTimeDto> getSuccessPage(@RequestBody PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BzWaybillController getSuccessPage {}", JSON.toJSONString(page));
        return bzWaybillService.getSuccessPage(page);
    }

    /**
     * 运单详情页 —— 详情+箱子分页
     *
     * @param page 查询分页参数
     * @return 详情页结果
     * @throws ResultException 异常信息
     */
    @PostMapping("/getWaybillDetail")
    @Operation(summary = "运单详情页 —— 详情+箱子分页", description = "运单详情页 —— 详情+箱子分页")
    public BzWaybillDetailDto getWaybillDetail(@RequestBody PageQuery<String> page) throws ResultException {
        log.info("BzWaybillController getWaybillDetail {}", JSON.toJSONString(page));
        return bzWaybillService.getWaybillDetail(page);
    }

    /**
     * 发运明细详情分页
     *
     * @param page 分页参数
     * @return 分页结果
     */
    @PostMapping("/getMaterialDetailPage")
    @Operation(summary = "发运明细详情分页查询", description = "发运明细详情分页查询")
    public Page<BzWaybillMaterialDetailPageVo> getMaterialDetailPage(@RequestBody PageQuery<BzWayBillPageDto> page) {
        log.info("BzWaybillController getMaterialDetailPage {}", JSON.toJSONString(page));
        return bzWaybillService.getMaterialDetailPage(page);
    }

    @PostMapping("/getMaterialDetailPageV2")
    @Operation(summary = "发运明细详情分页查询", description = "发运明细详情分页查询")
    public Page<BzWaybillMaterialDetailPageVo> getMaterialDetailPageV2(@RequestBody PageQuery<BzWayBillPageDto> page) {
        log.info("BzWaybillController getMaterialDetailPage {}", JSON.toJSONString(page));
        return bzWaybillService.getMaterialDetailPageV2(page);
    }

    @PostMapping("/getMaterialDetailPageExport")
    @Operation(summary = "发运明细详情导出", description = "发运明细详情导出")
    @AsyncExportTask(name = "发运明细详情表", methodPath = "BzWaybillService.getMaterialDetailPageV2")
    public Result getMaterialDetailPageExport(@RequestBody PageQuery<BzWayBillPageDto> page) {
        return ResultUtil.success();
    }

    @Deprecated
    @GetMapping("/syncWaybillTransportInfo/retry")
    @Operation(summary = "同步运单发运信息到WMS", description = "同步运单发运信息到WMS")
    public void syncWaybillTransportInfo(@RequestParam("waybillCode") String waybillCode, @RequestParam("source") String source, @RequestParam("businessType") String businessType) {
        bzWaybillService.syncWaybillTransportInfo(waybillCode, source, businessType);
    }

    @PutMapping("/updateWaybill")
    @Operation(summary = "编辑运单", description = "编辑运单")
    public Result<String> updateWaybill(@RequestBody BzWaybillDto waybillDto) {
        return bzWaybillService.updateWaybill(waybillDto);
    }

    /**
     * 运单总览分页查询(新)
     *
     * @param page
     * @return
     */
    @PostMapping("/newPage")
    @Operation(summary = "运单总览分页查询(新)", description = "运单总览分页查询(新)")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Page<BzWayBillNewPageVo> getNewPage(@RequestBody PageQuery<BzWayBillPageDto> page) throws ExecutionException, InterruptedException {
        return bzWaybillService.getNewPage(page);
    }

    @PostMapping("/matchTransport")
    @Operation(summary = "匹配运输", description = "匹配运输")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Result<String> matchTransport(@RequestBody List<Long> ids) {
        return bzWaybillService.matchTransport(ids);
    }

    @PostMapping("/batchPrint")
    @Operation(summary = "批量打印", description = "批量打印")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Result<String> batchPrint(@RequestBody List<Long> ids) {
        return bzWaybillService.batchPrint(ids);
    }

    @GetMapping("/publishAllWaybill")
    @Operation(summary = "手动发布", description = "手动发布")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Result<Map<String, Integer>> publishAllWaybill() {
        Map<String, Integer> result = bzWaybillService.publishAllWaybill();
        return ResultUtil.success(result);
    }

    @GetMapping("/changeState")
    @Operation(summary = "修改运单状态（取消、挂起、确认收货）", description = "修改运单状态")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Result<String> changeWaybillState(@RequestParam Integer action, @RequestParam Long id) {
        return bzWaybillService.changeWaybillState(action, id);
    }

    @PostMapping("/mergeWaybillList")
    @Operation(summary = "获取合单列表", description = "获取合单列表")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Page<Map<String, Object>> mergeWaybillList(@RequestBody PageQuery<Long> page) {
        return bzWaybillService.mergeWaybillList(page);
    }

    @PostMapping("/mergeWaybill")
    @Operation(summary = "合单", description = "合单")
    @Parameter(name = "Client-Type", in = ParameterIn.HEADER, example = "PC", description = "客户端类型（PC：统一返回Result和PageResult对象）")
    public Result<Map> mergeWaybill(@RequestBody @Validated MergeWaybillDto dto) {
        Map map = bzWaybillService.combineBzWaybill(dto.getChildWaybillId(), dto.getParentWaybillId());
        return ResultUtil.success(map);
    }

}
