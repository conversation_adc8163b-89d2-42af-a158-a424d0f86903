package com.xiaopeng.halley.ascm.boot.controller.v1.waybill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import com.xiaopeng.halley.ascm.boot.config.interfaceConfig.GlobalResponseBody;
import com.xiaopeng.halley.ascm.boot.dto.BasePrintOutTaskSubmit;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.service.*;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@GlobalResponseBody
@RestController
@RequestMapping("/operation")
public class OperationController {
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private BzErpReqBoxService erpReqBoxService;
	@Resource
	private BzWaybillBoxRelService bzWaybillBoxRelService;
	@Resource
	private BasePrintOutTaskService basePrintOutTaskService;
	@Resource
	private BzErpReqMaterialRelService erpReqMaterialRelService;

	@GetMapping("/getAddressCache")
	@Operation(summary = "计算")
	public Result<Object> getAddressCache() {
		System.err.println(bzWaybillService.getAddressCache().asMap().size());
		return ResultUtil.success(bzWaybillService.getAddressCache().asMap());
	}

	@PostMapping("/updatePrintTask")
	@Operation(summary = "计算")
	public Result<Boolean> updatePrintTask(@RequestBody BasePrintOutTaskSubmit request) {
		List<Long> ids = request.getIds();
		basePrintOutTaskService.lambdaUpdate().in(CollUtil.isNotEmpty(ids), BasePrintOutTask::getId, ids)
				.set(BasePrintOutTask::getStatus, request.getStatus())
				.update();
		return ResultUtil.success();
	}

	@PostMapping("/operate")
	@Operation(summary = "计算")
	public Result<Boolean> operate(@RequestBody JSONObject request) {
		String table = request.getString("table");
		JSONArray jsonArray = request.getJSONArray("data");
		if ("bz_erp_req_box".equals(table)) {
			erpReqBoxService.saveOrUpdateBatch(jsonArray.toJavaList(BzErpReqBox.class));
		} else if ("bz_waybill_box_rel".equals(table)) {
			bzWaybillBoxRelService.saveOrUpdateBatch(jsonArray.toJavaList(BzWaybillBoxRel.class));
		} else if ("bz_erp_req_material_rel".equals(table)) {
			erpReqMaterialRelService.saveOrUpdateBatch(jsonArray.toJavaList(BzErpReqMaterialRel.class));
		}
		return ResultUtil.success();
	}

	@PostMapping("/updateSync")
	@Operation(summary = "计算")
	public Result<Boolean> updateSync(@RequestBody JSONObject request) {
		String field = request.getString("field");
		String value = request.getString("value");
		if (SqlInjectionUtils.check(field) || SqlInjectionUtils.check(value)) {
			return ResultUtil.failed("ERROR");
		}
		JSONArray jsonArray = request.getJSONArray("ids");
		if (jsonArray.isEmpty()) {
			return ResultUtil.failed("运单不存在");
		}
		List<Long> ids = jsonArray.toJavaList(Long.class);
		boolean update = bzWaybillService.update().in("id", ids)
				.set(field, value)
				.update();
		return ResultUtil.success(update);
	}

	@PostMapping("/updateTotalBox")
	@Operation(summary = "计算")
	public Result<String> updateTotalBox(@RequestBody JSONObject request) {
		String waybillCode = request.getString("waybillCode");
		Integer totalBox = request.getInteger("totalBox");
		if (waybillCode == null || totalBox == null) {
			return ResultUtil.failed();
		}
		BzWaybill waybill = bzWaybillService.getOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, waybillCode));
		if (waybill == null) {
			return ResultUtil.failed();
		}
		Integer oldVal = waybill.getTotalBox();
		waybill.setTotalBox(totalBox);
		bzWaybillService.updateById(waybill);
		return ResultUtil.success(StrUtil.format("totalBox {} -> {}", oldVal, totalBox));
	}
}
