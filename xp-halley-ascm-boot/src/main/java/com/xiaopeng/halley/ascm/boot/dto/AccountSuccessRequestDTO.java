package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountSuccessRequestDTO {
    @Schema(name = "operationCode", description = "获取文件编码")
    @NotNull(message = "编号不可为空")
    private String operationCode;
}
