package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/8/8 14:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class ActualTimeDto {
    @ColumnWidth(10)
    @ExcelProperty("运单编码")
    @Schema(name = "waybillCode", description = "运单编码")
    String waybillCode;

    @ColumnWidth(10)
    @ExcelProperty("实际到达日期")
    @Schema(name = "date", description = "实际到达日期")
    String date;

    @ColumnWidth(10)
    @ExcelProperty("实际到达时间")
    @Schema(name = "time", description = "实际到达时间")
    String time;

    @ExcelIgnore
    Date signTime;

    @ColumnWidth(14)
    @ExcelProperty("失败原因")
    @Schema(name = "failedReason", description = "失败原因")
    String failedReason;
}
