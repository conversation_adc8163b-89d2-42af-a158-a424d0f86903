package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;

@ApiModel(value = "asm 发运单信息")
@Data
public class AsmWayBillReq {

	@ApiModelProperty(value = "oem出库单号")
	private String oemOutCode;

	@ApiModelProperty(value = "运输类型")
	private String transType;

	@ApiModelProperty(value = "承运商提货时间")
	private Date carrierPickupTime;

	@ApiModelProperty(value = "承运商到店时间")
	private Date carrierArriveShopTime;

	@ApiModelProperty(value = "司机姓名")
	private String driverName;

	@ApiModelProperty(value = "联系方式")
	private String driverPhone;

	@ApiModelProperty(value = "物流状态")
	private String logisticsState;

	@ApiModelProperty(value = "物流单号")
	private String logisticsCode;

	@ApiModelProperty(value = "发运单号")
	private String waybillCode;

	@ApiModelProperty(value = "出库扫描时间")
	private Date outScanTime;

	@ApiModelProperty(value = "箱号信息")
	private List<BoxInfo> boxList = new ArrayList<>();

	@ApiModelProperty(value = "物流轨迹")
	private List<AsmWaybillFreshReq.LogisticsTrack> logisticsTrack;

	private String concatStr;

	@ApiModel(value = "箱明细")
	@Data
	public static class BoxInfo {
		@ApiModelProperty(value = "箱号")
		private String boxCode;

		@ApiModelProperty(value = "配件编码")
		private String partNo;

		@ApiModelProperty(value = "数量")
		private Integer qty;
	}
}