package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "发运信息更新")
public class AsmWaybillFreshReq {

    @ApiModelProperty(value = "发运单号")
    private String wayBillCode;

    @ApiModelProperty("到店时间")
    private Date carrierArriveShopTime;

    @ApiModelProperty(value = "物流单号")
    private String logisticsCode;

    @ApiModelProperty(value = "物流状态")
    private String logisticsState;

    @ApiModelProperty(value = "物流轨迹")
    private List<LogisticsTrack> logisticsTrack = new ArrayList<>();

    @Data
    @ApiModel(value = "物流轨迹")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LogisticsTrack {
        @ApiModelProperty(value = "轨迹")
        private String track;

        @ApiModelProperty(value = "时间")
        private Date logisticsTime;
    }
}