package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseAsyncExportRequestDto {

    @Schema(name = "name", description = "文件名称")
    private String name;

    @Schema(name = "createUserName", description = "导出人")
    private String createUserName;

    @Schema(name = "state", description = "导出状态，导出中：0，导出成功：1，导出失败：2")
    private Integer state;

    @Schema(name = "startCreateTime", description = "创建时间开始")
    private Date startCreateTime;

    @Schema(name = "endCreateTime", description = "创建时间结束")
    private Date endCreateTime;

}
