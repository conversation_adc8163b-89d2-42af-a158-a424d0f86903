package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseAsyncExportResponseDto {

    @Schema(name = "id", description = "id")
    private Long id;

    @Schema(name = "name", description = "文件名称")
    private String name;

    @Schema(name = "state", description = "导出状态，导出中：0，导出成功：1，导出失败：2")
    private Integer state;

    @Schema(name = "createUserName", description = "导出人")
    private String createUserName;

    @Schema(name = "createTimeShow", description = "开始时间")
    private String createTimeShow;

    @Schema(name = "updateTimeShow", description = "结束时间")
    private String updateTimeShow;

    @Schema(name = "downloadCount", description = "用户下载次数")
    private Integer downloadCount;

    @Schema(name = "remark", description = "导出日志")
    private String remark;
}
