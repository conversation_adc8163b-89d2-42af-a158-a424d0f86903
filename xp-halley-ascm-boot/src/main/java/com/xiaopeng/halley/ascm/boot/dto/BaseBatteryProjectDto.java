package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/13 11:10
 */
@Data
public class BaseBatteryProjectDto implements Serializable {
    @Schema(name = "id", description = "唯一id")
    private Long id;
    @Schema(name = "packNum", description = "PACK国标码")
    private String packNum;
    @Schema(name = "packNums", description = "PACK国标码-多")
    private List<String> packNums;
    @Schema(name = "packTypeNum", description = "PACK型号")
    private String packTypeNum;
    @Schema(name = "packNumNine", description = "PACK国标码(9位)")
    private String packNumNine;
    @Schema(name = "packTypeNums", description = "PACK型号-多")
    private List<String> packTypeNums;
    @Schema(name = "project", description = "项目")
    private String project;
    @Schema(name = "projects", description = "项目-多")
    private List<String> projects;
    @Schema(name = "updateOrAdd", description = "更新：0 添加：1")
    private Integer updateOrAdd;

}
