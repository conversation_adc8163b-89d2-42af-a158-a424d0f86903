package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：huqizhi
 * @Date：2023/9/22 15:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseBatteryProjectExportVo implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ColumnWidth(10)
    @ExcelProperty("PACK国标码")
    @Schema(name = "packNum", description = "PACK国标码")
    private String packNum;

    @ColumnWidth(10)
    @ExcelProperty("PACK国际码(9位)")
    @Schema(name = "packNumNine", description = "PACK国际码(9位)")
    private String packNumNine;

    @ColumnWidth(10)
    @ExcelProperty("PACK型号")
    @Schema(name = "packTypeNum", description = "PACK型号")
    private String packTypeNum;

    @ColumnWidth(10)
    @ExcelProperty("项目")
    @Schema(name = "project", description = "项目")
    private String project;

    @ColumnWidth(10)
    @ExcelProperty("失败原因")
    @Schema(name = "failedReason", description = "失败原因")
    private String failedReason;
}
