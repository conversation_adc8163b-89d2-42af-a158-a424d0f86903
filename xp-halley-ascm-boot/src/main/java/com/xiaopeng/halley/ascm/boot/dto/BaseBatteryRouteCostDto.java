package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 14:35
 */
@Data
public class BaseBatteryRouteCostDto {
    @Schema(name = "id", description = "唯一id")
    private Long id;
    @Schema(name = "repairCenterNum", description = "维修中心")
    private String repairCenterNum;
    @Schema(name = "dispatchCity", description = "起运城市")
    private String dispatchCity;
    @Schema(name = "dispatchCities", description = "起运城市-多")
    private List<String> dispatchCities;
    @Schema(name = "arrivalCity", description = "目的城市")
    private String arrivalCity;
    @Schema(name = "arrivalCities", description = "目的城市-多")
    private List<String> arrivalCities;
    @Schema(name = "logisticsProvider", description = "物流服务供应商")
    private String logisticsProvider;
    @Schema(name = "logisticsProviders", description = "物流服务供应商-多")
    private List<String> logisticsProviders;
    @Schema(name = "logisticsType", description = "物流类型")
    private String logisticsType;
    @Schema(name = "logisticsTypes", description = "物流类型-多")
    private List<String> logisticsTypes;
    @Schema(name = "carType", description = "车型")
    private Integer carType;
    @Schema(name = "routeName", description = "线路名称")
    private String routeName;
    @Schema(name = "routeDeliveryTime", description = "线路时效")
    private Double routeDeliveryTime;
    @Schema(name = "kilometersNum", description = "公里数")
    private Integer kilometersNum;
    @Schema(name = "expense", description = "费用")
    private Double expense;
    @Schema(name = "taxRates", description = "税率")
    private Double taxRates;
    @Schema(name = "updateOrAdd", description = "更新：0 添加：1")
    private Integer updateOrAdd;
}
