package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.CarTypeConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 16:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseBatteryRouteCostExportVo {

    @ExcelIgnore
    @Schema(name = "id", description = "唯一id")
    private Long id;
    @ColumnWidth(10)
    @ExcelProperty("维修中心")
    @Schema(name = "repairCenterNum", description = "维修中心")
    private String repairCenterNum;
    @ExcelIgnore
    @Schema(name = "repairCenterName", description = "维修中心描述")
    private String repairCenterName;
    @ColumnWidth(10)
    @ExcelProperty("启运城市")
    @Schema(name = "dispatchCity", description = "起运城市")
    private String dispatchCity;
    @ColumnWidth(10)
    @ExcelProperty("目标城市")
    @Schema(name = "arrivalCity", description = "目的城市")
    private String arrivalCity;
    @ColumnWidth(10)
    @ExcelProperty("物流供应商")
    @Schema(name = "logisticsProvider", description = "物流供应商")
    private String logisticsProvider;
    @ColumnWidth(10)
    @ExcelProperty("物流类型")
    @Schema(name = "logisticsType", description = "物流类型")
    private String logisticsType;
    @ColumnWidth(10)
    @ExcelProperty("线路名称")
    @Schema(name = "routeName", description = "线路名称")
    private String routeName;
    @ColumnWidth(10)
    @ExcelProperty("时效(H)")
    @Schema(name = "routeDeliveryTime", description = "线路时效")
    private Double routeDeliveryTime;
    @ColumnWidth(10)
    @ExcelProperty("公里数")
    @Schema(name = "kilometersNum", description = "公里数")
    private Double kilometersNum;
    @ColumnWidth(10)
    @ExcelProperty(value = "车型", converter = CarTypeConverter.class)
    @Schema(name = "carType", description = "车型")
    private Integer carType;
    @ExcelIgnore
    @Schema(name = "carType", description = "车型中文")
    private String carTypeChinese;
    @ColumnWidth(10)
    @ExcelProperty("费用")
    @Schema(name = "expense", description = "费用")
    private Double expense;
    @ColumnWidth(10)
    @ExcelProperty("税率")
    @Schema(name = "taxRates", description = "税率")
    private Double taxRates;
    @ExcelIgnore
    @Schema(name = "updateUserName", description = "修改人")
    private String updateUserName;
    @ExcelIgnore
    @Schema(name = "updateTime", description = "修改日期")
    private Date updateTime;
    @ColumnWidth(10)
    @ExcelProperty("失败原因")
    @Schema(name = "failedReason", description = "失败原因")
    private String failedReason;
}
