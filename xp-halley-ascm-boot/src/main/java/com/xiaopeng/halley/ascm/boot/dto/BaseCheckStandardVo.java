package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/9/9 13:54
 */
@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseCheckStandardVo {
    @ExcelIgnore
    public String id;
    /**
     * 校验编码
     */
    @ExcelProperty("检验标准编码")
    @ColumnWidth(15)
    public String checkCode;
    /**
     * 校验描述
     */
    @ExcelProperty("检验标准描述")
    @ColumnWidth(20)
    public String checkDesc;
    /**
     * 校验文本
     */
    @ExcelProperty("检验标准文本")
    @ColumnWidth(60)
    public String checkDocs;
    /**
     * 更新人（维护）
     */
    @ExcelProperty("最后一次维护人")
    @ColumnWidth(10)
    public String updateUserName;
    /**
     * 更新时间（维护）
     */
    @ExcelProperty("维护时间")
    @ColumnWidth(20)
    public Date updateTime;
}
