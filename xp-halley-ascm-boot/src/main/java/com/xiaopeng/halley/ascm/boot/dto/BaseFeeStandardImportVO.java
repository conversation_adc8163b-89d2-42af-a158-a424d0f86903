package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xiaopeng.halley.ascm.boot.excel.ExcelObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel("零担快递费用标准导入实体")
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class BaseFeeStandardImportVO extends ExcelObject {

    @ExcelProperty(value = "合同编号")
    @ApiModelProperty("合同编号")
    private String contractCode;

    @ExcelProperty(value = "发出城市")
    @ApiModelProperty("发出城市")
    private String originCity;

    @ExcelProperty(value = "目标城市")
    @ApiModelProperty("目标城市")
    private String destinationCity;

    @ExcelProperty(value = "是否主覆盖区域(是/否)")
    @ApiModelProperty("是否主覆盖区域")
    private String isMainArea;

    @ExcelProperty(value = "未税最低运费(元)")
    @ApiModelProperty("未税最低运费(元)")
    private String minFee;

    @ExcelProperty(value = "未税单价(元/M3)")
    @ApiModelProperty("未税单价(元/M3)")
    private String unitPrice;

    @ExcelProperty(value = "未税首重运费(元)")
    @ApiModelProperty("未税首重运费(元)")
    private String firstWeightFee;

    @ExcelProperty(value = "续重未税单价(元/KG)")
    @ApiModelProperty("续重未税单价(元/kg)")
    private String extraWeightPrice;

    @ExcelProperty(value = "错误提示")
    @TableField(exist = false)
    private String errorMsg;
}
