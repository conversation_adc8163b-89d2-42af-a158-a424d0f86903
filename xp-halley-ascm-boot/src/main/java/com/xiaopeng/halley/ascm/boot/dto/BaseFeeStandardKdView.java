package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "零担快递费用查询结果")
public class BaseFeeStandardKdView {

    @ExcelProperty("合同编号")
    @Schema(description = "合同编号")
    private String contractCode;

    @ExcelProperty("发货仓库")
    @Schema(description = "发货仓库")
    private String lgort;

    @ExcelProperty("发货仓库名称")
    @Schema(description = "发货仓库名称")
    private String lgobe;

    @ExcelProperty("发货城市")
    @Schema(description = "发货城市")
    private String warehouseCity;

    @ExcelProperty("门店编码")
    @Schema(description = "门店编码")
    private String shopCode;

    @ExcelProperty("门店城市")
    @Schema(description = "门店城市")
    private String shopCity;

    @ExcelProperty("门店名称")
    @Schema(description = "门店名称")
    private String shopName;

    @ExcelProperty("运单号")
    @Schema(description = "运单号")
    private String waybillCode;

    @ExcelProperty("交货单号")
    @Schema(description = "交货单号")
    private String deliveryOrderCode;

    @ExcelProperty("实际发运时间")
    @Schema(description = "实际发运时间")
    private LocalDateTime departureTime;

    @ExcelProperty("运输方式")
    @Schema(description = "运输方式")
    private String transportType;

    @ExcelProperty("订单类型")
    @Schema(description = "订单类型")
    private String orderType;

    @ExcelProperty("快递单号")
    @Schema(description = "快递单号")
    private String logisticsCode;

    @ExcelProperty("总体积(m³)")
    @Schema(description = "总体积")
    private BigDecimal totalVolume;

    @ExcelProperty("总重量(kg)")
    @Schema(description = "总重量")
    private BigDecimal totalWeight;

    @ExcelProperty("零担费用(元)")
    @Schema(description = "零担费用(元)")
    private BigDecimal fee1;

    @ExcelProperty("快递费用(元)")
    @Schema(description = "快递费用(元)")
    private BigDecimal fee2;

    @ExcelProperty("专车费用(元)")
    @Schema(description = "专车费用(元)")
    private BigDecimal fee3;

    @ExcelProperty("最终结算费用(元)")
    @Schema(description = "最终结算费用(元)")
    private BigDecimal finalFee;

    @ExcelProperty("未税最低运费(元)")
    @Schema(description = "未税最低运费(元)")
    private Double minFee;

    @ExcelProperty("未税单价(元/M³)")
    @Schema(description = "未税单价(元/M³)")
    private Double unitPrice;

    @ExcelProperty("未税首重运费(元)")
    @Schema(description = "未税首重运费(元)")
    private Double firstWeightFee;

    @ExcelProperty("续重未税单价(元/kg)")
    @Schema(description = "续重未税单价(元/kg)")
    private Double extraWeightPrice;
}