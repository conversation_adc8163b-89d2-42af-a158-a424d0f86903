package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "运单费用查询参数")
public class BaseFeeStandardQuery {

	@Schema(description = "实际发运时间")
	private Date departureTime;

	@Schema(description = "类型 1-零担快递 2-专车")
	private Integer type;

	@Schema(description = "门店编码")
	private String shopCode;

	@Schema(description = "门店编码集合")
	private List<String> shopCodeList;

	@Schema(description = "合同编号")
	private String contractCode;

	@Schema(description = "合作伙伴")
	private String partners;

	@Schema(description = "开始时间")
	private Date startTime;

	@Schema(description = "结束时间")
	private Date endTime;

	@Schema(description = "运单号")
	private String waybillCode;

	@Schema(description = "运单号列表")
	private List<String> waybillCodes;

	@Schema(description = "交货单号列表")
	private List<String> deliveryOrderCodes;

	@Schema(description = "运输方式")
	private String transportType;

	@Schema(description = "运输方式列表")
	private List<String> transportTypes;

	@Schema(description = "合作仓库")
	private String operationWarehouse;

	@Schema(description = "合作仓库集合")
	private List<String> operationWarehouses;

	@Schema(description = "仓库编码")
	private String lgort;

	@Schema(description = "始发城市列表")
	private List<String> originCities;

	@Schema(description = "始发城市")
	private String originCity;

	@Schema(description = "目的城市列表")
	private List<String> destinationCities;

	@Schema(description = "目的城市")
	private String destinationCity;

	@Schema(description = "城市模糊查询")
	private String cityKeyword;
}