package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.xiaopeng.halley.ascm.boot.excel.ExcelObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@ApiModel("零担快递费用标准导入实体")
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class BaseFeeStandardSpecialCarImportVO extends ExcelObject {

    @ExcelProperty(value = "合同编号")
    @ApiModelProperty("合同编号")
    private String contractCode;

    @ExcelProperty(value = "发出城市")
    @ApiModelProperty("发出城市")
    private String originCity;

    @ExcelProperty(value = "目标城市")
    @ApiModelProperty("目标城市")
    private String destinationCity;

    @ExcelProperty(value = "车型")
    @ApiModelProperty("车型")
    private String carType;

    @ExcelProperty(value = "距离(KM)")
    @ApiModelProperty("距离(KM)")
    private String distance;

    @ExcelProperty(value = "未税单价(元/车)")
    @ApiModelProperty("未税单价(元/车)")
    private String unitPrice;
    
    @ExcelProperty(value = "错误提示")
    @TableField(exist = false)
    private String errorMsg;
}
