package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ExcelIgnoreUnannotated
public class BaseFeeStandardSpecialCarVO {

	@ApiModelProperty("主键")
	private Long id;

	@ExcelProperty("合同编号")
	@ApiModelProperty("合同编号")
	private String contractCode;

	@ExcelProperty("合同生效时间")
	@ApiModelProperty("合同生效时间")
	private LocalDate effectiveTime;

	@ExcelProperty("合同结束时间")
	@ApiModelProperty("合同结束时间")
	private LocalDate endTime;

	@ExcelProperty("运营仓库")
	@ApiModelProperty("运营仓库")
	private String operationWarehouse;

	@ExcelProperty("合作伙伴")
	@ApiModelProperty("合作伙伴")
	private String partners;

	@ExcelProperty("发出城市")
	@ApiModelProperty("发出城市")
	private String originCity;

	@ExcelProperty("目标城市")
	@ApiModelProperty("目标城市")
	private String destinationCity;

	@ExcelProperty(value = "距离(KM)")
	@ApiModelProperty("距离(KM)")
	private String distance;

	@ExcelProperty(value = "车型")
	@ApiModelProperty("车型")
	private String carType;

	@ExcelProperty("未税单价(元/车)")
	@ApiModelProperty("未税单价(元/车)")
	private Double unitPrice;

	@ApiModelProperty("类型")
	private Integer type;
}
