package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "零担快递费用查询结果")
public class BaseFeeStandardSpecialCarView {

    @ExcelProperty("合同编号")
    @Schema(description = "合同编号")
    private String contractCode;

    @ExcelProperty("发货仓库")
    @Schema(description = "发货仓库")
    private String lgort;

    @ExcelProperty("发货仓库名称")
    @Schema(description = "发货仓库名称")
    private String lgobe;

    @ExcelProperty("发货城市")
    @Schema(description = "发货城市")
    private String warehouseCity;

    @ExcelProperty("门店编码")
    @Schema(description = "门店编码")
    private String shopCode;

    @ExcelProperty("门店城市")
    @Schema(description = "门店城市")
    private String shopCity;

    @ExcelProperty("门店名称")
    @Schema(description = "门店名称")
    private String shopName;

    @ExcelProperty("运单号")
    @Schema(description = "运单号")
    private String waybillCode;

    @ExcelProperty("实际发运时间")
    @Schema(description = "实际发运时间")
    private LocalDateTime departureTime;
    
    @ExcelProperty("车牌号")
    @Schema(description = "车牌号")
    private String carPlate;

    @ExcelProperty("司机姓名")
    @Schema(description = "司机姓名")
    private String driverName;

    @ExcelProperty("电话")
    @Schema(description = "电话")
    private String driverPhone;

    @ExcelProperty("车型")
    @Schema(description = "车型")
    private String carType;

    @ExcelProperty("未税单价(元/车)")
    @Schema(description = "未税单价(元/车)")
    private Double unitPrice;

    @ExcelProperty("定位")
    @Schema(description = "定位")
    private String address;
}