package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ExcelIgnoreUnannotated
public class BaseFeeStandardVO {

	@ApiModelProperty("主键")
	private Long id;

	@ExcelProperty("运营仓库")
	@ApiModelProperty("运营仓库")
	private String operationWarehouse;

	@ExcelProperty("合作伙伴")
	@ApiModelProperty("合作伙伴")
	private String partners;

	@ExcelProperty("合同生效时间")
	@ApiModelProperty("合同生效时间")
	private LocalDate effectiveTime;

	@ExcelProperty("合同结束时间")
	@ApiModelProperty("合同结束时间")
	private LocalDate endTime;

	@ExcelProperty("合同编号")
	@ApiModelProperty("合同编号")
	private String contractCode;

	@ExcelProperty("发出城市")
	@ApiModelProperty("发出城市")
	private String originCity;

	@ExcelProperty("目标城市")
	@ApiModelProperty("目标城市")
	private String destinationCity;

	@ExcelProperty("是否主覆盖区域")
	@ApiModelProperty("是否主覆盖区域")
	private String isMainArea;

	@ExcelProperty("未税最低运费(元)")
	@ApiModelProperty("未税最低运费(元)")
	private Double minFee;

	@ExcelProperty("未税单价(元/M³)")
	@ApiModelProperty("未税单价(元/M³)")
	private Double unitPrice;

	@ExcelProperty("未税首重运费(元)")
	@ApiModelProperty("未税首重运费(元)")
	private Double firstWeightFee;

	@ExcelProperty("续重未税单价(元/kg)")
	@ApiModelProperty("续重未税单价(元/kg)")
	private Double extraWeightPrice;

	@ExcelProperty(value = "距离(KM)")
	@ApiModelProperty("距离(KM)")
	private String distance;

	@ExcelProperty(value = "车型")
	@ApiModelProperty("车型")
	private String carType;

	@ApiModelProperty("类型")
	private Integer type;
}
