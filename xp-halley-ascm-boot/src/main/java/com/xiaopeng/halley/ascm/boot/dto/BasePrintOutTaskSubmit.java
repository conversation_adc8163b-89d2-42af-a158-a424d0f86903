package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class BasePrintOutTaskSubmit {
	@Schema(description = "任务ID集合")
	private List<Long> ids;

	@Schema(description = "任务ID")
	private Long id;

	@Schema(description = "打印状态 0-待打印 1-打印中 2-打印完成 3-打印失败")
	private Integer status;

	@Schema(description = "状态码", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String state;

	@Schema(description = "错误消息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
	private String message;
}
