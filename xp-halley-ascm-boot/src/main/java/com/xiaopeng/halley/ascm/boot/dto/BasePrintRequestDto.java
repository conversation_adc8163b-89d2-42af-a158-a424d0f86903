package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasePrintRequestDto {

    @Schema(name = "id", description = "打印序号")
    private String id;

    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;

    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;

    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    @Schema(name = "status", description = "打印状态，枚举：0：待打印，1：打印中，2：打印完成，3：打印失败")
    private Integer status;

}
