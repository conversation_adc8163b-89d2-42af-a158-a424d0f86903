package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BasePrintResponseDto {

    @Schema(name = "id", description = "打印序号")
    private String id;

    @Schema(description = "触发端")
    private String triggerClient;

    @Schema(description = "打印机名称")
    private String printerName;

    @Schema(name = "route", description = "路线")
    private String route;

    @Schema(description = "pdf文件id")
    private Long baseFileId;

    @Schema(description = "pdf文件名称")
    private String fileName;

    @Schema(description = "pdf文件url")
    private String fileUrl;

    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;

    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;

    @Schema(name = "orderType", description = "订单类型")
    private String orderType;

    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;

    @Schema(name = "shopName", description = "门店名称")
    private String shopName;

    @Schema(name = "status", description = "打印状态，枚举：0：待打印，1：打印中，2：打印完成，3：打印失败")
    private Integer status;

    @Schema(name = "createTimeShow", description = "打印时间")
    private String createTimeShow;

    @Schema(name = "createUserName", description = "打印人")
    private String createUserName;

    @Schema(name = "createTime", description = "创建时间")
    private Date createTime;

    @Schema(name = "count", description = "打印次数")
    private String count;

}
