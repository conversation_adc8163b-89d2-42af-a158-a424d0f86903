package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 14:28
 */
@Data
public class BaseRepairCenterDto {

    @Schema(name = "id", description = "唯一id")
    private Long id;
    @Schema(name = "repairCenterNum", description = "维修中心编码")
    private String repairCenterNum;
    @Schema(name = "repairCenterNums", description = "维修中心编码-多")
    private List<String> repairCenterNums;
    @Schema(name = "repairCenterName", description = "维修中心名称")
    private String repairCenterName;
    @Schema(name = "repairCenterNames", description = "维修中心名称-多")
    private List<String> repairCenterNames;
    @Schema(name = "repairCenterRemark", description = "维修中心简称")
    private String repairCenterRemark;
    @Schema(name = "repairCenterProvince", description = "维修中心省份")
    private String repairCenterProvince;
    @Schema(name = "repairCenterProvinces", description = "维修中心省份-多")
    private List<String> repairCenterProvinces;
    @Schema(name = "repairCenterCity", description = "维修中心城市")
    private String repairCenterCity;
    @Schema(name = "repairCenterCities", description = "维修中心城市-多")
    private List<String> repairCenterCities;
    @Schema(name = "repairCenterAddress", description = "维修中心地址")
    private String repairCenterAddress;
    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;
    @Schema(name = "contactNums", description = "联系电话-多")
    private List<String> contactNums;
    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;
    @Schema(name = "contactPeople", description = "联系人-多")
    private List<String> contactPeople;
    @Schema(name = "status", description = "状态")
    private Integer status;
    @Schema(name = "statrCreateTime", description = "创建时间-开始")
    private Date statrCreateTime;
    @Schema(name = "endCreateTime", description = "创建时间-结束")
    private Date endCreateTime;
    @Schema(name = "updateOrAdd", description = "更新：0 添加：1")
    private Integer updateOrAdd;

}
