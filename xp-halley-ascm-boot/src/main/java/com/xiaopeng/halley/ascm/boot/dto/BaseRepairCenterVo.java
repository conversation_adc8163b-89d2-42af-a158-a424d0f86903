package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 15:16
 */
@ApiModel("分页查询返回类")
@Data
public class BaseRepairCenterVo {
    @Schema(name = "id", description = "唯一id")
    private Long id;
    @Schema(name = "repairCenterNum", description = "维修中心编码")
    private String repairCenterNum;
    @Schema(name = "repairCenterName", description = "维修中心名称")
    private String repairCenterName;
    @Schema(name = "repairCenterRemark", description = "维修中心简称")
    private String repairCenterRemark;
    @Schema(name = "repairCenterProvince", description = "维修中心省份")
    private String repairCenterProvince;
    @Schema(name = "repairCenterCity", description = "维修中心城市")
    private String repairCenterCity;
    @Schema(name = "repairCenterCity", description = "维修中心地址")
    private String repairCenterAddress;
    @Schema(name = "repairCenterCity", description = "联系电话")
    private String contactNum;
    @Schema(name = "repairCenterCity", description = "联系人")
    private String contactPerson;
    @Schema(name = "repairCenterCity", description = "状态")
    private Integer status;
    @Schema(name = "repairCenterCity", description = "创建人id")
    private String createUserId;
    @Schema(name = "repairCenterCity", description = "创建人")
    private String createUserName;
    @Schema(name = "repairCenterCity", description = "修改人id")
    private String updateUserId;
    @Schema(name = "repairCenterCity", description = "更新人")
    private String updateUserName;
    @Schema(name = "repairCenterCity", description = "创建时间")
    private Date createTime;
    @Schema(name = "repairCenterCity", description = "更新时间")
    private Date updateTime;
}
