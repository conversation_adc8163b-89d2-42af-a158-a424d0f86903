package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/14 13:59
 */
@Data
public class BaseShopDto {

    @Schema(name = "id", description = "唯一id")
    private String id;

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;

    @Schema(name = "shopCodes", description = "门店编码-多")
    private List<String> shopCodes;

    @Schema(name = "shopName", description = "门店名称")
    private String shopName;

    @Schema(name = "shopNames", description = "门店名称-多")
    private List<String> shopNames;

    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;

    @Schema(name = "shopProvinces", description = "门店省份-多")
    private List<String> shopProvinces;

    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;

    @Schema(name = "shopCities", description = "门店城市-多")
    private List<String> shopCities;

    @Schema(name = "shopRemark", description = "门店简称")
    private String shopRemark;

    @Schema(name = "shopRemarks", description = "门店简称-多")
    private List<String> shopRemarks;

    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;

    @Schema(name = "shopAddresses", description = "门店地址-多")
    private List<String> shopAddresses;

    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;

    @Schema(name = "lng", description = "经度")
    private Double lng;//经度

    @Schema(name = "lat", description = "纬度")
    private Double lat;//纬度

    @Schema(name = "status", description = "状态")
    private String status;

    @Schema(name = "contactPeople", description = "联系人-多")
    private List<String> contactPeople;

    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;

    @Schema(name = "xlShopCode", description = "骁龙门店编码")
    private String xlShopCode;

    @Schema(name = "xlShopCodes", description = "骁龙门店编码-多")
    private List<String> xlShopCodes;

    @Schema(name = "xlShopRemark", description = "骁龙门店简称")
    private String xlShopRemark;

    @Schema(name = "xlShopRemarks", description = "骁龙门店简称-多")
    private List<String> xlShopRemarks;

    @Schema(name = "fenceRange", description = "围栏范围")
    private String fenceRange;

    @Schema(name = "fenceRanges", description = "围栏范围-多")
    private List<String> fenceRanges;

    @Schema(name = "startCreateTime", description = "创建时间开始")
    private Date startCreateTime;

    @Schema(name = "endCreateTime", description = "创建时间结束")
    private Date endCreateTime;

    @Schema(name = "cargoContact", description = "装运联系人")
    private String cargoContact;//装货联系人

    @Schema(name = "cargoContactPhone", description = "装运联系人电话")
    private String cargoContactPhone;//装货联系人电话

    @Schema(name = "region", description = "区域")
    private String region;//区域

    @Schema(name = "updateOrAdd", description = "更新：0 添加：1")
    private Integer updateOrAdd;
}
