package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author：huqizhi
 * @Date：2023/9/14 13:45
 */
@Data
public class BaseShopVo {

    @Schema(name = "id", description = "唯一id")
    private String id;

    @Schema(name = "xlShopCode", description = "骁龙门店编码")
    private String xlShopCode;

    @Schema(name = "xlShopRemark", description = "骁龙门店简称")
    private String xlShopRemark;

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;

    @Schema(name = "shopName", description = "门店名称")
    private String shopName;

    @Schema(name = "shopRemark", description = "门店简称")
    private String shopRemark;

    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;

    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;

    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;

    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;

    @Schema(name = "lat", description = "纬度")
    private String lat;

    @Schema(name = "lng", description = "经度")
    private String lng;

    @Schema(name = "fenceRange", description = "围栏范围")
    private String fenceRange;

    @Schema(name = "status", description = "状态")
    private String status;

    @Schema(name = "createUserName", description = "创建人")
    private String createUserName;

    @Schema(name = "updateUserName", description = "更新人")
    private String updateUserName;

    @Schema(name = "createTime", description = "创建时间")
    private String createTime;

    @Schema(name = "updateTime", description = "更新时间")
    private String updateTime;

    @Schema(name = "dynamicUpdate", description = "是否动态更新(1-是 0-否)")
    private String dynamicUpdate;

    @Schema(name = "cargoContact", description = "装运联系人")
    private String cargoContact;//装货联系人

    @Schema(name = "cargoContactPhone", description = "装运联系人电话")
    private String cargoContactPhone;//装货联系人电话

    @Schema(name = "region", description = "区域")
    private String region;//区域

}
