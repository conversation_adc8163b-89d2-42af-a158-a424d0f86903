package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/8/7 10:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseTransportTimelinessVo implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ExcelIgnore
    @Schema(name = "id", description = "门店编码")
    private Long id;

    @ColumnWidth(10)
    @ExcelProperty("发出城市")
    @Schema(name = "warehouseCity", description = "发出城市")
    private String warehouseCity;

    @ColumnWidth(10)
    @ExcelProperty("接收城市")
    @Schema(name = "shopCity", description = "接收城市")
    private String shopCity;

    @ColumnWidth(10)
    @ExcelProperty("运输方式")
    @Schema(name = "transportType", description = "门店编码")
    private String transportType;

    @ColumnWidth(10)
    @ExcelProperty("运输时效（小时）")
    @Schema(name = "transportTime", description = "运输时效")
    private String transportTime;

    @ColumnWidth(10)
    @ExcelProperty("线路名称")
    @Schema(name = "routeName", description = "线路名称")
    private String routeName;

    @ExcelIgnore
    @Schema(name = "updateUserId", description = "门店编码")
    private String updateUserId;

    @ColumnWidth(10)
    @ExcelProperty("修改人")
    @Schema(name = "updateUserName", description = "门店编码")
    private String updateUserName;

    @ColumnWidth(10)
    @ExcelProperty("修改时间")
    @Schema(name = "updateTime", description = "门店编码")
    private Date updateTime;

    @ExcelIgnore
    @Schema(name = "failedReason", description = "失败原因")
    private String failedReason;

}
