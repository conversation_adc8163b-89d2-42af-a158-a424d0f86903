package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWarehouseInsertResponseDto {

    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;

    @Schema(name = "warehouseType", description = "仓库类型 1：WMS，2：捷富凯，3：京东，4：骁龙。")
    private Integer warehouseType;

    @Schema(name = "warehouseRemark", description = "仓库简称")
    private String warehouseRemark;

    @Schema(name = "warehouseProvince", description = "省份")
    private String warehouseProvince;

    @Schema(name = "warehouseCity", description = "城市")
    private String warehouseCity;

    @Schema(name = "warehouseAddress", description = "仓库地址")
    private String warehouseAddress;

    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;

    @Schema(name = "status", description = "状态 ， 0：启用，1：停用")
    private Integer status;

}
