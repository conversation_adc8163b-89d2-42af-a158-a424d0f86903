package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWarehouseRequestDto {

    @Schema(name = "status", description = "仓库状态")
    private Integer status;

    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;

    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;

    @Schema(name = "warehouseCity", description = "城市")
    private String warehouseCity;

    @Schema(name = "warehouseProvince", description = "省份")
    private String warehouseProvince;

    @Schema(name = "startCreateTime", description = "创建时间开始")
    private Date startCreateTime;

    @Schema(name = "endCreateTime", description = "创建时间结束")
    private Date endCreateTime;

}
