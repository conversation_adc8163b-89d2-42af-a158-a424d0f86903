package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWarehouseResponseDto {

    @Schema(name = "id", description = "id")
    private Long id;

    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;

    @Schema(name = "warehouseType", description = "仓库类型")
    private String warehouseType;

    @Schema(name = "warehouseRemark", description = "仓库简称")
    private String warehouseRemark;

    @Schema(name = "warehouseProvince", description = "省份")
    private String warehouseProvince;

    @Schema(name = "warehouseCity", description = "城市")
    private String warehouseCity;

    @Schema(name = "warehouseAddress", description = "仓库地址")
    private String warehouseAddress;

    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;

    @Schema(name = "status", description = "状态 ， 0：启用，1：停用")
    private Integer status;

    @Schema(name = "createUserName", description = "创建人")
    private String createUserName;

    @Schema(name = "updateUserName", description = "更新人")
    private String updateUserName;

    @Schema(name = "createTimeShow", description = "创建时间（显示）")
    private String createTimeShow;

    @Schema(name = "updateTimeShow", description = "更新时间（显示）")
    private String updateTimeShow;

}
