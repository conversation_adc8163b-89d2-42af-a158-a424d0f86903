package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWarehouseStopAndOpenResponseDto {

    @Schema(name = "id", description = "id")
    private Long id;

    @Schema(name = "status", description = "状态 ， 0：启用，1：停用")
    private Integer status;

}
