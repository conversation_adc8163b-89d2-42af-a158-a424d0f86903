package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.BaseWaybillRulesIsSpeciallyConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseWayBillRulesImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ColumnWidth(10)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;//门店编码

    @ColumnWidth(10)
    @ExcelProperty("仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;//仓库编码
    @ColumnWidth(15)
    @ExcelProperty("线路")
    @Schema(name = "path", description = "线路")
    private String path;//线路

    @ColumnWidth(22)
    @ExcelProperty("线路时效")
    @Schema(name = "pathExpiry", description = "线路时效")
    private Integer pathExpiry;//线路时效

    @ColumnWidth(8)
    @ExcelProperty("订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;//订单类型

    @ColumnWidth(8)
    @ExcelProperty("运输类型")
    @Schema(name = "transportType", description = "运输类型")
    private String transportType;//运输类型

    @ColumnWidth(12)
    @ExcelProperty("承运商编码")
    @Schema(name = "carrierCode", description = "承运商编码")
    private String carrierCode;//承运商编码

    @ColumnWidth(12)
    @ExcelProperty("承运商名称")
    @Schema(name = "carrierName", description = "承运商名称")
    private String carrierName;//承运商名称

    @ColumnWidth(5)
    @ExcelProperty(value = "是否专用", converter = BaseWaybillRulesIsSpeciallyConverter.class)
    @Schema(name = "isSpecially", description = "是否专用")
    private Integer isSpecially;//是否专用 0：否 1：是

    @ColumnWidth(10)
    @ExcelProperty("备注")
    @Schema(name = "remark", description = "备注")
    private String remark;//备注

    @ExcelIgnore
    private String shopProvince;//门店省份

    @ExcelIgnore
    private String shopCity;//门店城市

}
