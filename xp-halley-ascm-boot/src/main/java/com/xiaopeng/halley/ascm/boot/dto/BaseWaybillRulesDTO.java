package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.BaseWaybillRulesIsSpeciallyConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseWaybillRulesDTO implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ExcelIgnore
    @Schema(name = "id", description = "ID")
    private Long id;//主键id

    @ColumnWidth(10)
    @ExcelProperty("仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;//仓库编码

    @ColumnWidth(18)
    @ExcelProperty("仓库名称")
    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;//仓库名称

    @ColumnWidth(22)
    @ExcelProperty("仓库地址")
    @Schema(name = "warehouseAddress", description = "仓库地址")
    private String warehouseAddress;//仓库地址

    @ColumnWidth(15)
    @ExcelProperty("订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;//订单类型

    @ColumnWidth(15)
    @ExcelProperty("运输类型")
    @Schema(name = "transportType", description = "运输类型")
    private String transportType;//运输类型

    @ColumnWidth(22)
    @ExcelProperty("线路时效")
    @Schema(name = "pathExpiry", description = "线路时效")
    private Integer pathExpiry;//线路时效

    @ColumnWidth(15)
    @ExcelProperty("线路")
    @Schema(name = "path", description = "线路")
    private String path;//线路

    @ColumnWidth(20)
    @ExcelProperty("线路描述")
    @Schema(name = "pathRemark", description = "线路描述")
    private String pathRemark;

    @ColumnWidth(12)
    @ExcelProperty("承运商编码")
    @Schema(name = "carrierCode", description = "承运商编码")
    private String carrierCode;//承运商编码

    @ColumnWidth(12)
    @ExcelProperty("承运商名称")
    @Schema(name = "carrierName", description = "承运商名称")
    private String carrierName;//承运商名称

    @ColumnWidth(10)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;//门店编码

    @ColumnWidth(10)
    @ExcelProperty("门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;//门店名称

    @ColumnWidth(20)
    @ExcelProperty("门店地址")
    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;//门店地址
//
//    @ColumnWidth(5)
//    @ExcelProperty("状态")
//    @Schema(name = "status",description = "状态")
//    private Integer status;//状态

    @ColumnWidth(15)
    @ExcelProperty(value = "是否专用", converter = BaseWaybillRulesIsSpeciallyConverter.class)
    @Schema(name = "isSpecially", description = "是否专用")
    private Integer isSpecially;//是否专用 0：否 1：是

    @ColumnWidth(20)
    @ExcelProperty("门店省份")
    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;

    @ColumnWidth(20)
    @ExcelProperty("门店城市")
    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;

//    @TableField("create_user_id")
//    private String createUserId;//创建人id
//
//    @TableField("create_user_name")
//    private String createUserName;//创建人
//
//    @TableField("update_user_id")
//    private String updateUserId;//修改人id
//
//    @TableField("update_user_name")
//    private String updateUserName;//更新人
//
//    @TableField("is_delete")
//    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）
//
//    @TableField("create_time")
//    private Date createTime;//创建时间
//
//    @TableField("update_time")
//    private Date updateTime;//更新时间

}
