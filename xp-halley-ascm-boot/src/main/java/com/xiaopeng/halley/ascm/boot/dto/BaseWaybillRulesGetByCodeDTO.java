package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWaybillRulesGetByCodeDTO {

    @Schema(name = "type", description = "1:门店 2:仓库 3:承运商")
    Integer type;

    @Schema(name = "code", description = "编码")
    String code;

}
