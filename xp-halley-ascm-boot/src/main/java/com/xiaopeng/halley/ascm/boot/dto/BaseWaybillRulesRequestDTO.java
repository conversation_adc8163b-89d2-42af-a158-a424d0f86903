package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWaybillRulesRequestDTO {

    @Schema(name = "shopCodeList", description = "门店编码")
    private List<String> shopCodeList;

    @Schema(name = "shopName", description = "门店名称")
    private String shopName;

    @Schema(name = "orderType", description = "订单类型")
    private String orderType;

    @Schema(name = "transportType", description = "运输类型")
    private String transportType;

    @Schema(name = "carrierCodeList", description = "承运商编码")
    private List<String> carrierCodeList;

    @Schema(name = "carrierName", description = "承运商名称")
    private String carrierName;

}
