package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/20 14:08
 */
@Data
public class BatteryWayBillDto {

    @Schema(name = "id", description = "唯一id")
    private String id;

    @Schema(name = "repairCenterNum", description = "维修中心")
    private String repairCenterNum;

    @Schema(name = "repairDate", description = "报修日期")
    private Date repairDate;

    @Schema(name = "startRepairDate", description = "报修日期-开始")
    private Date startRepairDate;

    @Schema(name = "endRepairDate", description = "报修日期-结束")
    private Date endRepairDate;

    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;

    @Schema(name = "waybillCodes", description = "运单编号-多")
    private List<String> waybillCodes;

    @Schema(name = "packNum", description = "电池溯源码")
    private String packNum;

    @Schema(name = "packNum", description = "电池溯源码-多")
    private List<String> packNums;

    @Schema(name = "region", description = "区域")
    private String region;

    @Schema(name = "regions", description = "区域-多")
    private List<String> regions;

    @Schema(name = "waybillStatus", description = "运单状态（0-新建，1-已分配，2-已装运，3-已送到，4-已付款，5-关闭）")
    private Integer waybillStatus;

    @Schema(name = "xlShopCode", description = "骁龙门店编号")
    private String xlShopCode;

    @Schema(name = "xlShopCodes", description = "骁龙门店编号-多")
    private List<String> xlShopCodes;

    @Schema(name = "vinCode", description = "车架编码")
    private String vinCode;

    @Schema(name = "vinCodes", description = "车架编码-多")
    private List<String> vinCodes;

    @Schema(name = "distanceTravelled", description = "行驶里程")
    private Integer distanceTravelled;

    @Schema(name = "faultSummary", description = "故障简述")
    private String faultSummary;

    @Schema(name = "faultClassification", description = "故障分类(0-质量，1-非质量)")
    private Integer faultClassification;

    @Schema(name = "dispatchCity", description = "启动城市")
    private String dispatchCity;

    @Schema(name = "arrivalCity", description = "目的城市")
    private String arrivalCity;

    @Schema(name = "logisticsType", description = "物流类型")
    private Integer logisticsType;

    @Schema(name = "logisticsArrangementTime", description = "物流安排时间")
    private Date logisticsArrangementTime;

    @Schema(name = "startLogisticsArrangementTime", description = "物流安排时间-开始")
    private Date startLogisticsArrangementTime;

    @Schema(name = "endLogisticsArrangementTime", description = "物流安排时间-结束")
    private Date endLogisticsArrangementTime;

    @Schema(name = "logisticsProvider", description = "物流供应商")
    private String logisticsProvider;

    @Schema(name = "isSamePackage", description = "是否同包")
    private Integer isSamePackage;

    @Schema(name = "packageVin", description = "同包vin号")
    private String packageVin;

    @Schema(name = "carType", description = "车型")
    private String carType;

    @Schema(name = "pickupDate", description = "提货日期")
    private Date pickupDate;

    @Schema(name = "driverName", description = "司机姓名")
    private String driverName;

    @Schema(name = "driverPhone", description = "司机手机号")
    private String driverPhone;

    @Schema(name = "plateNumber", description = "车牌号")
    private String plateNumber;

    @Schema(name = "remark", description = "备注")
    private String remark;

    @Schema(name = "actualPaymentAmount", description = "实付费用")
    private Double actualPaymentAmount;

    @Schema(name = "deliveryTime", description = "送达时间")
    private Date deliveryTime;

    @Schema(name = "route_delivery_time", description = "路线时效")
    private String routeDeliveryTime;

    @Schema(name = "kilometers_num", description = "公里数")
    private String kilometersNum;
}
