package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.FaultClassificationConverter;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.TransportationTypeConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 15:22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BatteryWayBillImportVo implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ColumnWidth(10)
    @ExcelProperty("维修中心")
    @Schema(name = "repairCenterNum", description = "维修中心")
    private String repairCenterNum;

    @ColumnWidth(10)
    @ExcelProperty("电池溯源编码")
    @Schema(name = "packNum", description = "电池溯源编码")
    private String packNum;
    // 日期类型可以直接使用String 落库不影响
    @ColumnWidth(10)
    @ExcelProperty("报修日期")
    @Schema(name = "repairDate", description = "保修日期")
    private String repairDate;

    @ColumnWidth(10)
    @ExcelProperty("骁龙门店编号")
    @Schema(name = "xlShopCode", description = "骁龙门店编号")
    private String xlShopCode;

    @ColumnWidth(10)
    @ExcelProperty("车架编码")
    @Schema(name = "vinCode", description = "车架编码")
    private String vinCode;

    @ColumnWidth(10)
    @ExcelProperty("行驶里程")
    @Schema(name = "distanceTravelled", description = "行驶里程")
    private Double distanceTravelled;

    @ColumnWidth(10)
    @ExcelProperty("故障简述")
    @Schema(name = "faultSummary", description = "故障简述")
    private String faultSummary;

    @ColumnWidth(10)
    @ExcelProperty(value = "故障分类", converter = FaultClassificationConverter.class)
    @Schema(name = "faultClassification", description = "故障分类")
    private Integer faultClassification;

    @ColumnWidth(10)
    @ExcelProperty(value = "运输类型", converter = TransportationTypeConverter.class)
    @Schema(name = "transportationType", description = "运输类型")
    private Integer transportationType;

    @ColumnWidth(10)
    @ExcelProperty("失败原因")
    @Schema(name = "failedReason", description = "失败原因")
    private String failedReason;

    @ExcelIgnore
    @Schema(name = "project", description = "项目字段（用于回填）")
    private String project;

    @ExcelIgnore
    @Schema(name = "dispatchCity", description = "发运城市")
    private String dispatchCity;

    @ExcelIgnore
    @Schema(name = "dispatchCityAddress", description = "发运城市地址")
    private String dispatchAddress;

    @ExcelIgnore
    @Schema(name = "dispatchContacts", description = "发运城市联系人")
    private String loadingContact;

    @ExcelIgnore
    @Schema(name = "dispatchContactPhone", description = "发运城市联系人电话")
    private String loadingPhone;

    @ExcelIgnore
    @Schema(name = "arrivalCity", description = "收货城市")
    private String arrivalCity;

    @ExcelIgnore
    @Schema(name = "arrivalCityAddress", description = "收货城市地址")
    private String arrivalAddress;

    @ExcelIgnore
    @Schema(name = "arrivalContacts", description = "收货城市联系人")
    private String unloadingContact;

    @ExcelIgnore
    @Schema(name = "arrivalContactPhone", description = "收货城市联系人电话")
    private String unloadingPhone;
}
