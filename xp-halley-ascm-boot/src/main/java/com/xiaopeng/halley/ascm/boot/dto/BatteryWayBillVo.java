package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/9/20 14:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BatteryWayBillVo implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @ExcelIgnore
    @Schema(name = "id", description = "唯一id")
    private Long id;

    @ColumnWidth(10)
    @ExcelProperty("维修中心编号")
    @Schema(name = "repairCenterNum", description = "维修中心编号")
    private String repairCenterNum;

    @ColumnWidth(10)
    @ExcelProperty("维修中心简称")
    @Schema(name = "repairCenterName", description = "维修中心简称")
    private String repairCenterName;

    @ColumnWidth(10)
    @ExcelProperty("报修日期")
    @Schema(name = "repairDate", description = "报修日期")
    private Date repairDate;

    @ColumnWidth(10)
    @ExcelProperty("运单号")
    @Schema(name = "repairDate", description = "运单号")
    private String waybillCode;

    @ColumnWidth(10)
    @ExcelProperty(value = "运单状态", converter = WaybillStatusConverter.class)
    @Schema(name = "waybillStatus", description = "运单状态")
    private Integer waybillStatus;

    @ColumnWidth(10)
    @ExcelProperty("ERP门店编码")
    @Schema(name = "shopCode", description = "ERP门店编码")
    private String shopCode;

    @ColumnWidth(10)
    @ExcelProperty("骁龙门店编码")
    @Schema(name = "xlShopCode", description = "骁龙门店编码")
    private String xlShopCode;

    @ColumnWidth(10)
    @ExcelProperty("门店描述")
    @Schema(name = "shopRemark", description = "门店描述")
    private String shopRemark;

    @ColumnWidth(10)
    @ExcelProperty("区域")
    @Schema(name = "region", description = "区域")
    private String region;

    @ColumnWidth(10)
    @ExcelProperty("电池溯源")
    @Schema(name = "packNum", description = "电池溯源")
    private String packNum;

    @ColumnWidth(10)
    @ExcelProperty("车架号")
    @Schema(name = "packNum", description = "车架号")
    private String vinCode;

    @ColumnWidth(10)
    @ExcelProperty("项目")
    @Schema(name = "project", description = "项目")
    private String project;

    @ColumnWidth(10)
    @ExcelProperty("行驶里程")
    @Schema(name = "distanceTravelled", description = "行驶里程")
    private Double distanceTravelled;

    @ColumnWidth(10)
    @ExcelProperty("故障简述")
    @Schema(name = "faultSummary", description = "故障简述")
    private String faultSummary;

    @ColumnWidth(10)
    @ExcelProperty(value = "故障分类", converter = FaultClassificationConverter.class)
    @Schema(name = "faultClassification", description = "故障分类")
    private Integer faultClassification;

    @ColumnWidth(10)
    @ExcelProperty("发货起运地")
    @Schema(name = "dispatchAddress", description = "发货起运地")
    private String dispatchAddress;

    @ColumnWidth(10)
    @ExcelProperty("收货目的地")
    @Schema(name = "arrivalAddress", description = "收货目的地")
    private String arrivalAddress;

    @ColumnWidth(10)
    @ExcelProperty("发货城市")
    @Schema(name = "dispatchCity", description = "发货城市")
    private String dispatchCity;

    @ColumnWidth(10)
    @ExcelProperty("收货城市")
    @Schema(name = "arrivalCity", description = "收货城市")
    private String arrivalCity;

    @ColumnWidth(10)
    @ExcelProperty("路线")
    @Schema(name = "routeName", description = "路线")
    private String routeName;

    @ColumnWidth(10)
    @ExcelProperty("装货联系人")
    @Schema(name = "loadingContact", description = "装货联系人")
    private String loadingContact;

    @ColumnWidth(10)
    @ExcelProperty("收货联系人")
    @Schema(name = "unloadingContact", description = "收货联系人")
    private String unloadingContact;

    @ColumnWidth(10)
    @ExcelProperty("装货联系方式")
    @Schema(name = "loadingContact", description = "装货联系方式")
    private String loadingPhone;

    @ColumnWidth(10)
    @ExcelProperty("收货联系方式")
    @Schema(name = "unloadingContact", description = "收货联系方式")
    private String unloadingPhone;

    @ColumnWidth(10)
    @ExcelProperty("标准时效")
    @Schema(name = "routeDeliveryTime", description = "标准时效")
    private Integer routeDeliveryTime;

    @ColumnWidth(10)
    @ExcelProperty("公里数")
    @Schema(name = "kilometersNum", description = "公里、数门店里程(KM)")
    private Integer kilometersNum;

    @ColumnWidth(10)
    @ExcelProperty(value = "运输类型", converter = TransportationTypeConverter.class)
    @Schema(name = "transportationType", description = "运输类型")
    private Integer transportationType;

    @ColumnWidth(10)
    @ExcelProperty(value = "物流类型", converter = LogisticsTypeConverter.class)
    @Schema(name = "logisticsType", description = "物流类型")
    private Integer logisticsType;

    @ColumnWidth(10)
    @ExcelProperty("物流安排时间")
    @Schema(name = "logisticsArrangementTime", description = "物流安排时间")
    private Date logisticsArrangementTime;

    @ColumnWidth(10)
    @ExcelProperty("物流供应商")
    @Schema(name = "logisticsProvider", description = "物流供应商")
    private String logisticsProvider;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否同包", converter = SamePackageConverter.class)
    @Schema(name = "isSamePackage", description = "是否同包")
    private Integer isSamePackage;

    @ColumnWidth(10)
    @ExcelProperty("同包vin号")
    @Schema(name = "packageVin", description = "同包vin号")
    private String packageVin;

    @ColumnWidth(10)
    @ExcelProperty(value = "车型", converter = CarTypeStrConverter.class)
    @Schema(name = "carType", description = "车型")
    private String carType;

    @ColumnWidth(10)
    @ExcelProperty("提货日期")
    @Schema(name = "pickupDate", description = "提货日期")
    private Date pickupDate;

    @ColumnWidth(10)
    @ExcelProperty("司机姓名")
    @Schema(name = "driverName", description = "司机姓名")
    private String driverName;

    @ColumnWidth(10)
    @ExcelProperty("司机电话")
    @Schema(name = "driverName", description = "司机电话")
    private String driverPhone;

    @ColumnWidth(10)
    @ExcelProperty("车牌号")
    @Schema(name = "plateNumber", description = "车牌号")
    private String plateNumber;

    @ColumnWidth(10)
    @ExcelProperty("响应时效")
    @Schema(name = "responseTimeTolerance", description = "响应时效")
    private Integer responseTimeTolerance;

    @ColumnWidth(10)
    @ExcelProperty("运输时效")
    @Schema(name = "transportationTimeTolerance", description = "运输时效")
    private Integer transportationTimeTolerance;

    @ColumnWidth(10)
    @ExcelProperty("送达时间")
    @Schema(name = "transportationTimeTolerance", description = "送达时间")
    private Date deliveryTime;

    @ColumnWidth(10)
    @ExcelProperty("应付费用")
    @Schema(name = "expense", description = "应付费用")
    private Double expense;

    @ColumnWidth(10)
    @ExcelProperty("实付费用")
    @Schema(name = "actualPaymentAmount", description = "实付费用")
    private Double actualPaymentAmount;

    @ColumnWidth(10)
    @ExcelProperty("原始运单")
    @Schema(name = "originalWaybill", description = "原始运单")
    private String originalWaybill;

    @ColumnWidth(10)
    @ExcelProperty("最后更新人")
    @Schema(name = "updateUserName", description = "最后更新人")
    private String updateUserName;

    @ColumnWidth(10)
    @ExcelProperty("最后更新时间")
    @Schema(name = "updateTime", description = "最后更新时间")
    private Date updateTime;

    @ExcelIgnore
    @Schema(name = "updateTime", description = "备注")
    private String remark;
}
