package com.xiaopeng.halley.ascm.boot.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BzContractFileRequest {
	@NotNull(message = "合同ID不能为空")
	private Long contractId;

	@NotBlank(message = "所属年月不能为空")
	private String time;

	@NotEmpty(message = "附件内容不能为空")
	private List<MaterialPictureItemVo> fileItems;
}
