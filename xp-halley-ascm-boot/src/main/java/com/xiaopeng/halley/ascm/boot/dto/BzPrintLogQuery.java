package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BzPrintLogQuery {

	@ApiModelProperty("运单编号")
	private String waybillCode;

	@ApiModelProperty("交货单号")
	private String deliveryOrderCode;

	@ApiModelProperty("仓库号")
	private String lgort;

	@ApiModelProperty("仓库名称")
	private String lgobe;

	@ApiModelProperty("仓库号")
	private String shopCode;

	@ApiModelProperty("仓库名称")
	private String shopName;

	@ApiModelProperty("路线")
	private String route;

	@ApiModelProperty("打印机名称")
	private String printerName;

	@ApiModelProperty("触发端")
	private String triggerClient;

	@ApiModelProperty("打印状态")
	private List<Integer> status;

	@ApiModelProperty("状态码")
	private List<String> state;

	@ApiModelProperty("错误信息")
	private String message;

	@ApiModelProperty("打印单据id")
	private Long baseFileId;

	@ApiModelProperty("重试次数")
	private Integer retryCount;
}