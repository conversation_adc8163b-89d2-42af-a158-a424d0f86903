package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BzSelfWaybillAssignRequest {
	@ApiModelProperty("主键")
	private List<Long> ids;

	@ApiModelProperty("车牌号")
	private String carPlate;

	@ApiModelProperty("指定车型")
	private String carType;

	@ApiModelProperty("司机名称")
	private String driverName;

	@ApiModelProperty("司机手机号")
	private String driverPhone;

	@ApiModelProperty("趟次")
	private Integer trips;

	@ApiModelProperty("预计开始运输时间")
	private Date estimatedTranTime;

	@ApiModelProperty("预计送达时间")
	private Date estimatedArriveTime;
}