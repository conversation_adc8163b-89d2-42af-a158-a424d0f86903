package com.xiaopeng.halley.ascm.boot.dto;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("自建运单")
public class BzSelfWaybillCreateVO {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty("主键")
	private Long id;

	@ApiModelProperty("运单编号")
	private String waybillCode;

	@ApiModelProperty("指派仓库")
	private String lgort;

	@ApiModelProperty("仓库描述")
	private String lgobe;

	@ApiModelProperty("运输方式")
	private String transportType;

	@ApiModelProperty("运输要求")
	private String transportRequire;

	@ApiModelProperty("车牌号")
	private String carPlate;

	@ApiModelProperty("指定车型")
	private String carType;

	@ApiModelProperty("发货方")
	private String shipper;

	@ApiModelProperty("发货方描述")
	private String shipperName;

	@ApiModelProperty("发货城市")
	private String shipCity;

	@ApiModelProperty("发货联系人")
	private String shipContact;

	@ApiModelProperty("发货联系电话")
	private String shipContactNum;

	@ApiModelProperty("收货方")
	private String consignee;

	@ApiModelProperty("收货方名称")
	private String consigneeName;

	@ApiModelProperty("收货城市")
	private String receiveCity;

	@ApiModelProperty("收货地址")
	private String receiveAddress;

	@ApiModelProperty("收货联系人")
	private String receiveContact;

	@ApiModelProperty("收货联系电话")
	private String receiveContactNum;

	@ApiModelProperty("物料编码")
	private String materialCode;

	@ApiModelProperty("物料描述")
	private String materialName;

	@ApiModelProperty("数量")
	private Integer quantity;

	@ApiModelProperty("单位")
	private String unit;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建人")
	private String createUserName;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建时间")
	private Date createTime;

}