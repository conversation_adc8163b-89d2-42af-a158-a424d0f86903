package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.xiaopeng.halley.ascm.boot.excel.ExcelObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.PositiveOrZero;

@Data
@ApiModel("自建运单")
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class BzSelfWaybillExcel extends ExcelObject {

	@ColumnWidth(15)
	@NotBlank(message = "运输方式不能为空")
	@ExcelProperty("运输方式")
	@ApiModelProperty("运输方式")
	private String transportType;

	@ColumnWidth(15)
	@NotBlank(message = "发货方不能为空")
	@ExcelProperty("发货方")
	@ApiModelProperty("发货方")
	private String shipper;

	@ColumnWidth(20)
	@ExcelProperty("发货方描述")
	@ApiModelProperty("发货方描述")
	private String shipperName;

	@ColumnWidth(15)
	@ExcelProperty("发货城市")
	@ApiModelProperty("发货城市")
	private String shipCity;

	@ColumnWidth(20)
	@ExcelProperty("发货地址")
	@ApiModelProperty("发货地址")
	private String shipAddress;

	@ColumnWidth(15)
	@ExcelProperty("发货联系人")
	@ApiModelProperty("发货联系人")
	private String shipContact;

	@ColumnWidth(20)
	@ExcelProperty("发货联系电话")
	@ApiModelProperty("发货联系电话")
	private String shipContactNum;

	@ColumnWidth(15)
	@NotBlank(message = "收货方不能为空")
	@ExcelProperty("收货方")
	@ApiModelProperty("收货方")
	private String consignee;

	@ColumnWidth(20)
	@ExcelProperty("收货方名称")
	@ApiModelProperty("收货方名称")
	private String consigneeName;

	@ColumnWidth(15)
	@ExcelProperty("收货城市")
	@ApiModelProperty("收货城市")
	private String receiveCity;

	@ColumnWidth(20)
	@ExcelProperty("收货地址")
	@ApiModelProperty("收货地址")
	private String receiveAddress;

	@ColumnWidth(15)
	@ExcelProperty("收货联系人")
	@ApiModelProperty("收货联系人")
	private String receiveContact;

	@ColumnWidth(20)
	@ExcelProperty("收货联系电话")
	@ApiModelProperty("收货联系电话")
	private String receiveContactNum;

	@ColumnWidth(15)
	@NotBlank(message = "指定车型不能为空")
	@ExcelProperty("指定车型")
	@ApiModelProperty("指定车型")
	private String carType;

	@ColumnWidth(40)
	@NotBlank(message = "运输需求不能为空")
	@ExcelProperty("运输要求")
	@ApiModelProperty("运输要求")
	private String transportRequire;

	@ColumnWidth(20)
	@NotBlank(message = "物料号不能为空")
	@ExcelProperty("物料号")
	@ApiModelProperty("物料号")
	private String materialCode;

	@ColumnWidth(20)
	@NotBlank(message = "物料名称不能为空")
	@ExcelProperty("物料名称")
	@ApiModelProperty("物料名称")
	private String materialName;

	@ColumnWidth(10)
	@PositiveOrZero
	@NotBlank(message = "数量必须为有效的数字")
	@ExcelProperty("数量")
	@ApiModelProperty("数量")
	private String quantity;

	@ColumnWidth(10)
	@ExcelProperty("单位")
	@ApiModelProperty("单位")
	private String unit;

	/**
	 * 获取分组key 【运输方式】+【发货方】+【发货城市】+【收货方】+【收货城市】+【车型】+【运输需求】
	 */
	public String getGroupKey() {
		return transportType + transportRequire + shipper + shipCity + consignee + receiveCity + carType;
	}
}