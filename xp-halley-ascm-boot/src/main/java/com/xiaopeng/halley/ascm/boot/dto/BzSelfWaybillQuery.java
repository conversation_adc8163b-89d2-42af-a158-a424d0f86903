package com.xiaopeng.halley.ascm.boot.dto;

import com.xiaopeng.halley.ascm.boot.mp.QueryType;
import com.xiaopeng.halley.ascm.boot.mp.QueryField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("自建运单")
public class BzSelfWaybillQuery {

	@QueryField(value = QueryType.LIST_LEFT_LIKE)
	@ApiModelProperty("运单编号")
	private List<String> waybillCode;

	@QueryField(value = QueryType.LIST)
	@ApiModelProperty("运输方式")
	private List<String> transportType;

	@QueryField(value = QueryType.LIST_LEFT_LIKE)
	@ApiModelProperty("发货方")
	private List<String> shipper;

	@QueryField(value = QueryType.LIST_LEFT_LIKE)
	@ApiModelProperty("收货方")
	private List<String> consignee;

	@QueryField(value = QueryType.DATE_START, column = "create_time")
	@ApiModelProperty("开始时间")
	private Date createTimeStart;

	@QueryField(value = QueryType.DATE_END, column = "create_time")
	@ApiModelProperty("结束时间")
	private Date createTimeEnd;
}