package com.xiaopeng.halley.ascm.boot.dto;

import cn.hutool.core.date.DatePattern;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xiaopeng.halley.ascm.boot.excel.ExcelObject;
import com.xiaopeng.halley.ascm.boot.validator.DatePatternCheck;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class BzSelfWaybillStatusImport extends ExcelObject {

	@NotBlank(message = "运单号不能为空")
	@ExcelProperty("运单号")
	private String waybillCode;

	@DatePatternCheck(value = "实际开始运输时间", pattern = DatePattern.PURE_DATETIME_PATTERN)
	@ExcelProperty("实际开始运输时间")
	private String actualTranTime;

	@DatePatternCheck(value = "实际送达时间", pattern = DatePattern.PURE_DATETIME_PATTERN)
	@ExcelProperty("实际送达时间")
	private String actualArriveTime;
}

