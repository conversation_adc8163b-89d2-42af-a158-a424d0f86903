package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.xiaopeng.halley.ascm.boot.config.easyexcelConverter.CommonConstantConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/5/15 15:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BzWayBillNewPageVo implements Serializable {
    //唯一id
    @ExcelIgnore
    @Schema(name = "id", description = "ID")
    private Long id;
    //运单编号
    @ColumnWidth(10)
    @ExcelProperty(value = "运单编码")
    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;
    //门店编码
    @ColumnWidth(10)
    @ExcelProperty(value = "门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    //门店名称
    @ColumnWidth(10)
    @ExcelProperty(value = "门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;
    //总箱数
    @ColumnWidth(10)
    @ExcelProperty(value = "总箱数")
    @Schema(name = "totalBox", description = "总箱数")
    private Long totalBox;
    //是否包装完成
    @ColumnWidth(10)
    @ExcelProperty(value = "是否包装完成", converter = CommonConstantConverter.class)
    @Schema(name = "isCompletePacking", description = "是否包装完成")
    private Integer isCompletePacking;
    //交货单号
    @ColumnWidth(10)
    @ExcelProperty(value = "交货单号")
    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;

    @ColumnWidth(15)
    @ExcelProperty(value = "原交货单号")
    @Schema(name = "originDeliveryOrderCode", description = "原交货单号")
    private String originDeliveryOrderCode;

    //运输方式
    @ColumnWidth(10)
    @ExcelProperty(value = "运输方式")
    @Schema(name = "transportType", description = "运输方式")
    private String transportType;
    //运单状态
    @ColumnWidth(10)
    @ExcelProperty(value = "运单状态")
    @Schema(name = "status", description = "运单状态")
    private String status;
    //订单类型
    @ColumnWidth(10)
    @ExcelProperty(value = "订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;

    @ColumnWidth(10)
    @ExcelProperty(value = "交货单类型")
    @Schema(name = "deliveryOrderType", description = "交货单类型")
    private String deliveryOrderType;

    @ColumnWidth(10)
    @ExcelProperty(value = "发货工厂")
    @Schema(name = "factoryCode", description = "发货工厂")
    private String factoryCode;

    @ColumnWidth(10)
    @ExcelProperty(value = "发货工厂描述")
    @Schema(name = "factoryName", description = "发货工厂描述")
    private String factoryName;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店省份")
    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店城市")
    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店地址")
    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店联系人")
    @Schema(name = "shopContactPerson", description = "门店联系人")
    private String shopContactPerson;

    @ColumnWidth(10)
    @ExcelProperty(value = "门店联系电话")
    @Schema(name = "shopContactNum", description = "门店联系电话")
    private String shopContactNum;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否挂起", converter = CommonConstantConverter.class)
    @Schema(name = "isHangUp", description = "是否挂起")
    private Integer isHangUp;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否拆箱", converter = CommonConstantConverter.class)
    @Schema(name = "isDevanning", description = "是否拆箱")
    private Integer isDevanning;

    @ColumnWidth(10)
    @ExcelProperty(value = "拆单次数")
    @Schema(name = "demolitionCount", description = "拆单次数")
    private Integer demolitionCount;

    @ColumnWidth(10)
    @ExcelProperty(value = "合单次数")
    @Schema(name = "combineCount", description = "合单次数")
    private Integer combineCount;

    //仓库编码
    @ColumnWidth(10)
    @ExcelProperty(value = "仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;

    //仓库名称
    @ColumnWidth(10)
    @ExcelProperty(value = "仓库名称")
    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库简称")
    @Schema(name = "warehouseRemark", description = "仓库简称")
    private String warehouseRemark;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库省份")
    @Schema(name = "warehouseProvince", description = "仓库省份")
    private String warehouseProvince;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库城市")
    @Schema(name = "warehouseCity", description = "仓库城市")
    private String warehouseCity;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库地址")
    @Schema(name = "warehouseAddress", description = "仓库地址")
    private String warehouseAddress;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库联系人")
    @Schema(name = "warehouseContactPerson", description = "仓库联系人")
    private String warehouseContactPerson;

    @ColumnWidth(10)
    @ExcelProperty(value = "仓库联系人电话")
    @Schema(name = "warehouseContactNum", description = "仓库联系人电话")
    private String warehouseContactNum;

    //发运时间
    @ColumnWidth(10)
    @ExcelProperty(value = "发车时间")
    @Schema(name = "departureTime", description = "发车时间")
    private Date departureTime;

    //签收时间
    @ColumnWidth(10)
    @ExcelProperty(value = "签收时间")
    @Schema(name = "signTime", description = "签收时间")
    private Date signTime;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否异常", converter = CommonConstantConverter.class)
    @Schema(name = "isAbnormal", description = "是否异常")
    private Integer isAbnormal;

    @ColumnWidth(10)
    @ExcelProperty(value = "是否超时")
    @Schema(name = "isTimeout", description = "是否超时")
    private String isTimeout;

    @ColumnWidth(10)
    @ExcelProperty(value = "位置更新时间")
    @Schema(name = "localUpdateTime", description = "位置更新时间")
    private Date localUpdateTime;

    @ColumnWidth(10)
    @ExcelProperty(value = "最新位置")
    @Schema(name = "localInfo", description = "最新位置")
    private String localInfo;

    @ColumnWidth(10)
    @ExcelProperty(value = "承运商编码")
    @Schema(name = "carrierCode", description = "承运商编码")
    private String carrierCode;

    @ColumnWidth(10)
    @ExcelProperty(value = "承运商名称")
    @Schema(name = "carrierName", description = "承运商名称")
    private String carrierName;

    @ColumnWidth(10)
    @ExcelProperty(value = "承运商联系人")
    @Schema(name = "carrierContactPerson", description = "承运商联系人")
    private String carrierContactPerson;

    @ColumnWidth(10)
    @ExcelProperty(value = "承运商联系电话")
    @Schema(name = "carrierContactNum", description = "承运商联系电话")
    private String carrierContactNum;

    @ColumnWidth(10)
    @ExcelProperty(value = "司机名称")
    @Schema(name = "driverName", description = "司机名称")
    private String driverName;

    @ColumnWidth(10)
    @ExcelProperty(value = "司机手机号")
    @Schema(name = "driverPhone", description = "司机手机号")
    private String driverPhone;

    @ColumnWidth(10)
    @ExcelProperty(value = "车牌号")
    @Schema(name = "carPlate", title = "车牌号")
    private String carPlate;

    @ColumnWidth(10)
    @ExcelProperty(value = "车型")
    @Schema(name = "carType", title = "车型")
    private String carType;

    @ColumnWidth(10)
    @ExcelProperty(value = "物流单号")
    @Schema(name = "logisticsCode", description = "物流单号")
    private String logisticsCode;

    @ColumnWidth(10)
    @ExcelProperty(value = "物流公司")
    @Schema(name = "logisticsCompany", description = "物流公司")
    private String logisticsCompany;

    @ColumnWidth(10)
    @ExcelProperty(value = "创建时间")
    @Schema(name = "createTime", description = "创建时间")
    private Date createTime;
    //接收时间
    @ColumnWidth(10)
    @ExcelProperty(value = "接收时间")
    @Schema(name = "receivedTime", description = "接收时间")
    private Date receivedTime;
    //下发时间
    @ColumnWidth(10)
    @ExcelProperty(value = "下发时间")
    @Schema(name = "circulationTime", description = "下发时间")
    private Date circulationTime;

    @ColumnWidth(10)
    @ExcelProperty(value = "计划发运时间")
    @Schema(name = "planShippingTime", description = "计划发运时间")
    private Date planShippingTime;

    @ColumnWidth(10)
    @ExcelProperty(value = "PDA发运时间")
    @Schema(name = "pdaShippingTime", description = "pda发运时间")
    private Date pdaShippingTime;
    //计划到达时间
    @ColumnWidth(10)
    @ExcelProperty(value = "计划到达时间")
    @Schema(name = "expiryTime", description = "计划到达时间")
    private String expiryTime;

    @ColumnWidth(10)
    @ExcelProperty(value = "运单异常原因")
    @Schema(name = "abnormalCause", description = "运单异常原因")
    private String abnormalCause;

    //线路
    @ColumnWidth(10)
    @ExcelProperty(value = "线路")
    @Schema(name = "route", description = "线路")
    private String route;

    @ExcelIgnore
    @Schema(name = "devanning", description = "能否拆箱")
    private Integer devanning;

    @ExcelIgnore
    @Schema(name = "isOntime", description = "是否超时")
    private Integer isOntime;

    @ColumnWidth(10)
    @ExcelProperty(value = "总体积(m³)")
    @Schema(name = "totalVolume", description = "总体积(m³)")
    private Double totalVolume;

    @ColumnWidth(10)
    @ExcelProperty(value = "总重量(kg)")
    @Schema(name = "totalWeight", description = "总重量(kg)")
    private Double totalWeight;

    @ExcelIgnore
    @Schema(name = "receiveState", description = "收货情况")
    private Integer receiveState;

    @ExcelIgnore
    private Long total;

}
