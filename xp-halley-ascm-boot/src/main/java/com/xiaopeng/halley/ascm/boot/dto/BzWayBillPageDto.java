package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-16 9:16
 */
@Data
@Accessors(chain = true)
public class BzWayBillPageDto implements Serializable {

    @Schema(name = "driverName", description = "司机姓名")
    private String driverName;

    @Schema(name = "carPlate", description = "车牌号")
    private String carPlate;

    @Schema(name = "statusList", description = "运单状态，支持多个")
    private List<String> statusList;

    @Schema(name = "status", description = "运单状态")
    private String status;

    @Schema(name = "waybillCodeList", description = "运单编号，空格多个")
    private List<String> waybillCodeList;

    @Schema(name = "deliveryOrderCodeList", description = "交货单号")
    private List<String> deliveryOrderCodeList;

    @Schema(name = "originDeliveryOrderCodeList", description = "原始交货单号")
    private List<String> originDeliveryOrderCodeList;

    @Schema(name = "shopCodeList", description = "门店编码")
    private List<String> shopCodeList;

    @Schema(name = "shopProvinceList", description = "门店省份")
    private List<String> shopProvinceList;

    @Schema(name = "shopCityList", description = "门店城市")
    private List<String> shopCityList;

    @Schema(name = "orderType", description = "订单类型")
    private String orderType;

    @Schema(name = "transportType", description = "运输类型")
    private String transportType;

    @Schema(name = "lgortList", description = "发货仓库")
    private List<String> lgortList;

    @Schema(name = "matnrList", description = "物料编号")
    private List<String> matnrList;

    @Schema(name = "logisticsCodeList", description = "物流单号")
    private List<String> logisticsCodeList;

    @Schema(name = "startReceivedTime", description = "接收时间开始")
    private Date startReceivedTime;

    @Schema(name = "endReceivedTime", description = "接收时间结束")
    private Date endReceivedTime;

    @Schema(name = "startCirculationTime", description = "下发时间开始")
    private Date startCirculationTime;

    @Schema(name = "endCirculationTime", description = "下发时间结束")
    private Date endCirculationTime;

    @Schema(name = "startDepartureTime", description = "发车时间开始")
    private Date startDepartureTime;

    @Schema(name = "endDepartureTime", description = "发车时间结束")
    private Date endDepartureTime;

    @Schema(name = "startSignTime", description = "签收时间开始")
    private Date startSignTime;

    @Schema(name = "endSignTime", description = "签收时间结束")
    private Date endSignTime;

    @Schema(name = "boxCodeList", description = "箱号码")
    private List<String> boxCodeList;

    @Schema(name = "logisticsCompany", description = "物流公司")
    private String logisticsCompany;

    @Schema(name = "separateWarehouseFlag", description = "分仓调拨，1：分仓调拨")
    private Integer separateWarehouseFlag;

    @Schema(name = "数量差异", description = "数量差异")
    private Integer shortfallCount;

    @Schema(name = "是否超时", description = "是否超时")
    private String whetherTimeout;

    @Schema(name = "计划发运时间开始")
    private Date planShippingTimeStart;

    @Schema(name = "计划发运时间结束")
    private Date planShippingTimeEnd;

    @Schema(name = "计划到达时间开始")
    private Date expiryTimeStart;

    @Schema(name = "计划到达时间结束")
    private Date expiryTimeEnd;
}
