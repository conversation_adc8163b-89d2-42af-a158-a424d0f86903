package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-5-16 9:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BzWayBillPageVo implements Serializable {
    //唯一id
    @ExcelIgnore
    @Schema(name = "id", description = "ID")
    private Long id;
    //订单类型
    @ColumnWidth(10)
    @ExcelProperty("订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;
    //运单编号
    @ColumnWidth(15)
    @ExcelProperty("运单编号")
    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;
    //运单状态
    @ColumnWidth(10)
    @ExcelProperty("运单状态")
    @Schema(name = "status", description = "运单状态")
    private String status;
    //仓库编码
    @ColumnWidth(10)
    @ExcelProperty("仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;
    //仓库名称
    @ColumnWidth(10)
    @ExcelProperty("仓库名称")
    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;
    //门店编码
    @ColumnWidth(10)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    //门店名称
    @ColumnWidth(10)
    @ExcelProperty("门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;
    //接收时间
    @ColumnWidth(15)
    @ExcelProperty("接收时间")
    @Schema(name = "receivedTime", description = "接收时间")
    private Date receivedTime;
    //下发时间
    @ColumnWidth(15)
    @ExcelProperty("下发时间")
    @Schema(name = "circulationTime", description = "下发时间")
    private Date circulationTime;
    //线路
    @ColumnWidth(10)
    @ExcelProperty("线路")
    @Schema(name = "route", description = "线路")
    private String route;
    //是否包装完成
    @ColumnWidth(10)
    @ExcelProperty("是否包装完成")
    @Schema(name = "isCompletePacking", description = "是否包装完成")
    private Long isCompletePacking;
    //发运时间
    @ColumnWidth(15)
    @ExcelProperty("发运时间")
    @Schema(name = "departureTime", description = "发运时间")
    private Date departureTime;
    //计划到达时间
    @ColumnWidth(15)
    @ExcelProperty("计划到达时间")
    @Schema(name = "expiryTime", description = "计划到达时间")
    private String expiryTime;
    //签收时间
    @ColumnWidth(15)
    @ExcelProperty("签收时间")
    @Schema(name = "signTime", description = "签收时间")
    private Date signTime;
    //总箱数
    @ColumnWidth(10)
    @ExcelProperty("总箱数")
    @Schema(name = "totalBox", description = "总箱数")
    private Long totalBox;
    //运输方式
    @ColumnWidth(10)
    @ExcelProperty("运输方式")
    @Schema(name = "transportType", description = "运输方式")
    private String transportType;
    //交货单号
    @ColumnWidth(15)
    @ExcelProperty("交货单号")
    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;

    @ColumnWidth(15)
    @ExcelProperty("原始交货单号")
    @Schema(name = "originDeliveryOrderCode", description = "原始交货单号")
    private String originDeliveryOrderCode;

    @ExcelIgnore
    @Schema(name = "deliveryOrderType", description = "交货单类型")
    private String deliveryOrderType;

    @ExcelIgnore
    @Schema(name = "factoryCode", description = "发货工厂")
    private String factoryCode;

    @ExcelIgnore
    @Schema(name = "factoryName", description = "发货工厂描述")
    private String factoryName;

    @ExcelIgnore
    @Schema(name = "isDevanning", description = "是否拆箱")
    private Integer isDevanning;

    @ExcelIgnore
    @Schema(name = "demolitionCount", description = "拆单次数")
    private Integer demolitionCount;

    @ExcelIgnore
    @Schema(name = "combineCount", description = "合单次数")
    private Integer combineCount;

    @ExcelIgnore
    @Schema(name = "warehouseProvince", description = "仓库省份")
    private String warehouseProvince;

    @ExcelIgnore
    @Schema(name = "warehouseCity", description = "仓库城市")
    private String warehouseCity;

    @ExcelIgnore
    @Schema(name = "warehouseAddress", description = "仓库地址")
    private String warehouseAddress;

    @ExcelIgnore
    @Schema(name = "warehouseContactPerson", description = "仓库联系人")
    private String warehouseContactPerson;

    @ExcelIgnore
    @Schema(name = "warehouseContactNum", description = "仓库联系人电话")
    private String warehouseContactNum;

    @ExcelIgnore
    @Schema(name = "warehouseRemark", description = "仓库简称")
    private String warehouseRemark;

    @ExcelIgnore
    @Schema(name = "isAbnormal", description = "是否异常")
    private Integer isAbnormal;

    @ExcelIgnore
    @Schema(name = "isOntime", description = "是否超时")
    private Integer isOntime;

    @ExcelIgnore
    @Schema(name = "carrierCode", description = "承运商编码")
    private String carrierCode;

    @ExcelIgnore
    @Schema(name = "carrierName", description = "承运商名称")
    private String carrierName;

    @ExcelIgnore
    @Schema(name = "carrierContactPerson", description = "承运商联系人")
    private String carrierContactPerson;

    @ExcelIgnore
    @Schema(name = "carrierContactNum", description = "承运商联系电话")
    private String carrierContactNum;

    @ExcelIgnore
    @Schema(name = "driverName", description = "司机名称")
    private String driverName;

    @ExcelIgnore
    @Schema(name = "driverPhone", description = "司机手机号")
    private String driverPhone;

    @ExcelIgnore
    @Schema(name = "abnormalCause", description = "运单异常原因")
    private String abnormalCause;

    @ColumnWidth(10)
    @ExcelProperty("门店省份")
    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;

    @ColumnWidth(10)
    @ExcelProperty("门店城市")
    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;

    @ColumnWidth(10)
    @ExcelProperty("门店地址")
    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;

    @ColumnWidth(10)
    @ExcelProperty("门店联系人")
    @Schema(name = "shopContactPerson", description = "门店联系人")
    private String shopContactPerson;

    @ColumnWidth(10)
    @ExcelProperty("门店联系电话")
    @Schema(name = "shopContactNum", description = "门店联系电话")
    private String shopContactNum;

    @ExcelIgnore
    @Schema(name = "createTime", description = "创建时间")
    private Date createTime;

    @ColumnWidth(10)
    @ExcelProperty("最新位置")
    @Schema(name = "localInfo", description = "最新位置")
    private String localInfo;

    @ColumnWidth(10)
    @ExcelProperty("位置更新时间")
    @Schema(name = "localUpdateTime", description = "位置更新时间")
    private Date localUpdateTime;

    @ExcelIgnore
    @Schema(name = "pdaShippingTime", description = "pda发运时间")
    private Date pdaShippingTime;

    @ColumnWidth(10)
    @ExcelProperty("总体积")
    @Schema(name = "totalVolume", description = "总体积")
    private Double totalVolume;

    @ColumnWidth(10)
    @ExcelProperty("物流单号")
    @Schema(name = "logisticsCode", description = "物流单号")
    private String logisticsCode;

    @ColumnWidth(10)
    @ExcelProperty("物流公司")
    @Schema(name = "logisticsCompany", description = "物流公司")
    private String logisticsCompany;

    @ColumnWidth(10)
    @ExcelProperty("收货情况")
    @Schema(name = "receiveState", description = "收货情况")
    private Integer receiveState;

    @ColumnWidth(10)
    @ExcelProperty("是否挂起")
    @Schema(name = "isHangUp", description = "是否挂起")
    private Integer isHangUp;

    @ColumnWidth(10)
    @ExcelProperty("能否拆箱")
    @Schema(name = "devanning", description = "能否拆箱")
    private Integer devanning;

    @ColumnWidth(10)
    @ExcelProperty("是否超时")
    @Schema(name = "isTimeout", description = "是否超时")
    private String isTimeout;

    @ExcelIgnore
    private Long total;

    @ExcelIgnore
    @Schema(name = "carPlate", title = "车牌号")
    private String carPlate;
}
