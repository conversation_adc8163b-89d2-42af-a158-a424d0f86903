package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-16 9:16
 */
@Data
public class BzWayBillTrackPageDto implements Serializable {

    @Schema(name = "waybillCode", description = "运单编号，空格多个")
    private List<String> waybillCode;

    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private List<String> deliveryOrderCode;

    //门店编码(多选+模糊)
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    //门店省份(多选+模糊)
    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;
    //门店城市(多选+模糊)
    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;
    //订单类型(下拉)
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;
    //运输类型(下拉)
    @Schema(name = "transportType", description = "运输类型")
    private String transportType;
    //发货仓库（仓库编码）(多选+模糊)
    @Schema(name = "lgort", description = "发货仓库")
    private String lgort;
    //物流单号
    @Schema(name = "logisticsCode", description = "物流单号")
    private String logisticsCode;

    //接收时间开始(范围用)

    @Schema(name = "startReceivedTime", description = "接收时间开始")
    private Date startReceivedTime;
    //接收时间结束(范围用)

    @Schema(name = "endReceivedTime", description = "接收时间结束")
    private Date endReceivedTime;
    //接收时间(排序用)
    @Schema(name = "receivedTime", description = "接收时间")
    private String receivedTime;
    //下发时间开始(范围用)

    @Schema(name = "startCirculationTime", description = "下发时间开始")
    private Date startCirculationTime;
    //下发时间结束(范围用)

    @Schema(name = "endCirculationTime", description = "下发时间结束")
    private Date endCirculationTime;
    //下发时间(排序用)
    @Schema(name = "circulationTime", description = "下发时间")
    private String circulationTime;
    //发车时间开始(范围用）

    @Schema(name = "startDepartureTime", description = "发车时间开始")
    private Date startDepartureTime;
    //发车时间结束(范围用)

    @Schema(name = "endDepartureTime", description = "发车时间结束")
    private Date endDepartureTime;
    //发车时间(排序用)
    @Schema(name = "departureTime", description = "发车时间")
    private String departureTime;
    //签收时间结束(范围用）

    @Schema(name = "startSignTime", description = "签收时间开始")
    private Date startSignTime;
    //签收时间结束(范围用)

    @Schema(name = "endSignTime", description = "签收时间结束")
    private Date endSignTime;
    //签收时间(排序用)
    @Schema(name = "signTime", description = "签收时间")
    private String signTime;
    //计划到达时间(排序用)
    @Schema(name = "expiryTime", description = "计划到达时间")
    private String expiryTime;
    //箱号码(多选+模糊)
    @Schema(name = "boxCode", description = "箱号码")
    private String boxCode;
    //物流公司(模糊查询)
    @Schema(name = "logisticsCompany", description = "物流公司")
    private String logisticsCompany;
}
