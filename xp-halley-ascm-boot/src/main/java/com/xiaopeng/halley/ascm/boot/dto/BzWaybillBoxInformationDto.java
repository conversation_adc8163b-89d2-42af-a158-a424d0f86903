package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-5-18 15:34
 */
@Data
public class BzWaybillBoxInformationDto {
    @Schema(name = "status", description = "箱号状态")
    private Integer status;
    @Schema(name = "isDevanning", description = "是否拆离")
    private Integer isDevanning;
    @Schema(name = "boxCode", description = "箱号")
    private String boxCode;
    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;
}
