package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:35
 */
@Data
public class BzWaybillBoxInformationVO implements Serializable {
    //交货单号.
    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;
    //下单时间
    @Schema(name = "purchaseTime", description = "下单时间")
    private String purchaseTime;
    //配送时间
    @Schema(name = "deliveryTime", description = "配送时间")
    private String deliveryTime;
    //箱号
    @Schema(name = "boxCode", description = "箱号")
    private String boxCode;
    //包装类型
    @Schema(name = "packageType", description = "包装类型")
    private String packageType;
    //长(毫米)
    @Schema(name = "materialLong", description = "长(毫米)")
    private String materialLong;
    //宽(毫米)
    @Schema(name = "materialWidth", description = "宽(毫米)")
    private String materialWidth;
    //高(毫米)
    @Schema(name = "materialHeight", description = "高(毫米)")
    private String materialHeight;
    //物料体积(立方米)
    @Schema(name = "materialVolume", description = "物料体积(立方米)")
    private Double materialVolume;
    //箱号状态（0-未扫描，1-已扫描）
    @Schema(name = "status", description = "箱号状态")
    private Integer status;
    //是否拆箱（0-正常，1-已拆离）
    @Schema(name = "isDevanning", description = "是否拆箱")
    private Integer isDevanning;
    //修改人（对应拆箱人员）
    @Schema(name = "demolitionPerson", description = "修改人")
    private String demolitionPerson;
    //修改时间
    @Schema(name = "demolitionTime", description = "修改时间")
    private String demolitionTime;
    //扫描人（对应扫箱人员）
    @Schema(name = "scannerPerson", description = "扫描人")
    private String scannerPerson;
    //扫描时间
    @Schema(name = "scannerTime", description = "扫描时间")
    private String scannerTime;
    //是否收货
    @Schema(name = "isReceipt", description = "是否收货")
    private Integer isReceipt;
}
