package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/8/10 16:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BzWaybillMaterialDetailPageVo {

    @ExcelIgnore
    @Schema(name = "erpMaterialId", description = "ID")
    private Long erpMaterialId;

    //唯一id
    @ExcelIgnore
    @Schema(name = "id", description = "ID")
    private Long id;
    //订单类型
    @ColumnWidth(10)
    @ExcelProperty("订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;
    //运单编号
    @ColumnWidth(15)
    @ExcelProperty("运单编号")
    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;
    //运单状态
    @ColumnWidth(10)
    @ExcelProperty("运单状态")
    @Schema(name = "status", description = "运单状态")
    private String status;
    //仓库编码
    @ColumnWidth(10)
    @ExcelProperty("仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;
    //仓库名称
    @ColumnWidth(10)
    @ExcelProperty("仓库名称")
    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;
    //门店编码
    @ColumnWidth(10)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    //门店名称
    @ColumnWidth(10)
    @ExcelProperty("门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;
    //接收时间
    @ColumnWidth(15)
    @ExcelProperty("接收时间")
    @Schema(name = "receivedTime", description = "接收时间")
    private Date receivedTime;
    //下发时间
    @ColumnWidth(15)
    @ExcelProperty("下发时间")
    @Schema(name = "circulationTime", description = "下发时间")
    private Date circulationTime;
    //线路
    @ColumnWidth(10)
    @ExcelProperty("线路")
    @Schema(name = "route", description = "线路")
    private String route;
    //发运时间
    @ColumnWidth(15)
    @ExcelProperty("发运时间")
    @Schema(name = "departureTime", description = "发运时间")
    private Date departureTime;
    //计划到达时间
    @ColumnWidth(15)
    @ExcelProperty("计划到达时间")
    @Schema(name = "expiryTime", description = "计划到达时间")
    private String expiryTime;
    //签收时间
    @ColumnWidth(15)
    @ExcelProperty("实际到达时间")
    @Schema(name = "signTime", description = "签收时间")
    private String signTime;
    //总箱数
    @ColumnWidth(10)
    @ExcelProperty("总箱数")
    @Schema(name = "totalBox", description = "总箱数")
    private Long totalBox;
    //运输方式
    @ColumnWidth(10)
    @ExcelProperty("运输方式")
    @Schema(name = "transportType", description = "运输方式")
    private String transportType;
    //交货单号
    @ColumnWidth(15)
    @ExcelProperty("交货单号")
    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;

    @ColumnWidth(15)
    @ExcelProperty("原交货单号")
    @Schema(name = "originDeliveryOrderCode", description = "原交货单号")
    private String originDeliveryOrderCode;

    @ColumnWidth(10)
    @ExcelProperty("最新位置")
    @Schema(name = "localInfo", description = "最新位置")
    private String localInfo;

    @ColumnWidth(15)
    @ExcelProperty("位置更新时间")
    @Schema(name = "localUpdateTime", description = "位置更新时间")
    private Date localUpdateTime;

    @ColumnWidth(15)
    @ExcelProperty("箱号")
    @Schema(name = "boxCode", description = "箱号")
    private String boxCode;

    @ColumnWidth(15)
    @ExcelProperty("物料号")
    @Schema(name = "matnr", description = "物料号")
    private String matnr;

    @ColumnWidth(10)
    @ExcelProperty("物料描述")
    @Schema(name = "maktx", description = "物料描述")
    private String maktx;

    @ColumnWidth(10)
    @ExcelProperty("发货数量")
    @Schema(name = "packageCount", description = "发货数量")
    private Integer packageCount;

    @ColumnWidth(10)
    @ExcelProperty("收货数量")
    @Schema(name = "receiveCount", description = "收货数量")
    private Integer receiveCount;

    @ColumnWidth(10)
    @ExcelProperty("差异数量")
    @Schema(name = "shortfallCount", description = "差异数量")
    private Integer shortfallCount;

    @ColumnWidth(10)
    @ExcelProperty("物流单号")
    @Schema(name = "logisticsCode", description = "差异数量")
    private String logisticsCode;

    @ColumnWidth(10)
    @ExcelProperty("是否超时")
    @Schema(name = "whetherTimeout", description = "是否超时")
    private String whetherTimeout;

    @ColumnWidth(10)
    @ExcelProperty("司机姓名")
    @Schema(name = "driverName", description = "司机姓名")
    private String driverName;

    @ColumnWidth(10)
    @ExcelProperty("车牌号")
    @Schema(name = "carPlate", description = "车牌号")
    private String carPlate;

    @ColumnWidth(10)
    @ExcelProperty("车型")
    @Schema(name = "carType", description = "车型")
    private String carType;

    @ColumnWidth(10)
    @ExcelProperty("物料长度(mm)")
    @ApiModelProperty(name = "materialLong", value = "物料长度（毫米）")
    private String materialLong;

    @ColumnWidth(10)
    @ExcelProperty("物料宽度(mm)")
    @ApiModelProperty(name = "materialWidth", value = "物料宽度（毫米）")
    private String materialWidth;

    @ColumnWidth(10)
    @ExcelProperty("物料高度(mm)")
    @ApiModelProperty(name = "materialHeight", value = "物料高度（毫米）")
    private String materialHeight;

    @ColumnWidth(10)
    @ExcelProperty("物料体积(m³)")
    @ApiModelProperty(name = "materialVolume", value = "物料体积（立方米）")
    private Double materialVolume;

    @ColumnWidth(10)
    @ExcelProperty("物料重量(kg)")
    @ApiModelProperty(name = "materialWeight", value = "物料重量（千克）")
    private Double materialWeight;

    // 箱子信息
    @ColumnWidth(10)
    @ExcelProperty("箱子长度(mm)")
    @ApiModelProperty(name = "boxLong", value = "箱子长度（毫米）")
    private String boxLong;

    @ColumnWidth(10)
    @ExcelProperty("箱子宽度(mm)")
    @ApiModelProperty(name = "boxWidth", value = "箱子宽度（毫米）")
    private String boxWidth;

    @ColumnWidth(10)
    @ExcelProperty("箱子高度(mm)")
    @ApiModelProperty(name = "boxHeight", value = "箱子高度（毫米）")
    private String boxHeight;

    @ColumnWidth(10)
    @ExcelProperty("箱子实际体积(m³)")
    @ApiModelProperty(name = "boxVolume", value = "箱子实际体积（立方米）")
    private Double actualVolume;

    @ColumnWidth(10)
    @ExcelProperty("箱子重量(kg)")
    @ApiModelProperty(name = "boxWeight", value = "箱子重量（千克）")
    private Double boxWeight;
}
