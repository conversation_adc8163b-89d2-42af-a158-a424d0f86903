package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/4/23 17:40
 */
@Data
public class BzWaybillRetryDto {

    @Schema(name = "id", description = "唯一id")
    private String id;

    @Schema(name = "interfaceNumber", description = "接口编号")
    private String interfaceNumber;

    @Schema(name = "interfaceNumbers", description = "接口编号-多")
    private List<String> interfaceNumbers;

    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;

    @Schema(name = "waybillCodes", description = "运单编号-多")
    private List<String> waybillCodes;

    @Schema(name = "shopCode", description = "门店编号")
    private String shopCode;

    @Schema(name = "shopCodes", description = "门店编号-多")
    private List<String> shopCodes;

    @Schema(name = "firstSendDateStart", description = "首次发送时间-开始")
    private Date firstSendDateStart;

    @Schema(name = "firstSendDateEnd", description = "首次发送时间-结束")
    private Date firstSendDateEnd;
}
