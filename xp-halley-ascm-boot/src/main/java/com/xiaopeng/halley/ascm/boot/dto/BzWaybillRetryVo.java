package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/4/23 17:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BzWaybillRetryVo implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @Schema(name = "id", description = "唯一id")
    private Long id;

    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;

    @Schema(name = "warehouseNumber", description = "仓库编号")
    private String warehouseNumber;

    @Schema(name = "interfaceNumber", description = "接口编码")
    private String interfaceNumber;

    @Schema(name = "interfaceDescribe", description = "接口描述")
    private String interfaceDescribe;

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;

    @Schema(name = "shopDescribe", description = "门店描述")
    private String shopDescribe;

    @Schema(name = "targetSystem", description = "目标系统")
    private String targetSystem;

    @Schema(name = "firstSendDate", description = "首发日期")
    private Date firstSendDate;

    @Schema(name = "firstSendState", description = "首发状态")
    private Integer firstSendState;

    @Schema(name = "lastSendDate", description = "最后一次发送日期")
    private Date lastSendDate;

    @Schema(name = "lastSendName", description = "最后一次发人")
    private String lastSendName;

    @Schema(name = "retryReturnMsg", description = "重发返回信息")
    private Integer retryReturnMsg;

    @Schema(name = "createUserId", description = "创建人ID")
    private String createUserId;

    @Schema(name = "createUserName", description = "创建人")
    private String createUserName;

    @Schema(name = "updateUserId", description = "更新人ID")
    private String updateUserId;

    @Schema(name = "updateUserName", description = "更新人")
    private String updateUserName;

    @Schema(name = "createTime", description = "创建时间")
    private Date createTime;

    @Schema(name = "updateTime", description = "更新时间")
    private Date updateTime;
}
