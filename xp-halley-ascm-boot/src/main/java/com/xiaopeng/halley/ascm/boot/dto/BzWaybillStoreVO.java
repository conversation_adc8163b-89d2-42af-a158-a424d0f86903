package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Schema(description = "门店运单结果")
public class BzWaybillStoreVO {

    @Schema(description = "运单编号")
    private String waybillCode;

    @Schema(description = "运输类型")
    private String transportType;

    @Schema(description = "运单状态")
    private String status;

    @Schema(description = "PDA发运时间")
    private LocalDateTime pdaShippingTime;

    @Schema(description = "发车时间")
    private LocalDateTime departureTime;

    @Schema(description = "签收时间")
    private LocalDateTime signTime;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "司机手机号")
    private String driverPhone;

    @Schema(description = "物流单号")
    private String logisticsCode;

    @Schema(description = "位置信息")
    private String localInfo;

    private String boxCodes;

    @Schema(description = "交货单号")
    private List<String> deliveryCodeList;

    @Schema(description = "箱子信息")
    private List<BoxInfo> boxInfoList = new ArrayList<>();

    @Data
    @AllArgsConstructor
    public static class BoxInfo {
        private String boxCode;
        private List<String> materialCodeList;
    }
}