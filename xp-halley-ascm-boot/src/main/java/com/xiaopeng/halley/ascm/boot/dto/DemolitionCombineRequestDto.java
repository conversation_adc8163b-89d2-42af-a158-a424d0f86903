package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DemolitionCombineRequestDto {

    @Schema(name = "parentWaybillCode", description = "父运单")
    private String parentWaybillCode;

    @Schema(name = "childWaybillCode", description = "子运单")
    private String childWaybillCode;

    @Schema(name = "createUserName", description = "操作人")
    private String createUserName;

    @Schema(name = "action", description = "拆合动作 传入中文枚举值：拆，合")
    private String action;

    @Schema(name = "startCreateTime", description = "操作时间开始")
    private Date startCreateTime;

    @Schema(name = "endCreateTime", description = "操作时间结束")
    private Date endCreateTime;
}
