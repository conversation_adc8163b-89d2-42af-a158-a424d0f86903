package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DemolitionCombineResponseDto {

    @Schema(name = "id", description = "序号")
    private Long id;

    @Schema(name = "parentWaybillCode", description = "父运单编号")
    private String parentWaybillCode;

    @Schema(name = "action", description = "动作")
    private String action;

    @Schema(name = "childWaybillCode", description = "子运单编号")
    private String childWaybillCode;

    @Schema(name = "item", description = "端")
    private String item;

    @Schema(name = "createTimeShow", description = "创建时间（展示）")
    private String createTimeShow;

    @Schema(name = "createUserName", description = "创建人")
    private String createUserName;

}
