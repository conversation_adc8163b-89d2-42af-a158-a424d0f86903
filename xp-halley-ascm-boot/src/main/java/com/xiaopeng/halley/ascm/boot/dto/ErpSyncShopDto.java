package com.xiaopeng.halley.ascm.boot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-5-16 9:56
 */
@Data
public class ErpSyncShopDto {
    @JsonProperty(value = "KUNNR")
    private String KUNNR;//门店编码

    @JsonProperty(value = "NAME1")
    private String NAME1;//门店名称

    @JsonProperty(value = "ZPROVINCE1")
    private String ZPROVINCE1;//门店省份

    @JsonProperty(value = "CITY")
    private String CITY;//门店城市

    @JsonProperty(value = "ADDR")
    private String ADDR;//门店地址

    @JsonProperty(value = "TEL")
    private String TEL;//联系电话

    @JsonProperty(value = "CONTACTS")
    private String CONTACTS;//联系人

    @JsonProperty(value = "ZLONG")
    private Double ZLONG;//经度

    @JsonProperty(value = "ZLAT")
    private Double ZLAT;//纬度

    @JsonProperty(value = "ZYL01")
    private String ZYL01;//骁龙门店编码

    @JsonProperty(value = "ZYL02")
    private String ZYL02;//骁龙门店简称

    @JsonProperty(value = "ZYL03")
    private String ZYL03;//区域
}
