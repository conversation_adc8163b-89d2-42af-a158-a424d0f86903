package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 14:03
 * @Description:
 */
@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class GetMaterialCheckPageResponseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "物料id")
    @ExcelIgnore
    private Long id;

    /**
     * 物料编码
     */
    @Schema(name = "materialCode", description = "物料编码")
    @ExcelProperty("物料编码")
    @ColumnWidth(10)
    private String materialCode;
    /**
     * 物料描述
     */
    @Schema(name = "materialDesc", description = "物料描述")
    @ExcelProperty("物料描述")
    @ColumnWidth(30)
    private String materialDesc;

    /**
     * 校验编码
     */
    @Schema(name = "checkCode", description = "检验编码")
    @ExcelProperty("检验编码")
    @ColumnWidth(20)
    private String checkCode;

    /**
     * 校验标准描述
     */
    @Schema(name = "checkDesc", description = "检验标准描述")
    @ExcelProperty("检验标准描述")
    @ColumnWidth(30)
    private String checkDesc;

    /**
     * 是否单元包装
     */
    @Schema(name = "unitPackage", description = "是否单元包装")
    @ExcelProperty("是否单元包装")
    @ColumnWidth(10)
    private String unitPackageStr;
    /**
     * 校验标准文本
     */
    @Schema(name = "checkDocs", description = "检验标准文本")
    @ExcelProperty("检验标准文本")
    @ColumnWidth(60)
    private String checkDocs;

    @Schema(name = "updateUserName", description = "最后一次维护人")
    @ExcelProperty("最后一次维护人")
    @ColumnWidth(10)
    private String updateUserName;

    @Schema(name = "updateTime", description = "维护时间")
    @ExcelProperty("维护时间")
    @ColumnWidth(10)
    private Date updateTime;

}
