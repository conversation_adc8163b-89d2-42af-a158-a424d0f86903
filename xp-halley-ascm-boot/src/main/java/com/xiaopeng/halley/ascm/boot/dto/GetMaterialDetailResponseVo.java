package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-9 09:32
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetMaterialDetailResponseVo {
    @Schema(name = "materialCode", description = "物料编码")
    private String materialCode;
    @Schema(name = "materialDesc", description = "物料描述")
    private String materialDesc;
    @Schema(name = "picLeft", description = "图片-左")
    private String picLeft;
    @Schema(name = "picRight", description = "图片-右")
    private String picRight;
    @Schema(name = "picStamp", description = "图片-钢印")
    private String picStamp;
    @Schema(name = "picBefore", description = "图片-前")
    private String picBefore;
    @Schema(name = "picAfter", description = "图片-后")
    private String picAfter;
    @Schema(name = "picOther", description = "图片-其他")
    private String picOther;
    @Schema(name = "materialLength", description = "物料长(mm)")
    private String materialLength;
    @Schema(name = "materialWidth", description = "物料宽(mm)")
    private String materialWidth;
    @Schema(name = "materialHeight", description = "物料高(mm)")
    private String materialHeight;
    @Schema(name = "materialVolume", description = "物料体积(mm)")
    private String materialVolume;
    @Schema(name = "checkDocs", description = "校验标准文本")
    private String checkDocs;
    @Schema(name = "supplierItemVos", description = "承运商集合")
    private List<SupplierItemVo> supplierItemVos;
}
