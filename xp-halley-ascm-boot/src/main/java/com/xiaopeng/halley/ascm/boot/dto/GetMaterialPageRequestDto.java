package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 13:52
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetMaterialPageRequestDto {
    @Schema(name = "materialCodes", description = "物料编码")
    private List<String> materialCodes;
    @Schema(name = "asnCodes", description = "ASN单")
    private List<String> asnCodes;
    @Schema(name = "deliveryOrderCodes", description = "ERP入库单")
    private List<String> deliveryOrderCodes;
    @Schema(name = "materialDesc", description = "物料描述")
    private String materialDesc;
    @Schema(name = "supplierCodes", description = "供应商编码")
    private List<String> supplierCodes;
    @Schema(name = "supplierName", description = "供应商描述")
    private String supplierName;
}
