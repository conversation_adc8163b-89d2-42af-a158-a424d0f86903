package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 14:03
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class GetMaterialPageResonseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(name = "id", description = "物料id")
    @ExcelIgnore
    private Long id;

    @Schema(name = "materialCode", description = "物料编码")
    @ExcelProperty("物料编码")
    @ColumnWidth(10)
    private String materialCode;

    @Schema(name = "materialDesc", description = "物料描述")
    @ExcelProperty("物料描述")
    @ColumnWidth(30)
    private String materialDesc;

    @Schema(name = "picComplete", description = "图片-是否完整")
    @ExcelProperty("图片-是否完整")
    @ColumnWidth(10)
    private String picComplete;

    @Schema(name = "picLeft", description = "图片-左")
    @ExcelProperty("图片链接-左")
    @ColumnWidth(30)
    private String picLeft;

    @Schema(name = "picLeftRaw", description = "图片-左")
    @ExcelIgnore
    private String picLeftRaw;

    @Schema(name = "picRight", description = "图片-右")
    @ExcelProperty("图片链接-右")
    @ColumnWidth(50)
    private String picRight;

    @Schema(name = "picRightRaw", description = "图片-右")
    @ExcelIgnore
    private String picRightRaw;

    @Schema(name = "picStamp", description = "图片-钢印")
    @ExcelProperty("图片链接-钢印")
    @ColumnWidth(50)
    private String picStamp;

    @Schema(name = "picStampRaw", description = "图片-钢印")
    @ExcelIgnore
    private String picStampRaw;

    @Schema(name = "picBefore", description = "图片-前")
    @ExcelProperty("图片链接-前")
    @ColumnWidth(50)
    private String picBefore;

    @Schema(name = "picBeforeRaw", description = "图片-前")
    @ExcelIgnore
    private String picBeforeRaw;

    @Schema(name = "picAfter", description = "图片-后")
    @ExcelProperty("图片链接-后")
    @ColumnWidth(50)
    private String picAfter;

    @Schema(name = "picAfterRaw", description = "图片-后")
    @ExcelIgnore
    private String picAfterRaw;

    @Schema(name = "picOther", description = "图片-其他")
    @ExcelProperty("图片链接-其它")
    @ColumnWidth(50)
    private String picOther;

    @Schema(name = "picOtherRaw", description = "图片-其他")
    @ExcelIgnore
    private String picOtherRaw;

    @Schema(name = "unitPackage", description = "是否单元包装")
    @ExcelProperty("是否单元包装")
    @ColumnWidth(10)
    private String unitPackageStr;

    @Schema(name = "supplierPackage", description = "供应商外包装")
    @ExcelProperty("供应商外包装")
    @ColumnWidth(10)
    private String supplierPackage;

    @Schema(name = "checkDocs", description = "校验标准文本")
    @ExcelProperty("校验标准文本")
    @ColumnWidth(10)
    private String checkDocs;

    @Schema(name = "updateUserName", description = "最后一次维护人")
    @ExcelProperty("最后一次维护人")
    @ColumnWidth(10)
    private String updateUserName;

    @Schema(name = "updateTime", description = "维护时间")
    @ExcelProperty("最后一次维护时间")
    @ColumnWidth(10)
    private Date updateTime;

}
