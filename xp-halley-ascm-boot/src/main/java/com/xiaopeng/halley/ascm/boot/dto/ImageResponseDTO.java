package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("图片保存返回类")
public class ImageResponseDTO {

    @Schema(name = "fileId", description = "文件ID")
    private String fileId;

    @Schema(name = "url", description = "访问URL")
    private String url;

    @Schema(name = "fileName", description = "文件名称")
    private String fileName;

}
