package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("地址解析类")
public class LoactionRequestDTO {

    @NotNull(message = "纬度不可为空")
    @ApiModelProperty(value = "纬度")
    private String latitude;

    @NotNull(message = "经度不可为空")
    @ApiModelProperty(value = "经度")
    private String longitude;

}