package com.xiaopeng.halley.ascm.boot.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
@Builder
public class MaterialPhotoItem {
    private String photoName;
    private Integer photoType;
    private String photoUrl;

    @Getter
    public enum PhotoType {
        LEFT(1), RIGHT(2), STAMP(3), BEFORE(4), AFTER(5), OTHER(6);
        private final int value;

        PhotoType(int value) {
            this.value = value;
        }
    }
}