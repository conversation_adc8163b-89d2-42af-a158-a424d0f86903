package com.xiaopeng.halley.ascm.boot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author: tuyb
 * @Date: 2024-8-9 11:02
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MaterialPictureItemVo {
    @NotNull(message = "文件名不能为空")
    private String fileName;

    @NotNull(message = "文件名不能为空")
    private String saveName;

    @NotNull(message = "文件地址不能为空")
    private String url;

    public MaterialPictureItemVo(String fileName, String url) {
        this.fileName = fileName;
        this.url = url;
    }
}
