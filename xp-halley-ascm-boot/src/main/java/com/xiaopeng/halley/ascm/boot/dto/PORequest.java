package com.xiaopeng.halley.ascm.boot.dto;

import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PORequest {
    @Schema(title = "接口编码", description = "接口编码")
    private POInvokeEnum poInvokeEnum;

    @Schema(title = "业务数据", description = "业务数据")
    private Object data;

    @Schema(title = "接收系统", description = "接收系统")
    private String receiver;

    @Schema(title = "业务ID", description = "业务ID")
    private String busId;
}