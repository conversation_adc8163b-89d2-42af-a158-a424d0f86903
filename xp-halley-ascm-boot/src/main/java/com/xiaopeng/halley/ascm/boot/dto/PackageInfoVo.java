package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-9 10:33
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PackageInfoVo {
    /**
     * 供应商snp
     */
    @Schema(name = "supplierSnp", description = "供应商SNP")
    private String supplierSnp;

    /**
     * 物料长(mm)
     */
    @Schema(name = "materialLength", description = "长(mm)")
    private String materialLength;

    /**
     * 物料宽(mm)
     */
    @Schema(name = "materialWidth", description = "宽(mm)")
    private String materialWidth;

    /**
     * 物料高(mm)
     */
    @Schema(name = "materialHeight", description = "高(mm)")
    private String materialHeight;

    /**
     * 材料
     */
    @Schema(name = "stuff", description = "材料")
    private String stuff;

    /**
     * 材料名称
     */
    @Schema(name = "stuffName", description = "材料名称")
    private String stuffName;

    /**
     * 用量
     */
    @Schema(name = "dosage", description = "用量")
    private String dosage;

    /**
     * 是否回收
     */
    @Schema(name = "recycleStr", description = "是否回收")
    private String recycleStr;
}
