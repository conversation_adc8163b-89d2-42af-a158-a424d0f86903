package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author: tuyb
 * @Date: 2024-8-9 16:31
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaveMaterialCodeDto {
    @NotNull(message = "物料编号不能为空！")
    private String materialCode;
    @Schema(name = "picLeft", description = "图片-左")
    private String picLeft;
    @Schema(name = "picRight", description = "图片-右")
    private String picRight;
    @Schema(name = "picStamp", description = "图片-钢印")
    private String picStamp;
    @Schema(name = "picBefore", description = "图片-前")
    private String picBefore;
    @Schema(name = "picAfter", description = "图片-后")
    private String picAfter;
    @Schema(name = "picOther", description = "图片-其他")
    private String picOther;
}
