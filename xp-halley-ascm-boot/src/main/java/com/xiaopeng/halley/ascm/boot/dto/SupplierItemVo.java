package com.xiaopeng.halley.ascm.boot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: tuyb
 * @Date: 2024-8-9 09:37
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SupplierItemVo {
    @Schema(name = "id", description = "唯一ID")
    private Long id;
    @Schema(name = "supplierCode", description = "供应商编码")
    private String supplierCode;
    @Schema(name = "supplierName", description = "供应商描述")
    private String supplierName;
    @Schema(name = "supplierSnp", description = "供应商snp")
    private String supplierSnp;
}
