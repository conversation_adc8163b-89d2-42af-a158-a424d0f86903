package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/9/10 15:27
 */
@Data
public class ValidationRulesImportVo {
    @ExcelProperty("物料编码")
    private String materialCode;
    @ExcelProperty("检验标准编码")
    private String checkCode;
    @ExcelProperty("检验标准描述")
    private String checkDesc;
    @ExcelProperty("失败原因")
    private String failReason;
    @ExcelIgnore
    private Long checkCodeId;
}
