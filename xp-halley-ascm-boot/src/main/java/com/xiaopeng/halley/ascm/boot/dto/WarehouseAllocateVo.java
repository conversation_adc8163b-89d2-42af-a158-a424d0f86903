package com.xiaopeng.halley.ascm.boot.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-5-16 16:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class WarehouseAllocateVo implements Serializable {
    private static final long serialVersionUID = 1L;//序列化版本ID
    //运单状态
    @ColumnWidth(10)
    @ExcelProperty("运单状态")
    @Schema(name = "status", description = "运单状态")
    private String status;
    //订单类型
    @ColumnWidth(20)
    @ExcelProperty("订单类型")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;
    //运输方式
    @ColumnWidth(20)
    @ExcelProperty("运输方式")
    @Schema(name = "transportType", description = "运输方式")
    private String transportType;
    //运单编号
    @ColumnWidth(20)
    @ExcelProperty("运单编号")
    @Schema(name = "waybillCode", description = "运单编号")
    private String waybillCode;
    //总箱数
    @ColumnWidth(20)
    @ExcelProperty("总箱数")
    @Schema(name = "totalBox", description = "总箱数")
    private Long totalBox;
    //交货单号
    @ColumnWidth(20)
    @ExcelProperty("交货单号")
    @Schema(name = "deliveryOrderCode", description = "交货单号")
    private String deliveryOrderCode;
    //签收时间
    @ColumnWidth(20)
    @ExcelProperty("签收时间")
    @Schema(name = "signTime", description = "签收时间")
    private String signTime;
    //接收时间
    @ColumnWidth(20)
    @ExcelProperty("接收时间")
    @Schema(name = "receivedTime", description = "接收时间")
    private Date receivedTime;
    //下发时间
    @ColumnWidth(20)
    @ExcelProperty("下发时间")
    @Schema(name = "circulationTime", description = "下发时间")
    private Date circulationTime;
    //发运时间
    @ColumnWidth(20)
    @ExcelProperty("发运时间")
    @Schema(name = "departureTime", description = "发运时间")
    private Date departureTime;
    //计划到达时间
    @ColumnWidth(20)
    @ExcelProperty("计划到达时间")
    @Schema(name = "expiryTime", description = "计划到达时间")
    private String expiryTime;
    //仓库编码
    @ColumnWidth(20)
    @ExcelProperty("仓库编码")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;
    //仓库名称
    @ColumnWidth(20)
    @ExcelProperty("仓库名称")
    @Schema(name = "lgobe", description = "仓库名称")
    private String lgobe;
    //门店编码
    @ColumnWidth(20)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    //门店名称
    @ColumnWidth(20)
    @ExcelProperty("门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;
    //线路
    @ColumnWidth(20)
    @ExcelProperty("线路")
    @Schema(name = "route", description = "线路")
    private String route;
    //未收货箱数
    @ColumnWidth(20)
    @ExcelProperty("未收货箱数")
    @Schema(name = "unreceivedBoxNum", description = "未收货箱数")
    private String unreceivedBoxNum;

}
