package com.xiaopeng.halley.ascm.boot.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/7/5 13:56
 */
@Data
public class WaybillInfoSyncErpVO {
    /**
     * 运单编号
     */
    @JsonProperty(value = "WAYBILLCODE")
    @JsonAlias(value = "WAYBILLCODE")
    private String waybillCode;
    /**
     * 实际发运时间
     */
    private Date departureTime;
    /**
     * 实际签收时间
     */
    private Date signTime;
    /**
     * 箱号集合
     */
    @JsonProperty(value = "BOXLIST")
    @JsonAlias(value = "BOXLIST")
    private List<WaybillBoxListInfoDto> boxList;
}
