package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "箱物料请求参数")
public class BoxMaterialRequestDto {

	@JSONField(name = "CARTON")
	@Schema(description = "箱号")
	private String boxCode;

	@JSONField(name = "ZCD")
	@Schema(description = "箱长(MM)")
	private String boxLength;

	@JSONField(name = "ZKD")
	@Schema(description = "箱宽(MM)")
	private String boxWidth;

	@JSONField(name = "ZGD")
	@Schema(description = "箱高(MM)")
	private String boxHeight;

	@JSONField(name = "ZXTJ")
	@Schema(description = "箱体积(M3)")
	private Double boxVolume;

	@JSONField(name = "ZXZL")
	@Schema(description = "箱重量(G)")
	private Double boxWeight;

	@JSONField(name = "MATNR")
	@Schema(description = "物料号(商品化)")
	private String materialCode;

	@JSONField(name = "ZLENGTH")
	@Schema(description = "物料长(MM)")
	private String materialLength;

	@JSONField(name = "ZWIDTH")
	@Schema(description = "物料宽(MM)")
	private String materialWidth;

	@JSONField(name = "ZHIGH")
	@Schema(description = "物料高(MM)")
	private String materialHeight;

	@JSONField(name = "ZSNP_V")
	@Schema(description = "物料体积(M3)")
	private Double materialVolume;

	@JSONField(name = "ENTGE")
	@Schema(description = "物料重量(G)")
	private Double materialWeight;
}