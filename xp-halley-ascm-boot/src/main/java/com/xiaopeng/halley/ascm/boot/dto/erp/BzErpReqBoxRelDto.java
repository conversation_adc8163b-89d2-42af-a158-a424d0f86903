package com.xiaopeng.halley.ascm.boot.dto.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BzErpReqBoxRelDto implements Serializable {

    @Schema(name = "boxId", title = "箱号id")
    private String boxId;

    @Schema(name = "waybillCode", title = "运单号")
    private String waybillCode;

    @Schema(name = "deliveryOrderCode", title = "交货单号")
    private String deliveryOrderCode;

    @Schema(name = "boxCode", title = "箱号")
    private String boxCode;

    @Schema(name = "boxLong", title = "长")
    private String boxLong;

    @Schema(name = "boxWidth", title = "宽")
    private String boxWidth;

    @Schema(name = "boxHeight", title = "高")
    private String boxHeight;

    @Schema(name = "packageType", title = "包装类型")
    private String packageType;

    @Schema(name = "boxWeight", title = "箱重量")
    private String boxWeight;

    @Schema(name = "actualVolume", title = "实际体积(立方米)")
    private String actualVolume;

    @Schema(name = "status", title = "箱号状态")
    private String status;

    @Schema(name = "updateUserName", title = "更新人")
    private String updateUserName;

    @Schema(name = "updateTime", title = "更新时间")
    private String updateTime;

    @Schema(name = "boxVolume", title = "箱子体积(立方米)")
    private String boxVolume;

    @Schema(name = "packageTime", title = "包装时间")
    private String packageTime;

    @Schema(name = "packageCode", title = "包装材料编码")
    private String packageCode;

    @Schema(name = "materialList", title = "物料信息")
    private List<BzErpReqMaterialRelDto> materialList;

    private Double totalMaterialVolume;

    private String boxCodeDouble;

    private Double loadingRate;
}
