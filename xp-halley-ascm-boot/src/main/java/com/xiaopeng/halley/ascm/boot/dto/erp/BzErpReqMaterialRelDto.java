package com.xiaopeng.halley.ascm.boot.dto.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BzErpReqMaterialRelDto {
    @Schema(name = "deliveryOrderCode", title = "交货单号")
    private String deliveryOrderCode;

    @Schema(name = "matnr", title = "物料号")
    private String matnr;

    @Schema(name = "maktx", title = "物料描述")
    private String maktx;

    @Schema(name = "materialLong", title = "物料长")
    private String materialLong;

    @Schema(name = "materialWidth", title = "物料宽")
    private String materialWidth;

    @Schema(name = "materialHeight", title = "物料高")
    private String materialHeight;

    @Schema(name = "materialVolume", title = "物料体积")
    private String materialVolume;

    @Schema(name = "materialWeight", title = "物料重量")
    private String materialWeight;

    @Schema(name = "orderCount", title = "订单数量")
    private String orderCount;

    @Schema(name = "packageCount", title = "已包装数量")
    private String packageCount;
}
