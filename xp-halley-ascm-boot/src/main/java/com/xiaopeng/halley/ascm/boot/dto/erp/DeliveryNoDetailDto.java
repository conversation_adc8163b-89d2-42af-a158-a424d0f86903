package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DeliveryNoDetailDto {

    @Schema(name = "deliveryNo", title = "交货单号")
    private String deliveryNo;

    @Schema(name = "createTime", title = "创建时间")
    private Date createTime;

    @Schema(name = "lgort", title = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", title = "仓库名称")
    private String lgobe;

    @Schema(name = "warehouseProvince", title = "仓库省份")
    private String warehouseProvince;

    @Schema(name = "warehouseCity", title = "仓库城市")
    private String warehouseCity;

    @Schema(name = "contactNum", title = "仓库联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", title = "仓库联系人")
    private String contactPerson;

    @Schema(name = "warehouseAddress", title = "仓库地址")
    private String warehouseAddress;

    @Schema(name = "shopCode", title = "门店编码")
    private String shopCode;

    @Schema(name = "shopName", title = "门店名称")
    private String shopName;

    @Schema(name = "shopProvince", title = "门店省份")
    private String shopProvince;

    @Schema(name = "shopCity", title = "门店城市")
    private String shopCity;

    @Schema(name = "shopContactNum", title = "门店联系电话")
    private String shopContactNum;

    @Schema(name = "shopContactPerson", title = "门店联系人")
    private String shopContactPerson;

    @Schema(name = "shopAddress", title = "门店地址")
    private String shopAddress;

    @Schema(name = "reqMaterialRelList", title = "原始需求")
    private List<BzErpReqMaterialRel> reqMaterialRelList;

    @Schema(name = "boxRelList", title = "箱号信息")
    private List<BzErpReqBoxRelDto> boxRelList;

}
