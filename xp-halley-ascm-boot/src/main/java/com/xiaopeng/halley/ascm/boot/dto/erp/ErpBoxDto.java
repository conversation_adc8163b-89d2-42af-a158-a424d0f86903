package com.xiaopeng.halley.ascm.boot.dto.erp;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 箱号信息
 *
 * <AUTHOR>
 * @Date 2022/5/9 3:37 PM
 */
@Data
@Deprecated
public class ErpBoxDto {
    private static final long serialVersionUID = 1L;

    @JsonProperty(value = "CARTON")
    @Alias("boxCode")
    private String CARTON; //箱号

    @JsonProperty(value = "PMAT")
    @Alias("packageType")
    private String PMAT; // 包装类型

    @JsonProperty(value = "ZCD")
    @Alias("boxLong")
    private String ZCD; //长(MM)

    @JsonProperty(value = "ZKD")
    @Alias("boxWidth")
    private String ZKD; //宽(MM)

    @JsonProperty(value = "ZGD")
    @Alias("boxHeight")
    private String ZGD; //高(MM)

    @JsonProperty(value = "ZSJTJ")
    @Alias("actualVolume")
    private String ZSJTJ; //实际体积(立方米)
}
