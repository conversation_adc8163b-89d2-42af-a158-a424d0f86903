package com.xiaopeng.halley.ascm.boot.dto.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class ErpExportSearchDto {

    @Schema(name = "ASCM需求号")
    private String ascmCode;//ASCM需求号

    @Schema(name = "ASCM需求号集合")
    private String[] ascmCodeList;//ASCM需求号集合

    @Schema(name = "需求状态")
    private Integer reqStatus;//需求状态

    @Schema(name = "交货单号")
    private String deliveryOrderCode;//交货单号

    @Schema(name = "交货单号集合")
    private String[] deliveryOrderCodeList;//交货单号集合

    @Schema(name = "运单编号")
    private String waybillCode;//运单编号

    @Schema(name = "运单编号集合")
    private String[] waybillCodeList;//运单编号集合

    @Schema(name = "ERP订单类型")
    private String erpOrderType;//ERP订单类型

    @Schema(name = "订单类型")
    private String orderType;//订单类型

    @Schema(name = "波次")
    private String waveNum;//波次

    @Schema(name = "门店名称")
    private String shopName;//门店名称

    @Schema(name = "创建时间")
    private String createTime;//创建时间

    private Date createTimeStart;//创建时间

    private Date createTimeEnd;//创建时间

}
