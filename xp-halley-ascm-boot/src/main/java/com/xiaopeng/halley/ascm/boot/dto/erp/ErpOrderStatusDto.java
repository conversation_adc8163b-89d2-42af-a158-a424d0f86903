package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * ERP物料到货数据同步实体
 *
 * <AUTHOR>
 * @Date 2022/5/9 3:37 PM
 */
@Data
@Deprecated
public class ErpOrderStatusDto {

    @JsonProperty(value = "ZZVBELN")
    private String ZZVBELN; //ERP交货单号

    @JsonProperty(value = "STATUS_VALUE")
    private String STATUS_VALUE; // ERP订单状态 10：已过账 20：未过账

    @JsonProperty(value = "ITEMS")
    private List<ErpBoxDto> ITEMS; //箱号行信息
}
