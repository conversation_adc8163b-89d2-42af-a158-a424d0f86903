package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;

@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class ErpReqExportDto {

    @ExcelProperty(value = "交货单号")
    private String deliveryOrderCode;

    @ExcelProperty(value = "ERP订单类型")
    private String erpOrderType;

    @ExcelProperty(value = "订单类型")
    private String orderType;

    @ExcelProperty(value = "需求类型")
    private String reqType;

    @ExcelProperty(value = "仓库编号")
    private String lgort;

    @ExcelProperty(value = "仓库名称")
    private String lgobe;

    @ExcelProperty(value = "门店编码")
    private String shopCode;

    @ExcelProperty(value = "门店名称")
    private String shopName;

    @ExcelProperty(value = "门店地址")
    private String shopAddress;

    @ExcelProperty(value = "预估总体积(m³)")
    private String estimatedVolume;

    @ExcelProperty(value = "波次")
    private String waveNum;

    @ExcelProperty(value = "物料号")
    private String matnr;

    @ExcelProperty(value = "物料描述")
    private String maktx;

    @ExcelProperty(value = "数量")
    private String quantity;
}
