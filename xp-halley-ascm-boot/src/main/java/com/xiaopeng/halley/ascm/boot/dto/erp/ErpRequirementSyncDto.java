package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * ERP需求接收DTO
 *
 * <AUTHOR>
 * @Date 2022/7/8 9:51 AM
 */
@Data
public class ErpRequirementSyncDto {

    @JsonProperty("VBELN")
    private String vbeln; //ERP交货单号

    @JsonProperty("LFART")
    private String lfart; //ERP交货单类型

    @JsonProperty("VTEXT")
    private String vtext; //ERP交货单类型描述

    @JsonProperty("LPRIO")
    private String lprio; //订单类型

    @JsonProperty("BEZEI")
    private String bezei; //订单类型描述

    @JsonProperty("WERKS")
    private String werks; //发货工厂

    @JsonProperty("NAME1")
    private String name1; //发货工厂描述

    @JsonProperty("LGORT")
    private String lgort; //发货仓库

    @JsonProperty("LGOBE")
    private String lgobe; //发货仓库描述

    @JsonProperty("KUNNR")
    private String kunnr; //客户编号

    @JsonProperty("NAME_ORG1")
    private String nameOrg1; //客户描述

    @JsonProperty("ZSDCTY")
    private String zsdcty; //送达城市

    @JsonProperty("ZSCADD")
    private String zscadd; //送达地址

    @JsonProperty("ZYGTJ")
    private String zygtj; //预估总体积

    @JsonProperty("ZJSR")
    private String zjsr; //接车人

    @JsonProperty("ZJCRNUM")
    private String zjcrnum; //接车人联系方式

    @JsonProperty("ZFHR")
    private String zfhr; //发货仓库联系人

    @JsonProperty("ZFHNUM")
    private String zfhnum; //发货仓库联系人电话

    @JsonProperty("ZFHSF")
    private String zfhsf; //发货仓库省份

    @JsonProperty("ZFHCTY")
    private String zfhcty; //发货仓库城市

    @JsonProperty("ZFHADD")
    private String zfhadd; //发货仓库地址

    @JsonProperty("ZSDSF")
    private String zsdsf; //送达省份

    @JsonProperty("ZNUM")
    private String znum; //总项次

    @JsonProperty("ERDAT")
    private String erdat; //创建日期

    @JsonProperty("ERZET")
    private String erzet; //创建时间

    @JsonProperty("ITEMS")
    private List<ITEMSDTO> items;

    @Data
    public static class ITEMSDTO {

        @JsonProperty("POSNR")
        private String posnr; //行项目

        @JsonProperty("MATNR")
        private String matnr; //物料号

        @JsonProperty("MAKTX")
        private String maktx; //物料描述

        @JsonProperty("LFIMG")
        private String lfimg; //数量

        @JsonProperty("MEINS")
        private String meins; //单位

        @JsonProperty("ZYGTJ")
        private String zygtj; //预估体积(立方米)

        @JsonProperty("WAVE")
        private String wave; //波次号
    }
}
