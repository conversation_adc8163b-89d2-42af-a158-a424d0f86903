package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-5-16 11:37
 */
@Data
public class ErpSyncPTAndDTDto {
    @JsonProperty(value = "VBELN")
    private String VBELN;//ERP交货单

    @JsonProperty(value = "ZXDRQ")
    private String ZXDRQ;//下单日期

    @JsonProperty(value = "ZXDSJ")
    private String ZXDSJ;//下单时间

    @JsonProperty(value = "ZPHRQ")
    private String ZPHRQ;//配货日期

    @JsonProperty(value = "ZPHSJ")
    private String ZPHSJ;//配货时间

    @JsonProperty(value = "ZSEND")
    private String ZSEND;//发送标识

    @JsonProperty(value = "EXENAME")
    private String EXENAME;//发送者

    @JsonProperty(value = "EXEDATE")
    private String EXEDATE;//发送日期

    @JsonProperty(value = "EXETIME")
    private String EXETIME;//发送时间

    @JsonProperty(value = "ZYL01")
    private String ZYL01;//计划发运日期

    @JsonProperty(value = "ZYL02")
    private String ZYL02;//计划发运时间
}
