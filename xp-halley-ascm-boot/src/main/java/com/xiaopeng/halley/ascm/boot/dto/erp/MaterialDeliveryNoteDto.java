package com.xiaopeng.halley.ascm.boot.dto.erp;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-6 11:03
 * @Description:
 */
@Data
public class MaterialDeliveryNoteDto {
    /**
     * 交货单号
     */
    @JsonProperty(value = "VBELN")
    @Alias("deliveryOrderCode")
    private String VBELN;
    /**
     * ASN号
     */
    @JsonProperty(value = "VERUR")
    @Alias("asnCode")
    private String VERUR;
    /**
     * 预留01
     */
    @JsonProperty(value = "ZYL01")
    private String ZYL01;
    /**
     * 预留02
     */
    @JsonProperty(value = "ZYL02")
    private String ZYL02;
    /**
     * 预留03
     */
    @JsonProperty(value = "ZYL03")
    private String ZYL03;
    /**
     * 物料编号
     */
    @JsonProperty(value = "MATNR")
    private List<String> MATNR;
}
