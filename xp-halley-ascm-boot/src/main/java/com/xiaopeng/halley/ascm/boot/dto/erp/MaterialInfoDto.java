package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-6 10:41
 * @Description:
 */
@Data
public class MaterialInfoDto {
    /**
     * 物料编号
     */
    @JSONField(name = "MATNR")
    private String materialCode;
    /**
     * 物料描述
     */
    @JSONField(name = "MAKTX")
    private String materialDesc;
    /**
     * 物料长(mm)
     */
    @JSONField(name = "ZLENGTH")
    private String materialLength;
    /**
     * 物料宽(mm)
     */
    @J<PERSON>NField(name = "ZWIDTH")
    private String materialWidth;
    /**
     * 物料高(mm)
     */
    @JSONField(name = "ZHIGH")
    private String materialHeight;
    /**
     * 物料体积（mm3）
     */
    @JSONField(name = "ZVOLUME")
    private String materialVolume;
    /**
     * 供应商集合
     */
    private List<SupplyItem> ZSUPPLIERS;
}
