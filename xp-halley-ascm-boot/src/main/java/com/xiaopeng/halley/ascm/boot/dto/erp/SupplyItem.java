package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Author: tuyb
 * @Date: 2024-8-6 10:50
 * @Description:
 */
@Data
public class SupplyItem {
    /**
     * 供应商编码
     */
    @JSONField(name = "PARTNER")
    private String supplierCode;
    /**
     * 供应商描述
     */
    @JSONField(name = "NAME_ORG1")
    private String supplierName;
    /**
     * 供应商SNP
     */
    @JSONField(name = "ZVENSNP")
    private String supplierSnp;
    /**
     * 供应商外包装
     */
    @JSONField(name = "ZOUTP")
    private String supplierPackage;
    /**
     * 供应商包装方案
     */
    private List<SupplyPackPlanItem> ZWRAPINFO;
}
