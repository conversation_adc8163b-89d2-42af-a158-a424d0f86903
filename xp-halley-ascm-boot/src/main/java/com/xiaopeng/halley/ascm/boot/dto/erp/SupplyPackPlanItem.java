package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * @Author: tuyb
 * @Date: 2024-8-6 10:51
 * @Description:
 */
@Data
public class SupplyPackPlanItem {
    /**
     * 材料
     */
    @JSONField(name = "<PERSON><PERSON>")
    private String stuff;
    /**
     * 名称
     */
    @JSONField(name = "<PERSON><PERSON><PERSON>")
    private String stuffName;
    /**
     * 供应商货长
     */
    @JSONField(name = "ZVENL<PERSON>")
    private String materialLength;
    /**
     * 供应商货宽
     */
    @JSONField(name = "ZVENWID")
    private String materialWidth;
    /**
     * 供应商货高
     */
    @JSONField(name = "<PERSON>VE<PERSON>H<PERSON>")
    private String materialHeight;
    /**
     * 用量
     */
    @JSONField(name = "<PERSON><PERSON><PERSON>")
    private String dosage;
    /**
     * 是否回收
     */
    @JSONField(name = "ZRECEIVE")
    private String recycle;
}
