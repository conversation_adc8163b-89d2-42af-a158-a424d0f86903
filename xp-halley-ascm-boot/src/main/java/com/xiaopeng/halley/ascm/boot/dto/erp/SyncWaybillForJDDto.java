package com.xiaopeng.halley.ascm.boot.dto.erp;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/6/30 10:12
 */
@Data
public class SyncWaybillForJDDto {
    /**
     * 运单号
     */
    String waybillCode;
    /**
     * 物流公司
     */
    String logisticsCompany;
    /**
     * 订单类型
     */
    String orderType;
    /**
     * 运输方式
     */
    String transportType;
    /**
     * 接收时间
     */
    Date receiveTime;
    /**
     * 下发时间
     */
    Date sendTime;
    /**
     * 发运时间
     */
    Date dispatchTime;
    /**
     * 计划到达时间
     */
    Date planArriveTime;
    /**
     * 实际到达时间
     */
    Date actualArriveTime;
    /**
     * 仓库编码
     */
    String lgort;
    /**
     * 门店编码
     */
    String shopCode;
    /**
     * 门店名称
     */
    String shopName;
    /**
     * 门店地址
     */
    String shopAddress;
    /**
     * 运单号
     */
    String trackingNumber;
    /**
     * 总箱数
     */
    Integer totalBox;
    /**
     * 交货单集合
     */
    List<String> deliveryOrderCodeList;
    /**
     * 箱子集合
     */
    List<BoxListDto> boxList;
}
