package com.xiaopeng.halley.ascm.boot.dto.erp;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/7/4 14:49
 */
@Data
public class SyncWaybillResultDto {
    /**
     * 订单类型
     */
    String orderType;
    /**
     * 运输方式
     */
    String transportType;
    /**
     * 运单状态
     */
    String status;
    /**
     * 运单号
     */
    String waybillCode;
    /**
     * 仓库编码
     */
    String lgort;
    /**
     * 客户编码
     */
    String shopCode;
    /**
     * 司机联系电话
     */
    String driverContactNum;
    /**
     * 总体积
     */
    Double totalVolume;
    /**
     * 接收时间
     */
    Date receiveTime;
    /**
     * 下发时间
     */
    String sendTime;
    /**
     * 发运时间
     */
    String dispatchTime;
    /**
     * 路线
     */
    String path;
    /**
     * 总箱数
     */
    Integer totalBox;
    /**
     * 交货单集合
     */
    List<String> deliveryOrderCodeList;
    /**
     * 箱子集合
     */
    List<BoxListDto> boxList;
}
