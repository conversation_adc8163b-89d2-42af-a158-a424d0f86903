package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * wms运单传输结构体
 *
 * <AUTHOR>
 * @Date 2022/9/28 3:33 PM
 */
@NoArgsConstructor
@Data
public class WmsWaybillDto {

    @JsonProperty("ZYDBH")
    private String ZYDBH;//运单编号

    /**
     * 这个值的枚举是什么
     * 10：生成运单
     * 20：取消运单
     * 30：追加箱子
     * 40：追加物料
     */
    @JsonProperty("STATUS_VALUE")
    private String statusValue;//运单状态

    @JsonProperty("WERKS")
    private String werks; //发货工厂

    @JsonProperty("NAME1")
    private String name1; //发货工厂描述

    @JsonProperty("KUNNR")
    private String kunnr; //客户编号

    @JsonProperty("NAME_ORG1")
    private String nameOrg1; //客户描述

    @JsonProperty("ZSDSF")
    private String zsdsf; //送达省份

    @JsonProperty("ZSDCTY")
    private String zsdcty; //送达城市

    @JsonProperty("ZSCADD")
    private String zscadd; //送达地址

    @JsonProperty("ZJSR")
    private String zjsr; //接车人

    @JsonProperty("ZJCRNUM")
    private String zjcrnum; //接车人联系方式

    @JsonProperty("LGORT")
    private String lgort; //发货仓库

    @JsonProperty("LGOBE")
    private String lgobe; //发货仓库描述

    @JsonProperty("ZFHR")
    private String zfhr; //发货仓库联系人

    @JsonProperty("ZFHNUM")
    private String zfhnum; //发货仓库联系人电话

    @JsonProperty("ZFHSF")
    private String zfhsf; //发货仓库省份

    @JsonProperty("ZFHCTY")
    private String zfhcty; //发货仓库城市

    @JsonProperty("ZFHADD")
    private String zfhadd; //发货仓库地址

    @JsonProperty("ITEMS")
    private List<WmsWaybillItemDto> items;
}
