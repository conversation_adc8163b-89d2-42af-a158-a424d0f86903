package com.xiaopeng.halley.ascm.boot.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class WmsWaybillItemDto {

    @JsonProperty("CARTON")
    private String carton;//箱号

    @JsonProperty("ZDXZL")
    private Double zdxzl;//单箱重量

    @JsonProperty("POSNR")
    private String posnr; //行项目

    @JsonProperty("WAVE")
    private String wave; //波次号

    @JsonProperty("LPRIO")
    private String lprio; //订单类型

    @JsonProperty("BEZEI")
    private String bezei; //订单类型描述

    @JsonProperty("ZZVBELN")
    private String zzvbeln;//ERP交货单

    @JsonProperty("VTEXT")
    private String vtext; //ERP交货单类型描述

    @JsonProperty("ZOASOR")
    private String zoasor; //OAS订单号

    @JsonProperty("LFIMG")
    private Integer lfimg;//订单数量

    @JsonProperty("QTY")
    private Integer qty;//包装数量

    @JsonProperty("ZBZRQ")
    private String zbzrq;//包装日期

    @JsonProperty("ZBZSJ")
    private String zbzsj;//包装时间

    @JsonProperty("ZSJTJ")
    private Double zsjtj;//实际体积（立方米）

    @JsonProperty("ZCD")
    private String zcd;//包材长（毫米）

    @JsonProperty("ZKD")
    private String zkd;//包材宽（毫米）

    @JsonProperty("ZGD")
    private String zgd;//包材高（毫米）

    @JsonProperty("PMAT")
    private String pmat;//包装材料编号

    @JsonProperty("ZBZTJ")
    private Double zbztj;//包装体积（立方米）

    @JsonProperty("PRODUCTNO")
    private String productno;//物料号

    @JsonProperty("MAKTX")
    private String maktx;//物料描述

    @JsonProperty("CD")
    private String cd;//物料长

    @JsonProperty("KD")
    private String kd;//物料宽

    @JsonProperty("GD")
    private String gd;//物料高

    @JsonProperty("ZWLTJ")
    private Double zwltj;//物料体积

    @JsonProperty("ZYL02")
    private String ZYL02;//原交货单号
}

