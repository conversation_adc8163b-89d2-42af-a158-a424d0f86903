package com.xiaopeng.halley.ascm.boot.dto.importExport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author：huqizhi
 * @Date：2023/9/14 16:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseShopBatteryExport {

    @ColumnWidth(10)
    @ExcelProperty("骁龙门店编码")
    @Schema(name = "xlShopCode", description = "骁龙门店编码")
    private String xlShopCode;

    @ColumnWidth(10)
    @ExcelProperty("装运联系人")
    @Schema(name = "cargoContact", description = "装运联系人")
    private String cargoContact;//装货联系人

    @ColumnWidth(10)
    @ExcelProperty("装运联系人电话")
    @Schema(name = "cargoContactPhone", description = "装运联系人电话")
    private String cargoContactPhone;//装货联系人电话

    @ColumnWidth(10)
    @ExcelProperty("区域")
    @Schema(name = "region", description = "区域")
    private String region;//区域

    @ColumnWidth(10)
    @ExcelProperty("失败原因")
    @Schema(name = "failedReason", description = "失败原因")
    private String failedReason;
}
