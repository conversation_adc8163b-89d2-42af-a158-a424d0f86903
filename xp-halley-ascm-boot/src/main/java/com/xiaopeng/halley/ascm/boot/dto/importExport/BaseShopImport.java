package com.xiaopeng.halley.ascm.boot.dto.importExport;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023-5-16 13:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class BaseShopImport {
    @ColumnWidth(15)
    @ExcelProperty("门店编码")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;
    @ColumnWidth(22)
    @ExcelProperty("门店名称")
    @Schema(name = "shopName", description = "门店名称")
    private String shopName;
    @ColumnWidth(22)
    @ExcelProperty("门店省份")
    @Schema(name = "shopProvince", description = "门店省份")
    private String shopProvince;
    @ColumnWidth(12)
    @ExcelProperty("门店城市")
    @Schema(name = "shopCity", description = "门店城市")
    private String shopCity;
    @ColumnWidth(22)
    @ExcelProperty("门店地址")
    @Schema(name = "shopAddress", description = "门店地址")
    private String shopAddress;
    @ColumnWidth(12)
    @ExcelProperty("联系电话")
    @Schema(name = "contactNum", description = "联系电话")
    private String contactNum;
    @ColumnWidth(9)
    @ExcelProperty("联系人")
    @Schema(name = "contactPerson", description = "联系人")
    private String contactPerson;
    @ColumnWidth(7)
    @ExcelProperty("经度")
    @Schema(name = "lat", description = "经度")
    private String lat;
    @ColumnWidth(7)
    @ExcelProperty("纬度")
    @Schema(name = "lon", description = "纬度")
    private String lon;
    @ColumnWidth(9)
    @ExcelProperty("围栏范围")
    @Schema(name = "fenceRange", description = "围栏范围")
    private String fenceRange;
    @ColumnWidth(9)
    @ExcelProperty("失败原因")
    @Schema(name = "fenceRange", description = "失败原因")
    private String failureReason;
}
