package com.xiaopeng.halley.ascm.boot.dto.importExport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 导入导出
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportDto {

    @Schema(name = "tempFile", description = "下载模板")
    private String tempFile;

    @Schema(name = "importCheckList", description = "导入校验")
    private String importCheckList;

    @Schema(name = "failDownload", description = "下载错误文件")
    private String failDownload;

    @Schema(name = "importList", description = "导入成功数据")
    private String importList;

    @Schema(name = "export", description = "导出")
    private String export;

    @Schema(name = "搜索formID", description = "用于导出前端获取搜索值")
    private String formId;

}
