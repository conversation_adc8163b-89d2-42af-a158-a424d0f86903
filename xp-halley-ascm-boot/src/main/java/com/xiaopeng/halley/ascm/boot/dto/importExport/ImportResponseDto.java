package com.xiaopeng.halley.ascm.boot.dto.importExport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImportResponseDto {

    @Schema(name = "successCount", description = "成功条数")
    private Integer successCount;

    @Schema(name = "failCount", description = "失败条数")
    private Integer failCount;

    @Schema(name = "failFileCode", description = "文件获取代码")
    private String fileCode;
}
