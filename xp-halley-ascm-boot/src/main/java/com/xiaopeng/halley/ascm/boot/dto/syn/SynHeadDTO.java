package com.xiaopeng.halley.ascm.boot.dto.syn;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SynHeadDTO {

    @Schema(name = "BUSID", title = "接口编号：VSCM0010")
    @JsonProperty(value = "BUSID")
    private String BUSID;

    @Schema(name = "RECID", title = "消息ID：32位UUID")
    @JsonProperty(value = "RECID")
    private String RECID;

    @Schema(name = "SENDER", title = "发送方：VSCM")
    @JsonProperty(value = "SENDER")
    private String SENDER;

    @Schema(name = "RECEIVER", title = "接收方：ERP")
    @JsonProperty(value = "RECEIVER")
    private String RECEIVER;

    @Schema(name = "DTSEND", title = "发送时间：格式YYYYMMDDHHMMSS")
    @JsonProperty(value = "DTSEND")
    private String DTSEND;

}
