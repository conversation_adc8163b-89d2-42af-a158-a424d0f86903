package com.xiaopeng.halley.ascm.boot.dto.syn;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SynRequestDTO<T> {

    @Schema(name = "DATA", title = "提交数据")
    @JsonProperty(value = "DATA")
    private List<T> DATA;

    @Schema(name = "HEADER", title = "头数据")
    @JsonProperty(value = "HEADER")
    private SynHeadDTO HEADER;
}
