package com.xiaopeng.halley.ascm.boot.dto.syn;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SynReturnDTo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "RECID", title = "ASCM发送ID")
    @J<PERSON>NField(name = "RECID")
    private String RECID;

    @Schema(name = "ZSTATUS", title = "E:错误S:成功")
    @JSONField(name = "ZSTATUS")
    private String ZSTATUS;

    @Schema(name = "ZMESSAGE", title = "备注内容")
    @JSONField(name = "ZMESSAGE")
    private String ZMESSAGE;

    public static SynReturnDTo getSuccess(String RECID) {
        SynReturnDTo erpReturnDto = new SynReturnDTo();
        erpReturnDto.setRECID(RECID);
        erpReturnDto.setZMESSAGE("成功");
        erpReturnDto.setZSTATUS("S");
        return erpReturnDto;
    }

    public static SynReturnDTo getError(String RECID, String errorMsg) {
        SynReturnDTo erpReturnDto = new SynReturnDTo();
        erpReturnDto.setRECID(RECID);
        erpReturnDto.setZMESSAGE(errorMsg);
        erpReturnDto.setZSTATUS("E");
        return erpReturnDto;
    }

    public static void main(String[] args) {
        SynReturnDTo erpReturnDto = getSuccess("abc");
        System.out.println(JSONObject.toJSONString(erpReturnDto));
    }
}
