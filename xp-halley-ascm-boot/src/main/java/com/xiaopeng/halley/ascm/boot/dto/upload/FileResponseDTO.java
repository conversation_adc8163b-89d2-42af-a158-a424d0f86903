package com.xiaopeng.halley.ascm.boot.dto.upload;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileResponseDTO implements Serializable {

    @Schema(name = "fileId", description = "文件ID")
    private String fileId;

    @Schema(name = "url", description = "访问URL")
    private String url;

    @Schema(name = "fileName", description = "文件名称")
    private String fileName;
}
