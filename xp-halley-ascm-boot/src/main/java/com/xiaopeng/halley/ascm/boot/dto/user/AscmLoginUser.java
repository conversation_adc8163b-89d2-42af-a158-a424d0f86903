package com.xiaopeng.halley.ascm.boot.dto.user;

import lombok.Data;

@Data
public class AscmLoginUser {

    private String sid;
    private String userId;
    private String username;
    private String name;
    private String jobNum;
    private String avatar;
    private String sex;
    private String email;
    private String phone;
    private String status;
    private String positionId;
    private String positionName;
    private Integer positionType;
    private String positionTypeCode;
    private String positionTypeName;
    private String orgId;
    private String orgCode;
    private String orgName;
    private String deptId;
    private String deptCode;
    private String deptName;
    private boolean isEnable;
    private boolean isSuperAdmin = false;
    private String roleId;
    private String appId;
    private String appCode;
    private String appName;
    private Integer language;
    private String languageCode;
    private Integer timezone;
    private String timezoneCode;
    private String carrier;
    private String manager;
    private String factory;
    private String apptab;
    private String auth;
}
