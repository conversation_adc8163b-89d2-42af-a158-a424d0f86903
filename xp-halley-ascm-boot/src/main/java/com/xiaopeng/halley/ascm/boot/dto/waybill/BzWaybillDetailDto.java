package com.xiaopeng.halley.ascm.boot.dto.waybill;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BzWaybillDetailDto {

    @Schema(name = "id", title = "id")
    private Long id;

    @Schema(name = "createUserName", title = "创建人")
    private String createUserName;

    @Schema(name = "createTime", title = "创建时间")
    private Date createTime;

    @Schema(name = "updateUserName", title = "更新人")
    private String updateUserName;

    @Schema(name = "updateTime", title = "更新时间")
    private Date updateTime;

    @Schema(name = "status", title = "运单状态")
    private String status;

    @Schema(name = "orderType", title = "订单类型")
    private String orderType;

    @Schema(name = "transportType", title = "运输类型")
    private String transportType;

    @Schema(name = "waybillCode", title = "运单编码")
    private String waybillCode;

    @Schema(name = "ascmCodes", title = "ASCM号")
    private List<String> ascmCodes;

    @Schema(name = "carrierCode", title = "承运商编码")
    private String carrierCode;

    @Schema(name = "carrierName", title = "承运商名称")
    private String carrierName;

    @Schema(name = "pathExpiry", title = "线路时效")
    private int pathExpiry;

    @Schema(name = "path", title = "线路")
    private String path;

    @Schema(name = "logisticsCompany", title = "物流公司")
    private String logisticsCompany;

    @Schema(name = "logisticsCode", title = "物流单号")
    private String logisticsCode;

    @Schema(name = "driverName", title = "司机姓名")
    private String driverName;

    @Schema(name = "driverPhone", title = "司机电话")
    private String driverPhone;

    @Schema(name = "lgort", title = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", title = "仓库名称")
    private String lgobe;

    @Schema(name = "warehouseProvince", title = "仓库省份")
    private String warehouseProvince;

    @Schema(name = "warehouseCity", title = "仓库城市")
    private String warehouseCity;

    @Schema(name = "contactNum", title = "仓库联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", title = "仓库联系人")
    private String contactPerson;

    @Schema(name = "warehouseAddress", title = "仓库地址")
    private String warehouseAddress;

    @Schema(name = "compensateType", title = "索赔类型")
    private String compensateType;

    @Schema(name = "compensateReason", title = "索赔原因")
    private String compensateReason;

    @Schema(name = "shopCode", title = "门店编码")
    private String shopCode;

    @Schema(name = "shopName", title = "门店名称")
    private String shopName;

    @Schema(name = "shopProvince", title = "门店省份")
    private String shopProvince;

    @Schema(name = "shopCity", title = "门店城市")
    private String shopCity;

    @Schema(name = "shopContactNum", title = "门店联系电话")
    private String shopContactNum;

    @Schema(name = "shopContactPerson", title = "门店联系人")
    private String shopContactPerson;

    @Schema(name = "shopAddress", title = "门店地址")
    private String shopAddress;

    @Schema(name = "carPlate", title = "车牌号")
    private String carPlate;

    @Schema(name = "carType", title = "车型")
    private String carType;

    @Schema(name = "boxRelList", title = "箱号信息")
    private IPage<BzErpReqBoxRelDto> boxRelList;

    @Schema(name = "totalVolume", title = "总体积")
    private String totalVolume;

    @Schema(name = "totalWeight", title = "总重量")
    private String totalWeight;

//    @Schema(name = "materialList", title = "物料信息")
//    private List<BzErpReqMaterialRelDto> materialList;

}
