package com.xiaopeng.halley.ascm.boot.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto
 * @Date 2024/4/26 10:02
 */
@Data
public class BzWaybillDto {

    @NotEmpty
    @Schema(name = "id", title = "id")
    private Long id;

    @Schema(name = "status", title = "运单状态")
    private String status;

    @Schema(name = "orderType", title = "订单类型")
    private String orderType;

    @Schema(name = "transportType", title = "运输类型")
    private String transportType;

    @Schema(name = "waybillCode", title = "运单编码")
    private String waybillCode;

    @Schema(name = "carrierCode", title = "承运商编码")
    private String carrierCode;

    @Schema(name = "carrierName", title = "承运商名称")
    private String carrierName;

    @Schema(name = "logisticsCompany", title = "物流公司")
    private String logisticsCompany;

    @Schema(name = "logisticsCode", title = "物流单号")
    private String logisticsCode;

    @Schema(name = "driverName", title = "司机姓名")
    private String driverName;

    @Schema(name = "driverPhone", title = "司机电话")
    private String driverPhone;

    @Schema(name = "lgort", title = "仓库编码")
    private String lgort;

    @Schema(name = "lgobe", title = "仓库名称")
    private String lgobe;

    @Schema(name = "warehouseProvince", title = "仓库省份")
    private String warehouseProvince;

    @Schema(name = "warehouseCity", title = "仓库城市")
    private String warehouseCity;

    @Schema(name = "contactNum", title = "仓库联系电话")
    private String contactNum;

    @Schema(name = "contactPerson", title = "仓库联系人")
    private String contactPerson;

    @Schema(name = "warehouseAddress", title = "仓库地址")
    private String warehouseAddress;

    @Schema(name = "shopCode", title = "门店编码")
    private String shopCode;

    @Schema(name = "shopName", title = "门店名称")
    private String shopName;

    @Schema(name = "shopProvince", title = "门店省份")
    private String shopProvince;

    @Schema(name = "shopCity", title = "门店城市")
    private String shopCity;

    @Schema(name = "shopContactNum", title = "门店联系电话")
    private String shopContactNum;

    @Schema(name = "shopContactPerson", title = "门店联系人")
    private String shopContactPerson;

    @Schema(name = "shopAddress", title = "门店地址")
    private String shopAddress;

    @Schema(name = "carPlate", title = "车牌号")
    private String carPlate;

    @Schema(name = "carType", title = "车型")
    private String carType;

    @Schema(name = "totalVolume", title = "总体积")
    private Double totalVolume;

    @Schema(name = "totalWeight", title = "总重量")
    private Double totalWeight;

    @Schema(name = "path", title = "路线")
    private String path;

    private String route;

    private Integer sync;

    private Integer trackSync;
}
