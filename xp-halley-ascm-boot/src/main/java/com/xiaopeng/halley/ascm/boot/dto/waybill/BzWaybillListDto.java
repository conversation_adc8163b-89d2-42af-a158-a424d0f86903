package com.xiaopeng.halley.ascm.boot.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
  PDA运单列表Dto
 */
@Data
public class BzWaybillListDto {

    @Schema(name = "waybillCode", title = "运单编号")
    private String waybillCode;

    @Schema(name = "shopCode", title = "门店编号")
    private String shopCode;

    @Schema(name = "shopCity", title = "门店所在城市")
    private String shopCity;

    @Schema(name = "warehouseCity", title = "仓库所在城市")
    private String warehouseCity;

    @Schema(name = "totalBox", title = "箱子数量")
    private int totalBox;

    @Schema(name = "createTime", title = "订单创建时间")
    private String createTime;

    @Schema(name = "scannedBox", title = "已扫描的箱子数量")
    private int scannedBox;

    @Schema(name = "unscannedBox", title = "未扫描的箱子数量")
    private int unscannedBox;

    @Schema(name = "shopName", title = "仓库（起点）")
    private String shopName;

    @Schema(name = "lgobe", title = "仓库名称")
    private String lgobe;

    @Schema(name = "orderType", title = "订单类型")
    private String orderType;

    @Schema(name = "isCompletePacking", title = "是否包装完成：0未完成 1已完成")
    private Integer isCompletePacking;

    @Schema(name = "abnormalCause", title = "运单异常原因")
    private String abnormalCause;

    @Schema(name = "driverPhone", title = "司机手机号")
    private String driverPhone;

    @Schema(name = "transportType", title = "运输类型")
    private String transportType;

    private Double waybillCodeDouble;

}
