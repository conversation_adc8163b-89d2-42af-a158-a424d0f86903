package com.xiaopeng.halley.ascm.boot.dto.waybill;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.dto.waybill
 * @Date 2024/6/12 11:09
 */
@Data
public class MergeWaybillDto {

    @NotNull(message = "请选择需要合单的子运单")
    List<Object> childWaybillId;

    @NotNull(message = "请选择需要合单的父运单")
    Long parentWaybillId;

}
