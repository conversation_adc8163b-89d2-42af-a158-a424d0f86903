package com.xiaopeng.halley.ascm.boot.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderSeparateRequestDTO {

    @Schema(name = "waybillId", description = "运单号")
    @NotNull(message = "运单号")
    private Long waybillId;

    @Schema(name = "boxCode", description = "箱子号")
    private List<String> boxCodeList;
}
