package com.xiaopeng.halley.ascm.boot.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * PDA运单页面请求体
 *
 * <AUTHOR>
 * @Date 2022/7/18 4:58 PM
 */
@Data
public class PDAWaybillListPageRequestDto {
    @Schema(name = "searchKey", description = "门店名称或运单号")
    private String searchKey;

    @Schema(name = "city", description = "城市")
    private String city = "";

    @Schema(name = "shopName", description = "门店名称")
    private String shopName = "";

    @Schema(name = "shopCodeList", description = "门店编码 集合")
    private List<String> shopCodeList = new ArrayList<>();

    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode = "";

    @Schema(name = "status", description = "状态")
    private String status = "";

    @Schema(name = "lgort", description = "仓库")
    private String lgort = "";

    @Schema(name = "pageType", description = "页面类型")
    private Integer pageType = 0;
}
