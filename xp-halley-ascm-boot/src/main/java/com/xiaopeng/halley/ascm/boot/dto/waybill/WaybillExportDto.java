package com.xiaopeng.halley.ascm.boot.dto.waybill;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import lombok.Data;

import java.util.Date;

/**
 * 正向运单导出Dto
 *
 * <AUTHOR>
 * @Date 2022/7/14 6:00 PM
 */

@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
public class WaybillExportDto {

    @ExcelProperty(value = "运单编号")
    private String waybillCode;

    @ExcelProperty(value = "交货单号")
    private String deliveryOrderCode;

    @ExcelProperty(value = "总箱数")
    private String totalBox;

    @ExcelProperty(value = "订单类型")
    private String orderType;

    @ExcelProperty(value = "运输类型")
    private String transportType;

    @ExcelProperty(value = "仓库编号")
    private String lgort;

    @ExcelProperty(value = "仓库名称")
    private String lgobe;

    @ExcelProperty(value = "承运商名称")
    private String carrierName;

    @ExcelProperty(value = "门店省份")
    private String shopProvince;

    @ExcelProperty(value = "门店城市")
    private String shopCity;

    @ExcelProperty(value = "门店编码")
    private String shopCode;

    @ExcelProperty(value = "门店名称")
    private String shopName;

    @ExcelProperty(value = "门店地址")
    private String shopAddress;

    @ExcelProperty(value = "线路")
    private String path;

    @ExcelProperty(value = "线路时效")
    private Integer pathExpiry;

    @ExcelProperty(value = "发车时间")
    private Date departureTime;

    @ExcelProperty(value = "签收时间")
    private Date signTime;

    @ExcelProperty(value = "运单状态")
    private String waybillStatus;

    @ExcelProperty(value = "箱号")
    private String boxCode;

    @ExcelProperty(value = "实际总体积")
    private String actualVolume;

    @ExcelProperty(value = "箱号状态")
    private String status;

    @ExcelProperty(value = "包装类别")
    private String packageType;

    @ExcelProperty(value = "包装材料编号")
    private String packageCode;

    @ExcelProperty(value = "尺寸（长）")
    private String boxLong;

    @ExcelProperty(value = "尺寸（宽）")
    private String boxWidth;

    @ExcelProperty(value = "尺寸（高）")
    private String boxHeight;

    @ExcelProperty(value = "实际体积")
    private String boxVolume;

    @ExcelProperty(value = "单箱重量")
    private String boxWeight;

    @ExcelProperty(value = "发车时间2")
    private String newDepartureTime;

    @ExcelProperty(value = "签收时间2")
    private String newSignTime;

    @ExcelProperty(value = "应送达时间2")
    private String newExpiryTime;

    @ExcelProperty(value = "物流公司")
    private String logisticsCompany;

    @ExcelProperty(value = "物流单号")
    private String logisticsCode;

    @ExcelProperty(value = "运单创建时间")
    private Date waybillCreateTime;

    @ExcelProperty(value = "应送达时间")
    private Date expiryTime;

}
