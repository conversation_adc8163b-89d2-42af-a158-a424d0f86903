package com.xiaopeng.halley.ascm.boot.dto.waybill;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 正向运单导出搜索条件
 *
 * <AUTHOR>
 * @Date 2022/7/13 11:11 PM
 */
@Data
public class WaybillExportSearchDto {

    //    @Schema(name = "运单编号")
//    private String waybillCode;//运单编号
//
//    private String[] waybillCodeList;
//
//    @Schema(name = "运单状态")
//    private String status;//运单状态
//
//    @Schema(name = "订单类型")
//    private String orderType;//订单类型
//
//    @Schema(name = "发货仓库")
//    private String lgort;//发货仓库
//
//    private String[] lgortList;
//
//    @Schema(name = "门店编号")
//    private String shopCode;//门店编号
//
//    private String[] shopCodeList;
//
//    @Schema(name = "门店城市")
//    private String shopCity;//门店城市
//
//    private String[] shopCityList;
//
//    @Schema(name = "门店省份")
//    private String shopProvince;//门店省份
//
//    @Schema(name = "物流单号")
//    private String logisticsCode;//物流单号
//
//    private String[] logisticsCodeList;
//
    //    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
//    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @Schema(name = "创建时间")
    private List<String> waybillCreateTime;//创建时间

    private String createTimeStart;//创建时间

    private String createTimeEnd;//创建时间

    private Long exportId;

}
