package com.xiaopeng.halley.ascm.boot.dto.waybill;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xiaopeng.halley.ascm.boot.common.SensitiveSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class WaybillStatusDto {

    @Schema(name = "运单号")
    private String waybillCode;

    @Schema(name = "运单状态")
    private String status;

    @Schema(name = "司机手机")
    private String driverPhone;

    @Schema(name = "车牌号")
    private String carPlate;

    @Schema(name = "车型")
    private String carType;

    @Schema(name = "司机姓名")
    private String driverName;

    @Schema(name = "计划送达时间")
    private Date expiryTime;

    @Schema(name = "计划送达时间")
    private String transportType;

    @Schema(name = "线路时效")
    private Integer pathExpiry;

}
