package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("base_battery_project")
public class BaseBatteryProject implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    //PACK国标码
    @TableField("pack_num")
    private String packNum;
    //PACK国标码（9位）
    @TableField("pack_num_nine")
    private String packNumNine;
    //PACK型号
    @TableField("pack_type_num")
    private String packTypeNum;
    //项目
    @TableField("project")
    private String project;
    //创建人id
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    //创建人
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField("is_delete")
    private Integer isDelete;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}
