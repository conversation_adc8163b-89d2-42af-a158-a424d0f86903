package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("base_battery_route_cost")
public class BaseBatteryRouteCost {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("repair_center_num")
    private String repairCenterNum;
    @TableField("repair_center_name")
    private String repairCenterName;
    @TableField("dispatch_city")
    private String dispatchCity;
    @TableField("arrival_city")
    private String arrivalCity;
    @TableField("logistics_provider")
    private String logisticsProvider;
    @TableField("logistics_type")
    private String logisticsType;
    @TableField("route_name")
    private String routeName;
    @TableField("route_delivery_time")
    private Double routeDeliveryTime;
    @TableField("kilometers_num")
    private Double kilometersNum;
    @TableField("car_type")
    private Integer carType;
    @TableField("expense")
    private Double expense;
    @TableField("tax_rates")
    private Double taxRates;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;

}
