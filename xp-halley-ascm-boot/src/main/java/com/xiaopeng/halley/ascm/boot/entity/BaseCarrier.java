package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BaseCarrier实体
 */
@TableName("base_carrier")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BaseCarrier extends Model<BaseCarrier> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("carrier_code")
    private String carrierCode;//承运商编码

    @TableField("carrier_name")
    private String carrierName;//承运商名称

    @TableField("address")
    private String address;//供应商地址

    @TableField("contact_person")
    private String contactPerson;//联系人

    @TableField("contact_num")
    private String contactNum;//电话

    @TableField("email")
    private String email;//电子邮件

    @TableField("status")
    private Integer status;//状态

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField("create_user_name")
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField("update_user_name")
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BaseCarrier setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取承运商编码
     *
     * @return 承运商编码
     */
    public String getCarrierCode() {
        return this.carrierCode;
    }

    /**
     * 设置承运商编码
     *
     * @param carrierCode 承运商编码
     * @return 当前对象
     */
    public BaseCarrier setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
        return this;
    }

    /**
     * 获取承运商名称
     *
     * @return 承运商名称
     */
    public String getCarrierName() {
        return this.carrierName;
    }

    /**
     * 设置承运商名称
     *
     * @param carrierName 承运商名称
     * @return 当前对象
     */
    public BaseCarrier setCarrierName(String carrierName) {
        this.carrierName = carrierName;
        return this;
    }

    /**
     * 获取供应商地址
     *
     * @return 供应商地址
     */
    public String getAddress() {
        return this.address;
    }

    /**
     * 设置供应商地址
     *
     * @param address 供应商地址
     * @return 当前对象
     */
    public BaseCarrier setAddress(String address) {
        this.address = address;
        return this;
    }

    /**
     * 获取联系人
     *
     * @return 联系人
     */
    public String getContactPerson() {
        return this.contactPerson;
    }

    /**
     * 设置联系人
     *
     * @param contactPerson 联系人
     * @return 当前对象
     */
    public BaseCarrier setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
        return this;
    }

    /**
     * 获取电话
     *
     * @return 电话
     */
    public String getContactNum() {
        return this.contactNum;
    }

    /**
     * 设置电话
     *
     * @param contactNum 电话
     * @return 当前对象
     */
    public BaseCarrier setContactNum(String contactNum) {
        this.contactNum = contactNum;
        return this;
    }

    /**
     * 获取电子邮件
     *
     * @return 电子邮件
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * 设置电子邮件
     *
     * @param email 电子邮件
     * @return 当前对象
     */
    public BaseCarrier setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * 获取状态
     *
     * @return 状态
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     * @return 当前对象
     */
    public BaseCarrier setStatus(Integer status) {
        this.status = status;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BaseCarrier setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BaseCarrier setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BaseCarrier setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BaseCarrier setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BaseCarrier setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BaseCarrier setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BaseCarrier setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}