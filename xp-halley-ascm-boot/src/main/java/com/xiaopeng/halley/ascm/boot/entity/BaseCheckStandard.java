package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.entity
 * @Date 2024/9/9 11:15
 */
@Data
@TableName("base_check_standard")
public class BaseCheckStandard {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String checkCode;
    private String checkDesc;
    private String checkDocs;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    private Integer version;
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
}
