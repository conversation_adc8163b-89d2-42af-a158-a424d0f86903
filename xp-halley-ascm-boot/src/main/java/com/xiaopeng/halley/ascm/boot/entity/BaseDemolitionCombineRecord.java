package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
@TableName("base_demolition_combine_record")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
public class BaseDemolitionCombineRecord extends Model<BaseDemolitionCombineRecord> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("parent_waybill_code")
    private String parentWaybillCode;

    @TableField("action")
    private String action;

    @TableField("child_waybill_code")
    private String childWaybillCode;

    @TableField("item")
    private String item;

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间
}
