package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
@TableName("base_express_transshipment")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
public class BaseExpressTransshipment extends Model<BaseExpressTransshipment> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("company_name")
    private String companyName;//公司名称

    @TableField("company_code")
    private String companyCode;//公司编码

    @TableField("company_type")
    private String companyType;//公司类型

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间
}
