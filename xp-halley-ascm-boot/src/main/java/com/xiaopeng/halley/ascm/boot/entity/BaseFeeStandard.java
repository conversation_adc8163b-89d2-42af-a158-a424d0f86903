package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

@Data
@ApiModel("零担快递费用标准")
@TableName("base_fee_standard")
public class BaseFeeStandard {

	@TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("合同编号")
    private String contractCode;

    @ApiModelProperty("发出城市")
    private String originCity;

    @ApiModelProperty("目标城市")
    private String destinationCity;

    @ApiModelProperty("是否主覆盖区域")
    private String isMainArea;

    @ApiModelProperty("车型")
    private String carType;

    @ApiModelProperty("距离(KM)")
    private Double distance;

    @ApiModelProperty("未税最低运费(元)")
    private Double minFee;

    @ApiModelProperty("未税单价(元/M3)")
    private Double unitPrice;

    @ApiModelProperty("未税首重运费(元)")
    private Double firstWeightFee;

    @ApiModelProperty("续重未税单价(元/kg)")
    private Double extraWeightPrice;

    @ApiModelProperty("类型 1-零担快递 2-专车")
    private Integer type;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人id")
    private String createUserId;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改人id")
    private String updateUserId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人")
    private String updateUserName;

    @TableLogic
    @ApiModelProperty("是否删除（1 - 已删除，0 - 正常）")
    private Integer isDelete;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
