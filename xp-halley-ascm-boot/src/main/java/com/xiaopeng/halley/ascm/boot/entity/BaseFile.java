package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件实体
 *
 * <AUTHOR> 自动生成
 * @date 2022-12-06 02:44:57
 */
@Data
@TableName("base_file")
public class BaseFile implements Serializable {
    /**
     * 保存文件名称
     */
    public static final String COL_FILE_SAVE_NAME = "file_save_name";
    public static final String COL_ID = "id";
    public static final String COL_FILE_NAME = "file_name";
    public static final String COL_FILE_UUID = "file_uuid";
    public static final String COL_PROJECT = "project";
    public static final String COL_PROJECT_PATH = "project_path";
    public static final String COL_CREATE_USER_ID = "create_user_id";
    public static final String COL_CREATE_USER_NAME = "create_user_name";
    public static final String COL_UPDATE_USER_ID = "update_user_id";
    public static final String COL_UPDATE_USER_NAME = "update_user_name";
    public static final String COL_IS_DELETE = "is_delete";
    public static final String COL_CREATE_TIME = "create_time";
    public static final String COL_UPDATE_TIME = "update_time";
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件UUID
     */
    private String fileUuid;
    private String fileSaveName;
    /**
     * 项目（ASCM，VSCM，OSCM，ASCM）
     */
    private String project;
    /**
     * 项目路径（ASCM，VSCM，OSCM，ASCM）
     */
    private String projectPath;
    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUserId;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUserName;
    /**
     * 修改人id
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    /**
     * 是否删除（1 - 已删除，0 - 正常）
     */
    private Boolean isDelete;
    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;

}
