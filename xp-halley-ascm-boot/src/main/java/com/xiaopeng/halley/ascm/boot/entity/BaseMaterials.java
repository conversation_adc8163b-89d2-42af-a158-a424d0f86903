package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 物料表
 *
 * @TableName base_materials
 */
@Data
public class BaseMaterials implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 物料编号
     */
    private String materialCode;
    /**
     * 物料描述
     */
    private String materialDesc;
    /**
     * 图片-左
     */
    private String picLeft;
    /**
     * 图片-右
     */
    private String picRight;
    /**
     * 图片-钢印
     */
    private String picStamp;
    /**
     * 图片-前
     */
    private String picBefore;
    /**
     * 图片-左
     */
    private String picAfter;
    /**
     * 图片-其他
     */
    private String picOther;
    /**
     * 图片是否完整
     */
    private String picComplete;
    /**
     * 单元包装 0:否，1:是
     */
    private Integer unitPackage;
    /**
     * 供应商包装
     */
    private String supplierPackage;
    /**
     * 物料长(mm)
     */
    private Integer materialLength;
    /**
     * 物料宽(mm)
     */
    private Integer materialWidth;
    /**
     * 物料高(mm)
     */
    private Integer materialHeight;
    /**
     * 物料体积(mm)
     */
    private Integer materialVolume;
    /**
     * 校验编码ID
     */
    private Integer checkCodeId;
    /**
     * 乐观锁
     */
    private Integer version;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;

}