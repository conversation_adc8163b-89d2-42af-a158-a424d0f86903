package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("远程打印配置表")
@TableName("base_print_config")
public class BasePrintConfig {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("仓库号")
    private String lgort;

    @ApiModelProperty("仓库名称")
    private String lgobe;

    @ApiModelProperty("路线")
    private String route;

    @ApiModelProperty("打印机名称")
    private String printerName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人id")
    private String createUserId;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改人id")
    private String updateUserId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人")
    private String updateUserName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
