package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.Date;

@Data
@TableName("base_print_out_task")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
public class BasePrintOutTask extends Model<BasePrintOutTask> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("waybill_code")
    private String waybillCode;//运单编号

    @TableField("delivery_order_code")
    private String deliveryOrderCode;//交货单号

    @TableField("order_type")
    private String orderType;//订单类型

    @TableField("base_file_id")
    private Long baseFileId;//pdf文件id

    @TableField("route")
    private String route;//路线

    @TableField("printer_name")
    private String printerName;//打印机名称

    @TableField("trigger_client")
    private String triggerClient;//触发端

    @TableField("warehouse_type")
    private Integer warehouseType;//仓库类型

    @TableField("lgort")
    private String lgort;//仓库编码

    @TableField("lgobe")
    private String lgobe;//仓库名称

    @TableField("shop_code")
    private String shopCode;//门店编码

    @TableField("shop_name")
    private String shopName;//门店名称

    @TableField("status")
    private Integer status;//打印状态 0-待打印 1-打印中 2-打印完成 3-打印失败

    @TableField("count")
    private Integer count;//打印次数

    @TableField("retry_count")
    private Integer retryCount;//重试次数

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间
}
