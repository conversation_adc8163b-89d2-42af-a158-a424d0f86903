package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 11:05
 */
@Data
public class BaseRepairCenter {

    private Long id;
    private String repairCenterNum;
    private String repairCenterName;
    private String repairCenterRemark;
    private String repairCenterProvince;
    private String repairCenterCity;
    private String repairCenterAddress;
    private String contactNum;
    private String contactPerson;
    private Integer status;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
}
