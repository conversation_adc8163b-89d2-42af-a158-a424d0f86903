package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.xiaopeng.halley.ascm.boot.dto.ErpSyncShopDto;

import java.util.Date;

/**
 * BaseShop实体
 */
@TableName("base_shop")

@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BaseShop extends Model<BaseShop> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("shop_code")
    private String shopCode;//门店编码

    @TableField("xl_shop_code")
    private String xlShopCode;//骁龙门店编码

    @TableField("xl_shop_remark")
    private String xlShopRemark;//骁龙门店简称

    @TableField("shop_name")
    private String shopName;//门店名称

    @TableField("shop_remark")
    private String shopRemark;//门店简称

    @TableField("shop_province")
    private String shopProvince;//门店省份

    @TableField("shop_city")
    private String shopCity;//门店城市

    @TableField("shop_address")
    private String shopAddress;//门店地址

    @TableField("contact_num")
    private String contactNum;//联系电话

    @TableField("contact_person")
    private String contactPerson;//联系人

    @TableField("lng")
    private Double lng;//经度

    @TableField("lat")
    private Double lat;//纬度

    @TableField("fence_range")
    private String fenceRange;//围栏范围

    @TableField("status")
    private Integer status;//状态

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;//创建时间

    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;//更新时间

    @TableField("dynamic_update")
    private Integer dynamicUpdate;//是否动态更新
    @TableField("cargo_contact")
    private String cargoContact;//装货联系人
    @TableField("cargo_contact_phone")
    private String cargoContactPhone;//装货联系人电话
    @TableField("region")
    private String region;//区域

    public String getCargoContact() {
        return cargoContact;
    }

    public void setCargoContact(String cargoContact) {
        this.cargoContact = cargoContact;
    }

    public String getCargoContactPhone() {
        return cargoContactPhone;
    }

    public void setCargoContactPhone(String cargoContactPhone) {
        this.cargoContactPhone = cargoContactPhone;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Integer getDynamicUpdate() {
        return dynamicUpdate;
    }

    public void setDynamicUpdate(Integer dynamicUpdate) {
        this.dynamicUpdate = dynamicUpdate;
    }

    /**
     * erp数据同步转换方法
     *
     * @param erpSyncShopDto
     */
    public void syncData(ErpSyncShopDto erpSyncShopDto) {
        this.shopCode = erpSyncShopDto.getKUNNR();
        this.shopName = erpSyncShopDto.getNAME1();
        this.shopProvince = erpSyncShopDto.getZPROVINCE1();
        this.shopCity = erpSyncShopDto.getCITY();
        this.shopAddress = erpSyncShopDto.getADDR();
        this.contactPerson = erpSyncShopDto.getCONTACTS();
        this.contactNum = erpSyncShopDto.getTEL();
        this.lng = erpSyncShopDto.getZLONG();
        this.lat = erpSyncShopDto.getZLAT();
        this.xlShopCode = erpSyncShopDto.getZYL01();
        this.xlShopRemark = erpSyncShopDto.getZYL02();
        this.region = erpSyncShopDto.getZYL03();
    }

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BaseShop setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取门店编码
     *
     * @return 门店编码
     */
    public String getShopCode() {
        return this.shopCode;
    }

    /**
     * 设置门店编码
     *
     * @param shopCode 门店编码
     * @return 当前对象
     */
    public BaseShop setShopCode(String shopCode) {
        this.shopCode = shopCode;
        return this;
    }

    /**
     * 获取骁龙门店编码
     *
     * @return 骁龙门店编码
     */
    public String getXlShopCode() {
        return this.xlShopCode;
    }

    /**
     * 设置骁龙门店编码
     *
     * @param xlShopCode 骁龙门店编码
     * @return 当前对象
     */
    public BaseShop setXlShopCode(String xlShopCode) {
        this.xlShopCode = xlShopCode;
        return this;
    }

    /**
     * 获取骁龙门店简称
     *
     * @return 骁龙门店简称
     */
    public String getXlShopRemark() {
        return this.xlShopRemark;
    }

    /**
     * 设置骁龙门店简称
     *
     * @param xlShopRemark 骁龙门店简称
     * @return 当前对象
     */
    public BaseShop setXlShopRemark(String xlShopRemark) {
        this.xlShopRemark = xlShopRemark;
        return this;
    }

    /**
     * 获取门店名称
     *
     * @return 门店名称
     */
    public String getShopName() {
        return this.shopName;
    }

    /**
     * 设置门店名称
     *
     * @param shopName 门店名称
     * @return 当前对象
     */
    public BaseShop setShopName(String shopName) {
        this.shopName = shopName;
        return this;
    }

    /**
     * 获取门店简称
     *
     * @return 门店简称
     */
    public String getShopRemark() {
        return this.shopRemark;
    }

    /**
     * 设置门店简称
     *
     * @param shopRemark 门店简称
     * @return 当前对象
     */
    public BaseShop setShopRemark(String shopRemark) {
        this.shopRemark = shopRemark;
        return this;
    }

    /**
     * 获取门店省份
     *
     * @return 门店省份
     */
    public String getShopProvince() {
        return this.shopProvince;
    }

    /**
     * 设置门店省份
     *
     * @param shopProvince 门店省份
     * @return 当前对象
     */
    public BaseShop setShopProvince(String shopProvince) {
        this.shopProvince = shopProvince;
        return this;
    }

    /**
     * 获取门店城市
     *
     * @return 门店城市
     */
    public String getShopCity() {
        return this.shopCity;
    }

    /**
     * 设置门店城市
     *
     * @param shopCity 门店城市
     * @return 当前对象
     */
    public BaseShop setShopCity(String shopCity) {
        this.shopCity = shopCity;
        return this;
    }

    /**
     * 获取门店地址
     *
     * @return 门店地址
     */
    public String getShopAddress() {
        return this.shopAddress;
    }

    /**
     * 设置门店地址
     *
     * @param shopAddress 门店地址
     * @return 当前对象
     */
    public BaseShop setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
        return this;
    }

    /**
     * 获取联系电话
     *
     * @return 联系电话
     */
    public String getContactNum() {
        return this.contactNum;
    }

    /**
     * 设置联系电话
     *
     * @param contactNum 联系电话
     * @return 当前对象
     */
    public BaseShop setContactNum(String contactNum) {
        this.contactNum = contactNum;
        return this;
    }

    /**
     * 获取联系人
     *
     * @return 联系人
     */
    public String getContactPerson() {
        return this.contactPerson;
    }

    /**
     * 设置联系人
     *
     * @param contactPerson 联系人
     * @return 当前对象
     */
    public BaseShop setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
        return this;
    }

    /**
     * 获取经度
     *
     * @return 经度
     */
    public Double getLng() {
        return this.lng;
    }

    /**
     * 设置经度
     *
     * @param lng 经度
     * @return 当前对象
     */
    public BaseShop setLng(Double lng) {
        this.lng = lng;
        return this;
    }

    /**
     * 获取纬度
     *
     * @return 纬度
     */
    public Double getLat() {
        return this.lat;
    }

    /**
     * 设置纬度
     *
     * @param lat 纬度
     * @return 当前对象
     */
    public BaseShop setLat(Double lat) {
        this.lat = lat;
        return this;
    }

    /**
     * 获取围栏范围
     *
     * @return 围栏范围
     */
    public String getFenceRange() {
        return this.fenceRange;
    }

    /**
     * 设置围栏范围
     *
     * @param fenceRange 围栏范围
     * @return 当前对象
     */
    public BaseShop setFenceRange(String fenceRange) {
        this.fenceRange = fenceRange;
        return this;
    }

    /**
     * 获取状态
     *
     * @return 状态
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     * @return 当前对象
     */
    public BaseShop setStatus(Integer status) {
        this.status = status;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BaseShop setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BaseShop setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BaseShop setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BaseShop setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BaseShop setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BaseShop setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BaseShop setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}