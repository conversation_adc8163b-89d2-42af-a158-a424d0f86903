package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("base_transport_timeliness")
public class BaseTransportTimeliness implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("warehouse_city")
    private String warehouseCity;

    @TableField("shop_city")
    private String shopCity;

    @TableField("transport_type")
    private String transportType;

    @TableField("transport_time")
    private String transportTime;

    @TableField("route_name")
    private String routeName;

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @TableField("create_time")
    private Date createTime;

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField("update_time")
    private Date updateTime;

    @TableField("is_delete")
    private Integer isDelete;

}
