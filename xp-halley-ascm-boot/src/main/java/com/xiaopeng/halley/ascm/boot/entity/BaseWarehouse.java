package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BaseWarehouse实体
 */
@TableName("base_warehouse")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BaseWarehouse extends Model<BaseWarehouse> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("lgort")
    private String lgort;//仓库编码

    @TableField("lgobe")
    private String lgobe;//仓库名称

    @TableField("warehouse_type")
    private Integer warehouseType;//仓库类型

    @TableField("warehouse_remark")
    private String warehouseRemark;//仓库简称

    @TableField("warehouse_province")
    private String warehouseProvince;//仓库省份

    @TableField("warehouse_city")
    private String warehouseCity;//仓库城市

    @TableField("warehouse_address")
    private String warehouseAddress;//仓库地址

    @TableField("contact_num")
    private String contactNum;//联系电话

    @TableField("contact_person")
    private String contactPerson;//联系人

    @TableField("status")
    private Integer status;//状态

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    public Integer getWarehouseType() {
        return warehouseType;
    }

    public BaseWarehouse setWarehouseType(Integer warehouseType) {
        this.warehouseType = warehouseType;
        return this;
    }

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BaseWarehouse setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取仓库编码
     *
     * @return 仓库编码
     */
    public String getLgort() {
        return this.lgort;
    }

    /**
     * 设置仓库编码
     *
     * @param lgort 仓库编码
     * @return 当前对象
     */
    public BaseWarehouse setLgort(String lgort) {
        this.lgort = lgort;
        return this;
    }

    /**
     * 获取仓库名称
     *
     * @return 仓库名称
     */
    public String getLgobe() {
        return this.lgobe;
    }

    /**
     * 设置仓库名称
     *
     * @param lgobe 仓库名称
     * @return 当前对象
     */
    public BaseWarehouse setLgobe(String lgobe) {
        this.lgobe = lgobe;
        return this;
    }

    /**
     * 获取仓库简称
     *
     * @return 仓库简称
     */
    public String getWarehouseRemark() {
        return this.warehouseRemark;
    }

    /**
     * 设置仓库简称
     *
     * @param warehouseRemark 仓库简称
     * @return 当前对象
     */
    public BaseWarehouse setWarehouseRemark(String warehouseRemark) {
        this.warehouseRemark = warehouseRemark;
        return this;
    }

    /**
     * 获取仓库省份
     *
     * @return 仓库省份
     */
    public String getWarehouseProvince() {
        return this.warehouseProvince;
    }

    /**
     * 设置仓库省份
     *
     * @param warehouseProvince 仓库省份
     * @return 当前对象
     */
    public BaseWarehouse setWarehouseProvince(String warehouseProvince) {
        this.warehouseProvince = warehouseProvince;
        return this;
    }

    /**
     * 获取仓库城市
     *
     * @return 仓库城市
     */
    public String getWarehouseCity() {
        return this.warehouseCity;
    }

    /**
     * 设置仓库城市
     *
     * @param warehouseCity 仓库城市
     * @return 当前对象
     */
    public BaseWarehouse setWarehouseCity(String warehouseCity) {
        this.warehouseCity = warehouseCity;
        return this;
    }

    /**
     * 获取仓库地址
     *
     * @return 仓库地址
     */
    public String getWarehouseAddress() {
        return this.warehouseAddress;
    }

    /**
     * 设置仓库地址
     *
     * @param warehouseAddress 仓库地址
     * @return 当前对象
     */
    public BaseWarehouse setWarehouseAddress(String warehouseAddress) {
        this.warehouseAddress = warehouseAddress;
        return this;
    }

    /**
     * 获取联系电话
     *
     * @return 联系电话
     */
    public String getContactNum() {
        return this.contactNum;
    }

    /**
     * 设置联系电话
     *
     * @param contactNum 联系电话
     * @return 当前对象
     */
    public BaseWarehouse setContactNum(String contactNum) {
        this.contactNum = contactNum;
        return this;
    }

    /**
     * 获取联系人
     *
     * @return 联系人
     */
    public String getContactPerson() {
        return this.contactPerson;
    }

    /**
     * 设置联系人
     *
     * @param contactPerson 联系人
     * @return 当前对象
     */
    public BaseWarehouse setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
        return this;
    }

    /**
     * 获取状态
     *
     * @return 状态
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 设置状态
     *
     * @param status 状态
     * @return 当前对象
     */
    public BaseWarehouse setStatus(Integer status) {
        this.status = status;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BaseWarehouse setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BaseWarehouse setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BaseWarehouse setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BaseWarehouse setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BaseWarehouse setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BaseWarehouse setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BaseWarehouse setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}