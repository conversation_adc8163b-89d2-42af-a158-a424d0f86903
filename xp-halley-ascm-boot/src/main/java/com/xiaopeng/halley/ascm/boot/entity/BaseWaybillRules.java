package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * BaseWaybillRules实体
 */
@TableName("base_waybill_rules")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
@Data
public class BaseWaybillRules extends Model<BaseWaybillRules> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(name = "id", description = "主键id")
    private Long id;//主键id

    @TableField("shop_code")
    @Schema(name = "shopCode", description = "门店编码")
    private String shopCode;//门店编码

    @TableField("path_remark")
    @Schema(name = "pathRemark", description = "线路描述")
    private String pathRemark;//线路描述

    @TableField("path")
    @Schema(name = "path", description = "线路")
    private String path;//线路

    @TableField("path_expiry")
    @Schema(name = "pathExpiry", description = "线路时效")
    private Integer pathExpiry;//线路时效

    @TableField("order_type")
    @Schema(name = "orderType", description = "订单类型")
    private String orderType;//订单类型

    @TableField("transport_type")
    @Schema(name = "transportType", description = "运输类型")
    private String transportType;//运输类型

    @TableField("carrier_code")
    @Schema(name = "carrierCode", description = "承运商编码")
    private String carrierCode;//承运商编码

    @TableField("status")
    @Schema(name = "status", description = "状态")
    private Integer status;//状态

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField("create_user_name")
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField("update_user_name")
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    @TableField("lgort")
    @Schema(name = "lgort", description = "仓库编码")
    private String lgort;//仓库编码

    @Schema(name = "isSpecially", description = "是否专用")
    private Integer isSpecially;

    @Schema(name = "shopCity", description = "门店城市（无需传值）")
    private String shopCity;

    @Schema(name = "shopProvince", description = "门店省份（无需传值）")
    private String shopProvince;
}