package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 14:51
 */
@Data
public class BatteryWayBill {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String repairCenterNum;
    private Date repairDate;
    private String waybillCode;
    private String packNum;
    private Integer waybillStatus;
    private Integer transportationType;
    private Integer logisticsType;
    private Date logisticsArrangementTime;
    private String logisticsProvider;
    private String xlShopCode;
    private String vinCode;
    private String faultSummary;
    private Integer faultClassification;
    private String dispatchCity;
    private String arrivalCity;

    private String routeName;

    private Double routeDeliveryTime;

    private Double kilometersNum;

    private Double expense;

    @Schema(name = "dispatchAddress", description = "发运城市地址")
    private String dispatchAddress;

    @Schema(name = "loadingContact", description = "发运城市联系人")
    private String loadingContact;

    @Schema(name = "loadingPhone", description = "发运城市联系人电话")
    private String loadingPhone;

    @Schema(name = "arrivalAddress", description = "收货城市地址")
    private String arrivalAddress;

    @Schema(name = "unloadingContact", description = "收货城市联系人")
    private String unloadingContact;

    @Schema(name = "unloadingPhone", description = "收货城市联系人电话")
    private String unloadingPhone;
    private String project;
    private Integer isSamePackage;
    private String packageVin;
    private Double distanceTravelled;
    private String carType;
    private Date pickupDate;
    private String driverName;
    private String driverPhone;
    private String plateNumber;
    private Integer responseTimeTolerance;
    private Integer transportationTimeTolerance;
    private Double actualPaymentAmount;
    private Date deliveryTime;
    private String originalWaybill;
    private String remark;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    private Integer isDelete;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;
}
