package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 异步导出表实体
 *
 * <AUTHOR> 自动生成
 * @date 2023-01-09 06:17:47
 */
@Data
@TableName("bz_async_export_log")
public class BzAsyncExportLog implements Serializable {
    public static final String COL_ID = "id";
    public static final String COL_WAREHOUSE_NUMBER = "warehouse_number";
    public static final String COL_OPERATION_CODE = "operation_code";
    public static final String COL_METHOD_PATH = "method_path";
    public static final String COL_PARAMS = "params";
    public static final String COL_STATE = "state";
    public static final String COL_IS_DELETE = "is_delete";
    public static final String COL_OWNER_ORG_ID = "owner_org_id";
    public static final String COL_CREATE_USER_ID = "create_user_id";
    public static final String COL_CREATE_USER_NAME = "create_user_name";

    //增加一个同步异步标识
    public static final String COL_CREATE_TIME = "create_time";
    public static final String COL_UPDATE_USER_ID = "update_user_id";
    public static final String COL_UPDATE_USER_NAME = "update_user_name";
    public static final String COL_UPDATE_TIME = "update_time";
    public static final String COL_OPTIMISTIC_LOCK = "optimistic_lock";
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 仓库号
     */
    private String lgort;
    /**
     * 文件名称
     */
    private String name;
    /**
     * 下载文件码
     */
    private String operationCode;
    /**
     * 方法路径：类名.方法名
     */
    private String methodPath;
    /**
     * 参数集合
     */
    private String params;
    /**
     * 0：导入中;1: 导入成功;2:导入失败;
     */
    private Integer state;

    /**
     * 备注
     */
    private String remark;
    /**
     * 导出操作次数 失败重试三次以后不再操作
     */
    private Integer exportCount;
    /**
     * 用户下载次数
     */
    private Integer downloadCount;
    /**
     * 0:异步 1：同步
     */
    @TableField(exist = false)
    private Integer syncFlag = 0;
    /**
     * 删除标记
     */
    private Boolean isDelete;
    /**
     * 所属组织ID
     */
    private String ownerOrgId;
    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUserId;
    /**
     * 创建人姓名
     */
    @TableField(fill = FieldFill.INSERT)
    private String createUserName;
    /**
     * 创建时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date createTime;
    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    /**
     * 修改人姓名
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    /**
     * 修改时间
     */
    @TableField(insertStrategy = FieldStrategy.NEVER, updateStrategy = FieldStrategy.NEVER)
    private Date updateTime;
    /**
     * 乐观锁
     */
    @Version
    private Integer optimisticLock;

}
