package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
@ApiModel("合同信息")
@TableName("bz_contract")
public class BzContract {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id;

    @TableField
    @ApiModelProperty("合同编号")
    private String contractCode;

    @TableField
    @ApiModelProperty("运营仓库")
    private String operationWarehouse;

    @TableField
    @ApiModelProperty("合作伙伴")
    private String partners;

    @TableField
    @ApiModelProperty("合同生效时间")
    private LocalDate effectiveTime;

    @TableField
    @ApiModelProperty("合同结束时间")
    private LocalDate endTime;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人ID")
    private String createUserId;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人ID")
    private String updateUserId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新人姓名")
    private String updateUserName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private Date updateTime;

    @TableLogic
    @ApiModelProperty("是否删除（1-已删除，0-正常）")
    private Integer isDelete;

    @TableField(exist = false)
    private String operationWarehouseStr;
}