package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("合同文件")
@TableName("bz_contract_file")
public class BzContractFile {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("合同文件名称")
    private String name;

    @ApiModelProperty("合同ID")
    private Long contractId;

    @ApiModelProperty("文件ID")
    private Long baseFileId;

    @ApiModelProperty("所属年月")
    private Date time;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人id")
    private String createUserId;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createUserName;

    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private Date createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
