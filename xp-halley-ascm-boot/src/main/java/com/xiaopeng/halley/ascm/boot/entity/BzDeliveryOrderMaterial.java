package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 交货单和物料关联关系表
 *
 * @TableName bz_delivery_order_material
 */
@TableName(value = "bz_delivery_order_material")
@Data
public class BzDeliveryOrderMaterial implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 物料交货单ID
     */
    @TableField(value = "order_id")
    private Long orderId;
    /**
     * 物料ID
     */
    @TableField(value = "material_id")
    private Long materialId;
    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private Integer version;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
}