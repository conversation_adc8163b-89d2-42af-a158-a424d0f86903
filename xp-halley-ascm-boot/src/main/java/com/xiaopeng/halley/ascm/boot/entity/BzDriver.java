package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BzDriver实体
 */
@TableName("bz_driver")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzDriver extends Model<BzDriver> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;//主键

    @TableField("driver_name")
    private String driverName;//司机姓名

    @TableField("driver_phone")
    private String driverPhone;//司机电话

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField("create_user_name")
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField("update_user_name")
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    /**
     * 获取主键
     *
     * @return 主键
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     * @return 当前对象
     */
    public BzDriver setId(Integer id) {
        this.id = id;
        return this;
    }

    /**
     * 获取司机姓名
     *
     * @return 司机姓名
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置司机姓名
     *
     * @param driverName 司机姓名
     * @return 当前对象
     */
    public BzDriver setDriverName(String driverName) {
        this.driverName = driverName;
        return this;
    }

    /**
     * 获取司机电话
     *
     * @return 司机电话
     */
    public String getDriverPhone() {
        return this.driverPhone;
    }

    /**
     * 设置司机电话
     *
     * @param driverPhone 司机电话
     * @return 当前对象
     */
    public BzDriver setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BzDriver setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BzDriver setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BzDriver setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BzDriver setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzDriver setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzDriver setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzDriver setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}