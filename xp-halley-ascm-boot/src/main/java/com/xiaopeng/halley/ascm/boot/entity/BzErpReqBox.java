package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BzErpReqBox实体
 */
@TableName("bz_erp_req_box")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzErpReqBox extends Model<BzErpReqBox> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("box_code")
    private String boxCode;//箱号

    @TableField("box_long")
    private String boxLong;//长(毫米)

    @TableField("box_width")
    private String boxWidth;//宽(毫米)

    @TableField("box_height")
    private String boxHeight;

    @TableField("box_volume")
    private Double boxVolume;//包材体积(立方米)

    @TableField("box_weight")
    private Double boxWeight;//单箱重量（kg）

    @TableField("actual_volume")
    private Double actualVolume;//实际体积(立方米)

    @TableField("package_type")
    private String packageType;//包装类型

    @TableField("package_code")
    private String packageCode;//包装材料编号

    @TableField("package_time")
    private Date packageTime;//包装日期时间

    @TableField("waybill_code")
    private String waybillCode;//箱号所在运单

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField("create_user_name")
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField("update_user_name")
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    @TableField("receive_state")
    private Integer receiveState;//收货情况，0-初始化，1-完整，2-缺失

    public Integer getReceiveState() {
        return receiveState;
    }

    public void setReceiveState(Integer receiveState) {
        this.receiveState = receiveState;
    }

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BzErpReqBox setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取箱号
     *
     * @return 箱号
     */
    public String getBoxCode() {
        return this.boxCode;
    }

    /**
     * 设置箱号
     *
     * @param boxCode 箱号
     * @return 当前对象
     */
    public BzErpReqBox setBoxCode(String boxCode) {
        this.boxCode = boxCode;
        return this;
    }

    /**
     * 获取长(毫米)
     *
     * @return 长(毫米)
     */
    public String getBoxLong() {
        return this.boxLong;
    }

    /**
     * 设置长(毫米)
     *
     * @param boxLong 长(毫米)
     * @return 当前对象
     */
    public BzErpReqBox setBoxLong(String boxLong) {
        this.boxLong = boxLong;
        return this;
    }

    /**
     * 获取宽(毫米)
     *
     * @return 宽(毫米)
     */
    public String getBoxWidth() {
        return this.boxWidth;
    }

    /**
     * 设置宽(毫米)
     *
     * @param boxWidth 宽(毫米)
     * @return 当前对象
     */
    public BzErpReqBox setBoxWidth(String boxWidth) {
        this.boxWidth = boxWidth;
        return this;
    }

    public String getBoxHeight() {
        return this.boxHeight;
    }

    public BzErpReqBox setBoxHeight(String boxHeight) {
        this.boxHeight = boxHeight;
        return this;
    }

    /**
     * 获取包材体积(立方米)
     *
     * @return 包材体积(立方米)
     */
    public Double getBoxVolume() {
        return this.boxVolume;
    }

    /**
     * 设置包材体积(立方米)
     *
     * @param boxVolume 包材体积(立方米)
     * @return 当前对象
     */
    public BzErpReqBox setBoxVolume(Double boxVolume) {
        this.boxVolume = boxVolume;
        return this;
    }

    /**
     * 获取单箱重量（kg）
     *
     * @return 单箱重量（kg）
     */
    public Double getBoxWeight() {
        return this.boxWeight;
    }

    /**
     * 设置单箱重量（kg）
     *
     * @param boxWeight 单箱重量（kg）
     * @return 当前对象
     */
    public BzErpReqBox setBoxWeight(Double boxWeight) {
        this.boxWeight = boxWeight;
        return this;
    }

    /**
     * 获取实际体积(立方米)
     *
     * @return 实际体积(立方米)
     */
    public Double getActualVolume() {
        return this.actualVolume;
    }

    /**
     * 设置实际体积(立方米)
     *
     * @param actualVolume 实际体积(立方米)
     * @return 当前对象
     */
    public BzErpReqBox setActualVolume(Double actualVolume) {
        this.actualVolume = actualVolume;
        return this;
    }

    /**
     * 获取包装类型
     *
     * @return 包装类型
     */
    public String getPackageType() {
        return this.packageType;
    }

    /**
     * 设置包装类型
     *
     * @param packageType 包装类型
     * @return 当前对象
     */
    public BzErpReqBox setPackageType(String packageType) {
        this.packageType = packageType;
        return this;
    }

    /**
     * 获取包装材料编号
     *
     * @return 包装材料编号
     */
    public String getPackageCode() {
        return this.packageCode;
    }

    /**
     * 设置包装材料编号
     *
     * @param packageCode 包装材料编号
     * @return 当前对象
     */
    public BzErpReqBox setPackageCode(String packageCode) {
        this.packageCode = packageCode;
        return this;
    }

    /**
     * 获取包装日期时间
     *
     * @return 包装日期时间
     */
    public Date getPackageTime() {
        return this.packageTime;
    }

    /**
     * 设置包装日期时间
     *
     * @param packageTime 包装日期时间
     * @return 当前对象
     */
    public BzErpReqBox setPackageTime(Date packageTime) {
        this.packageTime = packageTime;
        return this;
    }

    /**
     * 获取箱号所在运单
     *
     * @return 箱号所在运单
     */
    public String getWaybillCode() {
        return this.waybillCode;
    }

    /**
     * 设置箱号所在运单
     *
     * @param waybillCode 箱号所在运单
     * @return 当前对象
     */
    public BzErpReqBox setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BzErpReqBox setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BzErpReqBox setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BzErpReqBox setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BzErpReqBox setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzErpReqBox setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzErpReqBox setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzErpReqBox setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}