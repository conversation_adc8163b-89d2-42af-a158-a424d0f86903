package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "ERP需求关联物料实体")
@TableName("bz_erp_req_material_rel")
public class BzErpReqMaterialRel extends Model<BzErpReqMaterialRel> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("delivery_order_code")
    private String deliveryOrderCode;//交货单号.

    @TableField("origin_delivery_order_code")
    private String originDeliveryOrderCode;//原交货单号.

    @TableField("delivery_order_type")
    private String deliveryOrderType;

    @TableField("line_number")
    private String lineNumber;

    @TableField("box_code")
    private String boxCode;//箱号

    @TableField("matnr")
    private String matnr;//物料号

    @TableField("maktx")
    private String maktx;//物料描述

    @TableField("quantity")
    private Integer quantity;//数量

    @TableField("unit")
    private String unit;//单位

    @TableField("estimated_volume")
    private Double estimatedVolume;//预估总体积(m³)

    @TableField("order_count")
    private Integer orderCount;//订单数量

    @TableField("package_count")
    private Integer packageCount;//包装数量

    @TableField("material_long")
    private String materialLong;//长(毫米)

    @TableField("material_width")
    private String materialWidth;//宽(毫米)

    @TableField("material_height")
    private String materialHeight;//高(毫米)

    @TableField("material_volume")
    private Double materialVolume;//物料体积(立方米)

    @TableField("material_weight")
    private Double materialWeight;//物料重量

    @TableField("create_user_id")
    private String createUserId;//创建人id

    @TableField("create_user_name")
    private String createUserName;//创建人

    @TableField("update_user_id")
    private String updateUserId;//修改人id

    @TableField("update_user_name")
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    @TableField("purchase_date")
    private String purchaseDate;//下单日期

    @TableField("purchase_time")
    private String purchaseTime;//下单时间

    @TableField("purchase_date_time")
    private Date purchaseDateTime;//下单日期时间

    @TableField("delivery_date")
    private String deliveryDate;//配送日期

    @TableField("delivery_time")
    private String deliveryTime;//配送时间

    @TableField("delivery_date_time")
    private Date deliveryDateTime;//配送日期时间

    @TableField("receive_count")
    private Integer receiveCount;//接收数量

    @TableField("plan_shipping_time")
    private Date planShippingTime;// 计划发运时间

    @TableField(exist = false)
    private String packageType;

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}