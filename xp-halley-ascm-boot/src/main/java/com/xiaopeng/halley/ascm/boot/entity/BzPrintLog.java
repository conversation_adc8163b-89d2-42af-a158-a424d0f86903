package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("远程打印日志表")
@TableName("bz_print_log")
public class BzPrintLog {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty("主键")
	private Long id;

	@ApiModelProperty("运单编号")
	private String waybillCode;

	@ApiModelProperty("交货单号")
	private String deliveryOrderCode;

	@ApiModelProperty("仓库号")
	private String lgort;

	@ApiModelProperty("仓库名称")
	private String lgobe;

	@ApiModelProperty("仓库号")
	private String shopCode;

	@ApiModelProperty("仓库名称")
	private String shopName;

	@ApiModelProperty("路线")
	private String route;

	@ApiModelProperty("打印机名称")
	private String printerName;

	@ApiModelProperty("触发端")
	private String triggerClient;

	@ApiModelProperty("打印状态")
	private Integer status;

	@ApiModelProperty("状态码")
	private String state;

	@ApiModelProperty("错误信息")
	private String message;

	@ApiModelProperty("打印单据id")
	private Long baseFileId;

	@ApiModelProperty("重试次数")
	private Integer retryCount;

	@ApiModelProperty("打印人")
	private String createUserName;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建时间")
	private Date createTime;
}