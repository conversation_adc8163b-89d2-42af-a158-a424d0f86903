package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 供应商和物料关联关系表
 *
 * @TableName bz_supplier_material
 */
@Data
public class BzSupplierMaterial implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 唯一id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 供应商ID
     */
    private Long supplierId;
    /**
     * 物料ID
     */
    private Long materialId;
    /**
     * 供应商snp
     */
    private String supplierSnp;
    /**
     * 乐观锁
     */
    private Integer version;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;

}