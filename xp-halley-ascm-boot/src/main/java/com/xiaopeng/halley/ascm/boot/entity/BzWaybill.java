package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("bz_waybill")
@Schema(description = "运单表实体")
@EqualsAndHashCode(callSuper = false)
public class BzWaybill extends Model<BzWaybill> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    @TableField("sort_num")
    @Schema(description = "排序规则(越大排序越高)")
    private Integer sortNum;

    @TableField("waybill_code")
    @Schema(description = "运单编号")
    private String waybillCode;

    @TableField("factory_desc")
    @Schema(description = "工厂名称")
    private String factoryDesc;

    @TableField("werks")
    @Schema(description = "工厂编码")
    private String werks;

    @TableField("status")
    @Schema(description = "运单状态")
    private String status;

    @TableField("order_type")
    @Schema(description = "订单类型")
    private String orderType;

    @TableField("is_abnormal")
    @Schema(description = "是否异常(0-正常 1-异常)")
    private Integer isAbnormal;

    @TableField("transport_type")
    @Schema(description = "运输类型")
    private String transportType;

    @TableField("lgort")
    @Schema(description = "仓库编码")
    private String lgort;

    @TableField("lgobe")
    @Schema(description = "仓库名称")
    private String lgobe;

    @TableField("warehouse_province")
    @Schema(description = "仓库省份")
    private String warehouseProvince;

    @TableField("warehouse_city")
    @Schema(description = "仓库城市")
    private String warehouseCity;

    @TableField("warehouse_address")
    @Schema(description = "仓库地址")
    private String warehouseAddress;

    @TableField("warehouse_contact_num")
    @Schema(description = "仓库联系电话")
    private String warehouseContactNum;

    @TableField("warehouse_contact_person")
    @Schema(description = "仓库联系人")
    private String warehouseContactPerson;

    @TableField("shop_code")
    @Schema(description = "门店编码")
    private String shopCode;

    @TableField("shop_name")
    @Schema(description = "门店名称")
    private String shopName;

    @TableField("shop_province")
    @Schema(description = "门店省份")
    private String shopProvince;

    @TableField("shop_city")
    @Schema(description = "门店城市")
    private String shopCity;

    @TableField("shop_address")
    @Schema(description = "门店地址")
    private String shopAddress;

    @TableField("shop_contact_num")
    @Schema(description = "门店联系电话")
    private String shopContactNum;

    @TableField("shop_contact_person")
    @Schema(description = "门店联系人")
    private String shopContactPerson;

    @TableField("is_ontime")
    @Schema(description = "是否准时送达(0-否 1-是)")
    private Integer isOntime;

    @TableField("local_update_time")
    @Schema(description = "位置更新时间")
    private Date localUpdateTime;

    @TableField("local_info")
    @Schema(description = "位置信息")
    private String localInfo;

    @TableField("total_box")
    @Schema(description = "总箱数量")
    private Integer totalBox;

    @TableField("total_volume")
    @Schema(description = "总体积(m³)")
    private Double totalVolume;

    @TableField("total_weight")
    @Schema(description = "总重量(g)")
    private Double totalWeight;

    @TableField("estimated_volume")
    @Schema(description = "预估总体积(m³)")
    private Double estimatedVolume;

    @TableField("carrier_code")
    @Schema(description = "承运商编码")
    private String carrierCode;

    @TableField("car_plate")
    @Schema(description = "车牌号")
    private String carPlate;

    @TableField(value = "car_type")
    @Schema(description = "车型")
    private String carType;

    @TableField("driver_name")
    @Schema(description = "司机名称")
    private String driverName;

    @TableField("driver_phone")
    @Schema(description = "司机手机号")
    private String driverPhone;

    @TableField("logistics_code")
    @Schema(description = "物流单号")
    private String logisticsCode;

    @TableField("logistics_company")
    @Schema(description = "物流公司")
    private String logisticsCompany;

    @TableField("departure_time")
    @Schema(description = "发车时间")
    private Date departureTime;

    @TableField("pda_shipping_time")
    @Schema(description = "PDA发运时间")
    private Date pdaShippingTime;

    @TableField("sign_time")
    @Schema(description = "签收时间")
    private Date signTime;

    @TableField("waybill_create_time")
    @Schema(description = "运单创建时间")
    private Date waybillCreateTime;

    @TableField("path")
    @Schema(description = "线路")
    private String path;

    @TableField("path_remark")
    @Schema(description = "线路描述")
    private String pathRemark;

    @TableField("path_expiry")
    @Schema(description = "线路时效")
    private Integer pathExpiry;

    @TableField("expiry_time")
    @Schema(description = "时效到期时间")
    private Date expiryTime;

    @TableField("compensate_type")
    @Schema(description = "索赔类型")
    private String compensateType;

    @TableField("compensate_reason")
    @Schema(description = "索赔原因")
    private String compensateReason;

    @TableField("is_devanning")
    @Schema(description = "是否拆箱(0-否 1-是)")
    private Integer isDevanning;

    @TableField("combine_count")
    @Schema(description = "合单次数")
    private Integer combineCount;

    @TableField("is_temp")
    @Schema(description = "是否为临时运单(0-否 1-是)")
    private Integer isTemp;

    @TableField("is_hang_up")
    @Schema(description = "是否挂起(0-否 1-是)")
    private Integer isHangUp;

    @TableField("hang_up_time")
    @Schema(description = "挂起时间")
    private Date hangUpTime;

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private String createUserId;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    @Schema(description = "创建人姓名")
    private String createUserName;

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人ID")
    private String updateUserId;

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人姓名")
    private String updateUserName;

    @TableLogic
    @TableField("is_delete")
    @Schema(description = "是否删除(0-正常 1-已删除)")
    private Integer isDelete;

    @TableField("create_time")
    @Schema(description = "创建时间")
    private Date createTime;

    @TableField("update_time")
    @Schema(description = "更新时间")
    private Date updateTime;

    @TableField("demolition_count")
    @Schema(description = "拆单次数")
    private Integer demolitionCount;

    @TableField("is_complete_packing")
    @Schema(description = "是否包装完成(0-未完成 1-已完成)")
    private Integer isCompletePacking;

    @TableField("abnormal_cause")
    @Schema(description = "运单异常原因")
    private String abnormalCause;

    @TableField("received_time")
    @Schema(description = "接收时间(WMS下发时间)")
    private Date receivedTime;

    @TableField("circulation_time")
    @Schema(description = "下发时间(推送给PDA的时间)")
    private Date circulationTime;

    @TableField("pickup_time")
    @Schema(description = "提货时间")
    private Date pickupTime;

    @TableField("update_status")
    @Schema(description = "运单时效更新状态")
    private Integer updateStatus;

    @TableField("sync")
    @Schema(description = "运单信息是否需要同步 0-否 1-是")
    private Integer sync;

    @TableField("track_sync")
    @Schema(description = "物流信息是否需要同步 0-否 1-是")
    private Integer trackSync;

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}