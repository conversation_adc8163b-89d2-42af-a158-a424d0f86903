package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("bz_waybill_box_rel")
@Schema(description = "运单箱关联表实体")
@EqualsAndHashCode(callSuper = false)
public class BzWaybillBoxRel extends Model<BzWaybillBoxRel> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("waybill_code")
    private String waybillCode;//运单编号

    @TableField("box_code")
    private String boxCode;//箱号

    @TableField("status")
    private Integer status;//箱号状态（0-未扫描，1-已扫描）

    @TableField("is_devanning")
    private Integer isDevanning;

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.UPDATE)
    private String updateUserName;//更新人

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}