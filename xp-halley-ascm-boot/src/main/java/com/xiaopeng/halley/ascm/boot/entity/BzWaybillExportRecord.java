package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BzWaybillExportRecord实体
 */
@TableName("bz_waybill_export_record")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzWaybillExportRecord extends Model<BzWaybillExportRecord> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键ID

    @TableField("status")
    private Integer status;//导出状态（0-导出中，1-已完成）

    @TableField("file_id")
    private String fileId;//导出文件Id

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    /**
     * 获取主键ID
     *
     * @return 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键ID
     *
     * @param id 主键ID
     * @return 当前对象
     */
    public BzWaybillExportRecord setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取导出状态（0-导出中，1-已完成）
     *
     * @return 导出状态（0-导出中，1-已完成）
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 设置导出状态（0-导出中，1-已完成）
     *
     * @param status 导出状态（0-导出中，1-已完成）
     * @return 当前对象
     */
    public BzWaybillExportRecord setStatus(Integer status) {
        this.status = status;
        return this;
    }

    /**
     * 获取导出文件Id
     *
     * @return 导出文件Id
     */
    public String getFileId() {
        return this.fileId;
    }

    /**
     * 设置导出文件Id
     *
     * @param fileId 导出文件Id
     * @return 当前对象
     */
    public BzWaybillExportRecord setFileId(String fileId) {
        this.fileId = fileId;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BzWaybillExportRecord setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BzWaybillExportRecord setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BzWaybillExportRecord setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BzWaybillExportRecord setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzWaybillExportRecord setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzWaybillExportRecord setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzWaybillExportRecord setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}