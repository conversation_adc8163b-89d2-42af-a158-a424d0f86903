package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BzWaybillFile实体
 */
@TableName("bz_waybill_file")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzWaybillFile extends Model<BzWaybillFile> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("waybill_code")
    private String waybillCode;//运单编号

    @TableField("file_id")
    private String fileId;

    @TableField("upload_type")
    private String uploadType;//文件上传类型（1为签收图片，2异常图片）

    @TableField("upload_time")
    private String uploadTime;//文件上传时间

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;//创建人id

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;//创建人

    @TableField(value = "update_user_id", fill = FieldFill.UPDATE)
    private String updateUserId;//修改人id

    @TableField(value = "update_user_name", fill = FieldFill.UPDATE)
    private String updateUserName;//更新人

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    public Long getId() {
        return this.id;
    }

    public BzWaybillFile setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取运单编号
     *
     * @return 运单编号
     */
    public String getWaybillCode() {
        return this.waybillCode;
    }

    /**
     * 设置运单编号
     *
     * @param waybillCode 运单编号
     * @return 当前对象
     */
    public BzWaybillFile setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
        return this;
    }

    public String getFileId() {
        return this.fileId;
    }

    public BzWaybillFile setFileId(String fileId) {
        this.fileId = fileId;
        return this;
    }

    /**
     * 获取文件上传类型（1为签收图片，2异常图片）
     *
     * @return 文件上传类型（1为签收图片，2异常图片）
     */
    public String getUploadType() {
        return this.uploadType;
    }

    /**
     * 设置文件上传类型（1为签收图片，2异常图片）
     *
     * @param uploadType 文件上传类型（1为签收图片，2异常图片）
     * @return 当前对象
     */
    public BzWaybillFile setUploadType(String uploadType) {
        this.uploadType = uploadType;
        return this;
    }

    /**
     * 获取文件上传时间
     *
     * @return 文件上传时间
     */
    public String getUploadTime() {
        return this.uploadTime;
    }

    /**
     * 设置文件上传时间
     *
     * @param uploadTime 文件上传时间
     * @return 当前对象
     */
    public BzWaybillFile setUploadTime(String uploadTime) {
        this.uploadTime = uploadTime;
        return this;
    }

    /**
     * 获取创建人id
     *
     * @return 创建人id
     */
    public String getCreateUserId() {
        return this.createUserId;
    }

    /**
     * 设置创建人id
     *
     * @param createUserId 创建人id
     * @return 当前对象
     */
    public BzWaybillFile setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
        return this;
    }

    /**
     * 获取创建人
     *
     * @return 创建人
     */
    public String getCreateUserName() {
        return this.createUserName;
    }

    /**
     * 设置创建人
     *
     * @param createUserName 创建人
     * @return 当前对象
     */
    public BzWaybillFile setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
        return this;
    }

    /**
     * 获取修改人id
     *
     * @return 修改人id
     */
    public String getUpdateUserId() {
        return this.updateUserId;
    }

    /**
     * 设置修改人id
     *
     * @param updateUserId 修改人id
     * @return 当前对象
     */
    public BzWaybillFile setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
        return this;
    }

    /**
     * 获取更新人
     *
     * @return 更新人
     */
    public String getUpdateUserName() {
        return this.updateUserName;
    }

    /**
     * 设置更新人
     *
     * @param updateUserName 更新人
     * @return 当前对象
     */
    public BzWaybillFile setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzWaybillFile setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzWaybillFile setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzWaybillFile setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}