package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("bz_waybill_info_sync")
public class BzWaybillInfoSync {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @TableField("waybill_id")
    private Long waybillId;
    @TableField("waybill_code")
    private String waybillCode;
    @TableField("dispatch_time_sync")
    private Integer dispatchTimeSync;
    @TableField("signed_time_sync")
    private Integer signedTimeSync;
    @TableField("is_delete")
    private Integer isDelete;
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;
    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;

}
