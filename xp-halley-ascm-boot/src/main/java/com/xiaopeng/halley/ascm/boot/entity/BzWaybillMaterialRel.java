package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("自建运单物料关联")
@TableName("bz_waybill_material_rel")
public class BzWaybillMaterialRel {

	@TableId(type = IdType.AUTO)
	@ApiModelProperty("主键")
	private Long id;

	@ApiModelProperty("运单编号")
	private String waybillCode;

	@ApiModelProperty("物料编码")
	private String materialCode;

	@ApiModelProperty("物料描述")
	private String materialName;

	@ApiModelProperty("数量")
	private Integer quantity;

	@ApiModelProperty("单位")
	private String unit;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建人ID")
	private String createUserId;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建人名称")
	private String createUserName;

	@TableField(fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty("更新人ID")
	private String updateUserId;

	@TableField(fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty("更新人名称")
	private String updateUserName;

	@TableField(fill = FieldFill.INSERT)
	@ApiModelProperty("创建时间")
	private Date createTime;

	@TableField(fill = FieldFill.INSERT_UPDATE)
	@ApiModelProperty("更新时间")
	private Date updateTime;

}