package com.xiaopeng.halley.ascm.boot.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("bz_waybill_retry")
public class BzWaybillRetry implements Serializable {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String waybillCode;

    private String warehouseNumber;

    private String interfaceNumber;

    private String interfaceDescribe;

    private String shopCode;

    private String shopDescribe;

    private String targetSystem;

    private Date firstSendDate;

    private Integer firstSendState;

    private Date lastSendDate;

    private String lastSendName;

    private Integer retryReturnMsg;

    private String payloadContent;

    private Integer requestCount;

    private Integer isShow;

    private String sendSource;

    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private String createUserId;

    @TableField(value = "create_user_name", fill = FieldFill.INSERT)
    private String createUserName;

    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

}
