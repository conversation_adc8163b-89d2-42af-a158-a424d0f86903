package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * BzWaybillTrackHistory实体
 */
@TableName("bz_waybill_track_history")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzWaybillTrackHistory extends Model<BzWaybillTrackHistory> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("waybill_code")
    @Schema(name = "waybillCode", description = "运单编码")
    private String waybillCode;//运单编码

    @TableField("province")
    @Schema(name = "province", description = "省份")
    private String province;//省

    @TableField("city")
    @Schema(name = "city", description = "城市")
    private String city;//城市

    @TableField("address")
    @Schema(name = "address", description = "详细地址")
    private String address;//详细地址

    @TableField("latitude")
    @Schema(name = "latitude", description = "纬度")
    private Double latitude;//纬度

    @TableField("longitude")
    @Schema(name = "longitude", description = "经度")
    private Double longitude;//经度

    @TableField("track_status")
    @Schema(name = "trackStatus", description = "状态(0-初始点，1-转交，2-在途点，3-交付）")
    private Integer trackStatus;//状态(0-初始点，1-转交，2-在途点，3-交付）

    @TableField("driver_name")
    @Schema(name = "driverName", description = "司机名称")
    private String driverName;//司机名称

    @TableField("driver_phone")
    @Schema(name = "driverPhone", description = "司机手机号")
    private String driverPhone;//司机手机号

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BzWaybillTrackHistory setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取运单编码
     *
     * @return 运单编码
     */
    public String getWaybillCode() {
        return this.waybillCode;
    }

    /**
     * 设置运单编码
     *
     * @param waybillCode 运单编码
     * @return 当前对象
     */
    public BzWaybillTrackHistory setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
        return this;
    }

    /**
     * 获取省
     *
     * @return 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置省
     *
     * @param province 省
     * @return 当前对象
     */
    public BzWaybillTrackHistory setProvince(String province) {
        this.province = province;
        return this;
    }

    /**
     * 获取城市
     *
     * @return 城市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置城市
     *
     * @param city 城市
     * @return 当前对象
     */
    public BzWaybillTrackHistory setCity(String city) {
        this.city = city;
        return this;
    }

    /**
     * 获取详细地址
     *
     * @return 详细地址
     */
    public String getAddress() {
        return this.address;
    }

    /**
     * 设置详细地址
     *
     * @param address 详细地址
     * @return 当前对象
     */
    public BzWaybillTrackHistory setAddress(String address) {
        this.address = address;
        return this;
    }

    /**
     * 获取纬度
     *
     * @return 纬度
     */
    public Double getLatitude() {
        return this.latitude;
    }

    /**
     * 设置纬度
     *
     * @param latitude 纬度
     * @return 当前对象
     */
    public BzWaybillTrackHistory setLatitude(Double latitude) {
        this.latitude = latitude;
        return this;
    }

    /**
     * 获取经度
     *
     * @return 经度
     */
    public Double getLongitude() {
        return this.longitude;
    }

    /**
     * 设置经度
     *
     * @param longitude 经度
     * @return 当前对象
     */
    public BzWaybillTrackHistory setLongitude(Double longitude) {
        this.longitude = longitude;
        return this;
    }

    /**
     * 获取状态(0-初始点，1-转交，2-在途点，3-交付）
     *
     * @return 状态(0 - 初始点 ， 1 - 转交 ， 2 - 在途点 ， 3 - 交付 ）
     */
    public Integer getTrackStatus() {
        return this.trackStatus;
    }

    /**
     * 设置状态(0-初始点，1-转交，2-在途点，3-交付）
     *
     * @param trackStatus 状态(0-初始点，1-转交，2-在途点，3-交付）
     * @return 当前对象
     */
    public BzWaybillTrackHistory setTrackStatus(Integer trackStatus) {
        this.trackStatus = trackStatus;
        return this;
    }

    /**
     * 获取司机名称
     *
     * @return 司机名称
     */
    public String getDriverName() {
        return this.driverName;
    }

    /**
     * 设置司机名称
     *
     * @param driverName 司机名称
     * @return 当前对象
     */
    public BzWaybillTrackHistory setDriverName(String driverName) {
        this.driverName = driverName;
        return this;
    }

    /**
     * 获取司机手机号
     *
     * @return 司机手机号
     */
    public String getDriverPhone() {
        return this.driverPhone;
    }

    /**
     * 设置司机手机号
     *
     * @param driverPhone 司机手机号
     * @return 当前对象
     */
    public BzWaybillTrackHistory setDriverPhone(String driverPhone) {
        this.driverPhone = driverPhone;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzWaybillTrackHistory setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzWaybillTrackHistory setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzWaybillTrackHistory setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}