package com.xiaopeng.halley.ascm.boot.entity;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import java.util.Date;

/**
 * BzWaybillTrackTruckDriver实体
 */
@TableName("bz_waybill_track_truck_driver")
@SuppressWarnings({"unused", "UnusedReturnValue", "WeakerAccess"})
final public class BzWaybillTrackTruckDriver extends Model<BzWaybillTrackTruckDriver> {

    private static final long serialVersionUID = 1L;//序列化版本ID

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;//主键id

    @TableField("waybill_code")
    private String waybillCode;//运单编码

    @TableField("province")
    private String province;//省

    @TableField("city")
    private String city;//城市

    @TableField("address")
    private String address;//详细地址

    @TableField("receive_driver_name")
    private String receiveDriverName;//接收方司机名称

    @TableField("receive_driver_phone")
    private String receiveDriverPhone;//接收方司机手机号

    @TableField("send_driver_name")
    private String sendDriverName;//送出方司机名称

    @TableField("send_driver_phone")
    private String sendDriverPhone;//送出方司机手机号

    @TableField("is_delete")
    private Integer isDelete;//是否删除（1 - 已删除，0 - 正常）

    @TableField("create_time")
    private Date createTime;//创建时间

    @TableField("update_time")
    private Date updateTime;//更新时间

    /**
     * 获取主键id
     *
     * @return 主键id
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置主键id
     *
     * @param id 主键id
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setId(Long id) {
        this.id = id;
        return this;
    }

    /**
     * 获取运单编码
     *
     * @return 运单编码
     */
    public String getWaybillCode() {
        return this.waybillCode;
    }

    /**
     * 设置运单编码
     *
     * @param waybillCode 运单编码
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
        return this;
    }

    /**
     * 获取省
     *
     * @return 省
     */
    public String getProvince() {
        return this.province;
    }

    /**
     * 设置省
     *
     * @param province 省
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setProvince(String province) {
        this.province = province;
        return this;
    }

    /**
     * 获取城市
     *
     * @return 城市
     */
    public String getCity() {
        return this.city;
    }

    /**
     * 设置城市
     *
     * @param city 城市
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setCity(String city) {
        this.city = city;
        return this;
    }

    /**
     * 获取详细地址
     *
     * @return 详细地址
     */
    public String getAddress() {
        return this.address;
    }

    /**
     * 设置详细地址
     *
     * @param address 详细地址
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setAddress(String address) {
        this.address = address;
        return this;
    }

    /**
     * 获取接收方司机名称
     *
     * @return 接收方司机名称
     */
    public String getReceiveDriverName() {
        return this.receiveDriverName;
    }

    /**
     * 设置接收方司机名称
     *
     * @param receiveDriverName 接收方司机名称
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setReceiveDriverName(String receiveDriverName) {
        this.receiveDriverName = receiveDriverName;
        return this;
    }

    /**
     * 获取接收方司机手机号
     *
     * @return 接收方司机手机号
     */
    public String getReceiveDriverPhone() {
        return this.receiveDriverPhone;
    }

    /**
     * 设置接收方司机手机号
     *
     * @param receiveDriverPhone 接收方司机手机号
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setReceiveDriverPhone(String receiveDriverPhone) {
        this.receiveDriverPhone = receiveDriverPhone;
        return this;
    }

    /**
     * 获取送出方司机名称
     *
     * @return 送出方司机名称
     */
    public String getSendDriverName() {
        return this.sendDriverName;
    }

    /**
     * 设置送出方司机名称
     *
     * @param sendDriverName 送出方司机名称
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setSendDriverName(String sendDriverName) {
        this.sendDriverName = sendDriverName;
        return this;
    }

    /**
     * 获取送出方司机手机号
     *
     * @return 送出方司机手机号
     */
    public String getSendDriverPhone() {
        return this.sendDriverPhone;
    }

    /**
     * 设置送出方司机手机号
     *
     * @param sendDriverPhone 送出方司机手机号
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setSendDriverPhone(String sendDriverPhone) {
        this.sendDriverPhone = sendDriverPhone;
        return this;
    }

    /**
     * 获取是否删除（1 - 已删除，0 - 正常）
     *
     * @return 是否删除（1 - 已删除，0 - 正常）
     */
    public Integer getIsDelete() {
        return this.isDelete;
    }

    /**
     * 设置是否删除（1 - 已删除，0 - 正常）
     *
     * @param isDelete 是否删除（1 - 已删除，0 - 正常）
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
        return this;
    }

    /**
     * 获取创建时间
     *
     * @return 创建时间
     */
    public Date getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    /**
     * 获取更新时间
     *
     * @return 更新时间
     */
    public Date getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     * @return 当前对象
     */
    public BzWaybillTrackTruckDriver setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }

}