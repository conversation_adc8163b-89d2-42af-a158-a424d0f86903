package com.xiaopeng.halley.ascm.boot.entity;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Map;

public class EgressGatewayRequest {
    @Schema(
            title = "系统ID",
            description = "系统ID"
    )
    private String appId;
    @Schema(
            title = "接口编码",
            description = "接口编码"
    )
    private String apiCode;
    @Schema(
            title = "业务数据",
            description = "业务数据"
    )
    private JSONObject data;
    @Schema(
            title = "拓展参数",
            description = "拓展参数"
    )
    private Map<String, Object> params;

    public EgressGatewayRequest() {
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(final String appId) {
        this.appId = appId;
    }

    public String getApiCode() {
        return this.apiCode;
    }

    public void setApiCode(final String apiCode) {
        this.apiCode = apiCode;
    }

    public JSONObject getData() {
        return this.data;
    }

    public void setData(final JSONObject data) {
        this.data = data;
    }

    public Map<String, Object> getParams() {
        return this.params;
    }

    public void setParams(final Map<String, Object> params) {
        this.params = params;
    }

    @Override
    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof EgressGatewayRequest)) {
            return false;
        } else {
            EgressGatewayRequest other = (EgressGatewayRequest) o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59:
                {
                    Object this$appId = this.getAppId();
                    Object other$appId = other.getAppId();
                    if (this$appId == null) {
                        if (other$appId == null) {
                            break label59;
                        }
                    } else if (this$appId.equals(other$appId)) {
                        break label59;
                    }

                    return false;
                }

                Object this$apiCode = this.getApiCode();
                Object other$apiCode = other.getApiCode();
                if (this$apiCode == null) {
                    if (other$apiCode != null) {
                        return false;
                    }
                } else if (!this$apiCode.equals(other$apiCode)) {
                    return false;
                }

                Object this$data = this.getData();
                Object other$data = other.getData();
                if (this$data == null) {
                    if (other$data != null) {
                        return false;
                    }
                } else if (!this$data.equals(other$data)) {
                    return false;
                }

                Object this$params = this.getParams();
                Object other$params = other.getParams();
                if (this$params == null) {
                    return other$params == null;
                } else {
                    return this$params.equals(other$params);
                }
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof EgressGatewayRequest;
    }

    @Override
    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : $appId.hashCode());
        Object $apiCode = this.getApiCode();
        result = result * 59 + ($apiCode == null ? 43 : $apiCode.hashCode());
        Object $data = this.getData();
        result = result * 59 + ($data == null ? 43 : $data.hashCode());
        Object $params = this.getParams();
        result = result * 59 + ($params == null ? 43 : $params.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "EgressGatewayRequest(appId=" + this.getAppId() + ", apiCode=" + this.getApiCode() + ", data=" + this.getData() + ", params=" + this.getParams() + ")";
    }
}