package com.xiaopeng.halley.ascm.boot.excel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public interface AbstractExcelImportHandler<T extends ExcelObject> {

	public static void main(String[] args) {
		System.err.println(BzSelfWaybill.class.getSimpleName());
	}

	/**
	 * 执行校验逻辑
	 */
	void doVerify(ImportContext<T> context);

	/**
	 * 执行导入数据库逻辑
	 */
	void doImport(List<T> successList);

	default boolean isAtomic() {
		return false;
	}

	/**
	 * 通用校验逻辑
	 */
	default void validate(ExcelObject excelObject) {
		Validator validator = SpringUtil.getBean(Validator.class);
		for (ConstraintViolation<?> violation : validator.validate(excelObject)) {
			excelObject.appendError(violation.getMessage());
		}
	}

	default Integer importDirect(InputStream inputStream, Class<T> clazz, Map<String, Object> params) {
		// 1. 读取Excel数据
		List<T> importDataList = EasyExcel.read(inputStream)
				.head(clazz)
				.sheet()
				.doReadSync();
		if (importDataList.isEmpty()) {
			throw new RuntimeException("导入数据为空");
		}
		// 2. 执行校验
		ImportContext<T> context = new ImportContext<>(importDataList, params, true);
		this.doVerify(context);
		if (context.getFailList().isEmpty()) {
			this.doImport(context.getSuccessList());
		}
		return context.getSuccessList().size();
	}

	default ImportResponseDto importVerify(InputStream inputStream, Class<T> clazz, Map<String, Object> params) {
		// 1. 读取Excel数据
		List<T> importDataList = EasyExcel.read(inputStream)
				.head(clazz)
				.sheet()
				.doReadSync();
		if (importDataList.isEmpty()) {
			throw new RuntimeException("导入数据为空");
		}
		// 2. 执行校验
		ImportContext<T> context = new ImportContext<>(importDataList, params, isAtomic());
		this.doVerify(context);
		// 3. 处理结果
		RedissonClient redisson = SpringUtil.getBean(RedissonClient.class);
		String fileCode = UUID.randomUUID().toString();

		List<T> successList = context.getSuccessList();
		List<T> failList = context.getFailList();

		if (!successList.isEmpty()) {
			// 原子性需保证全部成功
			if (!isAtomic() || failList.isEmpty()) {
				redisson.getBucket(getSuccessRedisKey(fileCode)).set(successList, 24, TimeUnit.HOURS);
			}
		}
		if (!failList.isEmpty()) {
			redisson.getBucket(getFailRedisKey(fileCode)).set(failList, 24, TimeUnit.HOURS);
		}

		ImportResponseDto responseDTO = new ImportResponseDto();
		responseDTO.setSuccessCount(successList.size());
		responseDTO.setFailCount(failList.size());
		responseDTO.setFileCode(fileCode);
		return responseDTO;
	}

	default String importData(String fileCode) {
		List<T> successList = getSuccessList(fileCode);
		if (successList.isEmpty()) {
			throw new RuntimeException("文件不存在");
		}
		List<T> failList = getFailList(fileCode);
		this.doImport(successList);
		// 删除redis缓存
		RedissonClient redisson = SpringUtil.getBean(RedissonClient.class);
		if (!successList.isEmpty()) {
			redisson.getBucket(getSuccessRedisKey(fileCode)).delete();
		}
		if (!failList.isEmpty()) {
			redisson.getBucket(getFailRedisKey(fileCode)).delete();
		}
		return StrUtil.format("本次导入更新成功{}个 更新失败{}个", successList.size(), failList.size());
	}

	default List<T> getSuccessList(String fileCode) {
		RedissonClient redisson = SpringUtil.getBean(RedissonClient.class);
		RBucket<List<T>> bucket = redisson.getBucket(getSuccessRedisKey(fileCode));
		return CollUtil.defaultIfEmpty(bucket.get(), new ArrayList<>());
	}

	default List<T> getFailList(String fileCode) {
		RedissonClient redisson = SpringUtil.getBean(RedissonClient.class);
		RBucket<List<T>> bucket = redisson.getBucket(getFailRedisKey(fileCode));
		return CollUtil.defaultIfEmpty(bucket.get(), new ArrayList<>());
	}

	default List<T> getFailListOrThrowEx(String fileCode) {
		List<T> failList = getFailList(fileCode);
		if (CollUtil.isEmpty(failList)) {
			throw new RuntimeException("错误文件不存在");
		}
		return failList;
	}

	default String getBusinessKey() {
		return getClass().getSimpleName();
	}

	default String getSheetName() {
		return StrUtil.firstNonBlank(
				Opt.ofTry(() -> getClass().getAnnotation(ApiModel.class).value()).exceptionOrElse(""),
				Opt.ofTry(() -> getClass().getAnnotation(Schema.class).description()).exceptionOrElse(""),
				getBusinessKey()
		);
	}

	default String getSuccessRedisKey(String fileCode) {
		return String.format("ascm:" + getBusinessKey() + ":import:success:%s", fileCode);
	}

	default String getFailRedisKey(String fileCode) {
		return String.format(getBusinessKey() + ":import:fail:%s", fileCode);
	}
}