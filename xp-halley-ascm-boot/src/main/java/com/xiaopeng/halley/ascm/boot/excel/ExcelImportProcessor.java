package com.xiaopeng.halley.ascm.boot.excel;

import com.baomidou.mybatisplus.core.toolkit.ReflectionKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ExcelImportProcessor implements SmartInitializingSingleton {
	@Resource
	private ApplicationContext applicationContext;
	private final Map<String, AbstractExcelImportHandler<?>> handlerMap = new HashMap<>();

	public AbstractExcelImportHandler<?> getHandler(String businessKey) {
		AbstractExcelImportHandler<?> handler = handlerMap.get(businessKey);
		if (handler == null) {
			throw new RuntimeException("businessKey不存在: " + businessKey);
		}
		return handler;
	}

	/**
	 * 反射或其它方式，推断导入Excel对应的实体类Class
	 */
	@SuppressWarnings("unchecked")
	public <T> Class<T> resolveExcelClass(AbstractExcelImportHandler<?> handler) {
		Class<?> superClassGenericType = ReflectionKit.getSuperClassGenericType(handler.getClass(), AbstractExcelImportHandler.class, 0);
		return (Class<T>) superClassGenericType;
	}

	@Override
	public void afterSingletonsInstantiated() {
		for (AbstractExcelImportHandler handler : applicationContext.getBeansOfType(AbstractExcelImportHandler.class).values()) {
			handlerMap.put(handler.getBusinessKey(), handler);
			log.info("Registered import handler: {}", handler.getBusinessKey());
		}
	}
}
