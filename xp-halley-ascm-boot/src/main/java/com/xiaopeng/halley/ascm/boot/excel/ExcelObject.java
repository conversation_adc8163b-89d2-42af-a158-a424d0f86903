package com.xiaopeng.halley.ascm.boot.excel;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public abstract class ExcelObject {
	@ExcelProperty("错误信息")
	protected String errorMsg;
	protected List<String> errorMsgList = new ArrayList<>();

	public void appendError(String errorMsg) {
		this.errorMsgList.add(errorMsg);
	}

	public void appendError(boolean expression, String errorMsg, Object ...params) {
		if (expression) {
			appendError(StrUtil.format(errorMsg, params));
		}
	}

	public boolean hasError() {
		return !this.errorMsgList.isEmpty();
	}

	public String getErrorMsg() {
		return String.join(",", this.errorMsgList);
	}
}
