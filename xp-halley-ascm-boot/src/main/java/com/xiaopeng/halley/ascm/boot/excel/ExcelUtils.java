package com.xiaopeng.halley.ascm.boot.excel;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@UtilityClass
public class ExcelUtils {
	private static final HorizontalCellStyleStrategy DEFAULT_STRATEGY;

	static {
		// 设置字体
		WriteFont font = new WriteFont();
		font.setFontName("阿里巴巴普惠体");
		font.setFontHeightInPoints((short) 10);
		font.setColor(IndexedColors.BLACK.index);

		// 设置头部样式,标题居中
		WriteCellStyle headStyle = new WriteCellStyle();
		headStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		headStyle.setWriteFont(font);

		// 设置内容样式,设置内容居中,内容自动换行
		WriteCellStyle contentStyle = new WriteCellStyle();
		contentStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentStyle.setWrapped(true);
		DEFAULT_STRATEGY = new HorizontalCellStyleStrategy(headStyle, contentStyle);
	}

	/**
	 * 导入Excel表格
	 */
	public <T> List<T> importExcel(MultipartFile file, Class<T> clazz) throws IOException {
		List<T> dataList = EasyExcel.read(file.getInputStream())
				.head(clazz)
				.sheet()
				.doReadSync();
		log.info("成功导入[{}]条数据", dataList.size());
		return dataList;
	}

	/**
	 * 导出Excel表格数据
	 */
	public <T> void exportExcel(HttpServletResponse response, List<T> dataList, Class<T> clazz, String sheetName) throws IOException {
		// 设置下载返回文件名
		setContentDisposition(response, sheetName, true);

		// 开始导出Excel
		EasyExcel.write(response.getOutputStream(), clazz)
				.excelType(ExcelTypeEnum.XLSX)
				.registerWriteHandler(DEFAULT_STRATEGY)
				.sheet(sheetName)
				.doWrite(dataList);

		log.info("成功导出[{}]条数据", dataList.size());
	}

	/**
	 * 创建Excel模版
	 */
	public <T> void createTemplate(HttpServletResponse response, Class<T> clazz, String sheetName) throws IOException {
		setContentDisposition(response, sheetName, false);
		EasyExcel.write(response.getOutputStream(), clazz)
				.excludeColumnFieldNames(Collections.singletonList("errorMsg"))
				.excelType(ExcelTypeEnum.XLSX)
				.registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 40, (short) 50))
				.sheet(sheetName)
				.doWrite(Collections.emptyList());
	}

	public void setContentDisposition(HttpServletResponse response, String sheetName, boolean withTimeStamp) {
		String timestamp = DateUtil.format(new Date(), DatePattern.CHINESE_DATE_TIME_PATTERN);
		String filename = URLUtil.encodeAll(withTimeStamp ? sheetName + "_" + timestamp + ExcelTypeEnum.XLSX.getValue()
				: sheetName + ExcelTypeEnum.XLSX.getValue(), StandardCharsets.UTF_8);
		response.setHeader(HttpHeaders.CONTENT_DISPOSITION, ContentDisposition.inline().filename(filename).build().toString());
	}
}
