package com.xiaopeng.halley.ascm.boot.excel;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
public class ImportContext<T> {

	private boolean atomic = false;

	/**
	 * 原始导入数据
	 */
	private List<T> dataList;

	/**
	 * 校验通过数据
	 */
	private List<T> successList = new ArrayList<>();

	/**
	 * 校验失败数据
	 */
	private List<T> failList = new ArrayList<>();

	/**
	 * 额外参数
	 */
	private Map<String, Object> params;

	public ImportContext(List<T> dataList, Map<String, Object> params, boolean atomic) {
		this.dataList = dataList;
		this.params = params;
		this.atomic = atomic;
	}
}