package com.xiaopeng.halley.ascm.boot.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.xiaopeng.halley.ascm.boot.component.SerialNumberGenerator;
import com.xiaopeng.halley.ascm.boot.constant.ImportKeyConstants;
import com.xiaopeng.halley.ascm.boot.dto.BzSelfWaybillExcel;
import com.xiaopeng.halley.ascm.boot.entity.BaseShop;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillMaterialRel;
import com.xiaopeng.halley.ascm.boot.excel.AbstractExcelImportHandler;
import com.xiaopeng.halley.ascm.boot.excel.ImportContext;
import com.xiaopeng.halley.ascm.boot.mapper.BaseShopMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BaseTransportTypeMapper;
import com.xiaopeng.halley.ascm.boot.service.AscmLoginUserHelper;
import com.xiaopeng.halley.ascm.boot.service.BzSelfWaybillService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillMaterialRelService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class BzSelfWaybillImportHandler implements AbstractExcelImportHandler<BzSelfWaybillExcel> {
	@Value("#{'${car-type-list}'.split(',')}")
	private List<String> carTypeList;
	@Resource
	private BaseShopMapper baseShopMapper;
	@Resource
	private BaseTransportTypeMapper baseTransportTypeMapper;
	@Resource
	private AscmLoginUserHelper ascmLoginUserHelper;
	@Resource
	private SerialNumberGenerator serialNumberGenerator;
	@Resource
	private BzWaybillMaterialRelService bzWaybillMaterialRelService;
	@Resource
	private BzSelfWaybillService bzSelfWaybillService;

	@Override
	public void doVerify(ImportContext<BzSelfWaybillExcel> context) {
		Map<String, BaseShop> shopMap = baseShopMapper.listMap();
		Set<String> transportTypeSet = baseTransportTypeMapper.getTransportTypeSet();

		// 存储运单号+物料号的唯一值,此时还没生成运单号，用分组的key
		Set<String> keySet = new HashSet<>();
		for (BzSelfWaybillExcel item : context.getDataList()) {
			this.validate(item);
			item.appendError(!transportTypeSet.contains(item.getTransportType()), "运输类型不存在");
			item.appendError(!carTypeList.contains(item.getCarType()), "车型必须为[{}]其中一个", carTypeList);

			if (shopMap.containsKey(item.getShipper())) {
				BaseShop baseShop = shopMap.get(item.getShipper());
				item.setShipperName(baseShop.getShopName());
				item.setShipCity(baseShop.getShopCity());
				item.setShipContact(baseShop.getContactPerson());
				item.setShipContactNum(baseShop.getContactNum());
			} else {
				item.appendError(StrUtil.isBlank(item.getShipCity()), "发货城市不能为空");
				item.appendError(StrUtil.isBlank(item.getShipContact()), "发货联系人不能为空");
				item.appendError(StrUtil.isBlank(item.getShipContactNum()), "发货联系电话不能为空");
			}

			if (shopMap.containsKey(item.getConsignee())) {
				BaseShop baseShop = shopMap.get(item.getConsignee());
				item.setConsigneeName(baseShop.getShopName());
				item.setReceiveCity(baseShop.getShopCity());
				item.setReceiveAddress(baseShop.getShopAddress());
				item.setReceiveContact(baseShop.getContactPerson());
				item.setReceiveContactNum(baseShop.getContactNum());
			} else {
				item.appendError(StrUtil.isBlank(item.getReceiveCity()), "收货城市不能为空");
				item.appendError(StrUtil.isBlank(item.getReceiveAddress()), "收货地址不能为空");
				item.appendError(StrUtil.isBlank(item.getReceiveContact()), "收货联系人不能为空");
				item.appendError(StrUtil.isBlank(item.getReceiveContactNum()), "收货联系电话不能为空");
			}
			item.appendError(!keySet.add(item.getGroupKey() + item.getMaterialCode()), "物料号重复");

			if (item.hasError()) {
				context.getFailList().add(item);
			} else {
				context.getSuccessList().add(item);
			}
		}
	}

	@Override
	@Transactional
	public void doImport(List<BzSelfWaybillExcel> successList) {
		List<BzSelfWaybill> bzSelfWaybills = new ArrayList<>();
		List<BzWaybillMaterialRel> bzWaybillMaterialRels = new ArrayList<>();
		BaseWarehouse currentWarehouse = ascmLoginUserHelper.getCurrentWarehouse();

		// 根据规则进行分组
		Map<String, List<BzSelfWaybillExcel>> waybillMap = successList.stream().collect(Collectors
				.groupingBy(BzSelfWaybillExcel::getGroupKey));

		for (List<BzSelfWaybillExcel> excels : waybillMap.values()) {
			// 生成流水运单号
			String waybillCode = serialNumberGenerator.generateWaybillCode(currentWarehouse.getLgort());
			BzSelfWaybill bzSelfWaybill = BeanUtil.copyProperties(excels.get(0), BzSelfWaybill.class);
			bzSelfWaybill.setWaybillCode(waybillCode);
			// 设置当前仓库
			bzSelfWaybill.setLgort(currentWarehouse.getLgort());
			bzSelfWaybill.setLgobe(currentWarehouse.getLgobe());
			bzSelfWaybills.add(bzSelfWaybill);
			// 添加物料关联信息
			List<BzWaybillMaterialRel> materialRels = BeanUtil.copyToList(excels, BzWaybillMaterialRel.class);
			materialRels.forEach(e -> e.setWaybillCode(waybillCode));
			bzWaybillMaterialRels.addAll(materialRels);
		}

		bzWaybillMaterialRelService.saveBatch(bzWaybillMaterialRels);
		bzSelfWaybillService.saveBatch(bzSelfWaybills);
	}

	@Override
	public boolean isAtomic() {
		return true;
	}

	@Override
	public String getBusinessKey() {
		return ImportKeyConstants.BZ_SELF_WAYBILL;
	}
}
