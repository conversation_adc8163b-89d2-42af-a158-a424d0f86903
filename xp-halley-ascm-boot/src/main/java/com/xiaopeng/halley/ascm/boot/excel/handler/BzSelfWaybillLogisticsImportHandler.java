package com.xiaopeng.halley.ascm.boot.excel.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xiaopeng.halley.ascm.boot.constant.ImportKeyConstants;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import com.xiaopeng.halley.ascm.boot.excel.AbstractExcelImportHandler;
import com.xiaopeng.halley.ascm.boot.excel.ImportContext;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto.LogisticsImportExcel;
import com.xiaopeng.halley.ascm.boot.service.BzSelfWaybillService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BzSelfWaybillLogisticsImportHandler implements AbstractExcelImportHandler<LogisticsImportExcel> {
	@Value("${delivery.selfCallbackUrl}")
	private String callbackUrl;
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private BzSelfWaybillService bzSelfWaybillService;

	@Override
	public void doVerify(ImportContext<LogisticsImportExcel> context) {
		Set<String> waybillCodes = context.getDataList().stream().map(LogisticsImportExcel::getWaybillCode)
				.collect(Collectors.toSet());

		Set<String> waybillSet = waybillCodes.isEmpty() ? Collections.emptySet() : bzSelfWaybillService.lambdaQuery()
				.in(BzSelfWaybill::getWaybillCode, waybillCodes).list()
				.stream().map(BzSelfWaybill::getWaybillCode).collect(Collectors.toSet());

		for (LogisticsImportExcel logisticsImport : context.getDataList()) {
			this.validate(logisticsImport);
			logisticsImport.appendError(!waybillSet.contains(logisticsImport.getWaybillCode()), "运单编号不存在");
		}
	}

	@Override
	public void doImport(List<LogisticsImportExcel> successList) {
		Map<String, BzSelfWaybill> waybillMap = bzSelfWaybillService.listMap(successList, LogisticsImportExcel::getWaybillCode);
		for (LogisticsImportExcel item : successList) {
			String waybillCode = item.getWaybillCode();
			try {
				Map result = bzWaybillService.subscribeDelivery(item.getLogisticsCode(), item.getLogisticsCompanyCode(), item.getDeliveryPhone(), callbackUrl);
				if ("200".equals(result.get("returnCode"))) {
					BzSelfWaybill bzSelfWaybill = waybillMap.get(waybillCode);
					bzSelfWaybill.setLogisticsCode(item.getLogisticsCode());
					bzSelfWaybill.setLogisticsCompany(item.getLogisticsCompany());
					log.info("快递100订阅成功, 运单号: {}, 物流单号: {}", waybillCode, item.getLogisticsCode());
				} else {
					throw new RuntimeException(JSON.toJSONString(result));
				}
			} catch (Exception e) {
				waybillMap.remove(waybillCode);
				log.error(StrUtil.format("快递100订阅失败, 运单号: {}, 物流单号: {}", waybillCode, item.getLogisticsCode()), e);
			}
		}
		bzSelfWaybillService.updateBatchById(waybillMap.values());
	}

	@Override
	public String getBusinessKey() {
		return ImportKeyConstants.BZ_SELF_WAYBILL_LOGISTICS;
	}
}