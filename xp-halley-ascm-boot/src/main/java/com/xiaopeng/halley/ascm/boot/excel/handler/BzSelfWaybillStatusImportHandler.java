package com.xiaopeng.halley.ascm.boot.excel.handler;

import com.xiaopeng.halley.ascm.boot.constant.ImportKeyConstants;
import com.xiaopeng.halley.ascm.boot.dto.BzSelfWaybillStatusImport;
import com.xiaopeng.halley.ascm.boot.excel.AbstractExcelImportHandler;
import com.xiaopeng.halley.ascm.boot.excel.ImportContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class BzSelfWaybillStatusImportHandler implements AbstractExcelImportHandler<BzSelfWaybillStatusImport> {
	@Override
	public void doVerify(ImportContext<BzSelfWaybillStatusImport> context) {
		for (BzSelfWaybillStatusImport item : context.getDataList()) {
			this.validate(item);
		}
	}

	@Override
	public void doImport(List<BzSelfWaybillStatusImport> successList) {

	}

	@Override
	public String getBusinessKey() {
		return ImportKeyConstants.BZ_SELF_WAYBILL_STATUS;
	}
}
