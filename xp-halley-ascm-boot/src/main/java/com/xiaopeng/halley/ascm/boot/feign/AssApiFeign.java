package com.xiaopeng.halley.ascm.boot.feign;

import com.xiaopeng.halley.ascm.boot.dto.AsmWayBillReq;
import com.xiaopeng.halley.ascm.boot.dto.AsmWaybillFreshReq;
import com.xpeng.athena.common.core.domain.Result;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
		name = "${dragon.appName}",
		path = "/asm/api/waybill"
)
@Tag(name = "骁龙接口")
public interface AssApiFeign {

	@PostMapping
	Result<String> syncWaybill(@RequestBody List<AsmWayBillReq> reqList);

	@PostMapping("/fresh")
	Result<String> freshWaybill(@RequestBody AsmWaybillFreshReq waybillFreshReq);
}