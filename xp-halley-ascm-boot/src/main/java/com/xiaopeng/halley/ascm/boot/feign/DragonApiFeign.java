package com.xiaopeng.halley.ascm.boot.feign;

import com.xiaopeng.halley.ascm.boot.dto.MaterialPicVO;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        name = "xp-ass-epc-cn-boot",
        path = "/api/halley"
)
@Tag(name = "骁龙接口")
public interface DragonApiFeign {

    @PostMapping("/syncPhoto")
    Result<Object> syncPhoto(@RequestBody List<MaterialPicVO> materialPicVOS) throws ResultException;

}