package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.ActualTimeDto;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/8/8 14:11
 */
public class ActualTimeListener implements ReadListener<ActualTimeDto> {
    /**
     * 缓存的数据
     */
    private final List<ActualTimeDto> cachedDataList = new ArrayList<>();

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ActualTimeDto data, AnalysisContext context) {
        //log.info("解析到一条数据:{}", JSON.toJSONString(data));
        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 获取结果
     *
     * @return 返回结果
     */
    public List<ActualTimeDto> getList() {
        return this.cachedDataList;
    }

    /**
     * 清除缓存
     */
    public void clear() {
        this.cachedDataList.clear();
    }
}
