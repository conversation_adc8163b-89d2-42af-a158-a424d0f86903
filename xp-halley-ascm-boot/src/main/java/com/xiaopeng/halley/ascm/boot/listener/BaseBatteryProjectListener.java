package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectExportVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/13 16:10
 */
public class BaseBatteryProjectListener implements ReadListener<BaseBatteryProjectExportVo> {
    /**
     * 缓存的数据
     */
    private final List<BaseBatteryProjectExportVo> cachedDataList = new ArrayList<>();

    @Override
    public void invoke(BaseBatteryProjectExportVo baseBatteryProjectVo, AnalysisContext analysisContext) {
        cachedDataList.add(baseBatteryProjectVo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 获取结果
     *
     * @return 返回结果
     */
    public List<BaseBatteryProjectExportVo> getList() {
        return this.cachedDataList;
    }

    /**
     * 清除缓存
     */
    public void clear() {
        this.cachedDataList.clear();
    }
}
