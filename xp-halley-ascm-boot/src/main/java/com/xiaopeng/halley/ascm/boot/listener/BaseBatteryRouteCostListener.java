package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryRouteCostExportVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 16:38
 */
public class BaseBatteryRouteCostListener implements ReadListener<BaseBatteryRouteCostExportVo> {

    /**
     * 缓存的数据
     */
    private final List<BaseBatteryRouteCostExportVo> cachedDataList = new ArrayList<>();

    @Override
    public void invoke(BaseBatteryRouteCostExportVo baseBatteryRouteCostExportVo, AnalysisContext analysisContext) {
        cachedDataList.add(baseBatteryRouteCostExportVo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 获取结果
     *
     * @return 返回结果
     */
    public List<BaseBatteryRouteCostExportVo> getList() {
        return this.cachedDataList;
    }

    /**
     * 清除缓存
     */
    public void clear() {
        this.cachedDataList.clear();
    }
}
