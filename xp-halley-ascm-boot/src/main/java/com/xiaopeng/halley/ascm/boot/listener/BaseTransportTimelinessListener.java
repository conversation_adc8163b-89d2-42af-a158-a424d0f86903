package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessExportVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/8/7 16:27
 */
// 有个很重要的点 DemoDataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
public class BaseTransportTimelinessListener implements ReadListener<BaseTransportTimelinessExportVo> {

    /**
     * 缓存的数据
     */
    private final List<BaseTransportTimelinessExportVo> cachedDataList = new ArrayList<>();

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(BaseTransportTimelinessExportVo data, AnalysisContext context) {
        //log.info("解析到一条数据:{}", JSON.toJSONString(data));
        cachedDataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    /**
     * 获取结果
     *
     * @return 返回结果
     */
    public List<BaseTransportTimelinessExportVo> getList() {
        return this.cachedDataList;
    }

    /**
     * 清除缓存
     */
    public void clear() {
        this.cachedDataList.clear();
    }

}
