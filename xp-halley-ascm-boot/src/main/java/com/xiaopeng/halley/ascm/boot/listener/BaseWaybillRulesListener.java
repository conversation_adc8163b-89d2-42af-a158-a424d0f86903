package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.BaseWayBillRulesImportDTO;

import java.util.ArrayList;
import java.util.List;

public class BaseWaybillRulesListener implements ReadListener<BaseWayBillRulesImportDTO> {
    /**
     * 缓存的数据
     */
    private final List<BaseWayBillRulesImportDTO> baseWayBillRulesImportDTOList = new ArrayList<>();

    @Override
    public void invoke(BaseWayBillRulesImportDTO baseWayBillRulesImportDTO, AnalysisContext analysisContext) {
        baseWayBillRulesImportDTOList.add(baseWayBillRulesImportDTO);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<BaseWayBillRulesImportDTO> getList() {
        return this.baseWayBillRulesImportDTOList;
    }

    public void clear() {
        this.baseWayBillRulesImportDTOList.clear();
    }
}
