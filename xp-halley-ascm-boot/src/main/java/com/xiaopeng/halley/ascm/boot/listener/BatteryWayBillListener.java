package com.xiaopeng.halley.ascm.boot.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.dto.BatteryWayBillImportVo;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 15:20
 */
public class BatteryWayBillListener implements ReadListener<BatteryWayBillImportVo> {
    /**
     * 缓存的数据
     */
    private final List<BatteryWayBillImportVo> batteryWayBillImportVoList = new ArrayList<>();

    @Override
    public void invoke(BatteryWayBillImportVo batteryWayBillImportVo, AnalysisContext analysisContext) {
        batteryWayBillImportVoList.add(batteryWayBillImportVo);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<BatteryWayBillImportVo> getList() {
        return this.batteryWayBillImportVoList;
    }

    public void clear() {
        this.batteryWayBillImportVoList.clear();
    }
}
