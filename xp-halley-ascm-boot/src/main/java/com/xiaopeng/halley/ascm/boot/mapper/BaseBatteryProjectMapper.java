package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseBatteryProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/13 11:04
 */
public interface BaseBatteryProjectMapper extends BaseMapper<BaseBatteryProject> {
    /**
     * 分页获取总数
     *
     * @param param
     * @return
     */
    long getPageTotal(@Param("param") BaseBatteryProjectDto param);

    /**
     * 分页明细
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BaseBatteryProjectVo> getPage(@Param("param") BaseBatteryProjectDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
