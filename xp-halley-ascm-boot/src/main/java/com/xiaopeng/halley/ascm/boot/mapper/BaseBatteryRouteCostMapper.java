package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryRouteCostDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseBatteryRouteCostVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseBatteryRouteCost;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 10:54
 */
public interface BaseBatteryRouteCostMapper extends BaseMapper<BaseBatteryRouteCost> {
    /**
     * 获取分页的总数
     *
     * @param param
     * @return
     */
    long getPageTotal(@Param("param") BaseBatteryRouteCostDto param);

    /**
     * 获取分页的详细数据
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BaseBatteryRouteCostVo> getPage(@Param("param") BaseBatteryRouteCostDto param, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 获取物流类型
     *
     * @param dispatchCity
     * @param arrivalCity
     * @param repairCenterNum
     * @return
     */
    List<String> getLogisticsProvider(@Param("dispatchCity") String dispatchCity, @Param("arrivalCity") String arrivalCity, @Param("repairCenterNum") String repairCenterNum);
}
