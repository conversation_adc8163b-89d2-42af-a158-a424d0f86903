package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseCheckStandardDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseCheckStandardVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseCheckStandard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.mapper
 * @Date 2024/9/9 13:49
 */
public interface BaseCheckStandardMapper extends BaseMapper<BaseCheckStandard> {
    long getPageTotal(@Param("param") BaseCheckStandardDto param);

    List<BaseCheckStandardVo> getPage(@Param("param") BaseCheckStandardDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
