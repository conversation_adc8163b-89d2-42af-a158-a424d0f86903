package com.xiaopeng.halley.ascm.boot.mapper;

import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.entity.BaseExpressTransshipment;
import org.apache.ibatis.annotations.Mapper;

/**
 * 拆合记录
 *
 * <AUTHOR> 自动生成
 * @date 2022-12-06 02:44:57
 */
@SuppressWarnings("unused")
@Mapper
public interface BaseExpressTransshipmentMapper extends BaseMapper<BaseExpressTransshipment> {

	/**
	 * 根据公司编码获取公司名称
	 * @param companyCode
	 * @return
	 */
	default String getCompanyName(String companyCode) {
		return Opt.ofTry(() -> this.selectOne(new LambdaQueryWrapper<BaseExpressTransshipment>()
				.eq(BaseExpressTransshipment::getCompanyCode, companyCode)
				.eq(BaseExpressTransshipment::getIsDelete, 0)
		).getCompanyName()).exceptionOrElse("");
	}

}
