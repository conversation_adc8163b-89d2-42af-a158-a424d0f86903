package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardQuery;
import com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardSummary;
import com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardVO;
import com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardView;
import com.xiaopeng.halley.ascm.boot.entity.BaseFeeStandard;

public interface BaseFeeStandardMapper extends BaseMapper<BaseFeeStandard> {

	Page<BaseFeeStandardSummary> pageSummary(Page<BaseFeeStandard> page, BaseFeeStandardQuery param);

	Page<BaseFeeStandardVO> page(Page<BaseFeeStandard> page, BaseFeeStandardQuery param);

	Page<BaseFeeStandardView> pageView(Page<BaseFeeStandard> page, BaseFeeStandardQuery param);
}