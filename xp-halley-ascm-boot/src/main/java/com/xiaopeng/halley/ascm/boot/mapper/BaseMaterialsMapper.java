package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.GetMaterialCheckPageRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.GetMaterialCheckPageResponseVo;
import com.xiaopeng.halley.ascm.boot.dto.GetMaterialPageRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.GetMaterialPageResonseVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseMaterials;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【base_materials(物料表)】的数据库操作Mapper
 * @createDate 2024-08-06 16:13:30
 * @Entity com.xiaopeng.halley.ascm.boot.entity.BaseMaterials
 */
@Mapper
public interface BaseMaterialsMapper extends BaseMapper<BaseMaterials> {
    /**
     * 查询总条数
     *
     * @param param 查询条件
     * @return 查询结果
     */
    long getPageTotal(@Param("param") GetMaterialPageRequestDto param);

    /**
     * 查询结果
     *
     * @param param      查询条件
     * @param startIndex 分页开始参数
     * @param size       分页大小
     * @return 查询结果
     */
    List<GetMaterialPageResonseVo> getPage(@Param("param") GetMaterialPageRequestDto param, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 校验标准分页总数
     *
     * @param param
     * @return
     */
    long getCheckPageTotal(@Param("param") GetMaterialCheckPageRequestDto param);

    /**
     * 校验标准分页查询
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<GetMaterialCheckPageResponseVo> getCheckPage(@Param("param") GetMaterialCheckPageRequestDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}




