package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseRepairCenter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 11:31
 */
public interface BaseRepairCenterMapper extends BaseMapper<BaseRepairCenter> {
    /**
     * 分页查询总数
     *
     * @param param
     * @return
     */
    long getPageTotal(@Param("param") BaseRepairCenterDto param);

    /**
     * 分页查询明细
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BaseRepairCenterVo> getPage(@Param("param") BaseRepairCenterDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
