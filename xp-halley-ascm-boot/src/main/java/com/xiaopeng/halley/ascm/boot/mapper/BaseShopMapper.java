package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseShop;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * BaseShop数据仓库
 */
@SuppressWarnings("unused")
public interface BaseShopMapper extends BaseMapper<BaseShop> {

    default Map<String, BaseShop> listMap() {
        return this.selectList(new LambdaQueryWrapper<BaseShop>().eq(BaseShop::getIsDelete, 0))
                .stream().collect(Collectors.toMap(BaseShop::getShopCode, item -> item));
    }

    List<Map<String, Object>> selectByShopCity(String city);

    /**
     * 获取分页数据的总数
     *
     * @param param
     * @return
     */
    long getPageTotal(@Param("param") BaseShopDto param);

    /**
     * 获取分页的明细
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BaseShopVo> getPage(@Param("param") BaseShopDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
