package com.xiaopeng.halley.ascm.boot.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseTransportTimeliness;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/8/7 10:03
 */
public interface BaseTransportTimelinessMapper extends BaseMapper<BaseTransportTimeliness> {

    default BaseTransportTimeliness getByCity(String warehouseCity, String shopCity) {
        List<BaseTransportTimeliness> baseTransportTimelineList = selectList(new LambdaQueryWrapper<BaseTransportTimeliness>()
                .eq(BaseTransportTimeliness::getWarehouseCity, warehouseCity)
                .eq(BaseTransportTimeliness::getShopCity, shopCity)
                .eq(BaseTransportTimeliness::getIsDelete, 0)
        );
        return CollUtil.getFirst(baseTransportTimelineList);
    }

    /**
     * 查询总条数
     *
     * @param param 查询条件
     * @return 查询结果
     */
    long getPageTotal(@Param("param") BaseTransportTimelinessDto param);

    /**
     * 查询结果
     *
     * @param param      查询条件
     * @param startIndex 分页开始参数
     * @param size       分页大小
     * @return 查询结果
     */
    List<BaseTransportTimelinessVo> getPage(@Param("param") BaseTransportTimelinessDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
