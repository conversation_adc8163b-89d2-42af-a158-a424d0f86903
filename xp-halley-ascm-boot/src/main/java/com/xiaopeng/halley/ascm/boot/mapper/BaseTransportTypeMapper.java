package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.entity.BaseTransportType;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * BaseTransportType数据仓库
 */
@SuppressWarnings("unused")
public interface BaseTransportTypeMapper extends BaseMapper<BaseTransportType> {

	default Set<String> getTransportTypeSet() {
		LambdaQueryWrapper<BaseTransportType> wrapper = new LambdaQueryWrapper<BaseTransportType>()
				.eq(BaseTransportType::getIsDelete, 0)
				.eq(BaseTransportType::getStatus, 0);
		return selectList(wrapper)
				.stream().map(BaseTransportType::getTransportType)
				.collect(Collectors.toSet());
	}
}