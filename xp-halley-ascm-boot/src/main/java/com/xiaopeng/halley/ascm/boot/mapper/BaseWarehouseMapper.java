package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import org.apache.ibatis.annotations.Mapper;

/**
 * BaseWarehouse数据仓库
 */
@SuppressWarnings("unused")
@Mapper
public interface BaseWarehouseMapper extends BaseMapper<BaseWarehouse> {

	Integer selectWarehouseTypeByWaybill(String waybillCode);

	default BaseWarehouse selectByLgort(String lgort) {
		return this.selectOne(new LambdaQueryWrapper<BaseWarehouse>()
				.eq(BaseWarehouse::getLgort, lgort)
				.eq(BaseWarehouse::getIsDelete, 0)
		);
	}
}
