package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseWaybillRulesDTO;
import com.xiaopeng.halley.ascm.boot.dto.BaseWaybillRulesRequestDTO;
import com.xiaopeng.halley.ascm.boot.entity.BaseWaybillRules;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BaseWaybillRules数据仓库
 */
@SuppressWarnings("unused")
public interface BaseWaybillRulesMapper extends BaseMapper<BaseWaybillRules> {

    long getPageTotal(@Param("param") BaseWaybillRulesRequestDTO param);

    List<BaseWaybillRulesDTO> getPage(@Param("param") BaseWaybillRulesRequestDTO param, @Param("startIndex") long startIndex, @Param("size") long size);

    void deleteAllNotSpecially();

    void insertBatch(List<BaseWaybillRules> list);

    void deleteByIds(List<Long> list);

    /**
     * 查询出城市和城市对应的运输规则的id
     *
     * @param dto
     * @return
     */
    List<String> selectRules(BaseTransportTimelinessDto dto);
}
