package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BatteryWayBillDto;
import com.xiaopeng.halley.ascm.boot.dto.BatteryWayBillVo;
import com.xiaopeng.halley.ascm.boot.entity.BatteryWayBill;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 14:56
 */
public interface BatteryWayBillMapper extends BaseMapper<BatteryWayBill> {

    /**
     * 获取总条数
     *
     * @param param
     * @return
     */
    long getPageTotal(@Param("param") BatteryWayBillDto param);

    /**
     * 获取明细
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BatteryWayBillVo> getPage(@Param("param") BatteryWayBillDto param, @Param("startIndex") long startIndex, @Param("size") long size);
}
