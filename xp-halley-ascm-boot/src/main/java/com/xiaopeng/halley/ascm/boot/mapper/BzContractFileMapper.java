package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BzContractQuery;
import com.xiaopeng.halley.ascm.boot.entity.BzContractFile;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

public interface BzContractFileMapper extends BaseMapper<BzContractFile> {

	List<Map<String, Object>> selectByTime(Page<BzContractFile> page, BzContractQuery param);

}