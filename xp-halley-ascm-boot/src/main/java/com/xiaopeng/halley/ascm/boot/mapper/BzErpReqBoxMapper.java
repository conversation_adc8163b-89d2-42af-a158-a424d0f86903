package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqBox;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BoxVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * BzErpReqBox数据仓库
 */
@SuppressWarnings("unused")
@Mapper
public interface BzErpReqBoxMapper extends BaseMapper<BzErpReqBox> {
    List<BoxVO> getListByWayBillCode(@Param("billCode") String billCode);

    List<Map> getBoxInfoByWaybillId(Long waybillId, String waybillCode, String status, String boxCode);

    List<Map> printBoxMsg(List<String> boxCodeList);

    IPage<BzErpReqBoxRelDto> getBoxesByWaybillCodePagePDA(@Param("page") Page<BzErpReqBoxRelDto> page, @Param("waybillCode") String waybillCode, @Param("type") int type);
}
