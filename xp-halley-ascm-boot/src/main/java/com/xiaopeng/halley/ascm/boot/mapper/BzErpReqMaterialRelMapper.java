package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationVO;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqMaterialRelDto;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqBox;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * BzErpReqMaterialRel数据仓库
 */
@SuppressWarnings("unused")
public interface BzErpReqMaterialRelMapper extends BaseMapper<BzErpReqMaterialRel> {
    List<BzErpReqMaterialRelDto> findAllByBoxCode(@Param("boxCode") String boxCode);

    Integer countBoxPackageCount(@Param("boxCodeList") List<String> boxCodeList);

    List<String> selectWaybillByDeliveryCode(@Param("deliveryOrderCode") Object[] deliveryOrderCode);

    List<Map> printMaterialRelMsg(String boxCode);

    /**
     * 运单箱号信息分页查询
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BzWaybillBoxInformationVO> deliveryOrderPage(@Param("param") BzWaybillBoxInformationDto param, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 运单箱号信息分页查询查询总数
     *
     * @param param
     * @return
     */
    long getdeliveryOrderPageTotal(@Param("param") BzWaybillBoxInformationDto param);

    /**
     * 聚合物料发送给JFK
     *
     * @param boxItem
     * @return
     */
    List<BzErpReqMaterialRel> selectMaterialList(BzErpReqBox boxItem);
}
