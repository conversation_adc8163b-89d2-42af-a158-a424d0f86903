package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.BzSelfWaybillCreateVO;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import org.apache.ibatis.annotations.Param;

public interface BzSelfWaybillMapper extends BaseMapper<BzSelfWaybill> {
	Page<BzSelfWaybillCreateVO> pageCreated(Page<BzSelfWaybill> page, @Param("ew") Wrapper<BzSelfWaybill> wrapper);
}