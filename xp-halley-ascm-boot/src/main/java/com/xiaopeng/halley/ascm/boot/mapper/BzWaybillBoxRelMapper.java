package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillBoxRel;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;

/**
 * BaseWaybillRules数据仓库
 */
@SuppressWarnings("unused")
public interface BzWaybillBoxRelMapper extends BaseMapper<BzWaybillBoxRel> {

    int insertBatch(ArrayList<BzWaybillBoxRel> insertList);

    IPage<BzErpReqBoxRelDto> getBoxesByWaybillCodePage(@Param("page") Page<BzErpReqBoxRelDto> page, @Param("waybillCode") String waybillCode, @Param("type") int type);

    IPage<BzErpReqBoxRelDto> getBoxes(@Param("page") Page<BzErpReqBoxRelDto> pageParameter, @Param("waybillCode") String param);
}
