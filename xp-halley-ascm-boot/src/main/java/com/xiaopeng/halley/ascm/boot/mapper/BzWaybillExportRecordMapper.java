package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillExportRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BzWaybillExportRecord数据仓库
 */
@SuppressWarnings("unused")
public interface BzWaybillExportRecordMapper extends BaseMapper<BzWaybillExportRecord> {

    List<BzWaybillExportRecord> findAll();

    int updateStatus(@Param("id") Long id, @Param("fileId") String fileId);

    int insertWaybill(BzWaybillExportRecord bzWaybillExportRecord);
}