package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import com.xiaopeng.halley.ascm.boot.dto.waybill.*;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.StatusVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.WayBillShopVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * BzWaybill数据仓库
 */
@SuppressWarnings("unused")
public interface BzWaybillMapper extends BaseMapper<BzWaybill> {

    @InterceptorIgnore(tenantLine = "true")
    List<AsmWayBillReq> queryStoreWaybill(List<String> waybillCodes);

    @InterceptorIgnore(tenantLine = "true")
    List<AsmWaybillFreshReq> queryFreshWaybill(List<String> waybillCodes);

    List<BzWaybillDetailDto> calVolumeWeight(List<String> waybillCodes);

    BzWaybillDetailDto getDetailById(@Param("waybillCode") String waybillCode, @Param("lgort") String lgort, @Param("carrierCode") String carrierCode, @Param("waybillCarrierCode") String waybillCarrierCode);

    List<String> getAscmCodesById(Long id);

    List<WaybillExportDto> export(WaybillExportSearchDto exportDto);

    List<BzWaybillListDto> getPDAWaybillList(@Param("paramData") PDAWaybillListPageRequestDto pdaWaybillListPageRequestDto, @Param("startIndex") long startIndex,
                                             @Param("pageSize") long pageSize);

    /**
     * 获取总条数
     */
    int getPADWaybillPageTotal(@Param("paramData") PDAWaybillListPageRequestDto pdaWaybillListPageRequestDto);

    List<Map<String, String>> statusNum(@Param("lgort") String lgort, @Param("carrierCode") String carrierCode);

    List<BzErpReqBoxRelDto> getBoxAllByCode(@Param("boxCode") String boxCode, @Param("lgort") String lgort, @Param("carrierCode") String carrierCode);

    WaybillStatusDto getWaybillStatus(String boxCode);

    WaybillStatusDto getWaybillStatusByNo(String waybillCode);

    Map<String, Integer> verifyStatus(@Param("waybillCode") String waybillCode, @Param("boxCode") String boxCode, @Param("lgort") String lgort, @Param("carrierCode") String carrierCode);

    int getCountByCode(String waybillCode);

    int getCodeByTransportType(String transportType);

    int updateByWaybillCode(BzWaybill bzWaybill);

    StatusVO findStateNum(@Param("driverPhone") String driverPhone);

    String findWaybill(@Param("waybill") String waybill);

    int checkWaybillCode(String waybillCode);

    List<WayBillShopVO> getWayBillListByCode(@Param("billCode") String... billCode);

    @MapKey("status")
    Map getAllWaybillStatusCount(String lgort, String shopCode, String carrierCode);

    Map printBzWaybillMsg(String waybillCode);

    IPage<BoxParamDto> boxesByWaybill(IPage<BoxParamDto> page, @Param("vo") BoxParamVo vo, @Param("isDelete") Integer isDelete);

    IPage<BzWaybillListDto> getMiniProgramWaybillList(IPage page, @Param("waybillCodes") Set<String> waybillCodes);

    String getDeliveryOrderCode(String wayBillCode);

    List<BzWaybillListDto> getMiniProgramWaybills(@Param("paramData") PDAWaybillListPageRequestDto param, @Param("startIndex") long startIndex, @Param("pageSize") long size, @Param("mobile") String mobile);

    int getMiniProgramWaybillPageTotal(@Param("paramData") PDAWaybillListPageRequestDto param, @Param("mobile") String mobile);

    /**
     * 订单总览分页查询
     *
     * @param page
     * @return
     */
    List<BzWayBillPageVo> getConditionPage(@Param("param") BzWayBillPageDto page, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 把delivery_order_code结果集封装
     *
     * @param waybillCode
     * @return
     */
    List<Map<String, Object>> getDeliveryOrderCodeList(String waybillCode);

    /**
     * 分仓调拨分页查询
     *
     * @param page
     * @return
     */
    List<WarehouseAllocateVo> warehouseAllocate(PageQuery<BzWayBillPageDto> page);

    /**
     * 运单总览分页查询的总数
     *
     * @param page
     * @return
     */
    long getPageTotal(@Param("param") BzWayBillPageDto page);

    /**
     * 分仓调拨分页查询的总数
     *
     * @param param
     * @return
     */
    long getWarehouseAllocatePageTotal(@Param("param") BzWayBillPageDto param);

    /**
     * 获取分仓调拨明细页面总数量
     *
     * @param param
     * @return
     */
//    @InterceptorIgnore(tenantLine = "true")
    long getMaterialDetailPageTotal(@Param("param") BzWayBillPageDto param);

    /**
     * 分仓调拨明细页面分页详情
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
//    @InterceptorIgnore(tenantLine = "true")
    List<BzWaybillMaterialDetailPageVo> getMaterialDetailPage(@Param("param") BzWayBillPageDto param, @Param("startIndex") long startIndex, @Param("size") long size);

    Page<BzWaybillMaterialDetailPageVo> getMaterialDetailPageV2(@Param("param") BzWayBillPageDto param, Page<?> page);

    /**
     * 运单总览查询交货单信息
     *
     * @param waybillCodes 运单编号
     * @return
     */
    List<Map<String, Object>> getDeliveryOrderCodes(List<String> waybillCodes);

    List<BzWayBillNewPageVo> getDeliveryOrderCodeMap(List<String> waybillCodes);

    /**
     * 同步运单状态
     *
     * @param waybillCode
     * @return
     */
    SyncWaybillInfoDto syncWaybillTransportInfo(String waybillCode);

    /**
     * 运单总览分页查询条目（新）
     *
     * @param param
     * @param startIndex
     * @param size
     * @return
     */
    List<BzWayBillNewPageVo> getNewPageItem(@Param("param") BzWayBillPageDto param, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 运单总览分页查询总量（新）
     *
     * @param param
     * @return
     */
    long getNewPageTotal(@Param("param") BzWayBillPageDto param);

    /**
     * 匹配运输
     *
     * @param ids
     * @return
     */
    List<Map> matchTransport(List<Long> ids);

    /**
     * 合单列表
     *
     * @param bzWaybill
     * @param startIndex
     * @param size
     * @return
     */
    List<Map<String, Object>> waybillJoinList(@Param("bzWaybill") BzWaybill bzWaybill, @Param("startIndex") long startIndex, @Param("size") long size);

    /**
     * 合单列表查询总量
     *
     * @param bzWaybill
     * @return
     */
    long waybillJoinListTotal(@Param("bzWaybill") BzWaybill bzWaybill);

    /**
     * 同步时间数据erp
     *
     * @param waybillCode
     * @return
     */
    WaybillInfoSyncErpVO waybillTimeInfoSyncErp(@Param("waybillCode") String waybillCode);

    /**
     * 查询需要同步的运单数据
     *
     * @param type
     * @return
     */
    List<BzWaybill> selectSyncData(int type, List<String> waybillCodeList);
}
