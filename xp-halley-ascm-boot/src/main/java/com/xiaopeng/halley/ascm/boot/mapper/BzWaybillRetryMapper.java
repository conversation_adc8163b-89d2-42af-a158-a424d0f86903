package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryVo;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillRetry;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.mapper
 * @Date 2024/4/23 17:26
 */
public interface BzWaybillRetryMapper extends BaseMapper<BzWaybillRetry> {

    long getPageTotal(@Param("param") BzWaybillRetryDto param);

    List<BzWaybillRetryVo> getPage(@Param("param") BzWaybillRetryDto param, @Param("startIndex") long startIndex, @Param("size") long size);

}
