package com.xiaopeng.halley.ascm.boot.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.xiaopeng.halley.ascm.boot.dto.AddressInfo;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayImageVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BzWaybillTrackHistory数据仓库
 */
@SuppressWarnings("unused")
public interface BzWaybillTrackHistoryMapper extends BaseMapper<BzWaybillTrackHistory> {

    default boolean updateLatestTrackHistory(String waybillCode) {
	    return new LambdaUpdateChainWrapper<>(this)
                .eq(BzWaybillTrackHistory::getWaybillCode, waybillCode)
                .eq(BzWaybillTrackHistory::getIsDelete, 0)
                .orderByDesc(BzWaybillTrackHistory::getUpdateTime)
                .last("limit 1")
                .set(BzWaybillTrackHistory::getTrackStatus, 3)
                .update();
    }

    List<AddressInfo> getAddressInfo(List<String> waybillCodes);

    List<BzWayBillTrackHistoryVO> getWaybillTransportationList(List<String> waybillCodes);

    List<Map<String, Object>> getMapDataByWaybillCode(String waybillCode);

    List<BzWayBillTrackHistoryVO> getWaybillTransportationDTO(String waybillCode);

    int insertList(@Param("list") ArrayList<BzWaybillTrackHistory> list);

    List<BzWayImageVO> getWaybillImage(String waybillCode);

    /**
     * 运单总览补充地址信息
     *
     * @param waybillCodes 运动单编号
     * @return
     */
    List<BzWaybillTrackHistory> getAddress(List<String> waybillCodes);

}