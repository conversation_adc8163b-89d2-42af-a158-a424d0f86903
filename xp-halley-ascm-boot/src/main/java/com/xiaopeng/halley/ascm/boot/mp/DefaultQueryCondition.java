package com.xiaopeng.halley.ascm.boot.mp;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 默认条件处理器
 */
@NoArgsConstructor
@AllArgsConstructor
public class DefaultQueryCondition implements QueryCondition {
	private String tableAlias;

	@Override
	public String getColumnName(QueryField queryField, String fieldName) {
		String tableAlias = this.tableAlias == null ? "" : this.tableAlias + ".";
		return tableAlias + StrUtil.blankToDefault(queryField.column(), StrUtil.toUnderlineCase(fieldName));
	}
}