package com.xiaopeng.halley.ascm.boot.mp;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface QueryField {

	/**
	 * 查询类型
	 */
	QueryType value() default QueryType.EQUAL;

	/**
	 * 列名称,默认为字段名称转下划线
	 */
	String column() default "";
}
