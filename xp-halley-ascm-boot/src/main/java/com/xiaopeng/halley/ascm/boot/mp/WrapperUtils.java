package com.xiaopeng.halley.ascm.boot.mp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;

@Slf4j
@UtilityClass
public class WrapperUtils {

	public <T> QueryWrapper<T> build(Object query, QueryCondition queryCondition) {
		QueryWrapper<T> wrapper = new QueryWrapper<>();
		for (Field field : ReflectUtil.getFields(query.getClass())) {
			QueryField queryField = field.getAnnotation(QueryField.class);
			if (queryField == null) {
				continue;
			}

			QueryType queryType = queryField.value();
			Object fieldValue = ReflectUtil.getFieldValue(query, field);
			// 获取处理的列名
			String column = queryCondition.getColumnName(queryField, field.getName());

			// 若字符串是空的或者集合数量为0，不添加查询条件
			if (StrUtil.isBlankIfStr(fieldValue)) {
				continue;
			}
			if (fieldValue instanceof Collection<?> && CollUtil.isEmpty((Collection<?>) fieldValue)) {
				continue;
			}
			addQueryCondition(queryType, wrapper, column, fieldValue);
		}
		return wrapper;
	}

	public <T> QueryWrapper<T> build(Object query) {
		return build(query, new DefaultQueryCondition());
	}

	public <T> LambdaQueryWrapper<T> buildLamda(Object query, QueryCondition queryCondition) {
		QueryWrapper<T> wrapper = build(query, queryCondition);
		return wrapper.lambda();
	}

	public <T> LambdaQueryWrapper<T> buildLamda(Object query) {
		QueryWrapper<T> wrapper = build(query);
		return wrapper.lambda();
	}

	private <T> void addQueryCondition(QueryType queryType, QueryWrapper<T> wrapper, String column, Object fieldValue) {
		switch (queryType) {
			case EQUAL:
				wrapper.eq(column, fieldValue);
				break;
			case RIGHT_LIKE:
				wrapper.likeRight(column, fieldValue);
				break;
			case LEFT_LIKE:
				wrapper.likeLeft(column, fieldValue);
				break;
			case LIKE:
				wrapper.like(column, fieldValue);
				break;
			case LIST:
				handleList(column, fieldValue, wrapper, (e, item) -> e.eq(column, item));
				break;
			case LIST_RIGHT_LIKE:
				handleList(column, fieldValue, wrapper, (e, item) -> e.likeRight(column, item));
				break;
			case LIST_LEFT_LIKE:
				handleList(column, fieldValue, wrapper, (e, item) -> e.likeLeft(column, item));
				break;
			case DATE_START:
				wrapper.ge(column, fieldValue);
				break;
			case DATE_END:
				wrapper.le(column, fieldValue);
				break;
		}
	}

	private <T> void handleList(String column, Object fieldValue, QueryWrapper<T> wrapper, BiConsumer<QueryWrapper<T>, Object> consumer) {
		try {
			List<?> list = (List<?>) fieldValue;
			if (list.size() == 1) {
				consumer.accept(wrapper, list.get(0));
			} else {
				wrapper.in(column, list);
			}
		} catch (ClassCastException e) {
			log.error("@Query标注的字段{}, 类型必须为List", column);
		}
	}
}
