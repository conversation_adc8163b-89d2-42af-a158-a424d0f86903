package com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant;

public enum WarehouseStatusEnum {
    START(0, "启用"), STOP(1, "停用");

    final Integer value;
    final String des;

    WarehouseStatusEnum(Integer value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(Integer value) {
        WarehouseStatusEnum[] warehouseStatusEnums = WarehouseStatusEnum.values();
        for (WarehouseStatusEnum item : warehouseStatusEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "状态未知";
    }

    public Integer getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}
