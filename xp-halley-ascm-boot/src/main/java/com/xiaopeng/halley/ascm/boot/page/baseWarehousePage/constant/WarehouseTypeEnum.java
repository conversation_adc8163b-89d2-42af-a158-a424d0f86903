package com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant
 * @Date 2024/4/22 14:05
 */
@Getter
public enum WarehouseTypeEnum {
    NONE(0, "-"),
    WMS(1, "WMS"),
    JF<PERSON>(2, "捷富凯"),
    JD(3, "京东"),
    XL(4, "骁龙"),
    XL_WMS(5, "骁龙自营")
    ;

    final Integer value;
    final String des;

    WarehouseTypeEnum(Integer value, String des) {
        this.value = value;
        this.des = des;
    }

    public static WarehouseTypeEnum of(Integer value) {
        WarehouseTypeEnum[] warehouseTypeEnums = WarehouseTypeEnum.values();
        for (WarehouseTypeEnum item : warehouseTypeEnums) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return NONE;
    }

    public static String getDescriptor(Integer value) {
        WarehouseTypeEnum[] warehouseTypeEnums = WarehouseTypeEnum.values();
        for (WarehouseTypeEnum item : warehouseTypeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "类型未知";
    }

    public static boolean isWms(Integer value) {
        return WMS.getValue().equals(value) || XL_WMS.getValue().equals(value);
    }
}
