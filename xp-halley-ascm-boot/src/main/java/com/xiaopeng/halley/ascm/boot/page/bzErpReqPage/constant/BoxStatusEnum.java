package com.xiaopeng.halley.ascm.boot.page.bzErpReqPage.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @Date 2022/7/11 5:43 PM
 */
public enum BoxStatusEnum {
    NOT_SCANNED(0, "未扫描"), SCANNED(1, "已扫描");

    final Integer value;
    final String des;

    BoxStatusEnum(Integer value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(Integer value) {
        BoxStatusEnum[] typeEnums = BoxStatusEnum.values();
        for (BoxStatusEnum item : typeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "状态未知";
    }

    public static List<Map> getAll() {
        BoxStatusEnum[] typeEnums = BoxStatusEnum.values();
        List<Map> statusList = new ArrayList<>();
        for (BoxStatusEnum item : typeEnums) {
            Map map = new HashMap<String, String>();
            map.put("value", item.value);
            map.put("des", item.des);
            statusList.add(map);
        }
        return statusList;
    }

    public Integer getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}