package com.xiaopeng.halley.ascm.boot.page.bzErpReqPage.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @Date 2022/7/11 5:43 PM
 */
public enum PackageTypeEnum {
    STAND_ALONE("P001", "独立包装"), MIXED("PACK01", "混合包装");

    final String value;
    final String des;

    PackageTypeEnum(String value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(String value) {
        PackageTypeEnum[] typeEnums = PackageTypeEnum.values();
        for (PackageTypeEnum item : typeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "混合包装";
    }

    public static List<Map> getAll() {
        PackageTypeEnum[] typeEnums = PackageTypeEnum.values();
        List<Map> statusList = new ArrayList<>();
        for (PackageTypeEnum item : typeEnums) {
            Map map = new HashMap<String, String>();
            map.put("value", item.value);
            map.put("des", item.des);
            statusList.add(map);
        }
        return statusList;
    }

    public String getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}