package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 箱号包装类型
 *
 * <AUTHOR>
 * @Date 2022/10/9 3:29 PM
 */
public enum BoxPackageTypeEnum {

    INDIVIDUAL_PACKAGE("0", "单独包装"), MIX_PACKAGE("1", "混合包装"), VIRTUAL_PACKAGE("2", "虚拟包装");

    final String value;
    final String des;

    BoxPackageTypeEnum(String value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(String value) {
        BoxPackageTypeEnum[] typeEnums = BoxPackageTypeEnum.values();
        for (BoxPackageTypeEnum item : typeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "包装未知";
    }

    public static List<Map> getAll() {
        BoxPackageTypeEnum[] typeEnums = BoxPackageTypeEnum.values();
        List<Map> statusList = new ArrayList<>();
        for (BoxPackageTypeEnum item : typeEnums) {
            Map map = new HashMap<String, String>();
            map.put("value", item.value);
            map.put("des", item.des);
            statusList.add(map);
        }
        return statusList;
    }

    public String getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}
