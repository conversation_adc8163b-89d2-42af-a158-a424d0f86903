package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单类型枚举
 *
 * <AUTHOR>
 * @Date 2022/7/11 5:43 PM
 */
public enum OrderTypeEnum {
    NORMAL("常规", "常规"), URGENT("紧急", "紧急")
//    ,MOST_URGENT("火急","火急")
    ;

    final String value;
    final String des;

    OrderTypeEnum(String value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(String value) {
        OrderTypeEnum[] typeEnums = OrderTypeEnum.values();
        for (OrderTypeEnum item : typeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "状态未知";
    }

    public static List<Map> getAll() {
        OrderTypeEnum[] typeEnums = OrderTypeEnum.values();
        List<Map> statusList = new ArrayList<>();
        for (OrderTypeEnum item : typeEnums) {
            Map map = new HashMap<String, String>();
            map.put("value", item.value);
            map.put("des", item.des);
            statusList.add(map);
        }
        return statusList;
    }

    public String getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}