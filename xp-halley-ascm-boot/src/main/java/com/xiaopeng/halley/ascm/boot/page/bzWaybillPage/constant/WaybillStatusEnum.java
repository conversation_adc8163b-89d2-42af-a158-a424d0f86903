package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运单状态枚举
 *
 * <AUTHOR>
 * @Date 2022/7/11 4:46 PM
 */
public enum WaybillStatusEnum {
    UNPUBLISHED("待发布", "待发布"), PUBLISHED("已发布", "已发布"), NOT_PICK_UP("待提货", "待提货"), WAIT_TRANSIT("待运输", "待运输"), IN_TRANSIT("运输中", "运输中"), COMPLETE("已完成", "已完成"), IN_THE_ABNORMAL("异常中", "异常中"), CANCEL("已取消", "已取消");

    final String value;
    final String des;

    WaybillStatusEnum(String value, String des) {
        this.value = value;
        this.des = des;
    }

    public static String getDescriptor(String value) {
        WaybillStatusEnum[] typeEnums = WaybillStatusEnum.values();
        for (WaybillStatusEnum item : typeEnums) {
            if (item.getValue().equals(value)) {
                return item.getDes();
            }
        }
        return "状态未知";
    }

    public static List<Map> getAll() {
        WaybillStatusEnum[] typeEnums = WaybillStatusEnum.values();
        List<Map> statusList = new ArrayList<>();
        for (WaybillStatusEnum item : typeEnums) {
            Map map = new HashMap<String, String>();
            map.put("value", item.value);
            map.put("des", item.des);
            statusList.add(map);
        }
        return statusList;
    }

    public String getValue() {
        return value;
    }

    public String getDes() {
        return des;
    }
}