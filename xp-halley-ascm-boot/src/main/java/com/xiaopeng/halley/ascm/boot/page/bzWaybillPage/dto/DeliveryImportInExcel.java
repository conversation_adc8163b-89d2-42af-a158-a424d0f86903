package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
final public class DeliveryImportInExcel extends Model<DeliveryImportInExcel> {

    @ExcelProperty(value = "运单编号")
    private String waybillCode;//运单编号

    @ExcelProperty(value = "寄件人姓名")
    private String deliveryName;//司机姓名

    @ExcelProperty(value = "寄件人电话")
    private String deliveryPhone;//联系电话

    @ExcelProperty(value = "物流单号")
    private String logisticsCode;//物流单号

    @ExcelProperty(value = "物流公司")
    private String logisticsCompany;//物流公司

    @ExcelProperty(value = "物流编码")
    private String deliveryCompanyCode;//物流公司

    @ExcelProperty(value = "备注")
    private String remark;
}