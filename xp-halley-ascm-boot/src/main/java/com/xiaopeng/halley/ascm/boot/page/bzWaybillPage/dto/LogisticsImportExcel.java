package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xiaopeng.halley.ascm.boot.excel.ExcelObject;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@ExcelIgnoreUnannotated
@EqualsAndHashCode(callSuper = true)
public class LogisticsImportExcel extends ExcelObject {

	@NotBlank(message = "运单编号不能为空")
	@ExcelProperty(value = "运单编号")
	private String waybillCode;

	@ExcelProperty(value = "寄件人姓名")
	private String deliveryName;

	@NotBlank(message = "寄件人电话不能为空")
	@ExcelProperty(value = "寄件人电话")
	private String deliveryPhone;

	@NotBlank(message = "物流单号不能为空")
	@ExcelProperty(value = "物流单号")
	private String logisticsCode;

	@NotBlank(message = "物流公司不能为空")
	@ExcelProperty(value = "物流公司")
	private String logisticsCompany;

	@NotBlank(message = "物流公司编码不能为空")
	@ExcelProperty(value = "物流公司编码")
	private String logisticsCompanyCode;
}