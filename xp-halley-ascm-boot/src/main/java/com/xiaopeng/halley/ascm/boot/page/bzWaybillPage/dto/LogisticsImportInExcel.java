package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
@HeadFontStyle(bold = BooleanEnum.FALSE, fontHeightInPoints = 10)
final public class LogisticsImportInExcel extends Model<LogisticsImportInExcel> {

    @ExcelProperty(value = "运单编号")
    private String waybillCode;//运单编号

    @ExcelProperty(value = "司机姓名")
    private String driverName;//司机姓名

    @ExcelProperty(value = "联系电话")
    private String driverPhone;//联系电话

    @ExcelProperty(value = "城市")
    private String localInfo;//城市

    @ExcelProperty(value = "具体位置")
    private String address;//具体位置

    @ExcelProperty(value = "省份")
    private String province;//省份

    @ExcelProperty(value = "位置更新时间")
    private String localUpdateTime;//位置更新时间

    @ExcelProperty(value = "物流单号")
    private String logisticsCode;//物流单号

    @ExcelProperty(value = "物流公司")
    private String logisticsCompany;//物流公司

    @ExcelProperty(value = "备注")
    private String remark;
}