package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto.DeliveryImportInExcel;

import java.util.ArrayList;
import java.util.List;

public class DeliveryImportDataListener implements ReadListener<DeliveryImportInExcel> {
    /**
     * 缓存的数据
     */
    private final List<DeliveryImportInExcel> deliveryImportInExcelList = new ArrayList<>();

    @Override
    public void invoke(DeliveryImportInExcel logisticsImportInExcel, AnalysisContext analysisContext) {
        deliveryImportInExcelList.add(logisticsImportInExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<DeliveryImportInExcel> getList() {
        return this.deliveryImportInExcelList;
    }

    public void clear() {
        this.deliveryImportInExcelList.clear();
    }
}
