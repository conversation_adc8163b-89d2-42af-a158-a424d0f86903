package com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto.LogisticsImportInExcel;

import java.util.ArrayList;
import java.util.List;

public class LogisticsImportDataListener implements ReadListener<LogisticsImportInExcel> {
    /**
     * 缓存的数据
     */
    private final List<LogisticsImportInExcel> logisticsImportInExcels = new ArrayList<>();

    @Override
    public void invoke(LogisticsImportInExcel logisticsImportInExcel, AnalysisContext analysisContext) {
        logisticsImportInExcels.add(logisticsImportInExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public List<LogisticsImportInExcel> getList() {
        return this.logisticsImportInExcels;
    }

    public void clear() {
        this.logisticsImportInExcels.clear();
    }
}
