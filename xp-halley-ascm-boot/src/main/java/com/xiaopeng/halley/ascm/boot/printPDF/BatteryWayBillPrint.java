package com.xiaopeng.halley.ascm.boot.printPDF;

import cn.hutool.core.util.StrUtil;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import com.xiaopeng.halley.ascm.boot.entity.BatteryWayBill;
import org.springframework.core.io.DefaultResourceLoader;

import java.io.ByteArrayOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Author：huqizhi
 * @Date：2023/9/26 10:56
 */
public class BatteryWayBillPrint {

    public static void getBatteryWayBillPdf(ZipOutputStream zipOutputStream, BatteryWayBill batteryWayBill) throws Exception {
        //Step 1—Create a Document.
        Document document = new Document(PageSize.A4, 50, 50, 30, 20); //页面大小-左边距-右边距-上边距-下边距。
        //第二步，创建Writer实例
        String s = String.valueOf(System.currentTimeMillis());
        String fileName = s + ".pdf";
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        //创建中文字体
        BaseFont bfchinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        Font topfont = new Font(bfchinese, 10, Font.BOLD);
        //第三步，打开文档
        document.open();
        //创建一列的格子
        PdfPTable goodTable = new PdfPTable(20);
        goodTable.setWidthPercentage(100);

        //第一行
        {
            //设置logo
            Image image = Image.getInstance(new DefaultResourceLoader().getResource("classpath:image/logo.png").getURL());
            image.scalePercent(20);
            PdfPCell cell = new PdfPCell(image, false);
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            //设置边框颜色
            cell.setUseVariableBorders(true);
            cell.setBorderColorTop(BaseColor.WHITE);
            cell.setBorderColorLeft(BaseColor.WHITE);
            cell.setBorderColorRight(BaseColor.WHITE);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell();
            //格子横跨14个格子
            cell.setColspan(4);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            //设置边框颜色
            cell.setUseVariableBorders(true);
            cell.setBorderColorTop(BaseColor.WHITE);
            cell.setBorderColorLeft(BaseColor.WHITE);
            cell.setBorderColorRight(BaseColor.WHITE);

            goodTable.addCell(cell);
        }

        {
            PdfPCell cell = new PdfPCell(new Phrase("物流运输单", new Font(bfchinese, 20, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(6);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            //设置边框颜色
            cell.setUseVariableBorders(true);
            cell.setBorderColorTop(BaseColor.WHITE);
            cell.setBorderColorLeft(BaseColor.WHITE);
            cell.setBorderColorRight(BaseColor.WHITE);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell();
            //格子横跨14个格子
            cell.setColspan(7);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            //设置边框颜色
            cell.setUseVariableBorders(true);
            cell.setBorderColorTop(BaseColor.WHITE);
            cell.setBorderColorLeft(BaseColor.WHITE);
            cell.setBorderColorRight(BaseColor.WHITE);

            goodTable.addCell(cell);
        }
        /*{
            //设置图片
            Image image= Image.getInstance(new DefaultResourceLoader().getResource("classpath:image/QRCode.png").getURL());
            image.scaleAbsolute(3,3);

            PdfPCell cell = new PdfPCell(image,false);
            //格子横跨2个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_MIDDLE);

            cell.setPadding(5);

            //设置边框颜色
            cell.setUseVariableBorders(true);
            cell.setBorderColorTop(BaseColor.WHITE);
            cell.setBorderColorLeft(BaseColor.WHITE);
            cell.setBorderColorRight(BaseColor.WHITE);

            goodTable.addCell(cell);
        }*/

        //第二行
        {
            goodTable.addCell(getCell("", 13, 1, 20, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("运输单：", 2, 1, 20, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell(batteryWayBill.getWaybillCode(), 5, 1, 20, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
        }
        //第三行
        {
            goodTable.addCell(getCell("序号", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("类型", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("重量/台", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("长*宽*高", 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("序号", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("类型", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("数量", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("长*宽*高", 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));

        }
        //第四行
        {
            goodTable.addCell(getCell("01", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("电池总成", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("500KG", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("2300*1800*400", 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("1", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell(carTypeConversion(batteryWayBill.getCarType()), 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));

        }
        {
            goodTable.addCell(getCell("", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 1, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("", 2, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell(failoverConversion(batteryWayBill.getFaultClassification()), 5, 1, 15, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));

        }
        //第五行
        {
            goodTable.addCell(getCell("运输要求", 3, 1, 20, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
            goodTable.addCell(getCell("需要绑带将电池包箱体与车身固定牢固，需要带侧门运输", 17, 1, 20, Element.ALIGN_LEFT, Element.ALIGN_MIDDLE));
        }
        //第六行
        {
            PdfPCell cell = new PdfPCell(new Phrase("注意事项", new Font(bfchinese, 13, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(4);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("电池包必须做好捆绑固定，电池包上盖禁止踩压和捆绑，注意运输安全，否则后果自负", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(9);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(4);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("捆绑样板", new Font(bfchinese, 13, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(4);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            Image image = Image.getInstance(new DefaultResourceLoader().getResource("classpath:image/print.png").getURL());
            image.scalePercent(47);
            PdfPCell cell = new PdfPCell(image, false);
            //格子横跨3个格子
            cell.setColspan(9);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(4);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第七行
        {
            goodTable.addCell(getCell("运输信息", 20, 1, 20, Element.ALIGN_CENTER, Element.ALIGN_CENTER));
        }
        //第八行
        {
            PdfPCell cell = new PdfPCell(new Phrase("地址", new Font(bfchinese, 13, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(2);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("起运地", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getDispatchAddress(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(17);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("目的地", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getArrivalAddress(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(17);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第九行
        {
            PdfPCell cell = new PdfPCell(new Phrase("通讯", new Font(bfchinese, 13, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(2);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("发货联系人", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getLoadingContact(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(5);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("联系电话", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getLoadingPhone(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(9);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("收货联系人", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getUnloadingContact(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(5);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("联系电话", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getUnloadingPhone(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(9);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第九行
        {
            PdfPCell cell = new PdfPCell(new Phrase("日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("起运日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        // 起运日期-日期填写的地方
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(6);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("到达日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        // 到达日期-日期填写的地方
        {
            PdfPCell cell = new PdfPCell(new Phrase(" ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(7);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十行
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输方 ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(14);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("起运地 ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("目的地 ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十一行
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输公司", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(" ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(12);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("姓名", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(" ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("姓名", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(" ", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十二行
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输司机", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("姓名", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(4);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("电话", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(6);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十三行
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输牌号", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(12);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("管理方", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输方", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十四行
        {
            PdfPCell cell = new PdfPCell(new Phrase("证件号/工号", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(12);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("姓名", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("姓名", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十五行
        {
            PdfPCell cell = new PdfPCell(new Phrase("备注", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(12);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("日期", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(1);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(2);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十六行
        {
            PdfPCell cell = new PdfPCell(new Phrase("车辆vin码", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getVinCode(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(17);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十七行
        {
            PdfPCell cell = new PdfPCell(new Phrase("电池包编号", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase(batteryWayBill.getPackNum(), new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(17);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        //第十八行
        {
            PdfPCell cell = new PdfPCell(new Phrase("运输类型", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(3);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }
        {
            PdfPCell cell = new PdfPCell(new Phrase("", new Font(bfchinese, 10, Font.BOLD)));
            //格子横跨3个格子
            cell.setColspan(17);
            //格子高度35px
            cell.setMinimumHeight(35);
            //格子纵跨1个格子
            cell.setRowspan(1);
            //格子内容左右居中
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            //格子内容上下居中
            cell.setVerticalAlignment(Element.ALIGN_TOP);

            goodTable.addCell(cell);
        }

        document.add(goodTable);
        //Step 5—Close the Document.
        document.close();
        baos.flush();
        byte[] xmpMetadata = baos.toByteArray();

        zipOutputStream.putNextEntry(new ZipEntry(batteryWayBill.getWaybillCode() + ".pdf"));
        zipOutputStream.write(xmpMetadata);
        zipOutputStream.flush();
    }

    /**
     * pdf 车型字段转换
     *
     * @param carType
     * @return
     */
    private static String carTypeConversion(String carType) {
        if (StrUtil.isEmpty(carType)) {
            return "4.2专车";
        } else if (0 == Integer.parseInt(carType)) {
            return "4.2专车";
        } else if (1 == Integer.parseInt(carType)) {
            return "6.8专车";
        } else if (2 == Integer.parseInt(carType)) {
            return "7.2专车";
        } else if (3 == Integer.parseInt(carType)) {
            return "9.6专车";
        }
        return "4.2专车";
    }

    public static PdfPCell getCell(String content, int colspan, int rowspan, Integer minHeight, int horizontalAlignment, int verticalAlignment) throws Exception {
        BaseFont bfchinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        PdfPCell cell = new PdfPCell(new Phrase(content, new Font(bfchinese, 7, Font.BOLD)));
        cell.setColspan(colspan);
        cell.setMinimumHeight(minHeight);
        cell.setRowspan(rowspan);
        cell.setHorizontalAlignment(horizontalAlignment);
        cell.setVerticalAlignment(verticalAlignment);
        return cell;
    }

    public static String failoverConversion(Integer faultClassification) {
        if (faultClassification == 1) {
            return "非质量";
        } else {
            return "质量";
        }
    }
}
