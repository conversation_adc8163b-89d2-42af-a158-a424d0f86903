package com.xiaopeng.halley.ascm.boot.printPDF;

import cn.hutool.core.util.StrUtil;
import com.itextpdf.kernel.events.Event;
import com.itextpdf.kernel.events.IEventHandler;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import com.itextpdf.layout.Canvas;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.xiaopeng.halley.ascm.boot.utils.PdfHelper;

import java.io.IOException;

public class PageNumberEventHandler implements IEventHandler {
	private static final float FONT_SIZE = 10; // 字体大小
	private static final float MARGIN_BOTTOM = 20; // 页码距离页面底部的距离
	private final PdfFont pageNumberFont; // 用于页码的字体
	private final PdfFormXObject totalPagesPlaceholder; // 用于总页数的占位符对象

	public PageNumberEventHandler() throws IOException {
		// 加载并嵌入字体，以确保PDF中字体显示的一致性
		this.pageNumberFont = PdfFontFactory.createFont(PdfHelper.FONT_DATA, "");
		// 创建总页数占位符，预留足够空间以容纳总页数文本
		this.totalPagesPlaceholder = new PdfFormXObject(new Rectangle(0, 0, 30, 10));
	}

	/**
	 * 处理PDF文档事件，绘制当前页码及总页数占位符。
	 * 此方法会在每一页渲染时被调用。
	 *
	 * @param event PDF文档事件。
	 */
	@Override
	public void handleEvent(Event event) {
		PdfDocumentEvent docEvent = (PdfDocumentEvent) event;
		PdfDocument pdfDoc = docEvent.getDocument();
		PdfPage page = docEvent.getPage();
		int currentPageNum = pdfDoc.getPageNumber(page); // 获取当前页码

		Rectangle pageSize = page.getPageSize(); // 获取页面尺寸

		// 将总页数占位符封装成Image对象，以便在布局层使用
		Image totalPagesImage = new Image(totalPagesPlaceholder);

		// 构建页码段落：格式为 "页码 X / [总页数]"
		Paragraph pageNumParagraph = new Paragraph()
				.setFont(pageNumberFont) // 设置字体
				.setFontSize(FONT_SIZE) // 设置字体大小
				.add(new Text(StrUtil.format("页码 {} / ", currentPageNum))) // 添加当前页码文本
				.add(totalPagesImage); // 添加总页数占位符图片

		// 计算页码在页面底部居中的位置
		float posX = pageSize.getWidth() / 2;
		float posY = pageSize.getBottom() + MARGIN_BOTTOM;

		// 使用Canvas在指定位置绘制整个页码段落，以实现精确布局
		try (Canvas canvas = new Canvas(new PdfCanvas(page), pageSize)) {
			// 将段落水平居中对齐，并垂直底部（基线）对齐到指定位置
			canvas.showTextAligned(pageNumParagraph, posX, posY, TextAlignment.CENTER, VerticalAlignment.BOTTOM);
		}
	}

	/**
	 * 填充总页数占位符的实际值。
	 * 此方法应在所有页面写入完毕且文档即将关闭前调用。
	 *
	 * @param pdfDoc 用于获取总页数的PDF文档对象。
	 */
	public void writeTotalPages(PdfDocument pdfDoc) {
		// 使用PdfCanvas直接写入占位符XObject
		PdfCanvas canvas = new PdfCanvas(totalPagesPlaceholder, pdfDoc);
		canvas.beginText();
		// 确保写入占位符的字体和大小与handleEvent中使用的段落设置一致，保证视觉统一
		canvas.setFontAndSize(pageNumberFont, FONT_SIZE);
		canvas.showText(String.valueOf(pdfDoc.getNumberOfPages())); // 写入实际的总页数
		canvas.endText();
	}
}