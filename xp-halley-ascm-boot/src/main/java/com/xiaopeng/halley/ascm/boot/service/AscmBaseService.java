package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.dto.LoactionRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AscmBaseService {


    @Value("${address.gdKey}")
    private String locationKey;

    private final static String LOCATIONFORMAT = "%s,%s";

    private final static String URL = "https://restapi.amap.com/v3/geocode/regeo";

    public JSONObject geocodeLocation(LoactionRequestDTO reqDTO) {

        // 经纬度（用实际坐标替换）
        String location = String.format(LOCATIONFORMAT, reqDTO.getLongitude(), reqDTO.getLatitude());
        // 返回基本地址信息
        String extensions = "base";

        // 道路等级，0代表不返回，1代表返回
        String roadlevel = "0";

        // 构造参数
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("key", locationKey);
        paramMap.put("location", location);
        paramMap.put("extensions", extensions);
        paramMap.put("roadlevel", roadlevel);

        // 发送 GET 请求
        String result = HttpUtil.get(URL, paramMap);

        // 输出结果
        log.info("高德 API 返回结果: " + result);

        JSONObject returnJSON = JSONObject.parseObject(result);

        return returnJSON;
    }
}