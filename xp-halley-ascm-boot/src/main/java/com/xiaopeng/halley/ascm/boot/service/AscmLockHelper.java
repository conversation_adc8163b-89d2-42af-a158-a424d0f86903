package com.xiaopeng.halley.ascm.boot.service;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 加锁工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-13
 */
@Slf4j
@Service
public class AscmLockHelper {

    @Resource
    private RedissonClient redisson;

    @Value("spring.application.name")
    private String applicationName;

    public Boolean tryLock(String buildKey, long waitTime, long leaseTime, TimeUnit unit) throws Exception {
        RLock lock = redisson.getLock(buildKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (InterruptedException e) {
            log.error("Interrupted ", e);
            log.error("获取分布式锁失败");
            throw new Exception("锁获取失败，请重试");
        }
    }

    public void unlock(String buildKey) {
        RLock lock = redisson.getLock(buildKey);
        lock.unlock();
    }

    protected String comboKey(String originKey) {
        return this.comboKey(this.applicationName, originKey);
    }

    protected String comboKey(String prefix, String originKey) {
        return prefix + ":" + originKey;
    }

    public boolean tryLockComboKey(String lockKey) {
        lockKey = this.comboKey(lockKey);
        RLock lock = this.redisson.getLock(lockKey);
        return lock.tryLock();
    }

    public void unlockComboKey(String lockKey) {
        lockKey = this.comboKey(lockKey);
        RLock lock = this.redisson.getLock(lockKey);
        lock.unlock();
    }

}
