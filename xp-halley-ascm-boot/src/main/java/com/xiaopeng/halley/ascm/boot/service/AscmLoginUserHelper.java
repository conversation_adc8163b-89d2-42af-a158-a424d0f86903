package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.mqi.basic.security.common.login.LoginInfo;
import com.xiaopeng.mqi.basic.security.common.login.LoginUserInfo;
import com.xiaopeng.mqi.common.LoginUser;
import com.xiaopeng.mqi.helper.LoginUserHelper;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.sdk.mbp.web.service.MbpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 组织新增帮助类
 *
 * <AUTHOR> 自动生成
 * @date 2021-10-30 10:12:08
 */
@Slf4j
@Service
public class AscmLoginUserHelper {

    private static final String CARRIER = "carrier";
    private static final String MANAGER = "manager";
    private static final String FACTORY = "factory";
    private static final String AUTH = "auth";
    private static final String SLIP_FLAG_SLIP = "&";
    private static final String SLIP_FLAG_SAME = "=";

    @Resource
    private MbpService mbpService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;

    public BaseWarehouse getCurrentWarehouse() {
        String remark = mbpService.getLoginInfo().getOrganizationInfo().getCurrentOrganization().getRemark();
        if (!remark.contains("=")) {
            return null;
        }
        String lgort = remark.split("=")[1];
        return baseWarehouseMapper.selectByLgort(lgort);
    }

    /**
     * 这个地方获取用户
     * 那我这边需要重新写一个自己加创建用户名和更新用户名的东西
     *
     * @return
     */
    public AscmLoginUser getLoginUser() {
        log.info("LoginUserHelper getLoginUser 开始执行");

        LoginUser loginUser = LoginUserHelper.getLoginUser();

        // 判空
        if (ObjectUtil.isNull(loginUser)) {
            return null;
        }

        // 缓存
        try {
            AscmLoginUser cacheUser = ascmRedisHelper.get(RedisKeyManager.ASCM_USER_CACHE.buildKey(loginUser.getUsername()), AscmLoginUser.class);

            if (ObjectUtil.isNotNull(cacheUser)) {
                return cacheUser;
            } else {
                // 没有用户，获取缓存
                AscmLoginUser targetUser = getAscmLoginUser();
                if (ObjectUtil.isNotNull(targetUser)) {
                    ascmRedisHelper.set(RedisKeyManager.ASCM_USER_CACHE.buildKey(loginUser.getUsername()), targetUser, 5, TimeUnit.MINUTES);
                    return targetUser;
                } else {
                    return null;
                }
            }

        } catch (ResultException e) {
            log.error(e.getMessage(), e);
            return getAscmLoginUser();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

    }

    private AscmLoginUser getAscmLoginUser() {
        LoginInfo loginInfo = mbpService.getLoginInfo();

        log.info("LoginUserHelper loginInfo [{}]", JSONObject.toJSONString(loginInfo));

        if (ObjectUtil.isNull(loginInfo)) {
            // 报错
            // throw new ResultException(500,"当前请求没有查询到用户");
            return null;
        } else {

            LoginUserInfo loginUserInfo = loginInfo.getLoginUserInfo();

            log.info("LoginUserHelper loginUserInfo [{}]", JSONObject.toJSONString(loginUserInfo));

            // 如果是空，或者没有用户名（唯一ID），则过掉
            if (ObjectUtil.isNull(loginUserInfo) || StrUtil.isBlank(loginUserInfo.getUsername())) {
                return null;
            }

            AscmLoginUser ascmLoginUser = new AscmLoginUser();

            // 塞进去用户信息
            ascmLoginUser.setUserId(loginUserInfo.getUsername());
            ascmLoginUser.setUsername(loginUserInfo.getUsername());
            ascmLoginUser.setName(StrUtil.isBlank(loginUserInfo.getName()) ? "未知" : loginUserInfo.getName());
            ascmLoginUser.setPhone(StrUtil.isBlank(loginUserInfo.getPhone()) ? "未知" : loginUserInfo.getPhone());

            // 如果当前组织不为空，并且remark字段不为空，就开始解析
            if (ObjectUtil.isNotNull(loginInfo.getOrganizationInfo().getCurrentOrganization()) && StrUtil.isNotBlank(loginInfo.getOrganizationInfo().getCurrentOrganization().getRemark())) {

                String remark = loginInfo.getOrganizationInfo().getCurrentOrganization().getRemark();

                log.info("LoginUserHelper remark [{}]", remark);

                String[] keyValueString = remark.split(SLIP_FLAG_SLIP);

                Map<String, String> remarkMap = new HashMap<>();

                for (String keyValueItem : keyValueString) {
                    String[] resultKeyValue = keyValueItem.split(SLIP_FLAG_SAME);
                    if (ObjectUtil.isNotNull(resultKeyValue) && resultKeyValue.length == 2) {
                        remarkMap.put(resultKeyValue[0], resultKeyValue[1]);
                    }
                }

                if (StrUtil.isNotBlank(remarkMap.get(CARRIER))) {
                    ascmLoginUser.setCarrier(remarkMap.get(CARRIER));
                }

                if (StrUtil.isNotBlank(remarkMap.get(MANAGER))) {
                    ascmLoginUser.setManager(remarkMap.get(MANAGER));
                }

                if (StrUtil.isNotBlank(remarkMap.get(FACTORY))) {
                    ascmLoginUser.setFactory(remarkMap.get(FACTORY));
                }

                if (StrUtil.isNotBlank(remarkMap.get(AUTH))) {
                    ascmLoginUser.setAuth(remarkMap.get(AUTH));
                } else {
                    ascmLoginUser.setAuth("default");
                }

            } else {
                ascmLoginUser.setAuth("default");
                log.info("LoginUserHelper 用户备注为空");
            }

            log.info("LoginUserHelper returnUser [{}]", JSONObject.toJSONString(ascmLoginUser));

            return ascmLoginUser;
        }
    }

}

