package com.xiaopeng.halley.ascm.boot.service;

import com.alibaba.fastjson.JSON;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Redis辅助类
 *
 * <AUTHOR> 自动生成
 * @date 2021-10-30 10:12:08
 */
@Slf4j
@Service
public class AscmRedisHelper {

    @Value("spring.application.name")
    private String applicationName;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    protected String comboKey(String originKey) {
        return this.comboKey(this.applicationName, originKey);
    }

    protected String comboKey(String prefix, String originKey) {
        return prefix + ":" + originKey;
    }

    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(this.redisTemplate.hasKey(this.comboKey(key)));
    }

    public void set(String key, Object data, long timeout, TimeUnit unit) throws ResultException {
        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            operations.set(key, data, timeout, unit);
        } catch (Exception var8) {
            log.error(var8.getMessage(), var8);
        }
    }

    public void delete(String key) throws ResultException {
        try {
            key = this.comboKey(key);
            this.redisTemplate.delete(key);
        } catch (Exception var4) {
            log.error(var4.getMessage(), var4);
        }
    }

    public Long incr(String key) throws ResultException {
        Long incr = 0L;

        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            incr = operations.increment(key);
            // todo 这里先做一个加操作，防止和老平台的key冲突
            return incr + 1000;
        } catch (Exception var5) {
            log.error(var5.getMessage(), var5);
            throw new ResultException(500, "Redis incr缓存数据异常：【key：" + key + "】");
        }
    }

    public <T> List<T> getList(String key, Class<T> classz) throws ResultException {
        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            Object obj = operations.get(key);
            if (obj != null) {
                return JSON.parseArray(JSON.toJSONString(obj), classz);
            }
        } catch (Exception var6) {
            log.error(var6.getMessage(), var6);
            throw new ResultException(500, "Redis获取(getList)缓存数据（对象集合）异常：【key：" + key + "，classz：" + classz + "】");
        }

        return Collections.emptyList();
    }

    public void hset(String key, String hashKey, Object obj) throws ResultException {
        try {
            key = this.comboKey(key);
            HashOperations<String, String, Object> operations = this.redisTemplate.opsForHash();
            operations.put(key, hashKey, obj);
        } catch (Exception var6) {
            log.error(var6.getMessage(), var6);
            throw new ResultException(500, "Redis新增Hash缓存数据异常：【key：" + key + "，hashKey：" + hashKey + "】");
        }
    }

    public void hdel(String key, String hashKey) throws ResultException {
        try {
            key = this.comboKey(key);
            HashOperations<String, String, Object> operations = this.redisTemplate.opsForHash();
            operations.delete(key, hashKey);
        } catch (Exception var5) {
            log.error(var5.getMessage(), var5);
            throw new ResultException(500, "Redis删除Hash缓存数据异常：【key：" + key + "，hashKey：" + hashKey + "】");
        }
    }

    public <T> Map<String, T> hgetall(String key, Class<T> classz) {
        try {
            key = this.comboKey(key);
            HashOperations<String, String, T> operations = this.redisTemplate.opsForHash();
            return operations.entries(key);
        } catch (Exception var5) {
            log.error(var5.getMessage(), var5);
            // throw new ResultException(500,"Redis获取Hash缓存数据（Map对象）异常：【key：" + key + "，classz：" + classz + "】");
            return Collections.emptyMap();
        }
    }

    public Object get(String key) throws ResultException {
        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            return operations.get(key);
        } catch (Exception var4) {
            log.error(var4.getMessage(), var4);
            throw new ResultException(500, "Redis获取缓存数据（Object）异常：【key：" + key + "】");
        }
    }

    public void set(String key, Object data) throws ResultException {
        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            operations.set(key, data);
        } catch (Exception var5) {
            log.error(var5.getMessage(), var5);
            throw new ResultException(500, "Redis新增缓存数据异常：【key：" + key + "】");
        }
    }

    public <T> T get(String key, Class<T> classz) throws ResultException {
        try {
            key = this.comboKey(key);
            ValueOperations<String, Object> operations = this.redisTemplate.opsForValue();
            Object obj = operations.get(key);
            return JSON.parseObject(JSON.toJSONString(obj), classz);
        } catch (Exception var6) {
            log.error(var6.getMessage(), var6);
            throw new ResultException(500, "Redis获取(get)缓存数据（对象）异常：【key：" + key + "，classz：" + classz + "】");
        }
    }

}

