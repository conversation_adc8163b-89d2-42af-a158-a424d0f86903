package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseBatteryProject;
import com.xiaopeng.halley.ascm.boot.listener.BaseBatteryProjectListener;
import com.xiaopeng.halley.ascm.boot.mapper.BaseBatteryProjectMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Vector;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author：huqizhi
 * @Date：2023/9/13 11:03
 */
@Service
@Slf4j
public class BaseBatteryProjectService extends ServiceImpl<BaseBatteryProjectMapper, BaseBatteryProject> {

    @Value("${fileTemp.BaseBatteryProject.tempFileId}")
    private String tempFileId;
    @Resource
    private ImageService imageService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;

    /**
     * 分页查询
     *
     * @param page
     * @return
     */
    public Page<BaseBatteryProjectVo> getPage(PageQuery<BaseBatteryProjectDto> page) {
        log.info("BaseBatteryProjectService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseBatteryProjectVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        Page<BaseBatteryProjectVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 电池项目维护新增或更新
     *
     * @param dto
     * @return
     */
    public Result updateOrAdd(BaseBatteryProjectDto dto) {
        log.info("BaseBatteryProjectService updateOrAdd {}", JSON.toJSONString(dto));
        Integer updateOrAdd = dto.getUpdateOrAdd();
        //如果是0表明是更新
        if (updateOrAdd == 0 && null != dto.getId()) {
            BaseBatteryProject baseBatteryProject = BeanUtil.copyProperties(dto, BaseBatteryProject.class);
            int updateResult = this.baseMapper.updateById(baseBatteryProject);
            if (1 == updateResult) {
                return ResultUtil.success("更新成功！");
            }
        } else if (updateOrAdd == 1) {
            //如果时1表明是新增
            //新增需要先校验是否有重复的，表中有唯一属性pack_num和project联合
            BaseBatteryProject baseBatteryProject = BeanUtil.copyProperties(dto, BaseBatteryProject.class);
            LambdaQueryWrapper<BaseBatteryProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseBatteryProject::getPackNum, baseBatteryProject.getPackNum())
                    .eq(BaseBatteryProject::getProject, baseBatteryProject.getProject()).eq(BaseBatteryProject::getIsDelete, 0);
            BaseBatteryProject baseBatteryProjectDb = this.baseMapper.selectOne(queryWrapper);
            // 如果是空，表明是新增
            if (BeanUtil.isEmpty(baseBatteryProjectDb)) {
                int insertResult = this.baseMapper.insert(baseBatteryProject);
                if (1 == insertResult) {
                    return ResultUtil.success("新增成功！");
                }
            } else {
                //表明是更新
                baseBatteryProject.setId(baseBatteryProjectDb.getId());
                int updateResult = this.baseMapper.updateById(baseBatteryProject);
                if (1 == updateResult) {
                    return ResultUtil.success("更新成功！");
                }
            }

        }
        return ResultUtil.failed("参数有误！");
    }

    /**
     * 电池项目维护删除
     *
     * @param id 条目id
     * @return
     */
    public Result delete(String id) {
        log.info("BaseBatteryProjectService delete {}", id);
        BaseBatteryProject baseBatteryProject = new BaseBatteryProject();
        baseBatteryProject.setId(Long.parseLong(id));
        baseBatteryProject.setIsDelete(1);
        //做逻辑删除
        int deleteResult = this.baseMapper.updateById(baseBatteryProject);
        if (1 == deleteResult) {
            return ResultUtil.success("删除成功！");
        }
        return ResultUtil.failed("删除失败！ ");
    }

    /**
     * 模板下载
     *
     * @return 下载模板的链接
     */
    public ImageResponseDTO tempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(tempFileId);
        return returnVO;
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return
     */
    public ImportResponseDto importFile(MultipartFile file) {

        BaseBatteryProjectListener baseBatteryProjectListener = new BaseBatteryProjectListener();

        //存放读取的数据
        List<BaseBatteryProjectExportVo> importDTOList = new ArrayList<>();

        //成功的数据
        List<BaseBatteryProjectExportVo> successList = new Vector<>();

        //失败的数据
        List<BaseBatteryProjectExportVo> failList = new Vector<>();
        try {
            //获取读取到的内容
            EasyExcel.read(file.getInputStream(), BaseBatteryProjectExportVo.class, baseBatteryProjectListener).sheet().doRead();
            //获取读取到的内容
            importDTOList = baseBatteryProjectListener.getList();
            //检验
            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }

            //并行校验
            importDTOList.parallelStream().forEach(item -> {
                //PACK国标码非空校验
                if (null == item.getPackNum()) {
                    item.setFailedReason("必填字段不能为空");
                    failList.add(item);
                    return;
                }
                //项目字段非空校验
                if (null == item.getProject()) {
                    item.setFailedReason("必填字段不能为空");
                    failList.add(item);
                    return;
                }
                //校验通添加到成功的集合
                successList.add(item);
            });
            //构造响应体
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_BATTERY_PROJECT.buildKey("fail", uuid), failList, 16, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_BATTERY_PROJECT.buildKey("success", uuid), successList, 16, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;
        } catch (IOException | ResultException e) {
            throw new RuntimeException(e);
        } finally {
            baseBatteryProjectListener.clear();
        }
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    public Page<BaseBatteryProjectVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseBatteryProjectVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_PROJECT.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseBatteryProjectVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BaseBatteryProjectVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseBatteryProjectVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    public void downloadFailFile(PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        log.info("BaseBatteryProjectService downloadFailFile 开始导出 {}", dto.getParam().getOperationCode());
        String operationCode = dto.getParam().getOperationCode();
        //获取redis中存储的失败数据
        List<BaseBatteryProjectExportVo> failResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_PROJECT.buildKey("fail", operationCode), BaseBatteryProjectExportVo.class);
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("电池项目批量导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BaseBatteryProjectVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseBatteryProjectService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    public Result importData(String operationCode) throws ResultException {

        log.info("BaseBatteryProjectService importData 开始添加数据 {}", operationCode);
        List<BaseBatteryProjectVo> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_PROJECT.buildKey("success", operationCode), BaseBatteryProjectVo.class);

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        successResult.forEach(item -> {

            LambdaQueryWrapper<BaseBatteryProject> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseBatteryProject::getPackNum, item.getPackNum())
                    .eq(BaseBatteryProject::getProject, item.getProject()).eq(BaseBatteryProject::getIsDelete, 0);
            BaseBatteryProject baseBatteryProjectDb = this.baseMapper.selectOne(queryWrapper);

            if (BeanUtil.isEmpty(baseBatteryProjectDb)) {
                //是null表明这个是新增
                BaseBatteryProject baseBatteryProject = BeanUtil.copyProperties(item, BaseBatteryProject.class);
                int insertResult = this.baseMapper.insert(baseBatteryProject);
                addCount.addAndGet(insertResult);
            } else {
                //不是空表明是修改
                baseBatteryProjectDb.setPackTypeNum(item.getPackTypeNum());
                baseBatteryProjectDb.setPackNumNine(item.getPackNumNine());
                int updateResult = this.baseMapper.updateById(baseBatteryProjectDb);
                updateCount.addAndGet(updateResult);
            }
        });

        log.info("BaseBatteryProjectService importData 本次导入新增{}个 更新{}个", addCount.get(), updateCount.get());

        if (successResult.size() != (addCount.get() + updateCount.get())) {
            throw new RuntimeException("BaseBatteryProjectService importData 本次导入发生错误！");
        }

        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", addCount.get(), updateCount.get()));
    }
}
