package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseBatteryRouteCost;
import com.xiaopeng.halley.ascm.boot.entity.BaseRepairCenter;
import com.xiaopeng.halley.ascm.boot.listener.BaseBatteryRouteCostListener;
import com.xiaopeng.halley.ascm.boot.mapper.BaseBatteryRouteCostMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author：huqizhi
 * @Date：2023/9/15 10:55
 */
@Service
@Slf4j
public class BaseBatteryRouteCostService extends ServiceImpl<BaseBatteryRouteCostMapper, BaseBatteryRouteCost> {

    @Value("${fileTemp.BaseBatteryRouteCost.tempFileId}")
    private String tempFileId;
    @Resource
    private ImageService imageService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private BaseRepairCenterService baseRepairCenterService;

    /**
     * 分页查询
     *
     * @param page
     * @return
     */
    public Page<BaseBatteryRouteCostVo> getPage(PageQuery<BaseBatteryRouteCostDto> page) {
        log.info("BaseBatteryRouteCostService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseBatteryRouteCostVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        //处理页面展示问题
        returnList.forEach(item -> {
            if (0 == item.getCarType()) {
                item.setCarTypeChinese("4.2");
            } else if (1 == item.getCarType()) {
                item.setCarTypeChinese("6.8");
            } else if (2 == item.getCarType()) {
                item.setCarTypeChinese("7.2");
            } else if (3 == item.getCarType()) {
                item.setCarTypeChinese("9.6");
            } else {
                item.setCarTypeChinese("");
            }
        });

        Page<BaseBatteryRouteCostVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 新增或更新
     *
     * @param dto 新增或更新的实体
     * @return 新增或更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result updateOrAdd(BaseBatteryRouteCostDto dto) {
        log.info("BaseBatteryRouteCostService updateOrAdd {}", JSON.toJSONString(dto));
        Integer updateOrAdd = dto.getUpdateOrAdd();
        LambdaQueryWrapper<BaseRepairCenter> newQueryWrapper = new LambdaQueryWrapper<>();
        newQueryWrapper.eq(BaseRepairCenter::getRepairCenterNum, dto.getRepairCenterNum()).eq(BaseRepairCenter::getIsDelete, 0);
        BaseRepairCenter repairCenter = baseRepairCenterService.getOne(newQueryWrapper);
        if (BeanUtil.isEmpty(repairCenter)) {
            return ResultUtil.failed("维修中心不存在");
        }
        //如果是0表明是更新
        if (updateOrAdd == 0 && null != dto.getId()) {
            BaseBatteryRouteCost baseBatteryRouteCost = BeanUtil.copyProperties(dto, BaseBatteryRouteCost.class);
            int updateResult = this.baseMapper.updateById(baseBatteryRouteCost);
            if (1 == updateResult) {
                return ResultUtil.success("更新成功！");
            }
        } else if (updateOrAdd == 1) {
            //如果是1表明是新增
            BaseBatteryRouteCost baseBatteryRouteCost = BeanUtil.copyProperties(dto, BaseBatteryRouteCost.class);
            LambdaQueryWrapper<BaseBatteryRouteCost> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseBatteryRouteCost::getRepairCenterNum, dto.getRepairCenterNum())
                    .eq(BaseBatteryRouteCost::getDispatchCity, dto.getDispatchCity())
                    .eq(BaseBatteryRouteCost::getArrivalCity, dto.getArrivalCity())
                    .eq(BaseBatteryRouteCost::getLogisticsProvider, dto.getLogisticsProvider())
                    .eq(BaseBatteryRouteCost::getLogisticsType, dto.getLogisticsType())
                    .eq(BaseBatteryRouteCost::getCarType, dto.getCarType()).eq(BaseBatteryRouteCost::getIsDelete, 0);
            BaseBatteryRouteCost baseBatteryRouteCostDb = this.baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseBatteryRouteCostDb)) {
                // 如果是空，表明是新增
                baseBatteryRouteCost.setRepairCenterName(repairCenter.getRepairCenterName());
                int insertResult = this.baseMapper.insert(baseBatteryRouteCost);
                if (1 == insertResult) {
                    return ResultUtil.success("新增成功！");
                }
            } else {
                //表明这个是更新
                baseBatteryRouteCost.setId(baseBatteryRouteCostDb.getId());
                int updateResult = this.baseMapper.updateById(baseBatteryRouteCost);
                if (1 == updateResult) {
                    return ResultUtil.success("更新成功！");
                }
            }
        }
        return ResultUtil.failed("参数有误！");
    }

    /**
     * 删除条目
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result delete(String id) {
        log.info("BaseBatteryRouteCostService delete {}", id);
        BaseBatteryRouteCost baseBatteryRouteCost = new BaseBatteryRouteCost();
        baseBatteryRouteCost.setId(Long.parseLong(id));
        baseBatteryRouteCost.setIsDelete(1);
        //做逻辑删除
        int deleteResult = this.baseMapper.updateById(baseBatteryRouteCost);
        if (1 == deleteResult) {
            return ResultUtil.success("删除成功！");
        }
        return ResultUtil.failed("删除失败！ ");
    }

    public ImageResponseDTO tempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(tempFileId);
        return returnVO;
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return
     */
    public ImportResponseDto importFile(MultipartFile file) {

        BaseBatteryRouteCostListener baseBatteryRouteCostListener = new BaseBatteryRouteCostListener();

        //存放读取的数据
        List<BaseBatteryRouteCostExportVo> importDTOList = new ArrayList<>();

        //成功的数据
        List<BaseBatteryRouteCostExportVo> successList = new Vector<>();

        //失败的数据
        List<BaseBatteryRouteCostExportVo> failList = new Vector<>();

        try {
            //获取读取到的内容
            EasyExcel.read(file.getInputStream(), BaseBatteryRouteCostExportVo.class, baseBatteryRouteCostListener).sheet().doRead();
            //获取读取到的内容
            importDTOList = baseBatteryRouteCostListener.getList();
            //检验
            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }
            //校验维修中心是否存在
            LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
            List<BaseRepairCenter> baseRepairCenterList = baseRepairCenterService.getBaseMapper().selectList(queryWrapper.eq(BaseRepairCenter::getIsDelete, 0));
            Map<String, String> baseRepairCenterMap = baseRepairCenterList.stream().collect(Collectors.toMap(BaseRepairCenter::getRepairCenterNum, BaseRepairCenter::getRepairCenterName));
            //重复的数据
            List<String> duplicate = new Vector<>();
            //并行校验
            importDTOList.parallelStream().forEach(item -> {
                //维修中心字段校验
                if (baseRepairCenterMap.containsKey(item.getRepairCenterNum())) {
                    item.setRepairCenterName(baseRepairCenterMap.get(item.getRepairCenterNum()));
                } else {
                    item.setFailedReason("维修中心编码不存在");
                    failList.add(item);
                    return;
                }
                //重复值校验
                //先拼接
                String string = item.getRepairCenterNum() + item.getDispatchCity() + item.getArrivalCity() + item.getLogisticsProvider() + item.getLogisticsType();
                if (duplicate.contains(string)) {
                    item.setFailedReason("存在重复的数据！");
                    failList.add(item);
                    return;
                } else {
                    duplicate.add(string);
                }
                //维修中心非空校验
                if (null == item.getRepairCenterNum()) {
                    item.setFailedReason("维修中心字段不能为空");
                    failList.add(item);
                    return;
                }
                //启运城市非空校验
                if (null == item.getDispatchCity()) {
                    item.setFailedReason("启运城市字段不能为空");
                    failList.add(item);
                    return;
                }
                //目标城市非空校验
                if (null == item.getArrivalCity()) {
                    item.setFailedReason("目标城市字段不能为空");
                    failList.add(item);
                    return;
                }
                //物流供应商非空校验
                if (null == item.getLogisticsProvider()) {
                    item.setFailedReason("物流供应商字段不能为空");
                    failList.add(item);
                    return;
                }
                //物流类型非空校验
                if (null == item.getLogisticsType()) {
                    item.setFailedReason("物流类型字段不能为空");
                    failList.add(item);
                    return;
                }
                //车型字段非空校验
                if (null == item.getCarType()) {
                    item.setFailedReason("车型字段不能为空");
                    failList.add(item);
                    return;
                }
                //线路名称非空校验
                if (null == item.getRouteName()) {
                    item.setFailedReason("线路名称字段不能为空");
                    failList.add(item);
                    return;
                }
                //公里数非空校验
                if (null == item.getKilometersNum()) {
                    item.setFailedReason("公里数字段不能为空");
                    failList.add(item);
                    return;
                }
                //时效非空校验
                if (null == item.getRouteDeliveryTime()) {
                    item.setFailedReason("时效字段不能为空");
                    failList.add(item);
                    return;
                }
                //税率非空校验
                if (null == item.getTaxRates()) {
                    item.setFailedReason("税率字段不能为空");
                    failList.add(item);
                    return;
                }
                //费用非空校验
                if (null == item.getExpense()) {
                    item.setFailedReason("费用字段不能为空");
                    failList.add(item);
                    return;
                }
                //税率实际情况校验
                if (20 <= item.getTaxRates()) {
                    item.setFailedReason("税率字段不能大于18");
                    failList.add(item);
                    return;
                }
                //校验通添加到成功的集合
                successList.add(item);
            });

            //构造响应体
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_BATTERY_ROUTE_COST.buildKey("fail", uuid), failList, 16, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_BATTERY_ROUTE_COST.buildKey("success", uuid), successList, 16, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;

        } catch (IOException | ResultException e) {
            throw new RuntimeException(e);
        } finally {
            baseBatteryRouteCostListener.clear();
        }
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    public Page<BaseBatteryRouteCostExportVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseBatteryRouteCostExportVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_ROUTE_COST.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseBatteryRouteCostExportVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BaseBatteryRouteCostExportVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseBatteryRouteCostExportVo> subList;
        //处理页面展示问题
        successResult.forEach(item -> {
            if (0 == item.getCarType()) {
                item.setCarTypeChinese("4.2");
            } else if (1 == item.getCarType()) {
                item.setCarTypeChinese("6.8");
            } else if (2 == item.getCarType()) {
                item.setCarTypeChinese("7.2");
            } else if (3 == item.getCarType()) {
                item.setCarTypeChinese("9.6");
            } else {
                item.setCarTypeChinese("");
            }
        });

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    public void downloadFailFile(PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        String operationCode = dto.getParam().getOperationCode();

        log.info("BaseBatteryRouteCostService downloadFailFile 开始导出 {}", operationCode);

        //获取redis中存储的失败数据
        List<BaseBatteryRouteCostExportVo> failResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_ROUTE_COST.buildKey("fail", operationCode), BaseBatteryRouteCostExportVo.class);
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("电池线路费用维护批量导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BaseBatteryRouteCostExportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseBatteryRouteCostService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    /**
     * 保存校验成功的规则
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Result importData(String operationCode) throws ResultException {

        log.info("BaseBatteryRouteCostService importData 开始添加数据 {}", operationCode);
        List<BaseBatteryRouteCostExportVo> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_BATTERY_ROUTE_COST.buildKey("success", operationCode), BaseBatteryRouteCostExportVo.class);

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        successResult.forEach(item -> {

            LambdaQueryWrapper<BaseBatteryRouteCost> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseBatteryRouteCost::getRepairCenterNum, item.getRepairCenterNum())
                    .eq(BaseBatteryRouteCost::getDispatchCity, item.getDispatchCity())
                    .eq(BaseBatteryRouteCost::getArrivalCity, item.getArrivalCity())
                    .eq(BaseBatteryRouteCost::getLogisticsProvider, item.getLogisticsProvider())
                    .eq(BaseBatteryRouteCost::getLogisticsType, item.getLogisticsType())
                    .eq(BaseBatteryRouteCost::getCarType, item.getCarType()).eq(BaseBatteryRouteCost::getIsDelete, 0);
            BaseBatteryRouteCost baseBatteryRouteCostDb = this.baseMapper.selectOne(queryWrapper);

            if (BeanUtil.isEmpty(baseBatteryRouteCostDb)) {
                //是null表明这个是新增
                BaseBatteryRouteCost baseBatteryRouteCost = BeanUtil.copyProperties(item, BaseBatteryRouteCost.class);
                int insertResult = this.baseMapper.insert(baseBatteryRouteCost);
                addCount.addAndGet(insertResult);
            } else {
                BaseBatteryRouteCost baseBatteryRouteCost = new BaseBatteryRouteCost();
                //不是空表明是修改
                baseBatteryRouteCost.setId(baseBatteryRouteCostDb.getId());
                baseBatteryRouteCost.setRouteName(item.getRouteName());
                baseBatteryRouteCost.setKilometersNum(item.getKilometersNum());
                baseBatteryRouteCost.setRouteDeliveryTime(item.getRouteDeliveryTime());
                baseBatteryRouteCost.setExpense(item.getExpense());
                baseBatteryRouteCost.setTaxRates(item.getTaxRates());
                int updateResult = this.baseMapper.updateById(baseBatteryRouteCost);
                updateCount.addAndGet(updateResult);
            }
        });
        log.info("BaseBatteryRouteCostService importData 本次导入新增{}个 更新{}个", addCount.get(), updateCount.get());

        if (successResult.size() != (addCount.get() + updateCount.get())) {
            throw new RuntimeException("BaseBatteryRouteCostService importData 本次导入发生错误！");
        }

        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", addCount.get(), updateCount.get()));
    }
}
