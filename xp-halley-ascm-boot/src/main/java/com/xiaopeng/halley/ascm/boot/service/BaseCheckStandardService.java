package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseCheckStandard;
import com.xiaopeng.halley.ascm.boot.entity.BaseMaterials;
import com.xiaopeng.halley.ascm.boot.mapper.BaseCheckStandardMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.service
 * @Date 2024/9/9 11:15
 */
@Slf4j
@Service
public class BaseCheckStandardService extends ServiceImpl<BaseCheckStandardMapper, BaseCheckStandard> {

    @Value("${fileTemp.BaseCheckStandard.tempFileId}")
    private String tempFileId;
    @Resource
    private ImageService imageService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Resource
    private BaseMaterialsService baseMaterialsService;
    @Resource
    private BaseCheckStandardMapper baseCheckStandardMapper;

    /**
     * 分页查询
     *
     * @param page 分页参数
     * @return 查询结果
     */
    public Page<BaseCheckStandardVo> getPage(PageQuery<BaseCheckStandardDto> page) {
        // 获取总条数
        long total = baseCheckStandardMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();
        // 分页查询
        List<BaseCheckStandardVo> returnList = baseCheckStandardMapper.getPage(page.getParam(), startIndex, page.getSize());
        Page<BaseCheckStandardVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 删除校验文本
     *
     * @param param 删除的参数
     * @return 删除结果
     */
    @Transactional
    public Result<String> deleteItem(BaseCheckStandardDto param) {
        if (CollUtil.isEmpty(param.getIds())) {
            return ResultUtil.failed("必填参数不能为空！");
        }
        // 删除前先校验是否已经存在物料已经匹配相应的规则
        LambdaQueryWrapper<BaseMaterials> lqw = new LambdaQueryWrapper<>();
        lqw.in(BaseMaterials::getCheckCodeId, param.getIds());
        List<BaseMaterials> baseMaterials = baseMaterialsService.list(lqw);
        if (CollUtil.isNotEmpty(baseMaterials)) {
            List<Integer> checkCodeIds = baseMaterials.stream().map(BaseMaterials::getCheckCodeId).collect(Collectors.toList());
            List<BaseCheckStandard> baseCheckStandards = query().in("id", checkCodeIds).list();
            String collect = baseCheckStandards.stream().map(BaseCheckStandard::getCheckCode).collect(Collectors.joining(","));
            return ResultUtil.failed("删除失败，[" + collect + "]校验标准已分配对应备件！");
        }
        boolean result = removeBatchByIds(param.getIds());
        return result ? ResultUtil.success("删除成功！") : ResultUtil.failed("删除失败！");
    }

    /**
     * 编辑校验文本
     *
     * @param param 编辑的参数
     * @return 结果
     */
    public Result<String> editItem(BaseCheckStandardDto param) {
        if (CollUtil.isEmpty(param.getIds())) {
            return ResultUtil.failed("必填参数不能为空！");
        }
        String checkDocs = param.getCheckDocs();
        BaseCheckStandard baseCheckStandard = new BaseCheckStandard();
        baseCheckStandard.setId(param.getIds().get(0));
        baseCheckStandard.setCheckDocs(checkDocs);
        boolean result = updateById(baseCheckStandard);
        return result ? ResultUtil.success("修改成功！") : ResultUtil.failed("修改失败！");
    }

    /**
     * 导入文件校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    public ImportResponseDto importFile(MultipartFile file) {

        // 解析文件
        List<BaseCheckStandardImportVo> baseCarExWarehouseImportVOList = new ArrayList<>();

        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            // excel映射字段
            Map<String, String> alias = new HashMap<>();
            alias.put("检验标准编码", "checkCode");
            alias.put("标准描述", "checkDesc");
            alias.put("检验标准", "checkDocs");
            excelReader.setHeaderAlias(alias);
            baseCarExWarehouseImportVOList = excelReader.readAll(BaseCheckStandardImportVo.class);
            alias.clear();
        } catch (IOException e) {
            String userInfo = JSON.toJSONString(ascmLoginUserHelper.getLoginUser());
            log.error("解析文件异常，请检查！ {} {}", userInfo, e.getMessage());
        }

        List<BaseCheckStandardImportVo> successList = new ArrayList<>();
        List<BaseCheckStandardImportVo> failList = new ArrayList<>();

        // 校验内容
        for (BaseCheckStandardImportVo baseCheckStandardImportVo : baseCarExWarehouseImportVOList) {
            if (StrUtil.isBlank(baseCheckStandardImportVo.getCheckCode())) {
                baseCheckStandardImportVo.setFailReason("检验标准编码不能为空");
                failList.add(baseCheckStandardImportVo);
                continue;
            }
            if (StrUtil.isBlank(baseCheckStandardImportVo.getCheckDesc())) {
                baseCheckStandardImportVo.setFailReason("标准描述不能为空");
                failList.add(baseCheckStandardImportVo);
                continue;
            }
            if (StrUtil.isBlank(baseCheckStandardImportVo.getCheckDocs())) {
                baseCheckStandardImportVo.setFailReason("检验标准不能为空");
                failList.add(baseCheckStandardImportVo);
                continue;
            }
            successList.add(baseCheckStandardImportVo);
        }
        // 存入redis,响应结果
        ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
        accountImportResponseDTO.setFailCount(failList.size());
        accountImportResponseDTO.setSuccessCount(successList.size());

        String uuid = UUID.fastUUID().toString(true);
        accountImportResponseDTO.setFileCode(uuid);

        try {
            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_CHECK_STANDARD.buildKey("fail", uuid), failList, 1, TimeUnit.HOURS);
            }
            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_CHECK_STANDARD.buildKey("success", uuid), successList, 1, TimeUnit.HOURS);
            }
        } catch (ResultException e) {
            log.error("导出文件异常，请检查！ {} {}", uuid, e.getMessage());
        }

        return accountImportResponseDTO;
    }

    /**
     * 导出校验失败的文件
     *
     * @param response
     */
    public void exportFailFile(String operationCode, HttpServletResponse response) {
        log.info("BaseCheckStandardService exportFailFile 开始导出 {}", operationCode);
        //获取redis中存储的失败数据
        try {
            List<BaseCheckStandardImportVo> failResult = ascmRedisHelper
                    .getList(RedisKeyManager.IMPORT_BASE_CHECK_STANDARD.buildKey("fail", operationCode), BaseCheckStandardImportVo.class);
            //响应出去
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("备件校验标准导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BaseCheckStandardImportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseCheckStandardService exportFailFile 导出异常！[{}]", e.getMessage());
        }
    }

    /**
     * 提交校验成功的文件
     *
     * @param operationCode
     * @return
     */
    public Result<String> submitFile(String operationCode) throws ResultException {
        log.info("BaseCheckStandardService submitFile 开始添加数据 {}", operationCode);
        List<BaseCheckStandardImportVo> successResult = ascmRedisHelper
                .getList(RedisKeyManager.IMPORT_BASE_CHECK_STANDARD.buildKey("success", operationCode), BaseCheckStandardImportVo.class);

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        successResult.forEach(item -> {
            // 查询是否存在，不存在就新增
            LambdaQueryWrapper<BaseCheckStandard> lqw = new LambdaQueryWrapper<>();
            lqw.eq(BaseCheckStandard::getCheckCode, item.getCheckCode());
            BaseCheckStandard baseCheckStandard = getOne(lqw);

            if (BeanUtil.isEmpty(baseCheckStandard)) {
                //是null表明这个是新增
                BaseCheckStandard baseCheckStandardCopy = BeanUtil.copyProperties(item, BaseCheckStandard.class);
                boolean result = save(baseCheckStandardCopy);
                addCount.addAndGet(result ? 1 : 0);
            } else {
                //不是空表明是更新
                BeanUtil.copyProperties(item, baseCheckStandard, "id");
                boolean result = updateById(baseCheckStandard);
                updateCount.addAndGet(result ? 1 : 0);
            }
        });

        log.info("BaseCheckStandardService submitFile 本次导入新增{}个 更新{}个", addCount.get(), updateCount.get());

        if (successResult.size() != (addCount.get() + updateCount.get())) {
            log.error("BaseCheckStandardService submitFile 本次导入发生错误！{}", operationCode);
        }
        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", addCount.get(), updateCount.get()));
    }

    /**
     * 备件检验信息导入模板下载
     *
     * @return 模板的OSS下载地址
     */
    public ImageResponseDTO tempFile() {
        return imageService.getTempURL(tempFileId);
    }

    public Page<BaseCheckStandardImportVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {

        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseCheckStandardImportVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_CHECK_STANDARD.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseCheckStandardImportVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BaseCheckStandardImportVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseCheckStandardImportVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    public List<BaseCheckStandard> getCheckCodeInfo(String checkDesc) {
        LambdaQueryWrapper<BaseCheckStandard> lqw = new LambdaQueryWrapper<>();
        lqw.like(StrUtil.isNotBlank(checkDesc), BaseCheckStandard::getCheckDesc, checkDesc).or().like(StrUtil.isNotBlank(checkDesc), BaseCheckStandard::getCheckCode, checkDesc);
        lqw.select(BaseCheckStandard::getId, BaseCheckStandard::getCheckCode,
                BaseCheckStandard::getCheckDesc, BaseCheckStandard::getCheckDocs);
        return list(lqw);
    }
}
