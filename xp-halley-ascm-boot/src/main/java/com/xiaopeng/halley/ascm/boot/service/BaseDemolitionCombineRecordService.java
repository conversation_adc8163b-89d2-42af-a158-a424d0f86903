package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.DemolitionCombineRequestDto;
import com.xiaopeng.halley.ascm.boot.dto.DemolitionCombineResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseDemolitionCombineRecord;
import com.xiaopeng.halley.ascm.boot.mapper.BaseDemolitionCombineRecordMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BaseDemolitionCombineRecordService {

    @Resource
    private BaseDemolitionCombineRecordMapper baseDemolitionCombineRecordMapper;

    public Page<DemolitionCombineResponseDto> getPage(PageQuery<DemolitionCombineRequestDto> page) {

        // 新分页查询
        LambdaQueryWrapper<BaseDemolitionCombineRecord> queryWrapper = new LambdaQueryWrapper<BaseDemolitionCombineRecord>().eq(BaseDemolitionCombineRecord::getIsDelete, 0);

        if (StrUtil.isNotBlank(page.getParam().getParentWaybillCode())) {
            queryWrapper.like(BaseDemolitionCombineRecord::getParentWaybillCode, page.getParam().getParentWaybillCode());
        }

        if (StrUtil.isNotBlank(page.getParam().getChildWaybillCode())) {
            queryWrapper.like(BaseDemolitionCombineRecord::getChildWaybillCode, page.getParam().getChildWaybillCode());
        }

        if (StrUtil.isNotBlank(page.getParam().getCreateUserName())) {
            queryWrapper.like(BaseDemolitionCombineRecord::getCreateUserName, page.getParam().getCreateUserName());
        }

        if (StrUtil.isNotBlank(page.getParam().getAction())) {
            queryWrapper.like(BaseDemolitionCombineRecord::getAction, page.getParam().getAction());
        }

        if (ObjectUtil.isNotNull(page.getParam().getStartCreateTime()) && ObjectUtil.isNotNull(page.getParam().getEndCreateTime())) {
            queryWrapper.between(BaseDemolitionCombineRecord::getCreateTime, page.getParam().getStartCreateTime(), page.getParam().getEndCreateTime());
        }

        queryWrapper.orderByDesc(BaseDemolitionCombineRecord::getCreateTime);

        IPage<BaseDemolitionCombineRecord> pageResult = this.baseDemolitionCombineRecordMapper.selectPage(page.convertPage(), queryWrapper);

        List<DemolitionCombineResponseDto> returnList = new ArrayList<>();

        pageResult.getRecords().forEach(item -> {
            DemolitionCombineResponseDto newItem = BeanUtil.copyProperties(item, DemolitionCombineResponseDto.class);

            newItem.setCreateTimeShow(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm"));

            returnList.add(newItem);
        });

        return new Page<DemolitionCombineResponseDto>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())
                .setRecords(returnList);

    }

}
