package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.entity.BaseFeeStandard;
import com.xiaopeng.halley.ascm.boot.entity.BzContract;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.excel.AbstractExcelImportHandler;
import com.xiaopeng.halley.ascm.boot.excel.ImportContext;
import com.xiaopeng.halley.ascm.boot.mapper.BaseFeeStandardMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.utils.AssertUtils;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
public class BaseFeeStandardService extends ServiceImpl<BaseFeeStandardMapper, BaseFeeStandard> implements AbstractExcelImportHandler<BaseFeeStandardImport> {
	@Value("#{'${car-type-list}'.split(',')}")
	private List<String> carTypeList;
	public static ThreadLocal<Boolean> threadLocal = ThreadLocal.withInitial(() -> false);
	@Resource
	private ImageService imageService;
	@Resource
	private BzWaybillMapper bzWaybillMapper;
	@Resource
	private BzContractService bzContractService;
	@Resource
	private BaseWarehouseService baseWarehouseService;
	@Resource
	private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;

	public Page<BaseFeeStandardSummary> querySummary(PageQuery<BaseFeeStandardQuery> request) {
		try {
			threadLocal.set(true);
			BaseFeeStandardQuery param = request.getParam();
			Optional.ofNullable(param.getDepartureTime()).ifPresent(e -> {
				param.setStartTime(DateUtil.beginOfDay(e));
				param.setEndTime(DateUtil.endOfDay(e));
			});

			Page<BaseFeeStandardSummary> pageResult = baseMapper.pageSummary(Page.of(request.getPage(), request.getSize()), param);
			if (pageResult.getRecords().isEmpty()) {
				return pageResult;
			}

			Set<String> waybillCodes = pageResult.getRecords().stream()
					.map(e -> StrUtil.split(e.getWaybillCode(), StrUtil.COMMA))
					.flatMap(List::stream).collect(Collectors.toSet());
			Map<String, BzWaybill> waybillMap = new LambdaQueryChainWrapper<>(bzWaybillMapper).in(BzWaybill::getWaybillCode, waybillCodes).list()
					.stream().collect(Collectors.toMap(BzWaybill::getWaybillCode, Function.identity()));

			for (BaseFeeStandardSummary record : pageResult.getRecords()) {
				ArrayList<BzWaybill> bzWaybills = MapUtil.valuesOfKeys(waybillMap, StrUtil.split(record.getWaybillCode(), StrUtil.COMMA).iterator());
				BigDecimal totalVolume = BigDecimal.valueOf(bzWaybills.stream().mapToDouble(BzWaybill::getTotalVolume).sum());
				record.setTotalVolume(totalVolume.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.valueOf(0.001) : totalVolume);

				// 立方米转成立方厘米 / 8000
				BigDecimal totalWeight = calWeight(totalVolume);
				record.setTotalWeight(totalWeight.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.valueOf(0.001) : totalWeight);
				this.calFee(record);
			}
			return pageResult;
		} finally {
			threadLocal.set(false);
		}
	}

	public BigDecimal calWeight(BigDecimal totalVolume) {
		return totalVolume.multiply(BigDecimal.valueOf(1000000))
				.divide(BigDecimal.valueOf(8000), 3, RoundingMode.HALF_UP);
	}

	public Page<?> page(PageQuery<BaseFeeStandardQuery> pageQuery) {
		Map<String, String> codeMapName = baseWarehouseService.getCodeMapName();
		BaseFeeStandardQuery param = ObjectUtil.defaultIfNull(pageQuery.getParam(), new BaseFeeStandardQuery());
		param.setType(ObjectUtil.defaultIfNull(param.getType(), 1));
		Page<BaseFeeStandardVO> page = baseMapper.page(Page.of(pageQuery.getPage(), pageQuery.getSize()), param);
		List<BaseFeeStandardVO> records = page.getRecords();
		records.forEach(e -> e.setOperationWarehouse(codeMapName.get(e.getOperationWarehouse())));

		if (param.getType().equals(1)) {
			return new Page<BaseFeeStandardKdVO>(page.getCurrent(), page.getSize(), page.getTotal())
					.setRecords(BeanUtil.copyToList(records, BaseFeeStandardKdVO.class));
		} else {
			return new Page<BaseFeeStandardSpecialCarVO>(page.getCurrent(), page.getSize(), page.getTotal())
					.setRecords(BeanUtil.copyToList(records, BaseFeeStandardSpecialCarVO.class));
		}
	}

	@SuppressWarnings("all")
	public Page<BaseFeeStandardKdVO> pageKd(PageQuery<BaseFeeStandardQuery> pageQuery) {
		return (Page<BaseFeeStandardKdVO>) page(pageQuery);
	}

	@SuppressWarnings("all")
	public Page<BaseFeeStandardSpecialCarVO> pageSpecialCar(PageQuery<BaseFeeStandardQuery> pageQuery) {
		return (Page<BaseFeeStandardSpecialCarVO>) page(pageQuery);
	}

	@SuppressWarnings("unused")
	public Page<BaseFeeStandardKdView> view(PageQuery<BaseFeeStandardQuery> pageQuery) {
		Page<BaseFeeStandardView> page = query(pageQuery);
		List<BaseFeeStandardView> records = page.getRecords();
		return new Page<BaseFeeStandardKdView>(page.getCurrent(), page.getSize(), page.getTotal())
				.setRecords(BeanUtil.copyToList(records, BaseFeeStandardKdView.class));
	}

	@SuppressWarnings("unused")
	public Page<BaseFeeStandardSpecialCarView> specialCarView(PageQuery<BaseFeeStandardQuery> pageQuery) {
		Page<BaseFeeStandardView> page = query(pageQuery);
		List<BaseFeeStandardView> records = page.getRecords();
		return new Page<BaseFeeStandardSpecialCarView>(page.getCurrent(), page.getSize(), page.getTotal())
				.setRecords(BeanUtil.copyToList(records, BaseFeeStandardSpecialCarView.class));
	}

	public void calFee(BaseFeeStandardSummary entity) {
		// 检查是否所有必要参数都存在
		if (entity == null || entity.getTotalVolume() == null || entity.getTotalWeight() == null || entity.getUnitPrice() == null
				|| entity.getMinFee() == null || entity.getFirstWeightFee() == null || entity.getExtraWeightPrice() == null) {
			return;
		}

		// 保留两位小数，四舍五入
		final int SCALE = 2;
		final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

		// 将Double转换为BigDecimal进行精确计算
		BigDecimal unitPrice = entity.getUnitPrice();
		BigDecimal totalVolume = entity.getTotalVolume();
		BigDecimal totalWeight = entity.getTotalWeight();

		// 零担计算方式 - 如果总体积等于0，那么就等于0，否则【总体积 * 体积单价】和【未税最低运费】两者取最大的
		BigDecimal minFee = entity.getMinFee();
		BigDecimal fee1 = totalVolume.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalVolume.multiply(unitPrice).max(minFee);
		entity.setFee1(fee1.setScale(SCALE, ROUNDING_MODE));

		// 快递计算方式 - 首重 + （IF（总重量 = 0,1，总重量）- 1）) * 续重
		BigDecimal firstWeightFee = entity.getFirstWeightFee();
		BigDecimal extraWeightPrice = entity.getExtraWeightPrice();
		BigDecimal adjustedWeight = totalWeight.compareTo(BigDecimal.ONE) < 0 ? BigDecimal.ONE : totalWeight;
		BigDecimal fee2 = firstWeightFee.add(adjustedWeight.subtract(BigDecimal.ONE).multiply(extraWeightPrice));
		entity.setFee2(fee2.setScale(SCALE, ROUNDING_MODE));

		// 专车计算方式
		entity.setFee3(unitPrice);

		switch (entity.getTransportType()) {
			case "零担": entity.setFinalFee(entity.getFee1()); break;
			case "快递": entity.setFinalFee(entity.getFee2()); break;
			case "专车": entity.setFinalFee(entity.getFee3()); break;
		}
	}

	public Page<BaseFeeStandardView> query(PageQuery<BaseFeeStandardQuery> pageQuery) {
		try {
			threadLocal.set(true);
			BaseFeeStandardQuery param = ObjectUtil.defaultIfNull(pageQuery.getParam(), new BaseFeeStandardQuery());
			param.setType(ObjectUtil.defaultIfNull(param.getType(), 1));
			Optional.ofNullable(param.getDepartureTime()).ifPresent(e -> {
				param.setStartTime(DateUtil.beginOfDay(e));
				param.setEndTime(DateUtil.endOfDay(e));
			});

			Page<BaseFeeStandardView> page = baseMapper.pageView(Page.of(pageQuery.getPage(), pageQuery.getSize()), param);

			List<BaseFeeStandardView> records = page.getRecords();
			if (records.isEmpty()) {
				return page;
			}

			List<String> waybillCodes = records.stream().map(BaseFeeStandardView::getWaybillCode).distinct().collect(Collectors.toList());
			Map<String, String> deliveryOrderCodeMap = waybillCodes.isEmpty() ? new HashMap<>() : bzWaybillMapper.getDeliveryOrderCodeMap(waybillCodes).stream()
					.collect(Collectors.toMap(BzWayBillNewPageVo::getWaybillCode, BzWayBillNewPageVo::getDeliveryOrderCode));
			Map<String, String> codeMapName = baseWarehouseService.getCodeMapName();

			for (BaseFeeStandardView record : records) {
				record.setLgobe(codeMapName.get(record.getLgort()));
				switch (record.getTransportType()) {
					case "零担": record.setFinalFee(record.getFee1()); break;
					case "快递": record.setFinalFee(record.getFee2()); break;
					case "专车": record.setFinalFee(record.getFee3()); break;
				}
				record.setDeliveryOrderCode(deliveryOrderCodeMap.get(record.getWaybillCode()));
			}

			// 专车的信息
			if (param.getType() != 2) {
				return page;
			}
			Map<String, List<AddressInfo>> addressMap = bzWaybillTrackHistoryMapper.getAddressInfo(waybillCodes).stream()
					.collect(Collectors.groupingBy(
							AddressInfo::getWaybillCode
					));

			// oss查询图片
			List<Long> fileIds = addressMap.values().stream()
					.flatMap(List::stream)
					.map(AddressInfo::getFileId)
					.collect(Collectors.toList());
			Map<String, ImageResponseDTO> imageMap = imageService.getTempURLList(fileIds).stream()
					.collect(Collectors.toMap(ImageResponseDTO::getFileId, Function.identity()));

			for (BaseFeeStandardView record : records) {
				List<AddressInfo> addressInfos = addressMap.get(record.getWaybillCode());
				if (CollUtil.isNotEmpty(addressInfos)) {
					// 获取交付定位
					addressInfos.stream()
							.min(Comparator.comparing(AddressInfo::getUpdateTime))
							.ifPresent(e -> record.setAddress(e.getAddress()));

					// 获取交付照片url
					List<String> deliveryUrl = addressInfos.stream().map(e -> imageMap.get(String.valueOf(e.getFileId())))
							.filter(Objects::nonNull)
							.map(ImageResponseDTO::getUrl)
							.limit(3)
							.collect(Collectors.toList());
					record.setDeliveryUrl(deliveryUrl);
				}
			}
			return page;
		} finally {
			threadLocal.set(false);
		}
	}

	@Override
	public void doVerify(ImportContext<BaseFeeStandardImport> context) {
		Integer type = MapUtil.getInt(context.getParams(), "type");

		Set<String> contractCodeSet = bzContractService.list()
				.stream().map(BzContract::getContractCode)
				.collect(Collectors.toSet());

		for (BaseFeeStandardImport entity : context.getDataList()) {
			entity.setType(type);
			try {
				// 必校验字段
				Assert.notBlank(entity.getContractCode(), "合同编码不能为空");
				Assert.isTrue(contractCodeSet.contains(entity.getContractCode()), "合同编码不存在");
				Assert.notBlank(entity.getOriginCity(), "发出城市不能为空");
				Assert.notBlank(entity.getDestinationCity(), "目标城市不能为空");
				AssertUtils.isNumberAndPositive(entity.getUnitPrice(), "未税单价");

				// 零担快递校验字段
				if (type.equals(1)) {
					Assert.isTrue(StrUtil.equalsAny(entity.getIsMainArea(), "是", "否"), "是否主覆盖区域必须为是或否");
					AssertUtils.isNumberAndPositive(entity.getMinFee(), "未税最低运费");
					AssertUtils.isNumberAndPositive(entity.getFirstWeightFee(), "未税首重运费");
					AssertUtils.isNumberAndPositive(entity.getExtraWeightPrice(), "续重未税单价");
				}
				// 专车校验字段
				else {
					Assert.notBlank(entity.getCarType(), "车型不能为空");
					AssertUtils.isNumberAndPositive(entity.getDistance(), "距离");
					Assert.isTrue(CollUtil.contains(carTypeList, entity.getCarType()), "车型必须为[{}]其中一个", carTypeList);
				}

				// 成功通过校验
				context.getSuccessList().add(entity);
			} catch (Exception e) {
				// 校验失败
				entity.setErrorMsg(e.getMessage());
				context.getFailList().add(entity);
			}
		}
	}

	@Override
	public void doImport(List<BaseFeeStandardImport> successList) {
		Map<String, BaseFeeStandard> feeStandardMap = this.list().stream()
				.collect(Collectors.toMap(this::getUniqueKey, Function.identity()));

		List<BaseFeeStandard> baseFeeStandards = new ArrayList<>();
		for (BaseFeeStandardImport baseFeeStandardImport : successList) {
			// 合同编码+发出城市+目的城市+车型相同，则设置id让数据库进行更新而非新增
			BaseFeeStandard oldEntity = feeStandardMap.get(getUniqueKey(baseFeeStandardImport));
			BaseFeeStandard baseFeeStandard = BeanUtil.copyProperties(baseFeeStandardImport, BaseFeeStandard.class);
			if (oldEntity != null) {
				baseFeeStandard.setId(oldEntity.getId());
			}
			baseFeeStandards.add(baseFeeStandard);
		}
		SpringUtil.getBean(this.getClass()).saveOrUpdateBatch(baseFeeStandards);
	}

	private String getUniqueKey(Object object) {
		if (object instanceof BaseFeeStandard) {
			BaseFeeStandard item = (BaseFeeStandard) object;
			return StrUtil.join("@", item.getContractCode(), item.getOriginCity(), item.getDestinationCity(), item.getCarType(), item.getType());
		}
		if (object instanceof BaseFeeStandardImport) {
			BaseFeeStandardImport item = (BaseFeeStandardImport) object;
			return StrUtil.join("@", item.getContractCode(), item.getOriginCity(), item.getDestinationCity(), item.getCarType(), item.getType());
		}
		return null;
	}

}