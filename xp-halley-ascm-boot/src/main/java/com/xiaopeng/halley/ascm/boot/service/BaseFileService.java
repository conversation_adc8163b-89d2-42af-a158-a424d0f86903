package com.xiaopeng.halley.ascm.boot.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.entity.BaseFile;
import com.xiaopeng.halley.ascm.boot.mapper.BaseFileMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件
 *
 * <AUTHOR> 自动生成
 * @date 2022-12-06 02:44:57
 */
@Service
public class BaseFileService extends ServiceImpl<BaseFileMapper, BaseFile> {
	@Resource
	private ImageService imageService;

	public <T> void setOssUrl(List<T> list, Function<T, Long> fileIdGetter, BiConsumer<T, ImageResponseDTO> consumer) {
		List<Long> baseFileIds = list.stream().map(fileIdGetter).filter(Objects::nonNull).distinct()
				.collect(Collectors.toList());
		if (baseFileIds.isEmpty()) {
			return;
		}

		Map<String, ImageResponseDTO> urlMap = imageService.getTempURLList(baseFileIds).stream()
				.collect(Collectors.toMap(ImageResponseDTO::getFileId, Function.identity()));
		for (T entity : list) {
			Long baseFileId = fileIdGetter.apply(entity);
			ImageResponseDTO responseDTO = urlMap.get(String.valueOf(baseFileId));
			if (responseDTO != null) {
				consumer.accept(entity, responseDTO);
			}
		}
	}

	public <T> void setOssUrls(List<T> list, Function<T, List<Long>> fileIdGetter, BiConsumer<T, List<ImageResponseDTO>> consumer) {
		// 1. 收集所有不为空的文件ID，并去重
		List<Long> baseFileIds = list.stream()
				.map(fileIdGetter)
				.filter(Objects::nonNull)
				.flatMap(Collection::stream) // 展开文件ID列表
				.distinct()
				.collect(Collectors.toList());

		if (baseFileIds.isEmpty()) {
			return;
		}

		// 2. 批量获取所有文件的临时URL
		Map<String, ImageResponseDTO> urlMap = imageService.getTempURLList(baseFileIds).stream()
				.collect(Collectors.toMap(imageResponseDTO -> String.valueOf(imageResponseDTO.getFileId()), Function.identity()));

		// 3. 遍历业务实体，将URL列表设置回去
		for (T entity : list) {
			List<Long> baseFileIdList = fileIdGetter.apply(entity);
			if (baseFileIdList != null && !baseFileIdList.isEmpty()) {
				List<ImageResponseDTO> responseDTOList = baseFileIdList.stream()
						.map(fileId -> urlMap.get(String.valueOf(fileId)))
						.filter(Objects::nonNull)
						.collect(Collectors.toList());
				if (!responseDTOList.isEmpty()) {
					consumer.accept(entity, responseDTOList);
				}
			}
		}
	}
}

