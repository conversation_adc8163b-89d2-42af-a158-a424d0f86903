package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.feign.DragonApiFeign;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author: tuyb
 * @Date: 2024-8-8 14:01
 * @Description:
 */
@Slf4j
@Service
public class BaseMaterialsService extends ServiceImpl<BaseMaterialsMapper, BaseMaterials> {
    @Value("${cos.ali.path}")
    private String ossPath;
    @Value("${fileTemp.BaseMaterials.rulesTempFileId}")
    private String rulesTempFileId;
    @Value("${fileTemp.BaseMaterials.packageTempFileId}")
    private String packageTempFileId;
    @Resource
    private BaseSuppliersMapper baseSuppliersMapper;
    @Resource
    private BaseCheckStandardMapper baseCheckStandardMapper;
    @Resource
    private BzSupplierMaterialMapper bzSupplierMaterialMapper;
    @Resource
    private BzSupplierPackageInfoMapper bzSupplierPackageInfoMapper;
    @Autowired
    private ImageService imageService;
    @Resource
    private BaseMaterialsMapper baseMaterialsMapper;
    @Resource
    private BzDeliveryOrderMaterialMapper bzDeliveryOrderMaterialMapper;
    @Resource
    private BzDeliveryOrdersMapper bzDeliveryOrdersMapper;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Resource
    private DragonApiFeign dragonApiFeign;

    /**
     * 同步照片到骁龙
     *
     * @param materialCodeList 物料编码集合
     */
    @Async
    public void syncPic(List<String> materialCodeList) {
        try {
            List<MaterialPicVO> materialPicVOS = this.queryPic(materialCodeList);
            log.info("开始同步照片到骁龙 materialCodeList: {}, materialPicVOS: {}", materialCodeList, JSON.toJSONString(materialPicVOS));
            Result<Object> result = dragonApiFeign.syncPhoto(materialPicVOS);
            if (ResultUtil.isSuccess(result)) {
                log.info("同步照片到骁龙成功!");
            } else {
                log.warn("同步照片到骁龙出现异常: {}", result.getMsg());
            }
        } catch (Exception e) {
            log.error("同步照片到骁龙失败", e);
        }
    }

    private void addIfNotEmpty(List<MaterialPhotoItem> list, String pic, MaterialPhotoItem.PhotoType photoType) throws ResultException {
        if (StrUtil.isNotBlank(pic)) {
            MaterialPhotoItem photoItem = MaterialPhotoItem.builder()
                    .photoName(StrUtil.removePrefix(pic, ossPath))
                    .photoType(photoType.getValue())
                    .photoUrl(getOssUrl(pic))
                    .build();
            list.add(photoItem);
        }
    }

    public List<MaterialPicVO> queryPic(List<String> materialCodeList) throws ResultException {
        materialCodeList = ListUtil.sub(materialCodeList, 0, 100);
        List<MaterialPicVO> result = new ArrayList<>();

        Map<String, List<BaseMaterials>> map = this.lambdaQuery().in(BaseMaterials::getMaterialCode, materialCodeList).list()
                .stream().collect(Collectors.groupingBy(BaseMaterials::getMaterialCode));

        for (String key : map.keySet()) {
            List<BaseMaterials> baseMaterials = map.get(key);
            for (BaseMaterials baseMaterial : baseMaterials) {
                List<MaterialPhotoItem> list = new ArrayList<>();
                addIfNotEmpty(list, baseMaterial.getPicLeft(), MaterialPhotoItem.PhotoType.LEFT);
                addIfNotEmpty(list, baseMaterial.getPicRight(), MaterialPhotoItem.PhotoType.RIGHT);
                addIfNotEmpty(list, baseMaterial.getPicStamp(), MaterialPhotoItem.PhotoType.STAMP);
                addIfNotEmpty(list, baseMaterial.getPicBefore(), MaterialPhotoItem.PhotoType.BEFORE);
                addIfNotEmpty(list, baseMaterial.getPicAfter(), MaterialPhotoItem.PhotoType.AFTER);
                addIfNotEmpty(list, baseMaterial.getPicOther(), MaterialPhotoItem.PhotoType.OTHER);
                result.add(new MaterialPicVO(key, list));
            }
        }
        return result;
    }

    public Page<GetMaterialPageResonseVo> getPage(PageQuery<GetMaterialPageRequestDto> page) throws ResultException {
        // 获取总条数
        long total = baseMaterialsMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<GetMaterialPageResonseVo> returnList = baseMaterialsMapper.getPage(page.getParam(), startIndex, page.getSize());
        if (CollectionUtils.isNotEmpty(returnList)) {
            for (GetMaterialPageResonseVo getMaterialPageResonseVo : returnList) {
                String picLeft = getMaterialPageResonseVo.getPicLeft();
                String picRight = getMaterialPageResonseVo.getPicRight();
                String picBefore = getMaterialPageResonseVo.getPicBefore();
                String picAfter = getMaterialPageResonseVo.getPicAfter();
                String picOther = getMaterialPageResonseVo.getPicOther();
                String picStamp = getMaterialPageResonseVo.getPicStamp();
                getMaterialPageResonseVo.setPicLeftRaw(getOssUrl(picLeft));
                getMaterialPageResonseVo.setPicRightRaw(getOssUrl(picRight));
                getMaterialPageResonseVo.setPicBeforeRaw(getOssUrl(picBefore));
                getMaterialPageResonseVo.setPicAfterRaw(getOssUrl(picAfter));
                getMaterialPageResonseVo.setPicOtherRaw(getOssUrl(picOther));
                getMaterialPageResonseVo.setPicStampRaw(getOssUrl(picStamp));
                // 原图
                getMaterialPageResonseVo.setPicLeft(getRawOssUrl(picLeft));
                getMaterialPageResonseVo.setPicRight(getRawOssUrl(picRight));
                getMaterialPageResonseVo.setPicBefore(getRawOssUrl(picBefore));
                getMaterialPageResonseVo.setPicAfter(getRawOssUrl(picAfter));
                getMaterialPageResonseVo.setPicOther(getRawOssUrl(picOther));
                getMaterialPageResonseVo.setPicStamp(getRawOssUrl(picStamp));
                // 是否完整字段处理
                if (StrUtil.isBlank(getMaterialPageResonseVo.getPicLeft()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicRight()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicBefore()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicAfter()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicOther()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicStamp())
                ) {
                    getMaterialPageResonseVo.setPicComplete("否");
                } else {
                    getMaterialPageResonseVo.setPicComplete("是");
                }
            }
        }
        Page<GetMaterialPageResonseVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 获取缩略图
     *
     * @param picInfo
     * @return
     * @throws ResultException
     */
    private String getOssUrl(String picInfo) throws ResultException {
        Object picAfter = ascmRedisHelper.get(RedisKeyManager.SPARE_PARTS_PIC_INFO.buildKey(picInfo));
        if (null == picAfter) {
            String ossUrl = imageService.getNewOssUrl(picInfo);
            if (StrUtil.isBlank(ossUrl)) {
                return "";
            }
            ascmRedisHelper.set(RedisKeyManager.SPARE_PARTS_PIC_INFO.buildKey(picInfo), ossUrl, 2, TimeUnit.HOURS);
            return ossUrl;
        }
        return String.valueOf(picAfter);
    }

    /**
     * 获取原图链接
     *
     * @param picInfo
     * @return
     * @throws ResultException
     */
    private String getRawOssUrl(String picInfo) throws ResultException {
        Object picAfter = ascmRedisHelper.get(RedisKeyManager.SPARE_PARTS_RAW_PIC_INFO.buildKey(picInfo));
        if (null == picAfter) {
            String ossUrl = imageService.getOssUrl(picInfo);
            if (StrUtil.isBlank(ossUrl)) {
                return "";
            }
            ascmRedisHelper.set(RedisKeyManager.SPARE_PARTS_RAW_PIC_INFO.buildKey(picInfo), ossUrl, 2, TimeUnit.HOURS);
            return ossUrl;
        }
        return String.valueOf(picAfter);
    }

    public Result<GetMaterialDetailResponseVo> detail(GetMaterialDetailRequestVo param) throws ResultException {
        BaseMaterials baseMaterials = baseMaterialsMapper.selectOne(new LambdaQueryWrapper<BaseMaterials>().eq(BaseMaterials::getId, param.getId()));
        Assert.isTrue(ObjectUtils.isNotEmpty(baseMaterials), "物料编码不存在！");
        GetMaterialDetailResponseVo getMaterialDetailResponseVo = BeanUtil.copyProperties(baseMaterials, GetMaterialDetailResponseVo.class);

        getMaterialDetailResponseVo.setPicLeft(getRawOssUrl(getMaterialDetailResponseVo.getPicLeft()));
        getMaterialDetailResponseVo.setPicRight(getRawOssUrl(getMaterialDetailResponseVo.getPicRight()));
        getMaterialDetailResponseVo.setPicBefore(getRawOssUrl(getMaterialDetailResponseVo.getPicBefore()));
        getMaterialDetailResponseVo.setPicAfter(getRawOssUrl(getMaterialDetailResponseVo.getPicAfter()));
        getMaterialDetailResponseVo.setPicOther(getRawOssUrl(getMaterialDetailResponseVo.getPicOther()));
        getMaterialDetailResponseVo.setPicStamp(getRawOssUrl(getMaterialDetailResponseVo.getPicStamp()));

        List<BzSupplierMaterial> bzSupplierMaterials = bzSupplierMaterialMapper.selectList(new LambdaQueryWrapper<BzSupplierMaterial>().eq(BzSupplierMaterial::getMaterialId, baseMaterials.getId()));
        if (CollectionUtils.isNotEmpty(bzSupplierMaterials)) {
            List<Long> supplierIds = bzSupplierMaterials.stream().map(BzSupplierMaterial::getSupplierId).collect(Collectors.toList());
            List<BaseSuppliers> baseSuppliers = baseSuppliersMapper.selectList(new LambdaQueryWrapper<BaseSuppliers>().in(BaseSuppliers::getId, supplierIds).orderByDesc(BaseSuppliers::getUpdateTime));
            List<SupplierItemVo> supplierItemVos = BeanUtil.copyToList(baseSuppliers, SupplierItemVo.class);
            for (SupplierItemVo supplierItemVo : supplierItemVos) {
                // 设置snp数量
                List<BzSupplierMaterial> supplierMaterials = bzSupplierMaterials.stream().filter(item -> item.getSupplierId().equals(supplierItemVo.getId())).collect(Collectors.toList());
                supplierItemVo.setSupplierSnp(supplierMaterials.get(0).getSupplierSnp());
            }
            getMaterialDetailResponseVo.setSupplierItemVos(supplierItemVos);
        }
        if (null != baseMaterials.getCheckCodeId()) {
            BaseCheckStandard baseCheckStandard = baseCheckStandardMapper.selectOne(new QueryWrapper<BaseCheckStandard>().lambda().eq(BaseCheckStandard::getId, baseMaterials.getCheckCodeId()));
            if (null != baseCheckStandard) {
                getMaterialDetailResponseVo.setCheckDocs(baseCheckStandard.getCheckDocs());
            }
        }
        return ResultUtil.success(getMaterialDetailResponseVo);
    }

    public Result<List<PackageInfoVo>> querySupplierPackage(QuerySupplierPackageDto param) {
        if (StrUtil.isBlank(param.getSupplierCode())) {
            return ResultUtil.failed("该物料不存在供应商");
        }
        BaseSuppliers baseSuppliers = baseSuppliersMapper.selectOne(new LambdaQueryWrapper<BaseSuppliers>().eq(BaseSuppliers::getSupplierCode, param.getSupplierCode()));
        List<BzSupplierPackageInfo> bzSupplierPackageInfos = bzSupplierPackageInfoMapper.selectList(new LambdaQueryWrapper<BzSupplierPackageInfo>().eq(BzSupplierPackageInfo::getMaterialId, param.getId()).eq(BzSupplierPackageInfo::getSupplierId, baseSuppliers.getId()));
        List<PackageInfoVo> list = new ArrayList<>();
        for (BzSupplierPackageInfo bzSupplierPackageInfo : bzSupplierPackageInfos) {
            PackageInfoVo packageInfoVo = BeanUtil.copyProperties(bzSupplierPackageInfo, PackageInfoVo.class);
            packageInfoVo.setRecycleStr(bzSupplierPackageInfo.getRecycle() == 1 ? "是" : "否");
            list.add(packageInfoVo);
        }
        return ResultUtil.success(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<String> importMaterialPicture(MaterialPictureFile param) {
        List<MaterialPictureItemVo> fileItems = param.getFileItems();
        if (CollectionUtils.isNotEmpty(fileItems)) {
            Map<String, List<MaterialPictureItemVo>> materialCodeMap = null;
            try {
                materialCodeMap = fileItems.stream().collect(Collectors.groupingBy(key -> key.getFileName().substring(0, key.getFileName().lastIndexOf("@"))));
            } catch (StringIndexOutOfBoundsException e) {
                log.warn(e.getMessage(), e);
                return ResultUtil.failed("文件名格式错误！");
            }
            Set<String> materialCodes = materialCodeMap.keySet();
            List<String> errMaterialCodes = new ArrayList<>();
            for (String materialCode : materialCodes) {
                BaseMaterials baseMaterials = baseMaterialsMapper.selectOne(new LambdaQueryWrapper<BaseMaterials>().eq(BaseMaterials::getMaterialCode, materialCode));
                log.info("BaseMaterialsService importMaterialPicture baseMaterials {}", JSON.toJSONString(baseMaterials));
                if (ObjectUtils.isEmpty(baseMaterials)) {
                    errMaterialCodes.add(materialCode);
                }
            }
            if (CollectionUtils.isNotEmpty(errMaterialCodes)) {
                // 不用报错，移除即可
                //return ResultUtil.failed("[" + StringUtils.join(errMaterialCodes, ",") + "]物料编号不存在！");
                errMaterialCodes.forEach(materialCodes::remove);
            }

            List<String> materialCodeList = new ArrayList<>();
            for (String materialCode : materialCodes) {
                List<MaterialPictureItemVo> materialPictureItemVos = materialCodeMap.get(materialCode);
                log.info("BaseMaterialsService importMaterialPicture materialPictureItemVos {}", JSON.toJSONString(materialPictureItemVos));
                BaseMaterials baseMaterials = null;
                try {
                    baseMaterials = getBaseMaterials(materialPictureItemVos);
                } catch (MalformedURLException | UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
                log.info("BaseMaterialsService importMaterialPicture baseMaterials {}", JSON.toJSONString(baseMaterials));
                int cnt = baseMaterialsMapper.update(baseMaterials, new LambdaQueryWrapper<BaseMaterials>().eq(BaseMaterials::getMaterialCode, materialCode));
                log.info("BaseMaterialsService importMaterialPicture cnt {}", cnt);
                if (cnt < 1) {
                    throw new RuntimeException("更新数据失败！");
                } else {
                    materialCodeList.add(materialCode);
                }
            }
            this.syncPic(materialCodeList);
            return ResultUtil.success("导入成功！");
        } else {
            return ResultUtil.failed("导入文件不能为空！");
        }

    }

    private BaseMaterials getBaseMaterials(List<MaterialPictureItemVo> materialPictureItemVos) throws MalformedURLException, UnsupportedEncodingException {
        BaseMaterials baseMaterials = new BaseMaterials();
        for (MaterialPictureItemVo fileItem : materialPictureItemVos) {
            String fileName = fileItem.getFileName();
            URL url = new URL(fileItem.getUrl());
            String path = url.getPath();
            path = path.substring(1);
            path = URLDecoder.decode(path, "UTF-8");
            delRedisKey(path);
            if (!fileName.contains("@")) {
                throw new RuntimeException("[" + fileName + "]文件名格式不正确！");
            }
            String suffix = fileName.substring(fileName.lastIndexOf("@"));
            if (suffix.contains("前")) {
                baseMaterials.setPicBefore(path);
            } else if (suffix.contains("后")) {
                baseMaterials.setPicAfter(path);
            } else if (suffix.contains("左")) {
                baseMaterials.setPicLeft(path);
            } else if (suffix.contains("右")) {
                baseMaterials.setPicRight(path);
            } else if (suffix.contains("钢印")) {
                baseMaterials.setPicStamp(path);
            } else if (suffix.contains("其它")) {
                baseMaterials.setPicOther(path);
            } else {
                throw new RuntimeException("[" + fileName + "]根据文件名无法识别位置！");
            }
        }
        return baseMaterials;
    }

    public Result<String> submit(SaveMaterialCodeDto param) {
        AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
        if (null == loginUser) {
            return ResultUtil.failed("用户未登录");
        }
        String name = loginUser.getName();
        String username = loginUser.getUsername();
        try {
            param.setPicBefore(getUrlPath(param.getPicBefore()));
            param.setPicAfter(getUrlPath(param.getPicAfter()));
            param.setPicLeft(getUrlPath(param.getPicLeft()));
            param.setPicRight(getUrlPath(param.getPicRight()));
            param.setPicOther(getUrlPath(param.getPicOther()));
            param.setPicStamp(getUrlPath(param.getPicStamp()));
        } catch (MalformedURLException | UnsupportedEncodingException e) {
            log.warn(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        boolean update = new LambdaUpdateChainWrapper<>(baseMaterialsMapper).eq(BaseMaterials::getMaterialCode, param.getMaterialCode())
                .set(BaseMaterials::getPicBefore, param.getPicBefore())
                .set(BaseMaterials::getPicAfter, param.getPicAfter())
                .set(BaseMaterials::getPicLeft, param.getPicLeft())
                .set(BaseMaterials::getPicRight, param.getPicRight())
                .set(BaseMaterials::getPicStamp, param.getPicStamp())
                .set(BaseMaterials::getPicOther, param.getPicOther())
                .set(BaseMaterials::getUpdateUserId, username)
                .set(BaseMaterials::getUpdateUserName, name).update();
        if (update) {
            if (StrUtil.isNotBlank(param.getPicBefore())) {
                delRedisKey(param.getPicBefore());
            }
            if (StrUtil.isNotBlank(param.getPicAfter())) {
                delRedisKey(param.getPicAfter());
            }
            if (StrUtil.isNotBlank(param.getPicLeft())) {
                delRedisKey(param.getPicLeft());
            }
            if (StrUtil.isNotBlank(param.getPicRight())) {
                delRedisKey(param.getPicRight());
            }
            if (StrUtil.isNotBlank(param.getPicStamp())) {
                delRedisKey(param.getPicStamp());
            }
            if (StrUtil.isNotBlank(param.getPicOther())) {
                delRedisKey(param.getPicOther());
            }
            this.syncPic(Collections.singletonList(param.getMaterialCode()));
        }
        return update ? ResultUtil.success("更新成功！") : ResultUtil.success("更新失败！");
    }

    private void delRedisKey(String param) {
        try {
            ascmRedisHelper.delete(RedisKeyManager.SPARE_PARTS_RAW_PIC_INFO.buildKey(param));
            ascmRedisHelper.delete(RedisKeyManager.SPARE_PARTS_PIC_INFO.buildKey(param));
        } catch (ResultException e) {
            log.error("删除redis的key异常 {} {}", param, e.getMessage());
        }
    }

    public String getUrlPath(String url) throws MalformedURLException, UnsupportedEncodingException {
        if (StringUtils.isNotEmpty(url)) {
            URL url1 = new URL(url);
            String path = url1.getPath();
            path = path.substring(1);
            if (!path.contains("@")) {
                return URLDecoder.decode(path, "UTF-8");
            } else {
                return path;
            }
        }
        return "";
    }

    public Result<String> deletePics(DeletePicsDto param) {
        // 没登陆调用可能报空指针
        String userId = ascmLoginUserHelper.getLoginUser().getUsername();
        String name = ascmLoginUserHelper.getLoginUser().getName();
        boolean remove = new LambdaUpdateChainWrapper<>(baseMaterialsMapper)
                .set(BaseMaterials::getPicBefore, "")
                .set(BaseMaterials::getPicAfter, "")
                .set(BaseMaterials::getPicLeft, "")
                .set(BaseMaterials::getPicRight, "")
                .set(BaseMaterials::getPicStamp, "")
                .set(BaseMaterials::getPicOther, "")
                .set(BaseMaterials::getUpdateUserName, name)
                .set(BaseMaterials::getUpdateUserId, userId).in(BaseMaterials::getId, param.getIds()).update();
        return remove ? ResultUtil.success("删除成功！") : ResultUtil.success("删除失败！");
    }

    public Page<GetMaterialPageResonseVo> exportItem(PageQuery<GetMaterialPageRequestDto> page) throws ResultException {
        // 获取总条数
        long total = baseMaterialsMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<GetMaterialPageResonseVo> returnList = baseMaterialsMapper.getPage(page.getParam(), startIndex, page.getSize());
        if (CollectionUtils.isNotEmpty(returnList)) {
            for (GetMaterialPageResonseVo getMaterialPageResonseVo : returnList) {
                // 原图
                getMaterialPageResonseVo.setPicLeft(imageService.getOssUrl(getMaterialPageResonseVo.getPicLeft()));
                getMaterialPageResonseVo.setPicRight(imageService.getOssUrl(getMaterialPageResonseVo.getPicRight()));
                getMaterialPageResonseVo.setPicBefore(imageService.getOssUrl(getMaterialPageResonseVo.getPicBefore()));
                getMaterialPageResonseVo.setPicAfter(imageService.getOssUrl(getMaterialPageResonseVo.getPicAfter()));
                getMaterialPageResonseVo.setPicOther(imageService.getOssUrl(getMaterialPageResonseVo.getPicOther()));
                getMaterialPageResonseVo.setPicStamp(imageService.getOssUrl(getMaterialPageResonseVo.getPicStamp()));
                // 是否完整字段处理
                if (StrUtil.isBlank(getMaterialPageResonseVo.getPicLeft()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicRight()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicBefore()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicAfter()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicOther()) ||
                        StrUtil.isBlank(getMaterialPageResonseVo.getPicStamp())
                ) {
                    getMaterialPageResonseVo.setPicComplete("否");
                } else {
                    getMaterialPageResonseVo.setPicComplete("是");
                }
            }
        }
        Page<GetMaterialPageResonseVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 备件分配标准分页查询
     *
     * @param page
     * @return
     */
    public Page<GetMaterialCheckPageResponseVo> getCheckPage(PageQuery<GetMaterialCheckPageRequestDto> page) {

        // 获取总条数
        long total = baseMaterialsMapper.getCheckPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<GetMaterialCheckPageResponseVo> returnList = baseMaterialsMapper.getCheckPage(page.getParam(), startIndex, page.getSize());
        Page<GetMaterialCheckPageResponseVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 删除匹配规则
     *
     * @param dto
     * @return
     */
    @Transactional
    public Result<String> delMatchingTies(GetMaterialCheckPageRequestDto dto) {
        List<Long> ids = dto.getIds();
        if (CollUtil.isEmpty(ids)) {
            return ResultUtil.failed("删除失败，必填为空。");
        }
        List<BaseMaterials> materialsList = new ArrayList<>();
        ids.forEach(item -> {
            BaseMaterials baseMaterials = new BaseMaterials();
            baseMaterials.setId(item);
            baseMaterials.setCheckCodeId(-1);
            materialsList.add(baseMaterials);
        });
        updateBatchById(materialsList);
        return ResultUtil.success("删除成功！");
    }

    /**
     * 导入单元包装
     *
     * @param file 导入文件
     */
    public ImportResponseDto importUnitPackage(MultipartFile file) {

        // 解析文件
        List<UnitPackageImportVo> unitPackageImportVoList = new ArrayList<>();

        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            // excel映射字段
            Map<String, String> alias = new HashMap<>();
            alias.put("物料编码", "materialCode");
            alias.put("是否单元包装", "unitPackage");
            excelReader.setHeaderAlias(alias);
            unitPackageImportVoList = excelReader.readAll(UnitPackageImportVo.class);
            alias.clear();
        } catch (IOException e) {
            String userInfo = JSON.toJSONString(ascmLoginUserHelper.getLoginUser());
            log.error("解析文件异常，请检查！ {} {}", userInfo, e.getMessage());
        }

        if (CollUtil.isEmpty(unitPackageImportVoList)) {
            throw new RuntimeException("文件不能为空！");
        }

        List<UnitPackageImportVo> successList = new ArrayList<>();
        List<UnitPackageImportVo> failList = new ArrayList<>();

        for (UnitPackageImportVo unitPackageImportVo : unitPackageImportVoList) {
            if (StrUtil.isBlank(unitPackageImportVo.getMaterialCode())) {
                unitPackageImportVo.setFailReason("物料编号不能为空");
                failList.add(unitPackageImportVo);
                continue;
            }
            if (StrUtil.isBlank(unitPackageImportVo.getUnitPackage()) || "否".equals(unitPackageImportVo.getUnitPackage())) {
                // 0表示否
                unitPackageImportVo.setUnitPackageNum(0);
            }
            if ("是".equals(unitPackageImportVo.getUnitPackage())) {
                // 1表示是
                unitPackageImportVo.setUnitPackageNum(1);
            }
            successList.add(unitPackageImportVo);
        }

        // 存入redis,响应结果
        ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
        accountImportResponseDTO.setFailCount(failList.size());
        accountImportResponseDTO.setSuccessCount(successList.size());

        String uuid = UUID.fastUUID().toString(true);
        accountImportResponseDTO.setFileCode(uuid);

        try {
            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_UNIT_PACKAGE_INFO.buildKey("fail", uuid), failList, 1, TimeUnit.HOURS);
            }
            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_UNIT_PACKAGE_INFO.buildKey("success", uuid), successList, 1, TimeUnit.HOURS);
            }
        } catch (ResultException e) {
            log.error("导出文件异常，请检查！ {} {}", uuid, e.getMessage());
        }

        return accountImportResponseDTO;
    }

    public Page<UnitPackageImportVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<UnitPackageImportVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_UNIT_PACKAGE_INFO.buildKey("success", accountSuccessRequestDTO.getOperationCode()), UnitPackageImportVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<UnitPackageImportVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<UnitPackageImportVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    public void exportFailUnitPackage(String operationCode, HttpServletResponse response) {
        log.info("BaseMaterialsService exportFailUnitPackage 开始导出 {}", operationCode);
        //获取redis中存储的失败数据
        try {
            List<UnitPackageImportVo> failResult = ascmRedisHelper
                    .getList(RedisKeyManager.IMPORT_UNIT_PACKAGE_INFO.buildKey("fail", operationCode), UnitPackageImportVo.class);
            //响应出去
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("单元包装校验失败数据导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), UnitPackageImportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseMaterialsService exportFailUnitPackage 导出异常！[{}]", e.getMessage());
        }
    }

    public Result<String> submitUnitPackage(String operationCode) throws ResultException {
        String username = ascmLoginUserHelper.getLoginUser().getName();
        log.info("BaseMaterialsService submitUnitPackage 开始添加数据 {}", operationCode);
        List<UnitPackageImportVo> successResult = ascmRedisHelper
                .getList(RedisKeyManager.IMPORT_UNIT_PACKAGE_INFO.buildKey("success", operationCode), UnitPackageImportVo.class);

        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();

        successResult.forEach(item -> {
            LambdaUpdateWrapper<BaseMaterials> luw = new LambdaUpdateWrapper<>();
            luw.eq(BaseMaterials::getMaterialCode, item.getMaterialCode());
            luw.set(BaseMaterials::getUnitPackage, item.getUnitPackageNum());
            luw.set(BaseMaterials::getUpdateUserName, username);

            boolean result = update(null, luw);
            successCount.addAndGet(result ? 1 : 0);
            failCount.addAndGet(result ? 1 : 0);
        });

        log.info("BaseMaterialsService submitUnitPackage 本次导入成功{}个 失败{}个", successCount.get(), failCount.get());

        if (successResult.size() != (successCount.get() + failCount.get())) {
            log.error("BaseMaterialsService submitUnitPackage 本次导入发生错误！{}", operationCode);
        }
        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", successCount.get(), failCount.get()));
    }

    public Result<String> editCheckInfo(String materialId, String checkCodeId) {
        String name = ascmLoginUserHelper.getLoginUser().getName();
        LambdaUpdateWrapper<BaseMaterials> luw = new LambdaUpdateWrapper<>();
        luw.eq(BaseMaterials::getId, materialId);
        luw.set(BaseMaterials::getCheckCodeId, checkCodeId);
        luw.set(BaseMaterials::getUpdateUserName, name);
        if (update(luw)) {
            return ResultUtil.success("更新成功！");
        }
        return ResultUtil.failed("更新失败！");
    }

    public ImageResponseDTO packageTempFile() {
        return imageService.getTempURL(packageTempFileId);
    }

    public void rulesTempFile(HttpServletResponse response) {
        // 先查询出所有的校验标准
        List<BaseCheckStandard> baseCheckStandardList = baseCheckStandardMapper
                .selectList(new LambdaQueryWrapper<BaseCheckStandard>().eq(BaseCheckStandard::getIsDelete, 0));
        List<BaseCheckStandardVo> baseCheckStandardVos = BeanUtil.copyToList(baseCheckStandardList, BaseCheckStandardVo.class);
        // 清理一下，避免OOM
        baseCheckStandardList.clear();

        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build()) {
            // 处理一下响应
            String fileName = "物料匹配校验规则导入模板 .xlsx";
            // 对文件名进行编码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            // 设置响应头
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 先构建第一页sheet
            WriteSheet writeSheet1 = EasyExcel.writerSheet("sheet1")
                    // 自动表头
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).head(ValidationRulesTempVo.class).build();
            // 写入空白模板
            excelWriter.write(new ArrayList<>(), writeSheet1);
            // 再构建第二页sheet
            WriteSheet writeSheet2 = EasyExcel.writerSheet("sheet2").head(BaseCheckStandardVo.class).build();
            // 写入校验标准数据
            excelWriter.write(baseCheckStandardVos, writeSheet2);
        } catch (IOException e) {
            log.error("rulesTempFile error {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public ImportResponseDto rulesImportCheck(MultipartFile file) {
        // 解析文件
        List<ValidationRulesImportVo> validationRulesImportVoList = new ArrayList<>();

        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            // excel映射字段
            Map<String, String> alias = new HashMap<>();
            alias.put("物料编码", "materialCode");
            alias.put("检验标准编码", "checkCode");
            alias.put("检验标准描述", "checkDesc");
            excelReader.setHeaderAlias(alias);
            validationRulesImportVoList = excelReader.readAll(ValidationRulesImportVo.class);
            alias.clear();
        } catch (IOException e) {
            String userInfo = JSON.toJSONString(ascmLoginUserHelper.getLoginUser());
            log.error("解析文件异常，请检查！ {} {}", userInfo, e.getMessage());
        }

        if (CollUtil.isEmpty(validationRulesImportVoList)) {
            throw new RuntimeException("文件不能为空！");
        }

        List<ValidationRulesImportVo> successList = new ArrayList<>();
        List<ValidationRulesImportVo> failList = new ArrayList<>();
        // TODO:导入大量数据可能会超时，就留给后续优化吧
        for (ValidationRulesImportVo validationRulesImportVo : validationRulesImportVoList) {
            if (StrUtil.isBlank(validationRulesImportVo.getMaterialCode())) {
                validationRulesImportVo.setFailReason("物料编码不能为空");
                failList.add(validationRulesImportVo);
                continue;
            }
            if (StrUtil.isBlank(validationRulesImportVo.getCheckCode())) {
                validationRulesImportVo.setFailReason("检验标准编码不能为空");
                failList.add(validationRulesImportVo);
                continue;
            }
            // 查询物料是否存在
            LambdaQueryWrapper<BaseMaterials> lqw = new LambdaQueryWrapper<>();
            lqw.eq(BaseMaterials::getMaterialCode, validationRulesImportVo.getMaterialCode());
            List<BaseMaterials> baseMaterials = list(lqw);
            if (CollUtil.isEmpty(baseMaterials)) {
                validationRulesImportVo.setFailReason("该物料编号不存在");
                failList.add(validationRulesImportVo);
                continue;
            }
            // 查询校验标准是否存在
            LambdaQueryWrapper<BaseCheckStandard> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseCheckStandard::getCheckCode, validationRulesImportVo.getCheckCode());
            List<BaseCheckStandard> baseCheckStandardList = baseCheckStandardMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(baseCheckStandardList)) {
                validationRulesImportVo.setFailReason("校验标准不存在");
                failList.add(validationRulesImportVo);
                continue;
            }
            validationRulesImportVo.setCheckCodeId(baseCheckStandardList.get(0).getId());
            successList.add(validationRulesImportVo);
        }

        // 存入redis,响应结果
        ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
        accountImportResponseDTO.setFailCount(failList.size());
        accountImportResponseDTO.setSuccessCount(successList.size());

        String uuid = UUID.fastUUID().toString(true);
        accountImportResponseDTO.setFileCode(uuid);

        try {
            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_CHECK_RULES_INFO.buildKey("fail", uuid), failList, 1, TimeUnit.HOURS);
            }
            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                ascmRedisHelper.set(RedisKeyManager.IMPORT_CHECK_RULES_INFO.buildKey("success", uuid), successList, 1, TimeUnit.HOURS);
            }
        } catch (ResultException e) {
            log.error("导出文件异常，请检查！ {} {}", uuid, e.getMessage());
        }

        return accountImportResponseDTO;
    }

    public Page<ValidationRulesImportVo> getSuccessRulesPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<ValidationRulesImportVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_CHECK_RULES_INFO.buildKey("success", accountSuccessRequestDTO.getOperationCode()), ValidationRulesImportVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<ValidationRulesImportVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<ValidationRulesImportVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    public void exportFailRules(String operationCode, HttpServletResponse response) {
        log.info("BaseMaterialsService exportFailRules 开始导出 {}", operationCode);
        //获取redis中存储的失败数据
        try {
            List<ValidationRulesImportVo> failResult = ascmRedisHelper
                    .getList(RedisKeyManager.IMPORT_CHECK_RULES_INFO.buildKey("fail", operationCode), ValidationRulesImportVo.class);
            //响应出去
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("备件校验规则匹配失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), ValidationRulesImportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseMaterialsService exportFailRules 导出异常！[{}]", e.getMessage());
        }
    }

    public Result<String> submitSuccessRules(String operationCode) throws ResultException {
        AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
        String name = loginUser.getName();
        String userId = loginUser.getUserId();
        log.info("BaseMaterialsService submitSuccessRules 开始添加数据 {}", operationCode);
        List<ValidationRulesImportVo> successResult = ascmRedisHelper
                .getList(RedisKeyManager.IMPORT_CHECK_RULES_INFO.buildKey("success", operationCode), ValidationRulesImportVo.class);

        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();

        successResult.forEach(item -> {
            LambdaUpdateWrapper<BaseMaterials> luw = new LambdaUpdateWrapper<>();
            luw.eq(BaseMaterials::getMaterialCode, item.getMaterialCode());
            luw.set(BaseMaterials::getCheckCodeId, item.getCheckCodeId());
            luw.set(BaseMaterials::getUpdateUserName, name);
            luw.set(BaseMaterials::getUpdateUserId, userId);
            boolean result = update(luw);
            successCount.addAndGet(result ? 1 : 0);
            failCount.addAndGet(result ? 1 : 0);
        });

        log.info("BaseMaterialsService submitSuccessRules 本次导入成功{}个 失败{}个", successCount.get(), failCount.get());

        if (successResult.size() != (successCount.get() + failCount.get())) {
            log.error("BaseMaterialsService submitSuccessRules 本次导入发生错误！{}", operationCode);
        }
        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", successCount.get(), failCount.get()));
    }
}
