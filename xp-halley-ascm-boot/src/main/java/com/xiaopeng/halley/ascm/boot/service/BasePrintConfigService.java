package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintConfig;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.mapper.BasePrintConfigMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xpeng.athena.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class BasePrintConfigService extends ServiceImpl<BasePrintConfigMapper, BasePrintConfig> {
	@Resource
	private BaseWarehouseMapper baseWarehouseMapper;

	public boolean create(BasePrintConfig request) {
		Assert.notBlank(request.getLgort(), "仓库编码不能为空");
		Assert.notBlank(request.getPrinterName(), "打印机名称不能为空");

		List<BasePrintConfig> printConfigs = this.lambdaQuery().eq(BasePrintConfig::getLgort, request.getLgort()).list();
		if (printConfigs.size() == 1 && isDefaultRoute(printConfigs.get(0))) {
			throw new BusinessException("仓库已配置默认打印机，如需配置路线请先删除");
		}
		if (!printConfigs.isEmpty() && isDefaultRoute(request)) {
			throw new BusinessException("如需配置默认打印机，请先删除配置的线路");
		}

		printConfigs.stream().filter(e -> StrUtil.equals(e.getRoute(), request.getRoute())).findAny().ifPresent(e -> {
			throw new BusinessException("远程打印配置重复！");
		});

		BaseWarehouse baseWarehouse = baseWarehouseMapper.selectByLgort(request.getLgort());
		request.setLgobe(baseWarehouse.getLgobe());
		return this.save(request);
	}

	/**
	 * 获取打印机名称
	 * 当某个配置路线为空时且只有1个配置时，该仓库所有路线都匹配这个打印机
	 * @param lgort 仓库号
	 * @param route 路线
	 * @return
	 */
	public String getPrinterName(String lgort, String route) {
		List<BasePrintConfig> printConfigs = this.lambdaQuery().eq(BasePrintConfig::getLgort, lgort).list();
		if (printConfigs.size() == 1 && isDefaultRoute(printConfigs.get(0))) {
			return printConfigs.get(0).getPrinterName();
		}
		String compareRoute = StrUtil.nullToEmpty(route);
		BasePrintConfig basePrintConfig = CollUtil.findOne(printConfigs, e -> StrUtil.equals(e.getRoute(), compareRoute));
		if (basePrintConfig == null) {
			return null;
		}
		return basePrintConfig.getPrinterName();
	}

	public boolean isDefaultRoute(BasePrintConfig basePrintConfig) {
		return StrUtil.isEmpty(basePrintConfig.getRoute());
	}
}