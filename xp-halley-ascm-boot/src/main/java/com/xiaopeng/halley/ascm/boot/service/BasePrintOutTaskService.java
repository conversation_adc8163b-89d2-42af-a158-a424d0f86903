package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.enums.PrintTaskStatusEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.TriggerClient;
import com.xiaopeng.halley.ascm.boot.config.PrintConfig;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintOutTask;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import com.xiaopeng.halley.ascm.boot.mapper.BasePrintOutTaskMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.utils.PdfHelper;
import com.xiaopeng.halley.ascm.boot.utils.VelocityHelper;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.awt.*;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BasePrintOutTaskService extends ServiceImpl<BasePrintOutTaskMapper, BasePrintOutTask> {
	@Resource
	private PrintConfig printConfig;
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private BasePrintOutTaskMapper basePrintOutTaskMapper;
	@Resource
	private BaseFeeStandardService baseFeeStandardService;
	@Resource
	private BzErpReqMaterialRelService bzErpReqMaterialRelService;

	public InputStream createPdf(String waybillCode, Date printTime) throws Exception {
		StopWatch stopWatch = new StopWatch();

		stopWatch.start("查询数据");
		// 构建参数
		BzWayBillPageVo bzWaybill = BeanUtil.toBean(bzWaybillService.getByCode(waybillCode), BzWayBillPageVo.class);
		List<BoxParamDto> boxList = bzWaybillService.boxesByWaybill(new BoxParamVo().setPageSize(3000).setBzWaybillId(bzWaybill.getId())).getRecords();
		// 计算重量
		for (BoxParamDto boxParamDto : boxList) {
			BigDecimal weight = baseFeeStandardService.calWeight(BigDecimal.valueOf(boxParamDto.getActualVolume()));
			boxParamDto.setBoxWeight(weight.doubleValue());
		}
		// 设置交货单号
		String deliveryOrderCode = boxList.stream().map(e -> StrUtil.split(e.getDeliveryOrderCode(), ","))
				.filter(Objects::nonNull)
				.flatMap(List::stream).distinct().collect(Collectors.joining(";"));
		bzWaybill.setDeliveryOrderCode(deliveryOrderCode);

		List<String> boxCodes = boxList.stream().map(BoxParamDto::getBoxCode).collect(Collectors.toList());
		List<BzErpReqMaterialRel> materialList = boxCodes.isEmpty() ? new ArrayList<>() : bzErpReqMaterialRelService.lambdaQuery().in(BzErpReqMaterialRel::getBoxCode, boxCodes).list();
		for (BzErpReqMaterialRel item : materialList) {
			// 设置所属箱子的包装类型
			boxList.stream().filter(e -> e.getBoxCode().equals(item.getBoxCode()))
					.findFirst().ifPresent(box -> item.setPackageType(box.getPackageType()));
		}
		stopWatch.stop();

		stopWatch.start("构建二维码和logo");
		HashMap<String, Object> bindingMap = new HashMap<>();
		bindingMap.put("waybill", bzWaybill);
		bindingMap.put("boxList", boxList);
		bindingMap.put("materialList", materialList);
		bindingMap.put("printTime", DateUtil.formatDateTime(printTime));
		bindingMap.put("total", materialList.stream().mapToInt(BzErpReqMaterialRel::getQuantity).sum());
		double totalVolume = boxList.stream().mapToDouble(BoxParamDto::getActualVolume).sum();
		bindingMap.put("totalVolume", new BigDecimal(totalVolume).setScale(3, RoundingMode.HALF_UP));
		// 构建运单二维码
		String url = printConfig.getUrl() + bzWaybill.getWaybillCode();
		bindingMap.put("imageData", QrCodeUtil.generateAsBase64(url, new QrConfig(), "png"));
		// 构建logo
		Image logoImage = ImgUtil.read(new ClassPathResource("image/logo.png").getInputStream());
		bindingMap.put("logoData", ImgUtil.toBase64DataUri(logoImage, "png"));
		bindingMap.put("DateUtil", new DateUtil());
		stopWatch.stop();

		stopWatch.start("html转成pdf");
		String html1 = VelocityHelper.render("template/waybill_delivery.html.vm", bindingMap);
		String html2 = VelocityHelper.render("template/waybill_box.html.vm", bindingMap);
		ByteArrayOutputStream result = new ByteArrayOutputStream();
		PdfHelper.merge(html1, html2, result);
		stopWatch.stop();

		log.info("生成pdf耗时: {}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
		return new ByteArrayInputStream(result.toByteArray());
	}

	public Page<BasePrintResponseDto> page(PageQuery<BasePrintRequestDto> page) {

		// 新分页查询
		LambdaQueryWrapper<BasePrintOutTask> queryWrapper = new LambdaQueryWrapper<BasePrintOutTask>().eq(BasePrintOutTask::getIsDelete, 0);

		if (StrUtil.isNotBlank(page.getParam().getId())) {
			queryWrapper.eq(BasePrintOutTask::getId, page.getParam().getId());
		}

		if (StrUtil.isNotBlank(page.getParam().getDeliveryOrderCode())) {
			queryWrapper.like(BasePrintOutTask::getDeliveryOrderCode, page.getParam().getDeliveryOrderCode());
		}

		if (StrUtil.isNotBlank(page.getParam().getWaybillCode())) {
			queryWrapper.like(BasePrintOutTask::getWaybillCode, page.getParam().getWaybillCode());
		}

		if (StrUtil.isNotBlank(page.getParam().getShopCode())) {
			queryWrapper.like(BasePrintOutTask::getShopCode, page.getParam().getShopCode());
		}

		if (StrUtil.isNotBlank(page.getParam().getLgort())) {
			queryWrapper.like(BasePrintOutTask::getLgort, page.getParam().getLgort());
		}

		if (ObjectUtil.isNotNull(page.getParam().getStatus())) {
			queryWrapper.eq(BasePrintOutTask::getStatus, page.getParam().getStatus());
		}

		queryWrapper.orderByDesc(BasePrintOutTask::getCreateTime);

		IPage<BasePrintOutTask> pageResult = this.basePrintOutTaskMapper.selectPage(page.toPage(), queryWrapper);

		List<BasePrintResponseDto> returnList = new ArrayList<>();

		pageResult.getRecords().forEach(item -> {
			BasePrintResponseDto newItem = BeanUtil.copyProperties(item, BasePrintResponseDto.class);

			newItem.setCreateTimeShow(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm"));

			returnList.add(newItem);
		});

		return new Page<BasePrintResponseDto>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())
				.setRecords(returnList);

	}

	public void cancelPrint(BasePrintCancelRequestDto cancelDto) throws ResultException {

		//修改
		BasePrintOutTask basePrintOutTask = basePrintOutTaskMapper.selectOne(new LambdaQueryWrapper<BasePrintOutTask>()
				.eq(BasePrintOutTask::getId, cancelDto.getId())
				.eq(BasePrintOutTask::getIsDelete, 0));
		if (basePrintOutTask.getStatus() != 0) {
			throw new ResultException(500, "无法取消任务");
		}
		int update = basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>().eq(BasePrintOutTask::getId, basePrintOutTask.getId()).set(BasePrintOutTask::getStatus, 3));

		if (update != 1) {
			throw new ResultException(500, "无法取消任务");
		}

	}

	public void print(BasePrintActionRequestDto page) throws ResultException {
		// 查询出来，看看什么状态
		LambdaQueryWrapper<BasePrintOutTask> wrapper = new LambdaQueryWrapper<>();
		wrapper.in(BasePrintOutTask::getId, page.getIds());
		wrapper.eq(BasePrintOutTask::getStatus, PrintTaskStatusEnum.PRINTING.getValue());
		List<BasePrintOutTask> resultList = basePrintOutTaskMapper.selectList(wrapper);

		if (!resultList.isEmpty()) {
			throw new ResultException(500, "打印中任务不能重复打印");
		}
		AscmLoginUser loginUser = SpringUtil.getBean(AscmLoginUserHelper.class).getLoginUser();
		this.lambdaUpdate().in(BasePrintOutTask::getId, page.getIds())
				.set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.UN_PRINTED.getValue())
				.set(BasePrintOutTask::getRetryCount, 0)
				.set(BasePrintOutTask::getBaseFileId, null)
				.set(BasePrintOutTask::getTriggerClient, TriggerClient.PC.name())
				.set(BasePrintOutTask::getCreateUserId, loginUser.getUserId())
				.set(BasePrintOutTask::getCreateUserName, loginUser.getName())
				.set(BasePrintOutTask::getCreateTime, new Date())
				.set(BasePrintOutTask::getUpdateTime, new Date())
				.update();
	}
}
