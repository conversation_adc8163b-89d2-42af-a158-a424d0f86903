package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseRepairCenter;
import com.xiaopeng.halley.ascm.boot.mapper.BaseRepairCenterMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/9/19 11:30
 */
@Slf4j
@Service
public class BaseRepairCenterService extends ServiceImpl<BaseRepairCenterMapper, BaseRepairCenter> {

    /**
     * 新增和更新
     *
     * @param dto
     * @return
     */
    public Result updateOrAdd(BaseRepairCenterDto dto) {
        log.info("BaseRepairCenterService updateOrAdd {}", JSON.toJSONString(dto));
        Integer updateOrAdd = dto.getUpdateOrAdd();
        BaseRepairCenter baseRepairCenter = BeanUtil.copyProperties(dto, BaseRepairCenter.class);
        // 如果是0说明这个是更新
        if (updateOrAdd == 0 && null != dto.getId()) {
            int updateResult = this.baseMapper.updateById(baseRepairCenter);
            if (1 == updateResult) {
                return ResultUtil.success("更新成功！");
            }
        } else if (updateOrAdd == 1) {
            // 是1说明是新增，需要先判断表中是否存在数据
            LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseRepairCenter::getRepairCenterNum, dto.getRepairCenterNum()).eq(BaseRepairCenter::getIsDelete, 0);
            BaseRepairCenter baseRepairCenterDb = this.baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseRepairCenterDb)) {
                //是空就表明是新增
                int insertResult = this.baseMapper.insert(baseRepairCenter);
                if (1 == insertResult) {
                    return ResultUtil.success("新增成功！");
                }
            } else {
                //不是空就表明是添加
                baseRepairCenter.setId(baseRepairCenterDb.getId());
                int updateResult = this.baseMapper.updateById(baseRepairCenter);
                if (1 == updateResult) {
                    return ResultUtil.success("更新成功！");
                }
            }
        }

        return ResultUtil.failed("参数有误！");
    }

    /**
     * 维修中心分页查询
     *
     * @param page
     * @return
     */
    public Page<BaseRepairCenterVo> getPage(PageQuery<BaseRepairCenterDto> page) {
        log.info("BaseRepairCenterService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseRepairCenterVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        Page<BaseRepairCenterVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 校验字段
     *
     * @param code
     * @return
     */
    public Result getRepairCenterNum(String code) {
        LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseRepairCenter::getRepairCenterNum, code).eq(BaseRepairCenter::getIsDelete, 0);
        BaseRepairCenter baseRepairCenter = this.baseMapper.selectOne(queryWrapper);
        if (BeanUtil.isNotEmpty(baseRepairCenter)) {
            return ResultUtil.failed("维修中心编码已存在");
        }
        return ResultUtil.success("success");
    }
}
