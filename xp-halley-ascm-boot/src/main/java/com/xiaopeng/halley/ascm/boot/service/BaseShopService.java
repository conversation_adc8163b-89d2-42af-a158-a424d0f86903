package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.AccountSuccessRequestDTO;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopDto;
import com.xiaopeng.halley.ascm.boot.dto.BaseShopVo;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.dto.importExport.BaseShopBatteryExport;
import com.xiaopeng.halley.ascm.boot.dto.importExport.BaseShopExport;
import com.xiaopeng.halley.ascm.boot.dto.importExport.BaseShopImport;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseShop;
import com.xiaopeng.halley.ascm.boot.mapper.BaseShopMapper;
import com.xiaopeng.halley.ascm.boot.utils.ObjectUtil;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:52
 */
@Slf4j
@Service
public class BaseShopService extends ServiceImpl<BaseShopMapper, BaseShop> {

    @Value("${fileTemp.shop.tempFileId}")
    private String tempFileId;

    @Value("${fileTemp.shopBattery.tempFileId}")
    private String batteryTempFile;
    @Resource
    private ImageService imageService;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Resource
    private AscmRedisHelper ascmRedisHelper;

    //与excel表建立映射关系
    public static Map<String, String> getAlias() {
        Map<String, String> alias = new HashMap<>();

        alias.put("门店编码（必填）", "shopCode");
        alias.put("门店名称（必填）", "shopName");
        alias.put("门店省份（必填）", "shopProvince");
        alias.put("门店城市（必填）", "shopCity");
        alias.put("门店地址（必填）", "shopAddress");
        alias.put("联系电话（必填）", "contactNum");
        alias.put("联系人（必填）", "contactPerson");
        alias.put("经度", "lat");
        alias.put("纬度", "lng");
        alias.put("围栏范围", "fenceRange");

        return alias;
    }

    /**
     * 状态反转
     *
     * @param status
     * @return
     */
    public static Integer inversionOfStatus(Integer status) {
        if (0 == status) {
            return 1;
        } else if (1 == status) {
            return 0;
        } else {
            return null;
        }
    }

    //与excel表建立映射关系
    public static Map<String, String> getBatteryAlias() {
        Map<String, String> alias = new HashMap<>();

        alias.put("骁龙门店编码", "xlShopCode");
        alias.put("装运联系人", "cargoContact");
        alias.put("联系电话", "cargoContactPhone");
        alias.put("区域", "region");

        return alias;
    }

    /**
     * 门店更新模板下载
     *
     * @return
     */
    public ImageResponseDTO tempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(tempFileId);
        return returnVO;
    }

    /**
     * 门店数据导出
     *
     * @param response
     */
    public void shopExport(HttpServletResponse response) {
        List<BaseShop> baseShops = baseMapper.selectList(null);
        List<BaseShopExport> BaseShopExports = new ArrayList<>();
        for (BaseShop baseShop : baseShops) {
            //属性转换一下
            BaseShopExport baseShopExport = BeanUtil.copyProperties(baseShop, BaseShopExport.class);
            BaseShopExports.add(baseShopExport);
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("门店数据表导出", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BaseShopExport.class).sheet("sheet").doWrite(BaseShopExports);
        } catch (Exception e) {
            log.error("BzDeliveryLogService export 导出异常！[{}]", e.toString());
            log.error("BzDeliveryLogService export 导出异常！[{}]", e);
        }
    }

    /**
     * 门店数据导入
     *
     * @param file
     * @return
     */
    public ImportResponseDto shopImport(MultipartFile file) throws ResultException {
        //获取文件大小，对传入文件的大小做现在
        long fileSize = file.getSize();
        if (1048576 < fileSize) {
            log.info("文件大小超过1M");
            throw new ResultException(500, "文件大小超过1M");
        }
        //获取文件名称
        String originalFileName = file.getOriginalFilename();
        //获取最后一个.的位置
        int lastIndexOf = originalFileName.lastIndexOf(".");
        //获取文件的后缀名 .xlsx  .xls
        String suffix = originalFileName.substring(lastIndexOf);

        String prefix = originalFileName.substring(0, lastIndexOf);
        //对文件类型做判断
        if (!".xlsx".equals(suffix) && !".xls".equals(suffix)) {
            log.info("文件类型不合格，请获取模板后正确填入");
            throw new ResultException(500, "文件类型不合格，请获取模板后正确填入");
        }

        //List<MutilFreezeImportVO> freezeImportVOList;

        // 这里的错误和成功需要排序
        List<BaseShopImport> failureFiles = new ArrayList<>();
        List<BaseShopExport> baseShopExports;
        //解析excel
        try {
            ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
            Map<String, String> alias = getAlias();
            excelReader.setHeaderAlias(alias);
            baseShopExports = excelReader.readAll(BaseShopExport.class);

            log.info("baseShopExports size [{}]", baseShopExports.size());
            log.info("baseShopExports list [{}]", JSONObject.toJSONString(baseShopExports));
        } catch (IOException e) {
            log.error("文件解析失败 [{}]", e.getMessage());
            log.error("文件解析失败");
            throw new ResultException(500, "导入文件解析失败");
        }
        //解析出的文件不能为空
        if (CollUtil.isNotEmpty(baseShopExports)) {
            for (BaseShopExport baseShopExport : baseShopExports) {
                //必填字段做非空判断
                boolean result = ObjectUtil.checkObjFieldsIsNotNull(baseShopExport, Arrays.asList("lat", "lon", "fenceRange"));
                if (result) {
                    //做更新操作
                    BaseShop baseShop = BeanUtil.copyProperties(baseShopExport, BaseShop.class);
                    //添加更新人的id和名字
                    baseShop.setUpdateUserId(ascmLoginUserHelper.getLoginUser().getUserId());
                    baseShop.setUpdateUserName(ascmLoginUserHelper.getLoginUser().getUsername());
                    //
                    //门店编码不可以随便更改吧？如果上传者随意更改就会出问题的
                    baseMapper.update(baseShop, new UpdateWrapper<BaseShop>().eq("shop_code", baseShop.getShopCode()));
                } else {
                    //不做更新操作，记录到失败文件后备打印
                    BaseShopImport baseShopImport = BeanUtil.copyProperties(baseShopExport, BaseShopImport.class);
                    baseShopImport.setFailureReason("必填字段为空");
                    failureFiles.add(baseShopImport);
                }
            }
        }
        ImportResponseDto importResponseDto = new ImportResponseDto();
        importResponseDto.setFailCount(failureFiles.size());

        return importResponseDto;
    }

    /**
     * 门店数据分页查询
     *
     * @param page 查询参数
     * @return 查询结果
     */
    public Page<BaseShopVo> getPage(PageQuery<BaseShopDto> page) {

        log.info("BaseShopService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseShopVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        Page<BaseShopVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;

    }

    /**
     * 更新门店状态
     *
     * @param id
     * @param status
     * @return
     */
    public Result updateStatus(String id, Integer status) {

        LambdaUpdateWrapper<BaseShop> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(BaseShop::getId, id)
                .eq(BaseShop::getIsDelete, 0)
                .eq(BaseShop::getStatus, inversionOfStatus(status))
                .set(BaseShop::getStatus, status);
        int updateResult = this.baseMapper.update(null, updateWrapper);
        if (1 == updateResult) {
            return ResultUtil.success("更新状态成功！");
        }
        return ResultUtil.failed("更新状态失败！");
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    public ImportResponseDto batteryImportFile(MultipartFile file) throws Exception {

        //成功的数据
        List<BaseShopBatteryExport> successList = new Vector<>();

        //失败的数据
        List<BaseShopBatteryExport> failList = new Vector<>();

        //读取excel
        ExcelReader excelReader = ExcelUtil.getReader(file.getInputStream());
        Map<String, String> alias = getBatteryAlias();
        excelReader.setHeaderAlias(alias);
        List<BaseShopBatteryExport> baseShopBatteryExports = excelReader.readAll(BaseShopBatteryExport.class);

        //非空校验
        if (CollUtil.isEmpty(baseShopBatteryExports)) {
            throw new ResultException(500, "导入内容为空！");
        }

        //先查询出所有的骁龙门店编码，用于校验是否存在
        LambdaQueryWrapper<BaseShop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BaseShop::getIsDelete, 0);
        List<BaseShop> baseShopList = this.baseMapper.selectList(queryWrapper);
        Set<String> xlShopCodes = baseShopList.stream().map(BaseShop::getXlShopCode).collect(Collectors.toSet());

        //用于校验重复字段
        List<String> repeatXlShopCode = new Vector<>();
        //并行校验
        baseShopBatteryExports.parallelStream().forEach(item -> {
            //骁龙门店编码校验
            if (!xlShopCodes.contains(item.getXlShopCode())) {
                item.setFailedReason("骁龙门店编码不存在");
                failList.add(item);
                return;
            }

            //区域
            if (null == item.getRegion()) {
                item.setFailedReason("区域字段不能为空");
                failList.add(item);
                return;
            }
            //装货联系人
            if (null == item.getCargoContact()) {
                item.setFailedReason("装运联系人字段不能为空");
                failList.add(item);
                return;
            }
            //装货联系人电话
            if (null == item.getCargoContactPhone()) {
                item.setFailedReason("联系电话字段不能为空");
                failList.add(item);
                return;
            }
            //校验重复的骁龙门店编码
            if (repeatXlShopCode.contains(item.getXlShopCode())) {
                //有就说明是重复的
                item.setFailedReason("重复的骁龙门店编码");
                failList.add(item);
                return;
            } else {
                //没有就添加进这个集合
                repeatXlShopCode.add(item.getXlShopCode());
            }
            //校验通添加到成功的集合
            successList.add(item);
        });

        //构造响应体
        ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
        accountImportResponseDTO.setFailCount(failList.size());
        accountImportResponseDTO.setSuccessCount(successList.size());

        String uuid = UUID.randomUUID().toString();
        accountImportResponseDTO.setFileCode(uuid);

        if (!failList.isEmpty()) {
            log.info("创建错误导出码 [{}]", uuid);
            log.info("错误内容为 {}", JSON.toJSONString(failList));
            // 存入redis
            ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_SHOP_BATTERY_DATA.buildKey("fail", uuid), failList, 16, TimeUnit.HOURS);
        }

        if (!successList.isEmpty()) {
            log.info("创建成功导出码 [{}]", uuid);
            // 存入redis
            ascmRedisHelper.set(RedisKeyManager.IMPORT_BASE_SHOP_BATTERY_DATA.buildKey("success", uuid), successList, 16, TimeUnit.HOURS);
        }

        return accountImportResponseDTO;
    }

    /**
     * 更新或添加
     *
     * @param dto
     * @return
     */
    public Result updateOrAdd(BaseShopDto dto) {
        log.info("BaseShopService updateOrAdd {}", JSON.toJSONString(dto));
        Integer updateOrAdd = dto.getUpdateOrAdd();
        if (0 == updateOrAdd && null != dto.getId()) {
            //是0表明是更新，所以必须带上id
            BaseShop baseShop = BeanUtil.copyProperties(dto, BaseShop.class);
            int updateResult = this.baseMapper.updateById(baseShop);
            if (1 == updateResult) {
                return ResultUtil.success("更新成功！");
            }
        } else if (1 == updateOrAdd) {
            //是1表明是新增
            BaseShop baseShop = BeanUtil.copyProperties(dto, BaseShop.class);
            LambdaQueryWrapper<BaseShop> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseShop::getShopCode, dto.getShopCode()).eq(BaseShop::getIsDelete, 0);
            BaseShop baseShopDb = this.baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseShopDb)) {
                //没有找到，说明是新增
                int insertResult = this.baseMapper.insert(baseShop);
                if (1 == insertResult) {
                    return ResultUtil.success("添加成功！");
                }
            } else {
                //有说明是更新
                baseShop.setId(baseShopDb.getId());
                int updateResult = this.baseMapper.updateById(baseShop);
                if (1 == updateResult) {
                    return ResultUtil.success("更新成功！");
                }
            }
        }
        return ResultUtil.failed("参数有误！");
    }

    public Page<BaseShopBatteryExport> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseShopBatteryExport> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_SHOP_BATTERY_DATA.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseShopBatteryExport.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BaseShopBatteryExport> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseShopBatteryExport> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    public void downloadFailFile(PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        String operationCode = dto.getParam().getOperationCode();

        log.info("BaseShopService downloadFailFile 开始导出 {}", operationCode);

        //获取redis中存储的失败数据
        List<BaseShopBatteryExport> failResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_SHOP_BATTERY_DATA.buildKey("fail", operationCode), BaseShopBatteryExport.class);
        log.info("BaseShopService downloadFailFile {}", JSON.toJSONString(failResult));
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("导入电池装运联系人批量导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BaseShopBatteryExport.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseBatteryProjectService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    @Transactional
    public Result importData(String operationCode) throws ResultException {

        log.info("BaseShopService importData 开始添加数据 {}", operationCode);
        List<BaseShopBatteryExport> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_BASE_SHOP_BATTERY_DATA.buildKey("success", operationCode), BaseShopBatteryExport.class);

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        successResult.stream().forEach(item -> {

            LambdaQueryWrapper<BaseShop> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseShop::getXlShopCode, item.getXlShopCode()).eq(BaseShop::getIsDelete, 0).eq(BaseShop::getStatus, 0);
            BaseShop baseShopDb = this.baseMapper.selectOne(queryWrapper);

            if (BeanUtil.isEmpty(baseShopDb)) {
                // 如果是空，表明是新增
                BaseShop baseShop = BeanUtil.copyProperties(item, BaseShop.class);
                int insertResult = this.baseMapper.insert(baseShop);
                addCount.addAndGet(insertResult);
            } else {
                // 不是空说明是修改
                baseShopDb.setCargoContact(item.getCargoContact());
                baseShopDb.setCargoContactPhone(item.getCargoContactPhone());
                baseShopDb.setRegion(item.getRegion());
                //  避免自动填充失效，设置为""
                baseShopDb.setUpdateUserName("");
                int updateResult = this.baseMapper.updateById(baseShopDb);
                updateCount.addAndGet(updateResult);
            }
        });

        log.info("BaseBatteryProjectService importData 本次导入新增{}个 更新{}个", addCount.get(), updateCount.get());

        if (successResult.size() != (addCount.get() + updateCount.get())) {
            throw new RuntimeException("BaseBatteryProjectService importData 本次导入发生错误！");
        }

        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", addCount.get(), updateCount.get()));

    }

    /**
     * 门店电池联系人模板下载
     *
     * @return
     */
    public ImageResponseDTO batteryTempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(batteryTempFile);
        return returnVO;
    }
}
