package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.BaseShop;
import com.xiaopeng.halley.ascm.boot.entity.BaseTransportTimeliness;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BaseWaybillRules;
import com.xiaopeng.halley.ascm.boot.listener.BaseTransportTimelinessListener;
import com.xiaopeng.halley.ascm.boot.mapper.BaseShopMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BaseTransportTimelinessMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWaybillRulesMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Author：huqizhi
 * @Date：2023/8/7 10:02
 */
@Service
@Slf4j
public class BaseTransportTimelinessService extends ServiceImpl<BaseTransportTimelinessMapper, BaseTransportTimeliness> {

    @Value("${fileTemp.TransportTimeliness.tempFileId}")
    private String tempFileId;
    @Resource
    private ImageService imageService;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
    @Resource
    private BaseShopMapper baseShopMapper;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private BaseWaybillRulesMapper baseWaybillRulesMapper;

    /**
     * 运输时效分页查询
     *
     * @param page 查询参数
     * @return 查询结果
     */
    public Page<BaseTransportTimelinessVo> getPage(PageQuery<BaseTransportTimelinessDto> page) {
        log.info("BaseTransportTimelinessService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseTransportTimelinessVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        Page<BaseTransportTimelinessVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 新增或添加
     *
     * @param dto 新增或添加的实体
     * @return 新增或添加结果
     */
    @Transactional
    public Result updateOrAdd(BaseTransportTimelinessDto dto) {
        //添加或更新标识
        Integer updateOrAdd = dto.getUpdateOrAdd();
        log.info("BaseTransportTimelinessService updateOrAdd {}", updateOrAdd);
        if (updateOrAdd == 0) {
            BaseTransportTimeliness baseTransportTimeliness = BeanUtil.copyProperties(dto, BaseTransportTimeliness.class);
            int i = this.baseMapper.updateById(baseTransportTimeliness);
            if (1 == i) {
                updateRuleTime(dto);
                return ResultUtil.success("更新成功!");
            }
            return ResultUtil.success("更新失败!");
        } else if (updateOrAdd == 1) {
            BaseTransportTimeliness baseTransportTimeliness = BeanUtil.copyProperties(dto, BaseTransportTimeliness.class);
            LambdaQueryWrapper<BaseTransportTimeliness> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseTransportTimeliness::getShopCity, baseTransportTimeliness.getShopCity());
            queryWrapper.eq(BaseTransportTimeliness::getWarehouseCity, baseTransportTimeliness.getWarehouseCity());
            queryWrapper.eq(BaseTransportTimeliness::getTransportType, baseTransportTimeliness.getTransportType());
            BaseTransportTimeliness baseTransportTimelinessInDb = this.baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseTransportTimelinessInDb)) {
                int insert = this.baseMapper.insert(baseTransportTimeliness);
                if (1 == insert) {
                    updateRuleTime(dto);
                    return ResultUtil.success("添加成功!");
                }
                return ResultUtil.success("添加失败!");
            } else {
                baseTransportTimeliness.setId(baseTransportTimelinessInDb.getId());
                baseTransportTimeliness.setUpdateTime(new Date());
                int i = this.baseMapper.updateById(baseTransportTimeliness);
                if (1 == i) {
                    updateRuleTime(dto);
                    return ResultUtil.success("更新成功!");
                }
                return ResultUtil.success("更新失败!");
            }
        } else {
            return ResultUtil.success("参数有误!");
        }
    }

    private void updateRuleTime(BaseTransportTimelinessDto dto) {
        // 查询出运单匹配规则对应规则的id
        List<String> ruleId = baseWaybillRulesMapper.selectRules(dto);
        for (String id : ruleId) {
            // 根据id,更新匹配规则的时效
            BaseWaybillRules baseWaybillRule = new BaseWaybillRules();
            baseWaybillRule.setId(Long.valueOf(id));
            baseWaybillRule.setPathExpiry(Integer.valueOf(dto.getTransportTime()));
            int result = baseWaybillRulesMapper.updateById(baseWaybillRule);
            log.info("BaseTransportTimelinessService updateOrAdd 运输时效更新结果：{}", result);
        }
    }

    /**
     * 运输时效模板下载
     *
     * @return 模板的下载路径名称等
     */
    public ImageResponseDTO tempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(tempFileId);
        return returnVO;
    }

    /**
     * 导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    public ImportResponseDto importFile(MultipartFile file) {

        BaseTransportTimelinessListener baseTransportTimelinessListener = new BaseTransportTimelinessListener();

        //存放读取的数据
        List<BaseTransportTimelinessExportVo> importDTOList = new ArrayList<>();

        //成功的数据
        List<BaseTransportTimelinessExportVo> successList = new Vector<>();

        //失败的数据
        List<BaseTransportTimelinessExportVo> failList = new Vector<>();

        try {
            //读取 excel 内容
            EasyExcel.read(file.getInputStream(), BaseTransportTimelinessExportVo.class, baseTransportTimelinessListener).sheet().doRead();
            //获取读取到的内容
            importDTOList = baseTransportTimelinessListener.getList();
            //检验
            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }
            //查询出所有仓库的城市
            Set<String> warehouseCities = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0))
                    .stream().map(BaseWarehouse::getWarehouseCity).collect(Collectors.toSet());
            //查询出所有门店的城市
            Set<String> shopCities = baseShopMapper.selectList(new QueryWrapper<BaseShop>().eq("is_delete", 0))
                    .stream().map(BaseShop::getShopCity).collect(Collectors.toSet());
            //运输方式目前是写死
            List<String> transportTypeList = new ArrayList<>();
            transportTypeList.add("专车");
            transportTypeList.add("快递");
            transportTypeList.add("零担");
            //重复的数据
            List<String> duplicate = new Vector<>();
            //并行校验
            importDTOList.parallelStream().forEach(item -> {
                //发出城市校验
                if (!warehouseCities.contains(item.getWarehouseCity())) {
                    item.setFailedReason("发出城市不存在此仓库！");
                    failList.add(item);
                    return;
                }
                //接收城市校验
                if (!shopCities.contains(item.getShopCity())) {
                    item.setFailedReason("门店主数据不存在这个接收城市！");
                    failList.add(item);
                    return;
                }
                //运输方式校验
                if (!transportTypeList.contains(item.getTransportType())) {
                    item.setFailedReason("运输方式错误！");
                    failList.add(item);
                    return;
                }
                //运输时效非空校验
                if (Objects.isNull(item.getTransportTime())) {
                    item.setFailedReason("运输时效错误！");
                    failList.add(item);
                    return;
                }
                //重复值校验
                //先拼接
                String string = item.getShopCity() + item.getWarehouseCity() + item.getTransportType();
                if (duplicate.contains(string)) {
                    item.setFailedReason("发运城市收货城市和运输方式重复！");
                    failList.add(item);
                    return;
                } else {
                    duplicate.add(string);
                }
                //校验通添加到成功的集合
                successList.add(item);
            });
            //构造响应体
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_TRANSPORT_TIMELINESS.buildKey("fail", uuid), failList, 16, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_TRANSPORT_TIMELINESS.buildKey("success", uuid), successList, 16, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;

        } catch (IOException | ResultException e) {
            throw new RuntimeException(e);
        } finally {
            baseTransportTimelinessListener.clear();
        }
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    public Page<BaseTransportTimelinessVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseTransportTimelinessVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_TRANSPORT_TIMELINESS.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseTransportTimelinessVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BaseTransportTimelinessVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseTransportTimelinessVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 下载错误的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    public void downloadFailFile(PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        if (BeanUtil.isEmpty(dto.getPage())) {
            new ResultException(500, "参数异常！");
        }
        log.info("BaseTransportTimelinessService downloadFailFile 开始导出 {}", dto.getParam().getOperationCode());
        String operationCode = dto.getParam().getOperationCode();
        //获取redis中存储的失败数据
        List<BaseTransportTimelinessExportVo> failResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_TRANSPORT_TIMELINESS.buildKey("fail", operationCode), BaseTransportTimelinessExportVo.class);
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("运输时效批量导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BaseTransportTimelinessExportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BaseTransportTimelinessService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    @Transactional
    public Result importData(String operationCode) throws ResultException {
        log.info("BaseTransportTimelinessService importData 开始添加规则 {}", operationCode);
        List<BaseTransportTimelinessExportVo> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_TRANSPORT_TIMELINESS.buildKey("success", operationCode), BaseTransportTimelinessExportVo.class);

        AtomicInteger addCount = new AtomicInteger();
        AtomicInteger updateCount = new AtomicInteger();

        //批量导入  区分新增还是修改
        successResult.forEach(item -> {
            BaseTransportTimeliness baseTransportTimeliness = this.baseMapper.selectOne(new LambdaQueryWrapper<BaseTransportTimeliness>()
                    .eq(BaseTransportTimeliness::getShopCity, item.getShopCity())
                    .eq(BaseTransportTimeliness::getWarehouseCity, item.getWarehouseCity())
                    .eq(BaseTransportTimeliness::getTransportType, item.getTransportType()));
            if (BeanUtil.isEmpty(baseTransportTimeliness)) {
                // 是空，表明是新增
                BaseTransportTimeliness newBaseTransportTimeliness = BeanUtil.copyProperties(item, BaseTransportTimeliness.class);
                int insert = this.baseMapper.insert(newBaseTransportTimeliness);
                if (insert == 1) {
                    BaseTransportTimelinessDto baseTransportTimelinessDto = BeanUtil.copyProperties(item, BaseTransportTimelinessDto.class);
                    updateRuleTime(baseTransportTimelinessDto);
                }
                addCount.addAndGet(insert);
            } else {
                // 不是空，表明是修改
                baseTransportTimeliness.setTransportTime(item.getTransportTime());
                baseTransportTimeliness.setRouteName(item.getRouteName());
                int i = this.baseMapper.updateById(baseTransportTimeliness);
                if (i == 1) {
                    BaseTransportTimelinessDto baseTransportTimelinessDto = BeanUtil.copyProperties(item, BaseTransportTimelinessDto.class);
                    updateRuleTime(baseTransportTimelinessDto);
                }
                updateCount.addAndGet(i);
            }
        });

        log.info("BaseTransportTimelinessService importData 本次导入新增{}个 更新{}个", addCount.get(), updateCount.get());

        if (successResult.size() != (addCount.get() + updateCount.get())) {
            throw new RuntimeException("BaseTransportTimelinessService importData 本次导入发生错误！");
        }
        return ResultUtil.success(String.format("本次导入新增%d个 更新%d个", addCount.get(), updateCount.get()));
    }
}
