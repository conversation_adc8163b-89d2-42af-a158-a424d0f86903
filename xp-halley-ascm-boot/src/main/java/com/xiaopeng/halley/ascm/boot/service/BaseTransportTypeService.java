package com.xiaopeng.halley.ascm.boot.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.entity.BaseTransportType;
import com.xiaopeng.halley.ascm.boot.mapper.BaseTransportTypeMapper;
import com.xpeng.athena.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BaseTransportTypeService extends ServiceImpl<BaseTransportTypeMapper, BaseTransportType> {

    public void checkExists(BaseTransportType transportType) {
        boolean exists = this.lambdaQuery().eq(BaseTransportType::getTransportType, transportType.getTransportType())
                .ne(transportType.getId() != null, BaseTransportType::getId, transportType.getId()).exists();
        if (exists) {
            throw new BusinessException("运输类型重复!");
        }
    }

    /**
     * 获取所有的运输类型
     *
     * @return
     */
    public List<String> getTypeAll() {
        List<BaseTransportType> baseTransportTypeList = baseMapper.selectList(new QueryWrapper<BaseTransportType>().eq("is_delete", 0).eq("status", 0));
        return baseTransportTypeList.stream().map(BaseTransportType::getTransportType).collect(Collectors.toList());
    }
}
