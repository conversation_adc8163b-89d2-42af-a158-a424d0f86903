package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BaseWarehouseService extends ServiceImpl<BaseWarehouseMapper, BaseWarehouse> {


    public Page<BaseWarehouseResponseDto> getPage(PageQuery<BaseWarehouseRequestDto> page) {

        // 新分页查询
        LambdaQueryWrapper<BaseWarehouse> queryWrapper = new LambdaQueryWrapper<BaseWarehouse>().eq(BaseWarehouse::getIsDelete, 0);

        queryWrapper.eq(page.getParam().getStatus() != null, BaseWarehouse::getStatus, page.getParam().getStatus());
        if (StrUtil.isNotBlank(page.getParam().getLgort())) {
            queryWrapper.like(BaseWarehouse::getLgort, page.getParam().getLgort());
        }

        if (StrUtil.isNotBlank(page.getParam().getLgobe())) {
            queryWrapper.like(BaseWarehouse::getLgobe, page.getParam().getLgobe());
        }

        if (StrUtil.isNotBlank(page.getParam().getContactNum())) {
            queryWrapper.like(BaseWarehouse::getContactNum, page.getParam().getContactNum());
        }

        if (StrUtil.isNotBlank(page.getParam().getContactPerson())) {
            queryWrapper.like(BaseWarehouse::getContactPerson, page.getParam().getContactPerson());
        }

        if (StrUtil.isNotBlank(page.getParam().getWarehouseCity())) {
            queryWrapper.like(BaseWarehouse::getWarehouseCity, page.getParam().getWarehouseCity());
        }

        if (StrUtil.isNotBlank(page.getParam().getWarehouseProvince())) {
            queryWrapper.like(BaseWarehouse::getWarehouseProvince, page.getParam().getWarehouseProvince());
        }

        if (ObjectUtil.isNotNull(page.getParam().getStartCreateTime()) && ObjectUtil.isNotNull(page.getParam().getEndCreateTime())) {
            queryWrapper.between(BaseWarehouse::getCreateTime, page.getParam().getStartCreateTime(), page.getParam().getEndCreateTime());
        }

        queryWrapper.orderByDesc(BaseWarehouse::getCreateTime);

        IPage<BaseWarehouse> pageResult = this.baseMapper.selectPage(page.convertPage(), queryWrapper);

        List<BaseWarehouseResponseDto> returnList = new ArrayList<>();

        pageResult.getRecords().forEach(item -> {
            BaseWarehouseResponseDto newItem = BeanUtil.copyProperties(item, BaseWarehouseResponseDto.class);

            newItem.setCreateTimeShow(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
            newItem.setUpdateTimeShow(DateUtil.format(item.getUpdateTime(), "yyyy-MM-dd HH:mm"));

            returnList.add(newItem);
        });

        return new Page<BaseWarehouseResponseDto>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())
                .setRecords(returnList);

    }

    public void stopAndOpen(BaseWarehouseStopAndOpenResponseDto update) throws ResultException {
        log.info("stopAndOpen [{}]", JSONObject.toJSONString(update));

        BaseWarehouse targetBW = this.baseMapper.selectById(update.getId());

        if (targetBW == null) {
            throw new ResultException(500, "查询不到对应的仓库");
        }

        targetBW.setStatus(update.getStatus());

        this.baseMapper.updateById(targetBW);
    }

    public void insertItem(BaseWarehouseInsertResponseDto insert) throws ResultException {

        // lgort
        LambdaQueryWrapper<BaseWarehouse> lqw = new LambdaQueryWrapper<>();
        lqw.eq(BaseWarehouse::getIsDelete, 0);
        lqw.eq(BaseWarehouse::getLgort, insert.getLgort());

        List<BaseWarehouse> resultList = this.baseMapper.selectList(lqw);

        if (ObjectUtil.isNotNull(resultList) && resultList.size() > 0) {
            throw new ResultException(500, "仓库编码已经存在");
        }

        BaseWarehouse newItem = BeanUtil.copyProperties(insert, BaseWarehouse.class);
        this.baseMapper.insert(newItem);
    }

    public void updateItem(BaseWarehouseUpdateResponseDto update) {
        BaseWarehouse newItem = BeanUtil.copyProperties(update, BaseWarehouse.class);
        this.baseMapper.updateById(newItem);
    }

    public Map<String, String> getCodeMapName() {
        return this.lambdaQuery().eq(BaseWarehouse::getIsDelete, 0).list().stream()
                .collect(Collectors.toMap(BaseWarehouse::getLgort, BaseWarehouse::getLgobe));
    }
}
