package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.listener.BaseWaybillRulesListener;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 运单匹配规则
 *
 * <AUTHOR> 自动生成
 * @date 2023-01-09 06:17:47
 */
@Slf4j
@Service
public class BaseWaybillRulesService extends ServiceImpl<BaseWaybillRulesMapper, BaseWaybillRules> {

    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    @Resource
    private ApplicationContext context;

    @Resource
    private BaseShopMapper baseShopMapper;

    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;

    @Resource
    private BaseCarrierMapper baseCarrierMapper;

    @Resource
    private BaseTransportTypeMapper baseTransportTypeMapper;

    @Resource
    private AscmRedisHelper ascmRedisHelper;

    @Resource
    private ImageService imageService;

    @Value("${fileTemp.wabillRules.tempFileId}")
    private String tempFileId;

    @Value("${waybillRule.orderType}")
    private String orderTypeConver;
    @Resource
    private BaseTransportTimelinessMapper baseTransportTimelinessMapper;
    @Resource
    private BaseTransportTimelinessService baseTransportTimelinessService;

    /**
     * 分页查询
     *
     * @param page
     * @return
     */
    public Page<BaseWaybillRulesDTO> page(PageQuery<BaseWaybillRulesRequestDTO> page) {
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BaseWaybillRulesDTO> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        Page<BaseWaybillRulesDTO> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);

        return returnPage;
    }

    /**
     * 根据类型和编码 取出对应信息
     *
     * @param param
     * @return
     * @throws ResultException
     */
    public List<Object> getByCode(BaseWaybillRulesGetByCodeDTO param) throws ResultException {

        QueryWrapper queryWrapper = new QueryWrapper<>();

        // 1:门店 2:仓库 3:承运商
        if (param.getType() == 1) {
            //门店编码联动
            if (StrUtil.isNotBlank(param.getCode())) {
                queryWrapper.like("shop_code", param.getCode());
            }
            queryWrapper.eq("is_delete", 0);
            queryWrapper.eq("status", 0);
            return baseShopMapper.selectList(queryWrapper);

        } else if (param.getType() == 2) {
            //仓库编码联动
            if (StrUtil.isNotBlank(param.getCode())) {
                queryWrapper.like("lgort", param.getCode());
            }
            queryWrapper.eq("is_delete", 0);
            queryWrapper.eq("status", 0);

            return baseWarehouseMapper.selectList(queryWrapper);

        } else if (param.getType() == 3) {
            //承运商编码联动
            if (StrUtil.isNotBlank(param.getCode())) {
                queryWrapper.like("carrier_code", param.getCode());
            }
            queryWrapper.eq("is_delete", 0);
            queryWrapper.eq("status", 0);

            return baseCarrierMapper.selectList(queryWrapper);

        }
        throw new ResultException(500, "类型错误");
    }

    /**
     * 新增规则
     *
     * @param param
     * @return
     */
    public boolean createRule(BaseWaybillRules param) throws ResultException {
        if (ObjectUtil.isNull(param)) {
            throw new ResultException(500, "参数不能为空!");
        }

        if (StrUtil.isBlank(param.getShopCode())) {
            throw new ResultException(500, "门店编码不能为空!");
        }

        if (StrUtil.isBlank(param.getPath())) {
            throw new ResultException(500, "线路不能为空!");
        }
        if (StrUtil.isBlank(param.getTransportType())) {
            throw new ResultException(500, "运输类型不能为空!");
        }

        //校验完参数校验数据真实性
        if (ObjectUtil.isNull(baseShopMapper.selectOne(new QueryWrapper<BaseShop>().eq("shop_code", param.getShopCode()).last("limit 1")))) {
            throw new ResultException(500, "门店不存在!");
        }

        checkRuleRepeat(param);

        setUser(param, ascmLoginUserHelper.getLoginUser());

        try {
            int count = baseMapper.insert(param);
            return count > 0;
        } catch (Exception e) {
            throw new ResultException(500, "当前已存在订单状态为紧急与常规的运输规则");
        }
    }

    public void checkRuleRepeat(BaseWaybillRules baseWaybillRules) throws ResultException {
        QueryWrapper<BaseWaybillRules> baseWaybillRulesQueryWrapper = new QueryWrapper<>();

        if (StrUtil.isNotBlank(baseWaybillRules.getShopCode())) {
            baseWaybillRulesQueryWrapper.eq("shop_code", baseWaybillRules.getShopCode());
        }

        if (StrUtil.isNotBlank(baseWaybillRules.getOrderType())) {
            baseWaybillRulesQueryWrapper.eq("order_type", baseWaybillRules.getOrderType());
        }

        if (StrUtil.isNotBlank(baseWaybillRules.getLgort())) {
            baseWaybillRulesQueryWrapper.eq("lgort", baseWaybillRules.getLgort());
        }

        baseWaybillRulesQueryWrapper.ne("id", baseWaybillRules.getId());

        baseWaybillRulesQueryWrapper.eq("is_delete", 0);
        baseWaybillRulesQueryWrapper.last("limit 1");
        if (ObjectUtil.isNotNull(baseMapper.selectOne(baseWaybillRulesQueryWrapper))) {
            throw new ResultException(500, "仓库编码、门店编码、订单类型 组合存在重复");
        }
    }

    /**
     * 填充用户和仓库
     */
    public void setUser(BaseWaybillRules baseWaybillRules, AscmLoginUser ascmLoginUser) {
        baseWaybillRules.setCreateUserId(ascmLoginUser.getUserId());
        baseWaybillRules.setUpdateUserId(ascmLoginUser.getUserId());
        baseWaybillRules.setCreateUserName(ascmLoginUser.getName());
        baseWaybillRules.setUpdateUserName(ascmLoginUser.getName());
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    public boolean deleteByIds(List<Long> ids) throws ResultException {

        if (CollUtil.isEmpty(ids)) {
            throw new ResultException(500, "参数不能为空");
        }

        baseMapper.deleteByIds(ids);

        return true;
    }

    /**
     * 下载模版
     *
     * @return
     */
    public ImageResponseDTO tempFile() {
        return imageService.getTempURL(tempFileId);
    }

    /**
     * 导入校验
     *
     * @param file
     * @return
     */
    public ImportResponseDto importList(MultipartFile file) throws ResultException {
        BaseWaybillRulesListener baseWaybillRulesListener = new BaseWaybillRulesListener();

        //存放读取的数据
        List<BaseWayBillRulesImportDTO> importDTOList = new ArrayList<>();

        //成功的数据
        List<BaseWayBillRulesImportDTO> successList = new ArrayList<>();

        //失败的数据
        List<BaseWayBillRulesImportDTO> failList = new ArrayList<>();

        try {
            EasyExcel.read(file.getInputStream(), BaseWayBillRulesImportDTO.class, baseWaybillRulesListener).sheet().doRead();

            importDTOList = baseWaybillRulesListener.getList();

            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }

            //门店编码 取出 为map key：code value：内容 数据量还不足以需要考虑到效率问题
            Map<String, BaseShop> baseShopMap = baseShopMapper.selectList(new QueryWrapper<BaseShop>().eq("is_delete", 0)).stream().collect(Collectors.toMap(item -> item.getShopCode(), value -> value, (k1, k2) -> k1));

            //仓库编码 取出同上操作 便于校验数据真实性和数据补充 但不用转为map 因为只需要校验真实性即可 list足以操作
            List<String> lgortList = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0)).stream().map(BaseWarehouse::getLgort).collect(Collectors.toList());

            //承运商编码
            // List<String> baseCarrierList = baseCarrierMapper.selectList(new QueryWrapper<BaseCarrier>().eq("is_delete",0)).stream().map(BaseCarrier::getCarrierCode).collect(Collectors.toList());

            //运输类型
            List<String> baseTransportTypeList = baseTransportTypeMapper.selectList(new QueryWrapper<BaseTransportType>().eq("is_delete", 0).eq("status", 0)).stream().map(BaseTransportType::getTransportType).collect(Collectors.toList());

            //订单类型目前是写死
            List<String> orderTypeList = new ArrayList<>();
            orderTypeList.add("常规");
            orderTypeList.add("紧急");
            orderTypeList.add("火急");
            importDTOList.forEach(item -> {

                if (StrUtil.isBlank(item.getShopCode())) {
                    item.setRemark("门店编码不能为空");
                    failList.add(item);
                    return;
                }
                if (StrUtil.isBlank(item.getLgort())) {
                    item.setRemark("仓库编码不能为空");
                    failList.add(item);
                    return;
                }
                if (StrUtil.isBlank(item.getOrderType())) {
                    item.setRemark("订单类型不能为空");
                    failList.add(item);
                    return;
                }
                if (StrUtil.isBlank(item.getTransportType())) {
                    item.setRemark("运输类型不能为空");
                    failList.add(item);
                    return;
                }

                if (!baseShopMap.containsKey(item.getShopCode())) {
                    item.setRemark("门店编码不存在！");
                    failList.add(item);
                    return;
                }

                if (!lgortList.contains(item.getLgort())) {
                    item.setRemark("仓库编码不存在！");
                    failList.add(item);
                    return;
                }
                /*if (StrUtil.isNotBlank(item.getCarrierCode()) && !baseCarrierList.contains(item.getCarrierCode())){
                    item.setRemark("承运商编码不存在！");
                    failList.add(item);
                    return;
                }*/
                if (!baseTransportTypeList.contains(item.getTransportType())) {
                    item.setRemark("运输类型不存在！");
                    failList.add(item);
                    return;
                }
                if (!orderTypeList.contains(item.getOrderType())) {
                    item.setRemark("订单类型不存在！");
                    failList.add(item);
                    return;
                }

                item.setShopCity(baseShopMap.get(item.getShopCode()).getShopCity());
                item.setShopProvince(baseShopMap.get(item.getShopCode()).getShopProvince());

                successList.add(item);

            });
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_WAYBILL_RULES.buildKey("fail", uuid), failList, 24, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_WAYBILL_RULES.buildKey("success", uuid), successList, 24, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;

        } catch (IOException e) {
            e.printStackTrace();
        } catch (ResultException e) {
            e.printStackTrace();
        } finally {
            baseWaybillRulesListener.clear();
        }

        throw new ResultException(500, "导入异常");
    }

    public Page<BaseWayBillRulesImportDTO> getAccountPage(PageQuery<AccountSuccessRequestDTO> page) throws ResultException {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BaseWayBillRulesImportDTO> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_WAYBILL_RULES.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BaseWayBillRulesImportDTO.class);
        Page<BaseWayBillRulesImportDTO> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BaseWayBillRulesImportDTO> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 下载错误文件
     */
    public List<BaseWayBillRulesImportDTO> failDownload(PageQuery<AccountSuccessRequestDTO> pageQuery) throws ResultException {
        List<BaseWayBillRulesImportDTO> list = ascmRedisHelper.getList(RedisKeyManager.IMPORT_WAYBILL_RULES.buildKey("fail", pageQuery.getParam().getOperationCode()), BaseWayBillRulesImportDTO.class);
        return list;
    }

    public ImportResponseDto importAccount(String operationCode) throws ResultException {
        List<BaseWayBillRulesImportDTO> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_WAYBILL_RULES.buildKey("success", operationCode), BaseWayBillRulesImportDTO.class);
        if (CollUtil.isEmpty(successResult)) {
            throw new ResultException(500, "提交文件不能为空");
        }
        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();

        List<Long> deleteList = new ArrayList<>();

        // 删除重复的
        successResult.forEach(item -> {
            QueryWrapper<BaseWaybillRules> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("shop_code", item.getShopCode());
            queryWrapper.eq("lgort", item.getLgort());
            queryWrapper.eq("order_type", item.getOrderType());
            queryWrapper.last("limit 1");

            BaseWaybillRules target = baseMapper.selectOne(queryWrapper);
            if (ObjectUtil.isNotNull(target)) {
                deleteList.add(target.getId());
            }
        });

        if (!deleteList.isEmpty()) {
            this.baseMapper.deleteByIds(deleteList);
        }

        successResult.forEach(item -> {
            BaseWaybillRules baseWaybillRules = BeanUtil.copyProperties(item, BaseWaybillRules.class);

            setUser(baseWaybillRules, ascmLoginUserHelper.getLoginUser());

            if (baseMapper.insert(baseWaybillRules) > 0) {
                successCount.addAndGet(1);
            } else {
                failCount.addAndGet(1);
            }

        });
        ImportResponseDto importResponseDto = new ImportResponseDto();

        importResponseDto.setFailCount(failCount.get());
        importResponseDto.setSuccessCount(successCount.get());
        importResponseDto.setFileCode(operationCode);
        return importResponseDto;
    }

    public boolean updateRules(BaseWaybillRules baseWaybillRules) throws ResultException {
        if (ObjectUtil.isNull(baseWaybillRules.getId())) {
            throw new ResultException(500, "id不能为空白");
        }
        BaseShop baseShop = baseShopMapper.selectOne(new QueryWrapper<BaseShop>().eq("is_delete", 0).eq("shop_code", baseWaybillRules.getShopCode()));
        if (ObjectUtil.isNull(baseShop)) {
            throw new ResultException(500, "门店不存在");
        }

        BaseWarehouse baseWarehouse = baseWarehouseMapper.selectOne(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0).eq("lgort", baseWaybillRules.getLgort()));
        if (ObjectUtil.isNull(baseWarehouse)) {
            throw new ResultException(500, "仓库不存在");
        }

        checkRuleRepeat(baseWaybillRules);

        //TODO 校验
        UpdateWrapper<BaseWaybillRules> updateWrapper = new UpdateWrapper<>();

        updateWrapper.eq("id", baseWaybillRules.getId());
        updateWrapper.set("shop_code", baseWaybillRules.getShopCode());
        updateWrapper.set("shop_province", baseShop.getShopProvince());
        updateWrapper.set("shop_city", baseShop.getShopCity());
        updateWrapper.set("lgort", baseWaybillRules.getLgort());
        updateWrapper.set("order_type", baseWaybillRules.getOrderType());
        updateWrapper.set("path", baseWaybillRules.getPath());
        updateWrapper.set("path_expiry", baseWaybillRules.getPathExpiry());
        updateWrapper.set("is_specially", baseWaybillRules.getIsSpecially());
        updateWrapper.set("carrier_code", baseWaybillRules.getCarrierCode());
        updateWrapper.set("transport_type", baseWaybillRules.getTransportType());

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 运输匹配规则生成数据
     */
    public void generateRules() {
        //删除所有非专用的数据
        baseMapper.deleteAllNotSpecially();

        //拿到订单类型与运输方式对应关系
        Map<String, String> orderTypeMap = getTypeConversion();
        //取出门店 仓库数据
        //若门店和仓库任意为0 则此次无需生成数据
        List<BaseShop> shopList = baseShopMapper.selectList(new QueryWrapper<BaseShop>().eq("is_delete", 0).eq("status", 0));
        if (CollUtil.isEmpty(shopList)) {
            return;
        }

        List<BaseWarehouse> warehouseList = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0).eq("status", 0));
        if (CollUtil.isEmpty(warehouseList)) {
            return;
        }

        Map<String, BaseTransportTimeliness> transportTimelinessMap = baseTransportTimelinessService.lambdaQuery().eq(BaseTransportTimeliness::getIsDelete, 0).list()
                .stream().collect(Collectors.toMap(e -> e.getWarehouseCity() + e.getShopCity() + e.getTransportType(), Function.identity()));
        //取出专用数据 仓库数据+订单类型+运输方式+门店 组合为唯一值存入map
        List<BaseWaybillRules> waybillRulesList = baseMapper.selectList(new QueryWrapper<BaseWaybillRules>().eq("is_specially", 1).eq("is_delete", 0));
        Map<String, BaseWaybillRules> waybillRulesMap = waybillRulesList.stream().collect(Collectors.toMap(key -> key.getLgort() + key.getOrderType() + key.getShopCode(), value -> value, (k1, k2) -> k1));

        //生成 仓库 对应 订单类型 对应 运输方式 对应 门店 1对多 1对1 多对多
        List<BaseWaybillRules> generateList = new ArrayList<>();
        // 用户存储时效
        Map<String, Integer> timeliness = new HashMap<>();
        //此处需要三层循环 外层用仓库 中间用订单类型 内层循环用门店
        warehouseList.forEach(item -> {
            //遍历订单类型
            orderTypeMap.entrySet().stream().forEach(entry -> {
                //补充门店数据
                shopList.forEach(baseShop -> {
                    BaseWaybillRules baseWaybillRules = new BaseWaybillRules();
                    //先组合仓库数据
                    baseWaybillRules.setLgort(item.getLgort());
                    //补充订单数据
                    baseWaybillRules.setOrderType(entry.getKey());
                    baseWaybillRules.setTransportType(entry.getValue());
                    //门店数据
                    baseWaybillRules.setShopCity(baseShop.getShopCity());
                    baseWaybillRules.setShopProvince(baseShop.getShopProvince());
                    baseWaybillRules.setShopCode(baseShop.getShopCode());
                    // 时效数据 (存入一个map中，避免多次查询数据库，后续还可以优化存入redis,在更新门店时就直接存入redis,用的时候直接取出）
                    setPathExpiryMethod(item.getWarehouseCity(), baseShop.getShopCity(), entry.getValue(), timeliness, baseWaybillRules, transportTimelinessMap);
                    if (waybillRulesMap.containsKey(baseWaybillRules.getLgort() + baseWaybillRules.getOrderType() + baseWaybillRules.getShopCode())) {
                    } else {
                        //放入数据集合用于后续排查跳过
                        generateList.add(baseWaybillRules);
                    }
                });
            });
        });

        //跳过专用数据 (可优化为批量新增) INSERT INTO base_waybill_rules (shop_code, order_type, transport_type, lgort, shop_city, shop_province) VALUES (?, ?, ?, ?, ?, ?)
        List<List<BaseWaybillRules>> splitList = new ArrayList<>();
        int temp = 200;
        for (int i = 0; i < generateList.size(); i += temp) {
            splitList.add(generateList.subList(i, Math.min(i + temp, generateList.size())));
        }

        List<List<BaseWaybillRules>> returnList = Collections.synchronizedList(splitList);

        returnList.parallelStream().forEach(item -> {
            //落库 报错应该是重复问题（上面代码已经限制过了 应该不会存在这方面问题） 就跳过
            try {
                baseMapper.insertBatch(item);
            } catch (Exception e) {
                log.error("运输匹配规则生成数据 insertBatch异常", e);
            }
        });
    }

    /**
     * 设置运单匹配规则的运输时效
     *
     * @param warehouseCity    发运城市
     * @param shopCity         收货城市
     * @param value            运输方式
     * @param timeliness       时效集合
     * @param baseWaybillRules 需要设置的参数
     */
    private void setPathExpiryMethod(String warehouseCity, String shopCity, String value, Map<String, Integer> timeliness, BaseWaybillRules baseWaybillRules, Map<String, BaseTransportTimeliness> transportTimelinessMap) {
        try {
            // 构建map集合的key
            String stringAppend = warehouseCity + shopCity + value;
            log.info(stringAppend);
            // 没有的话就要去查询，然后set进去，同时添加到集合中
            BaseTransportTimeliness baseTransportTimeliness = transportTimelinessMap.get(stringAppend);
            // set进去
            if (BeanUtil.isNotEmpty(baseTransportTimeliness)) {
                baseWaybillRules.setPath(baseTransportTimeliness.getRouteName());
                baseWaybillRules.setPathExpiry(Integer.valueOf(baseTransportTimeliness.getTransportTime()));
                // 同时存入map中
                timeliness.put(stringAppend, Integer.valueOf(baseTransportTimeliness.getTransportTime()));
            } else {
                baseWaybillRules.setPathExpiry(0);
            }
        } catch (Exception e) {
            log.error("setPathExpiryMethod异常", e);
        }
    }

    /**
     * 订单类型配置文件转换
     *
     * @return
     */
    public Map<String, String> getTypeConversion() {
        String[] strings = orderTypeConver.split(",");
        Map<String, String> returnMap = new HashMap<>();
        for (int i = 0; i < strings.length; i++) {
            String s1 = strings[i].trim();
            returnMap.put(s1.substring(0, s1.indexOf("=")), s1.substring(s1.indexOf("=") + 1));
        }
        return returnMap;
    }

}