package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.listener.BatteryWayBillListener;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.printPDF.BatteryWayBillPrint;
import com.xiaopeng.halley.ascm.boot.utils.DateUtils;
import com.xiaopeng.halley.ascm.boot.utils.IndexTools;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipOutputStream;

/**
 * @Author：huqizhi
 * @Date：2023/9/18 14:56
 */
@Slf4j
@Service
public class BatteryWayBillService extends ServiceImpl<BatteryWayBillMapper, BatteryWayBill> {

    //正则表达式校验日期格式是否正确
    private static final String datePattern = "^(19|20)\\d\\d(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])$";
    @Resource
    private ImageService imageService;
    @Value("${fileTemp.BatteryWayBill.tempFileId}")
    private String tempFileId;
    @Resource
    private BaseRepairCenterService baseRepairCenterService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private IndexTools indexTools;
    @Resource
    private BzWaybillFileService bzWaybillFileService;
    @Resource
    private BzWaybillFileMapper bzWaybillFileMapper;
    @Resource
    private BaseShopMapper baseShopMapper;
    @Resource
    private BaseRepairCenterMapper baseRepairCenterMapper;
    @Resource
    private BaseBatteryProjectMapper baseBatteryProjectMapper;
    @Resource
    private BaseBatteryRouteCostMapper baseBatteryRouteCostMapper;

    /**
     * 返厂电池运单导入参数校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    public ImportResponseDto importFile(MultipartFile file) {

        BatteryWayBillListener batteryWayBillListener = new BatteryWayBillListener();

        //存放读取的数据
        List<BatteryWayBillImportVo> importDTOList = new ArrayList<>();

        //成功的数据
        List<BatteryWayBillImportVo> successList = new Vector<>();

        //失败的数据
        List<BatteryWayBillImportVo> failList = new Vector<>();

        try {
            //获取读取到的内容
            EasyExcel.read(file.getInputStream(), BatteryWayBillImportVo.class, batteryWayBillListener).sheet().doRead();
            //获取读取到的内容
            importDTOList = batteryWayBillListener.getList();
            //非空检验
            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }
            //查询出维修中心编号，用于校验是否存在
            List<BaseRepairCenter> baseRepairCenters = baseRepairCenterService.getBaseMapper().selectList(new LambdaQueryWrapper<BaseRepairCenter>().eq(BaseRepairCenter::getIsDelete, 0));
            Map<String, BaseRepairCenter> baseRepairCentersMap = baseRepairCenters.stream().collect(Collectors.toMap(BaseRepairCenter::getRepairCenterNum, item -> {
                return item;
            }));
            //查询出骁龙门店编码，用于校验是否存在
            List<BaseShop> baseShopList = baseShopMapper.selectList(new LambdaQueryWrapper<BaseShop>().eq(BaseShop::getIsDelete, 0).eq(BaseShop::getStatus, 0));
            Map<String, BaseShop> baseShopsMap = baseShopList.stream().filter(item -> {
                return StrUtil.isNotBlank(item.getXlShopCode());
            }).collect(Collectors.toMap(BaseShop::getXlShopCode, item -> {
                return item;
            }, (v1, v2) -> v2));
            //查询出所有电池项目，用与回填项目字段
            List<BaseBatteryProject> baseBatteryProjects = baseBatteryProjectMapper.selectList(new LambdaQueryWrapper<BaseBatteryProject>().eq(BaseBatteryProject::getIsDelete, 0));
            // 当有重复的pack_num时,随便取其中一个(v1, v2) -> v1)
            Map<String, String> baseBatteryProjectsMap = baseBatteryProjects.stream().collect(Collectors.toMap(BaseBatteryProject::getPackNum, BaseBatteryProject::getProject, (v1, v2) -> v1));
            // 日期格式校验的方法
            Pattern pattern = Pattern.compile(datePattern);
            //并行字段校验
            importDTOList.parallelStream().forEach(item -> {
                // 报修日期格式校验
                if (!pattern.matcher(item.getRepairDate()).matches()) {
                    item.setFailedReason("报修日期格式有误，例如：20231007");
                    failList.add(item);
                    return;
                }

                //项目字段非空校验
                if (null == item.getPackNum()) {
                    item.setFailedReason("必填字段不能为空");
                    failList.add(item);
                    return;
                }
                // 校验vin号为17位
                if (StrUtil.isEmpty(item.getVinCode())) {
                    item.setFailedReason("vin号字段不能为空");
                    failList.add(item);
                    return;
                }
                if (17 != item.getVinCode().length()) {
                    item.setFailedReason("vin号字段不能不等于17位");
                    failList.add(item);
                    return;
                }
                //维修中心编号是否存在校验
                BaseRepairCenter baseRepairCenter = baseRepairCentersMap.get(item.getRepairCenterNum());
                if (!baseRepairCentersMap.containsKey(item.getRepairCenterNum())) {
                    item.setFailedReason("填写的维修中心不存在");
                    failList.add(item);
                    return;
                } else {
                    //判断是返厂还是反店
                    if (0 == item.getTransportationType()) {
                        //说明是返厂的运单  返厂的运单设置接收城市，接收的相关信息在维修中心中获取
                        item.setArrivalAddress(baseRepairCenter.getRepairCenterAddress());
                        item.setArrivalCity(baseRepairCenter.getRepairCenterCity());
                        item.setUnloadingContact(baseRepairCenter.getContactPerson());
                        item.setUnloadingPhone(baseRepairCenter.getContactNum());
                    } else {
                        //说明是返店的运单  返店的运单设置发运城市，发运的相关信息在维修中心中获取
                        item.setDispatchCity(baseRepairCenter.getRepairCenterCity());
                        item.setDispatchAddress(baseRepairCenter.getRepairCenterAddress());
                        item.setLoadingContact(baseRepairCenter.getContactPerson());
                        item.setLoadingPhone(baseRepairCenter.getContactNum());
                    }

                }
                //骁龙门店是否存在校验
                BaseShop baseShop = baseShopsMap.get(item.getXlShopCode());
                if (!baseShopsMap.containsKey(item.getXlShopCode())) {
                    item.setFailedReason("填写的骁龙门店不存在");
                    failList.add(item);
                    return;
                } else {
                    //判断是返厂还是返店
                    if (0 == item.getTransportationType()) {
                        //说明是返厂的运单  返厂的运单设置发运城市，发运的相关信息在门店基础数据中获取
                        item.setDispatchCity(baseShop.getShopCity());
                        item.setDispatchAddress(baseShop.getShopAddress());
                        item.setLoadingContact(baseShop.getContactPerson());
                        item.setLoadingPhone(baseShop.getContactNum());
                    } else {
                        //说明是返店的运单  返店的运单设置接收城市，接收的相关信息在门店基础数据中获取
                        item.setArrivalAddress(baseShop.getShopAddress());
                        item.setArrivalCity(baseShop.getShopCity());
                        item.setUnloadingContact(baseShop.getContactPerson());
                        item.setUnloadingPhone(baseShop.getContactNum());
                    }
                }
                //校验电池溯源码长度
                if (item.getPackNum().length() < 14) {
                    item.setFailedReason("填写的电池溯源码不正确");
                    failList.add(item);
                    return;
                }
                //校验电池溯源码是否存在
                String packNum = item.getPackNum().substring(0, 14);
                if (baseBatteryProjectsMap.containsKey(packNum)) {
                    item.setProject(baseBatteryProjectsMap.get(packNum));
                } else {
                    item.setFailedReason("填写的电池溯源码不正确");
                    failList.add(item);
                    return;
                }
                //校验通添加到成功的集合
                successList.add(item);
            });

            //构造响应体
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_THE_RETURN_BATTERY_WAYBILL.buildKey("fail", uuid), failList, 16, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_THE_RETURN_BATTERY_WAYBILL.buildKey("success", uuid), successList, 16, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;

        } catch (IOException | ResultException e) {
            throw new RuntimeException(e);
        } finally {
            batteryWayBillListener.clear();
        }
    }

    /**
     * 返厂电池运单导入数据获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    public Page<BatteryWayBillImportVo> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<BatteryWayBillImportVo> successResult = null;
        try {
            successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_THE_RETURN_BATTERY_WAYBILL.buildKey("success", accountSuccessRequestDTO.getOperationCode()), BatteryWayBillImportVo.class);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<BatteryWayBillImportVo> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<BatteryWayBillImportVo> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 返厂电池运单导入数据下载错误数据的文件
     *
     * @param dto      数据UUID
     * @param response 响应文件
     */
    public void downloadFailFile(PageQuery<AccountSuccessRequestDTO> dto, HttpServletResponse response) throws ResultException {
        String operationCode = dto.getParam().getOperationCode();

        log.info("BatteryWayBillService downloadFailFile 开始导出 {}", operationCode);

        //获取redis中存储的失败数据
        List<BatteryWayBillImportVo> failResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_THE_RETURN_BATTERY_WAYBILL.buildKey("fail", operationCode), BatteryWayBillImportVo.class);
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("电池线路费用维护批量导入失败导出表", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), BatteryWayBillImportVo.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BatteryWayBillService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    /**
     * 保存校验成功的数据
     *
     * @param operationCode 成功的 UUID
     * @return 结果
     */
    @Transactional
    public Result importData(String operationCode) throws ResultException {
        log.info("BatteryWayBillService importData 开始添加数据 {}", operationCode);
        List<BatteryWayBillImportVo> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_THE_RETURN_BATTERY_WAYBILL.buildKey("success", operationCode), BatteryWayBillImportVo.class);

        //success的集合
        List<BatteryWayBill> successList = new Vector<>();

        //日期，用于生成运单编号
        Date date = new Date();

        //设置参数，后面直接  批量插入
        successResult.parallelStream().forEach(item -> {
            //bean转换一下
            BatteryWayBill batteryWayBill = BeanUtil.copyProperties(item, BatteryWayBill.class);

            //设置维修中心描述
            //生成运单号
            StringBuffer waybillCode = new StringBuffer();
            waybillCode.append(item.getRepairCenterNum());
            waybillCode.append(DateUtil.format(date, "yyyyMMdd"));
            Long todayASCMIndex = indexTools.getTodayRepairIndex(item.getRepairCenterNum());
            waybillCode.append(String.format("%03d", todayASCMIndex));
            batteryWayBill.setWaybillCode(waybillCode.toString());
            //设置运单状态
            batteryWayBill.setWaybillStatus(0);
            // 设置物流安排时间
            batteryWayBill.setLogisticsArrangementTime(date);
            //设置完成后，存入集合中
            successList.add(batteryWayBill);
        });

        //设置完成后批量插入
        boolean b = saveBatch(successList);
        if (b) {
            return ResultUtil.success("导入成功！");
        }

        return ResultUtil.failed("导入失败！");
    }

    /**
     * 电池运单明细页面分页查询
     *
     * @param page
     * @return
     */
    public Page<BatteryWayBillVo> getPage(PageQuery<BatteryWayBillDto> page) {
        log.info("BatteryWayBillService getPage Start pagination");
        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BatteryWayBillVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        //计算时效
        returnList.parallelStream().forEach(item -> {
            // 获取提货日期
            Date pickupDate = item.getPickupDate();
            // 获取物流安排日期
            Date logisticsArrangementTime = item.getLogisticsArrangementTime();
            // 获取送达日期
            Date deliveryTime = item.getDeliveryTime();

            if (null != pickupDate) {
                // 响应时效
                if (null != logisticsArrangementTime) {
                    Integer integer = DateUtils.dateBetween(logisticsArrangementTime, pickupDate);
                    item.setResponseTimeTolerance(integer);
                }
                // 运输时效
                if (null != deliveryTime) {
                    Integer integer = DateUtils.dateBetween(pickupDate, deliveryTime);
                    item.setTransportationTimeTolerance(integer);
                }
            }
        });

        Page<BatteryWayBillVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 关闭运单
     *
     * @param waybill 运单编号
     * @return
     */
    public Result closeWaybill(String waybill) {
        LambdaUpdateWrapper<BatteryWayBill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BatteryWayBill::getWaybillStatus, 5).eq(BatteryWayBill::getWaybillCode, waybill).eq(BatteryWayBill::getIsDelete, 0);

        int updateResult = this.baseMapper.update(null, updateWrapper);

        if (1 == updateResult) {
            return ResultUtil.success("关闭成功!");
        }
        return ResultUtil.failed("关闭失败！");
    }

    public Result imagesUpload(MultipartFile imagesfile, String waybillCode) throws IOException {
        log.info("BatteryWayBillService imagesUpload waybillCode {}", waybillCode);
        //存入oss

        log.info("BatteryWayBillService imagesUpload imagesfilename {}", imagesfile.getOriginalFilename());
        String pictureFileId = bzWaybillFileService.getPictureFileId(imagesfile);
        BzWaybillFile bzWaybillFile = new BzWaybillFile();
        bzWaybillFile.setFileId(pictureFileId);
        bzWaybillFile.setWaybillCode(waybillCode);
        bzWaybillFile.setUploadType("3");
        //入库记录
        bzWaybillFileMapper.insert(bzWaybillFile);

        ImageResponseDTO tempURL = imageService.getTempURL(pictureFileId);

        return ResultUtil.success(tempURL);
    }

    /**
     * 电池运单创建返回运单
     *
     * @param waybillCode
     * @return
     */
    @Transactional
    public Result createReverseWaybill(List<String> waybillCode) {
        //查询出对应的运单
        LambdaQueryWrapper<BatteryWayBill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BatteryWayBill::getIsDelete, 0).ne(BatteryWayBill::getWaybillStatus, 5).in(BatteryWayBill::getWaybillCode, waybillCode);
        List<BatteryWayBill> batteryWayBills = this.baseMapper.selectList(queryWrapper);
        if (waybillCode.size() != batteryWayBills.size()) {
            //如果没有就报错
            return ResultUtil.failed("有运单不存在！");
        }
        // 已经创建过返回运单的不能再创建了
        // 查询出是否已经创建了返回运单
        LambdaQueryWrapper<BatteryWayBill> newQueryWrapper = new LambdaQueryWrapper<>();
        newQueryWrapper.eq(BatteryWayBill::getIsDelete, 0).in(BatteryWayBill::getOriginalWaybill, waybillCode);
        List<BatteryWayBill> newBatteryWayBills = this.baseMapper.selectList(newQueryWrapper);
        if (CollUtil.isNotEmpty(newBatteryWayBills)) {
            String result = newBatteryWayBills.stream().map(BatteryWayBill::getOriginalWaybill).distinct().collect(Collectors.joining(","));
            return ResultUtil.failed("以下运单已经有对应的返回运单：" + result);
        }
        //转成map
        Map<String, BatteryWayBill> batteryWayBillsMap = batteryWayBills.stream().collect(Collectors.toMap(BatteryWayBill::getWaybillCode, item -> {
            return item;
        }));
        //创建接收参数的List集合
        List<BatteryWayBill> batteryWayBillList = new Vector<>();
        //反向创建运单
        batteryWayBills.parallelStream().forEach(item -> {

            BatteryWayBill batteryWayBill = new BatteryWayBill();
            //取出存再map中的数据
            BatteryWayBill batteryWayBillDb = batteryWayBillsMap.get(item.getWaybillCode());
            batteryWayBill.setRepairCenterNum(batteryWayBillDb.getRepairCenterNum());
            batteryWayBill.setRepairDate(batteryWayBillDb.getRepairDate());
            batteryWayBill.setXlShopCode(batteryWayBillDb.getXlShopCode());
            batteryWayBill.setPackNum(batteryWayBillDb.getPackNum());
            batteryWayBill.setVinCode(batteryWayBillDb.getVinCode());
            batteryWayBill.setProject(batteryWayBillDb.getProject());
            batteryWayBill.setOriginalWaybill(batteryWayBillDb.getWaybillCode());

            //生成运单号
            StringBuffer newWaybillCode = new StringBuffer();
            newWaybillCode.append(batteryWayBillDb.getRepairCenterNum());
            newWaybillCode.append(DateUtil.format(new Date(), "yyyyMMdd"));
            Long todayASCMIndex = indexTools.getTodayRepairIndex(batteryWayBillDb.getRepairCenterNum());
            newWaybillCode.append(String.format("%03d", todayASCMIndex));
            batteryWayBill.setWaybillCode(newWaybillCode.toString());
            batteryWayBill.setTransportationType(1);
            batteryWayBill.setCreateUserName("");
            batteryWayBill.setUpdateUserName("");
            batteryWayBill.setFaultClassification(batteryWayBillDb.getFaultClassification());
            batteryWayBill.setFaultSummary(batteryWayBillDb.getFaultSummary());
            batteryWayBill.setDistanceTravelled(batteryWayBillDb.getDistanceTravelled());
            batteryWayBill.setDispatchAddress(batteryWayBillDb.getArrivalAddress());
            batteryWayBill.setDispatchCity(batteryWayBillDb.getArrivalCity());
            batteryWayBill.setArrivalCity(batteryWayBillDb.getDispatchCity());
            batteryWayBill.setLoadingContact(batteryWayBillDb.getUnloadingContact());
            batteryWayBill.setLoadingPhone(batteryWayBillDb.getUnloadingPhone());
            batteryWayBill.setArrivalAddress(batteryWayBillDb.getDispatchAddress());
            batteryWayBill.setUnloadingContact(batteryWayBillDb.getLoadingContact());
            batteryWayBill.setUnloadingPhone(batteryWayBillDb.getLoadingPhone());

            batteryWayBillList.add(batteryWayBill);
        });

        boolean b = this.saveBatch(batteryWayBillList);

        if (b) {
            return ResultUtil.success("运单创建成功！");
        }

        return ResultUtil.failed("未知异常！");
    }

    /**
     * 编辑运单页面
     *
     * @param batteryWayBillDto
     * @return
     */
    public Result editWaybill(BatteryWayBillDto batteryWayBillDto) {
        BatteryWayBill batteryWayBill = BeanUtil.copyProperties(batteryWayBillDto, BatteryWayBill.class);
        //更新运单状态 --- 已分配 物流供应商不为空，提货日期为空，送达日期为空，实际付款金额为空
        // 防止空指针
        if (null == batteryWayBill.getActualPaymentAmount()) {
            batteryWayBill.setActualPaymentAmount(0.0);
        }
        int waybillStatus = batteryWayBill.getWaybillStatus();
        // 不管有没有填，都按照最大的状态来
        if (0 != batteryWayBill.getActualPaymentAmount()) {
            batteryWayBill.setWaybillStatus(Math.max(waybillStatus, 4));
        } else if (null != batteryWayBill.getDeliveryTime()) {
            batteryWayBill.setWaybillStatus(Math.max(waybillStatus, 3));
        } else if (null != batteryWayBill.getPickupDate()) {
            batteryWayBill.setWaybillStatus(Math.max(waybillStatus, 2));
        } else if (StrUtil.isNotBlank(batteryWayBill.getLogisticsProvider())) {
            batteryWayBill.setWaybillStatus(Math.max(waybillStatus, 1));
        }
        // 车型转换
        String logisticsType = logisticsTypeConverter(batteryWayBill.getLogisticsType());
        // 回填 路线、标准时效、门店里程、应付费用
        if (StrUtil.isNotBlank(batteryWayBill.getLogisticsProvider())) {
            LambdaQueryWrapper<BaseBatteryRouteCost> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseBatteryRouteCost::getRepairCenterNum, batteryWayBill.getRepairCenterNum())
                    .eq(BaseBatteryRouteCost::getArrivalCity, batteryWayBill.getArrivalCity())
                    .eq(BaseBatteryRouteCost::getDispatchCity, batteryWayBill.getDispatchCity())
                    .eq(BaseBatteryRouteCost::getCarType, batteryWayBill.getCarType())
                    .eq(BaseBatteryRouteCost::getLogisticsProvider, batteryWayBill.getLogisticsProvider())
                    .eq(BaseBatteryRouteCost::getLogisticsType, logisticsType)
                    .eq(BaseBatteryRouteCost::getIsDelete, 0);
            BaseBatteryRouteCost baseBatteryRouteCost = baseBatteryRouteCostMapper.selectOne(queryWrapper);
            if (BeanUtil.isNotEmpty(baseBatteryRouteCost)) {
                batteryWayBill.setRouteDeliveryTime(baseBatteryRouteCost.getRouteDeliveryTime());
                batteryWayBill.setRouteName(baseBatteryRouteCost.getRouteName());
                batteryWayBill.setKilometersNum(baseBatteryRouteCost.getKilometersNum());
                batteryWayBill.setExpense(baseBatteryRouteCost.getExpense());
                batteryWayBill.setLogisticsProvider(baseBatteryRouteCost.getLogisticsProvider());
            }

        }

        int updateResult = this.baseMapper.updateById(batteryWayBill);
        if (1 == updateResult) {
            return ResultUtil.success("更新成功！");
        }
        return ResultUtil.failed("更新失败！");
    }

    /**
     * 车型转换
     *
     * @param logisticsType
     * @return
     */
    private String logisticsTypeConverter(Integer logisticsType) {
        if (0 == logisticsType) {
            return "快递";
        } else if (1 == logisticsType) {
            return "零担";
        } else if (2 == logisticsType) {
            return "专车";
        }
        return "专车";
    }

    /**
     * 校验输入城市是否有误
     *
     * @param dispatchCity       发运城市
     * @param arrivalCity        接收城市
     * @param transportationType 运输类型
     * @return
     */
    public Result cityVerification(String dispatchCity, String arrivalCity, Integer transportationType) {
        // 如果发运城市不空，接收城市为空，运输类型为返厂
        if (null != dispatchCity && null == arrivalCity && 0 == transportationType) {
            //说明发运城市是要查询门店表中的数据
            LambdaQueryWrapper<BaseShop> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseShop::getShopCity, dispatchCity);
            BaseShop baseShop = baseShopMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseShop)) {
                return ResultUtil.failed("发运门店城市不存在");
            }
        }
        // 如果发运城市不空，接收城市为空，运输类型为返店
        if (null != dispatchCity && null == arrivalCity && 1 == transportationType) {
            //说明发运城市是要查询维修中心表中的数据
            LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseRepairCenter::getRepairCenterCity, dispatchCity);
            BaseRepairCenter baseRepairCenter = baseRepairCenterMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseRepairCenter)) {
                return ResultUtil.failed("发运维修中心城市不存在");
            }
        }
        // 如果发运城市为空，接收城市不空，运输类型为返厂
        if (null == dispatchCity && null != arrivalCity && 0 == transportationType) {
            //说明接收城市是要查询维修中心表中的数据
            LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseRepairCenter::getRepairCenterCity, dispatchCity);
            BaseRepairCenter baseRepairCenter = baseRepairCenterMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseRepairCenter)) {
                return ResultUtil.failed("接收维修中心城市不存在");
            }
        }
        // 如果发运城市为空，接收城市不空，运输类型为返店
        if (null == dispatchCity && null != arrivalCity && 1 == transportationType) {
            //说明接收城市是要查询门店表中的数据
            LambdaQueryWrapper<BaseRepairCenter> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BaseRepairCenter::getRepairCenterCity, dispatchCity);
            BaseRepairCenter baseRepairCenter = baseRepairCenterMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(baseRepairCenter)) {
                return ResultUtil.failed("发运门店城市不存在");
            }
        }
        return ResultUtil.success("校验成功！");
    }

    /**
     * 模板下载
     *
     * @return 下载模板的链接
     */
    public ImageResponseDTO tempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(tempFileId);
        return returnVO;
    }

    /**
     * 下载运单打印pdf文件
     *
     * @param waybillCodes 多个运单编号
     * @param response
     */
    public void getPrintFile(List<String> waybillCodes, HttpServletResponse response) throws Exception {
        response.setContentType("application/x-zip-compressed");
        String printWaybill = URLEncoder.encode("运单打印", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;fileName=" + printWaybill + ".zip");
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setCharacterEncoding("utf-8");
        ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());
        for (String waybillCode : waybillCodes) {
            BatteryWayBill batteryWayBill = this.baseMapper.selectOne(new LambdaQueryWrapper<BatteryWayBill>().eq(BatteryWayBill::getWaybillCode, waybillCode).eq(BatteryWayBill::getIsDelete, 0));
            BatteryWayBillPrint.getBatteryWayBillPdf(zipOutputStream, batteryWayBill);
        }
        zipOutputStream.close();
    }
}
