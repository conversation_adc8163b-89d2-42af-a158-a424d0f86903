package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Throwables;
import com.xiaopeng.halley.ascm.boot.common.constant.Constants;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.BzAsyncExportLog;
import com.xiaopeng.halley.ascm.boot.excel.LocalDateStringConverter;
import com.xiaopeng.halley.ascm.boot.mapper.BzAsyncExportLogMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.sdk.mbp.web.service.MbpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 异步导出表
 *
 * <AUTHOR> 自动生成
 * @date 2023-01-09 06:17:47
 */
@Slf4j
@Service
public class BzAsyncExportLogService extends ServiceImpl<BzAsyncExportLogMapper, BzAsyncExportLog> {

    public static ThreadLocal threadLocalUser = new ThreadLocal<>();
    private final String FULL_QUERY_MSG = "不能进行全量导出！请添加查询参数";
    @Resource
    private ApplicationContext context;
    @Resource
    private ImageService xpFileOssFeign;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    @Resource
    private AscmLockHelper ascmLockHelper;
    // 一页条数，测试10条导出，必须使用到分页功能
    @Value("${export.pageSize}")
    private int PAGE_SIZE = 1000;

    //首字母小写
    public static String captureName(String name) {
        char[] cs = name.toCharArray();
        //已经是小写
        if (cs[0] >= 97) {
            return name;
        }
        cs[0] += 32;
        return String.valueOf(cs);

    }

    /**
     * 新增
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BzAsyncExportLog insert(Object param, String name, String mathedPath) {
        BzAsyncExportLog bzAsyncExportLog = new BzAsyncExportLog();
        bzAsyncExportLog.setName(name);
        bzAsyncExportLog.setMethodPath(mathedPath);
        if (ObjectUtil.isNotNull(param)) {
            bzAsyncExportLog.setParams(JSONObject.toJSONString(param));
        }

        // 全部都要记录，对应的仓库号
        log.info("hehhehehheheh [{}]", ascmLoginUserHelper.getLoginUser());
        log.info("hehhehehheheh 2 [{}]", ascmLoginUserHelper.getLoginUser().getAuth());

        bzAsyncExportLog = setUserAndWarehouseNumber(bzAsyncExportLog, ascmLoginUserHelper.getLoginUser(), ascmLoginUserHelper.getLoginUser().getAuth());
        this.baseMapper.insert(bzAsyncExportLog);
        bzAsyncExportLog.setId(bzAsyncExportLog.getId());
        return bzAsyncExportLog;
    }

    /**
     * 更新
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BzAsyncExportLog update(BzAsyncExportLog bzAsyncExportLogVo) throws ResultException {
        BzAsyncExportLog bzAsyncExportLog = this.get(bzAsyncExportLogVo.getId());
        if (bzAsyncExportLog == null) {
            throw new ResultException(500, "操作失败,查询不到数据");
        }
        if (this.baseMapper.updateById(bzAsyncExportLog) > 0) {
            return bzAsyncExportLogVo;
        } else {
            throw new ResultException(500, "操作失败,乐观锁异常");
        }
    }

    /**
     * 查询单个
     *
     * @param id
     * @return
     */
    public BzAsyncExportLog get(Long id) throws ResultException {
        if (ObjectUtil.isNull(id)) {
            throw new ResultException(500, "操作失败,ID不能为空");
        }
        return this.baseMapper.selectById(id);
    }

    /**
     * 填充用户和仓库
     */
    public BzAsyncExportLog setUserAndWarehouseNumber(BzAsyncExportLog bzAsyncExportLog, AscmLoginUser ascmLoginUser, String warehouseNumber) {
        bzAsyncExportLog.setLgort(warehouseNumber);

        if (Constants.MUL_LGORT_METHOD_LIST.contains(bzAsyncExportLog.getMethodPath())) {
            MbpService mbpService = context.getBean(MbpService.class);
            String lgortSplit = mbpService.getLoginInfo().getOrganizationInfo().getOrganizationRelatedList().stream()
                    .filter(e -> StrUtil.contains(e.getRemark(), "="))
                    .map(e -> e.getRemark().split("=")[1])
                    .collect(Collectors.joining(","));
            bzAsyncExportLog.setLgort(lgortSplit);
        }

        bzAsyncExportLog.setCreateUserId(ascmLoginUser.getUserId());
        bzAsyncExportLog.setUpdateUserId(ascmLoginUser.getUserId());
        bzAsyncExportLog.setCreateUserName(ascmLoginUser.getName());
        bzAsyncExportLog.setUpdateUserName(ascmLoginUser.getName());
        return bzAsyncExportLog;
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id) throws ResultException {
        BzAsyncExportLog bzAsyncExportLog = this.get(id);
        if (bzAsyncExportLog == null) {
            throw new ResultException(500, "操作失败,查询不到数据");
        }
        bzAsyncExportLog.setIsDelete(true);
        if (this.baseMapper.updateById(bzAsyncExportLog) > 0) {
            return true;
        } else {
            throw new ResultException(500, "操作失败，乐观锁异常");
        }

    }

    /**
     * 分页查询
     *
     * @param page
     * @return
     */
    public Page<BzAsyncExportLog> page(PageQuery<BzAsyncExportLog> page) {
        QueryWrapper<BzAsyncExportLog> queryWrapper = new QueryWrapper<BzAsyncExportLog>().eq(BzAsyncExportLog.COL_IS_DELETE, 0);

        if (page.getParam() != null) {
            BzAsyncExportLog eo = new BzAsyncExportLog();
            BeanUtils.copyProperties(page.getParam(), eo);
            queryWrapper.setEntity(eo);
        }
        IPage<BzAsyncExportLog> pageResult = this.baseMapper.selectPage(page.convertPage(), queryWrapper);
        return new Page<BzAsyncExportLog>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())
                .setRecords(pageResult.getRecords());
    }

    /**
     * 查询列表
     *
     * @return
     */
    public List<BzAsyncExportLog> list(BzAsyncExportLog bzAsyncExportLogVo) {
        QueryWrapper<BzAsyncExportLog> queryWrapper = new QueryWrapper<BzAsyncExportLog>().eq(BzAsyncExportLog.COL_IS_DELETE, 0);

        if (bzAsyncExportLogVo != null) {
            queryWrapper.setEntity(bzAsyncExportLogVo);
        }
        List<BzAsyncExportLog> list = this.baseMapper.selectList(queryWrapper);
        return list;
    }

    private boolean isEmptyParam(String json) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (jsonObject == null) {
                return true;
            }
            JSONObject param = jsonObject.getJSONObject("param");
            return param == null || param.isEmpty();
        } catch (Exception e) {
            return true;
        }
    }

    public void export() {
        //获取所有的导出任务
        LambdaQueryWrapper<BzAsyncExportLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(wrapper -> {
            wrapper.eq(BzAsyncExportLog::getState, 0).or().eq(BzAsyncExportLog::getState, 2);//只拿导入中的和失败的
        });
        queryWrapper.eq(BzAsyncExportLog::getIsDelete, 0);
        queryWrapper.le(BzAsyncExportLog::getExportCount, 3);
        List<BzAsyncExportLog> list = baseMapper.selectList(queryWrapper);

        list = list.stream().filter(e -> !FULL_QUERY_MSG.equals(e.getRemark())).collect(Collectors.toList());
        for (BzAsyncExportLog item : list) {
            try {
                if (isEmptyParam(item.getParams())) {
                    item.setState(2);
                    item.setRemark("不能进行全量导出！请添加查询参数");
                    baseMapper.updateById(item);
                    continue;
                }
                //加锁防止重复导出
                String buildKey = "ascm:bz:async:export:" + item.getId();
                if (ascmLockHelper.tryLockComboKey(buildKey)) {
                    try {
                        openMain(item, null);
                    } finally {
                        ascmLockHelper.unlockComboKey(buildKey);
                    }
                }
            } catch (Exception e) {
                log.error(StrUtil.format("导出失败! item: {}", JSONObject.toJSONString(item)), e);
            }
        }
    }

    public void openMain(BzAsyncExportLog item, HttpServletResponse response) throws Exception {
        String[] headPath = item.getMethodPath().split("\\.");
        String serviceName = captureName(headPath[0]);
        //处理任务
        String servicePath = context.getBean(serviceName).getClass().getName().substring(0, context.getBean(serviceName).getClass().getName().indexOf("$"));
        //拿到service
        Class<?> clazz = Class.forName(servicePath);
        Object serviceBean = context.getBean(clazz);
        //获取当前service下所有的方法 准备遍历

        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            //获取方法的名称
            String methodName = method.getName();

            String returnClassName = "";

            // 命中目标方法
            if (methodName.equals(headPath[1])) {

                File excelFile = null;

                try {

                    // 获取方法返回对象类型
                    returnClassName = getReturnClassName(method, methodName, returnClassName);

                    // 创建临时文件，这里临时文件的大小其实也是挺危险
                    excelFile = File.createTempFile(item.getName() + DateUtil.format(new Date(), "yyyy-MM-dd HH:mm"), ".xlsx");
                    ExcelWriter excelWriter = EasyExcel.write(excelFile, Class.forName(returnClassName))
                            .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                            .registerConverter(new LocalDateStringConverter())
                            .build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("sheet1").build();

                    // 如果有多租户信息填入
                    setTenant(item);

                    try {
                        // 获取第一页
                        Object returnParam = invokePage(item, serviceBean, method, 1);

                        // 判断下是否为分页内容
                        if (returnParam instanceof Page) {
                            Object returnList = ((Page<?>) returnParam).getRecords();

                            // 总页数
                            long pages = ((Page<?>) returnParam).getPages();

                            // 先写入第一页
                            excelWriter.write((Collection<?>) returnList, writeSheet);

                            // 遍历查询
                            if (pages > 1) {
                                for (int page = 2; page <= pages; ++page) {
                                    Object nextPageReturn = invokePage(item, serviceBean, method, page);
                                    Object nextReturnList = ((Page<?>) nextPageReturn).getRecords();
                                    // 分页追加写入
                                    excelWriter.write((Collection<?>) nextReturnList, writeSheet);
                                }
                            }
                        } else {
                            excelWriter.write((Collection<?>) returnParam, writeSheet);
                        }

                        // 完成输出
                        excelWriter.finish();
                    } finally {
                        // 删除租户信息
                        threadLocalUser.remove();
                    }

                    //同步处理直接返回二进制文件
                    if (item.getSyncFlag() == 1 && ObjectUtil.isNotNull(response)) {
                        // 同步直接返回文件对象

                        log.info("BzAsyncExportLogService openMain 同步处理生成生成二进制文件 ");
                        String fileName = URLEncoder.encode(item.getName(), "UTF-8");
                        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
                        outPutFile(response, excelFile);

                        return;
                    } else {
                        log.info("BzAsyncExportLogService openMain 生成二进制文件 ");
                        //拿到OSS返回文件码 保存至数据库 TODO 时区
                        String fileName = item.getName() + DateUtil.format(DateUtil.offsetHour(new Date(), 8), "yyyy-MM-dd HH:mm") + ".xlsx";
                        String fileCode = xpFileOssFeign.putFileToServer(fileName, excelFile, fileName).getFileId();

                        log.info("BzAsyncExportLogService openMain OSS反馈文件码【{}】", JSONObject.toJSONString(fileCode));
                        item.setOperationCode(fileCode);
                        item.setState(1);
                        return;
                    }

                } catch (Exception e) {
                    item.setState(2);
                    item.setRemark(StrUtil.subWithLength(Throwables.getStackTraceAsString(e), 0, 400));
                    log.error("导出异常", e);
                } finally {

                    if (excelFile != null) {
                        excelFile.delete();
                    }

                    //异步情况落入表数据
                    if (item.getSyncFlag() == 0) {
                        item.setExportCount(item.getExportCount() + 1);
                        this.baseMapper.updateById(item);
                    }
                }
            }
        }

    }

    private void outPutFile(HttpServletResponse response, File excelFile) {
        try (FileInputStream fis = new FileInputStream(excelFile);
             OutputStream os = response.getOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void setTenant(BzAsyncExportLog item) {
        Map map = new HashMap<>();
        map.put("lgort", item.getLgort());
        log.info("BzAsyncExportLogService openMain warehouseNumber值:{}", map);
        threadLocalUser.set(map);
    }

    private Object invokePage(BzAsyncExportLog item, Object serviceBean, Method method, long page) throws InstantiationException, IllegalAccessException, InvocationTargetException {
        //开始构建参数 拿到返回数据 执行导出
        //目前参数是对象方式传输 拿第一个对象即可
        Class parameter = method.getParameterTypes()[0];
        //如果是同步返回情况 那么第二个参数一定要是Response;

        Object parameterInstance = parameter.newInstance();//参数对象

        JSONObject jsonObject = new JSONObject();//参数
        if (parameterInstance instanceof PageQuery) {
            //  将当前参数转化为当前方法的入参类型
            Type[] parameterTypes = method.getGenericParameterTypes();
            for (Type paramType : parameterTypes) {
                //ParameterizedType:参数化类型，判断是否是参数化类型。
                if (paramType instanceof ParameterizedType) {
                    //获得源码中的真正的参数类型
                    Type[] genericType = ((ParameterizedType) paramType).getActualTypeArguments();
                    Class<?> actualTypeArgument = (Class<?>) genericType[0];
                    JSONObject params = (StrUtil.isBlank(item.getParams()) ? null : JSONObject.parseObject(item.getParams()));
                    if (ObjectUtil.isNotNull(params.get("param"))) {
                        params = JSONObject.parseObject(JSONObject.toJSONString(params.get("param")));
                    }
                    Object requestParam = actualTypeArgument.newInstance();
                    //放上对象
                    setField(actualTypeArgument, requestParam, params);

                    jsonObject.put("param", requestParam);
                    // 第一页
                    jsonObject.put("page", page);
                    jsonObject.put("size", PAGE_SIZE);

                    log.info("invokePage page [{}]", page);
                    log.info("invokePage size [{}]", PAGE_SIZE);

                    break;
                }

            }

        } else {
            //数据传输时一定是 PageQuery类型 但是方法入参类型不一定是
            //需要取出来其中的param

            jsonObject = StrUtil.isBlank(item.getParams()) ? null : JSONObject.parseObject(item.getParams());
            jsonObject = jsonObject == null ? null : JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get("param")));
        }

        //给所有的字段设置值
        if (ObjectUtil.isNotNull(jsonObject)) {
            setField(parameter, parameterInstance, jsonObject);
        }

        log.info("BzAsyncExportLogService openMain o 字段赋值完毕【{}】", JSONObject.toJSONString(parameterInstance));

        Object returnParam = method.invoke(serviceBean, parameterInstance);

        return returnParam;
    }

    private String getReturnClassName(Method method, String methodName, String returnClassName) {
        //返回一个Type对象，表示由该方法对象表示的方法的正式返回类型。
        //比如public List<User> getAll();那么返回的是List<User>
        Type genericReturnType = method.getGenericReturnType();
        //获取实际返回的参数名
        String returnTypeName = genericReturnType.getTypeName();
        log.info(methodName + "的返回参数是：" + returnTypeName);
        //判断是否是参数化类型
        if (genericReturnType instanceof ParameterizedType) {
            //如果是参数化类型,则强转
            ParameterizedType parameterizedType = (ParameterizedType) genericReturnType;
            //获取实际参数类型数组，比如List<User>，则获取的是数组[User]，Map<User,String> 则获取的是数组[User,String]
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            for (Type type : actualTypeArguments) {
                //强转
                Class<?> actualTypeArgument = (Class<?>) type;
                //获取实际参数的类名
                returnClassName = actualTypeArgument.getName();
                log.info(methodName + "的返回值类型是参数化类型，其类型为：" + returnClassName);
            }

        } else {
            //不是参数化类型,直接获取返回值类型
            Class<?> returnType = method.getReturnType();
            //获取返回值类型的类名
            returnClassName = returnType.getName();
            log.info(methodName + "的返回值类型不是参数化类型其类型为：" + returnClassName);

        }
        return returnClassName;
    }

    /**
     * @param parameter  目标对象class
     * @param o          需要被赋值的目标对象
     * @param jsonObject 参数
     * @throws IllegalAccessException
     */
    public void setField(Class parameter, Object o, JSONObject jsonObject) throws IllegalAccessException {
        Field[] fields = parameter.getDeclaredFields();//所有字段
        Field.setAccessible(fields, true);//可以拿到私有变量
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            //判断是否为final类型
            if (java.lang.reflect.Modifier.isFinal(field.getModifiers())) {
                continue;
            }
            String paraName = field.getName();
            if (ObjectUtil.isNull(jsonObject.get(paraName)) || StrUtil.isBlank(jsonObject.get(paraName).toString())) {
                continue;
            }
            //处理日期
            if (field.getType() == Date.class && jsonObject.get(paraName).getClass() == Long.class) {
                Date date = new Date();
                date.setTime((long) jsonObject.get(paraName));
                field.set(o, date);
            } else if (field.getType() == List.class) {
                List list = (List) jsonObject.get(paraName);
                if (CollUtil.isNotEmpty(list) && list.get(0).getClass() == Long.class) {
                    List<Date> dateList = new ArrayList<>();
                    for (int i1 = 0; i1 < list.size(); i1++) {
                        Date date = new Date();
                        date.setTime((long) list.get(i1));
                        dateList.add(date);
                    }
                    field.set(o, dateList);
                } else {
                    field.set(o, list);
                }
            } else {
                field.set(o, jsonObject.get(paraName));
            }
        }
    }

    /**
     * 日期字段加8小时
     *
     * @param page
     * @param returnList
     */
    public void dateAdd(PageQuery page, List returnList) {
        if (page.getSize() == Long.MAX_VALUE && CollUtil.isNotEmpty(returnList)) {
            returnList.forEach(item -> {
                //拿到所有字段
                Field[] declaredFields = item.getClass().getDeclaredFields();
                Field.setAccessible(declaredFields, true);
                for (Field field : declaredFields) {
                    if (field.getType() == Date.class) {
                        try {
                            if (ObjectUtil.isNotNull(field.get(item))) {
                                field.set(item, DateUtil.offsetHour((Date) field.get(item), 8));
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
                }
            });

        }
    }

    public Page<BaseAsyncExportResponseDto> newPage(PageQuery<BaseAsyncExportRequestDto> page) {

        // 新分页查询
        LambdaQueryWrapper<BzAsyncExportLog> queryWrapper = new LambdaQueryWrapper<BzAsyncExportLog>().eq(BzAsyncExportLog::getIsDelete, 0);

        if (StrUtil.isNotBlank(page.getParam().getName())) {
            queryWrapper.like(BzAsyncExportLog::getName, page.getParam().getName());
        }

        if (StrUtil.isNotBlank(page.getParam().getCreateUserName())) {
            queryWrapper.like(BzAsyncExportLog::getCreateUserName, page.getParam().getCreateUserName());
        }

        if (ObjectUtil.isNotNull(page.getParam().getState())) {
            queryWrapper.eq(BzAsyncExportLog::getState, page.getParam().getState());
        }

        if (ObjectUtil.isNotNull(page.getParam().getStartCreateTime()) && ObjectUtil.isNotNull(page.getParam().getEndCreateTime())) {
            queryWrapper.between(BzAsyncExportLog::getCreateTime, page.getParam().getStartCreateTime(), page.getParam().getEndCreateTime());
        }

        queryWrapper.orderByDesc(BzAsyncExportLog::getCreateTime);

        IPage<BzAsyncExportLog> pageResult = this.baseMapper.selectPage(page.convertPage(), queryWrapper);

        List<BaseAsyncExportResponseDto> returnList = new ArrayList<>();

        pageResult.getRecords().forEach(item -> {
            BaseAsyncExportResponseDto newItem = BeanUtil.copyProperties(item, BaseAsyncExportResponseDto.class);

            newItem.setCreateTimeShow(DateUtil.format(item.getCreateTime(), "yyyy-MM-dd HH:mm"));
            newItem.setUpdateTimeShow(DateUtil.format(item.getUpdateTime(), "yyyy-MM-dd HH:mm"));

            returnList.add(newItem);
        });

        return new Page<BaseAsyncExportResponseDto>(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal())
                .setRecords(returnList);

    }

    public BaseAsyncExportGetURLResponseDto getDownloadURL(BaseAsyncExportGetURLRequestDto requestDto) throws ResultException {

        // 查询，然后获取
        BzAsyncExportLog targetVO = this.baseMapper.selectById(requestDto.getId());

        if (ObjectUtil.isNull(targetVO)) {
            throw new ResultException(500, "查询不到任务");
        }

        //通过文件码拿到下载地址 open
        ImageResponseDTO tempResponseDTO = xpFileOssFeign.getTempURL(targetVO.getOperationCode());

        if (StrUtil.isNotBlank(tempResponseDTO.getUrl())) {
            BaseAsyncExportGetURLResponseDto returnVO = new BaseAsyncExportGetURLResponseDto();
            returnVO.setDownloadUrl(tempResponseDTO.getUrl());

            targetVO.setDownloadCount(targetVO.getDownloadCount() + 1);

            this.baseMapper.updateById(targetVO);

            return returnVO;
        } else {
            throw new ResultException(500, "数据异常无法下载");
        }

    }

}

