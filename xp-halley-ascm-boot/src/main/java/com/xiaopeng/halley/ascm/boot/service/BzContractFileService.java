package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.entity.BaseFile;
import com.xiaopeng.halley.ascm.boot.entity.BzContractFile;
import com.xiaopeng.halley.ascm.boot.mapper.BzContractFileMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BzContractFileService extends ServiceImpl<BzContractFileMapper, BzContractFile> {
	@Resource
	private ImageService imageService;
	@Resource
	private BaseFileService baseFileService;
	public static String TIME_FORMAT_PATTERN = "yyyyMM";

	@Transactional(rollbackFor = Exception.class)
	public void create(BzContractFileRequest request) {
		DateTime time = DateUtil.parse(request.getTime(), TIME_FORMAT_PATTERN);
		List<BzContractFile> bzContractFiles = this.lambdaQuery().eq(BzContractFile::getContractId, request.getContractId())
				.eq(BzContractFile::getTime, DateUtil.formatDate(time))
				.list();
		if (!bzContractFiles.isEmpty()) {
			// 获取新增的文件名
			List<BzContractFile> delContractFiles = new ArrayList<>();
			Map<String, BzContractFile> nameMap = bzContractFiles.stream().collect(Collectors.toMap(BzContractFile::getName, Function.identity()));
			for (MaterialPictureItemVo fileItem : request.getFileItems()) {
				BzContractFile bzContractFile = nameMap.get(fileItem.getFileName());
				if (bzContractFile != null) {
					delContractFiles.add(bzContractFile);
				}
			}

			// 删除文件名重复的BaseFile文件
			if (!delContractFiles.isEmpty()) {
				this.removeByIds(delContractFiles);
				baseFileService.removeByIds(delContractFiles.stream().map(BzContractFile::getBaseFileId).collect(Collectors.toList()));
			}
		}

		List<BzContractFile> contractFiles = new ArrayList<>();
		for (MaterialPictureItemVo fileItem : request.getFileItems()) {
			BaseFile baseFile = new BaseFile();
			baseFile.setFileName(fileItem.getFileName());
			baseFile.setFileUuid(UUID.fastUUID().toString());
			baseFile.setFileSaveName(fileItem.getSaveName());
			baseFile.setProject("ascm");
			baseFile.setProjectPath(ImageService.ALI_OSS_PATH);
			baseFileService.save(baseFile);

			BzContractFile bzContractFile = new BzContractFile();
			bzContractFile.setName(baseFile.getFileName());
			bzContractFile.setContractId(request.getContractId());
			bzContractFile.setBaseFileId(baseFile.getId());
			bzContractFile.setTime(time);
			contractFiles.add(bzContractFile);
		}
		this.saveBatch(contractFiles);
	}

	public Page<BzContractFileTimeVO> convertToVO(Page<BzContractFile> page) {
		List<BzContractFile> bzContractFiles = page.getRecords();
		if (bzContractFiles.isEmpty()) {
			return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
		}
		// 根据时间进行分组
		Map<Date, List<BzContractFile>> map = bzContractFiles.stream()
				.collect(Collectors.groupingBy(BzContractFile::getTime, LinkedHashMap::new, Collectors.toList()));

		Map<Date, List<BzContractFile>> sortedMap = new LinkedHashMap<>();
		map.entrySet().stream()
				.sorted(Map.Entry.comparingByKey(Comparator.reverseOrder()))
				.forEachOrdered(entry -> sortedMap.put(entry.getKey(), entry.getValue()));

		// 查询oss文件路径
		List<Long> baseFileIds = bzContractFiles.stream().map(BzContractFile::getBaseFileId).collect(Collectors.toList());
		Map<String, ImageResponseDTO> imageMap = imageService.getTempURLList(baseFileIds).stream()
				.collect(Collectors.toMap(ImageResponseDTO::getFileId, Function.identity()));

		List<BzContractFileTimeVO> result = new ArrayList<>();
		sortedMap.forEach((date, contractFiles) -> {
			List<BzContractFileTimeVO.BzContractFileItem> fileItems = new ArrayList<>();
			for (BzContractFile contractFile : contractFiles) {
				ImageResponseDTO responseDTO = imageMap.get(String.valueOf(contractFile.getBaseFileId()));
				if (responseDTO == null) {
					continue;
				}
				BzContractFileTimeVO.BzContractFileItem fileItem = new BzContractFileTimeVO.BzContractFileItem();
				fileItem.setId(contractFile.getId());
				fileItem.setFileUrl(responseDTO.getUrl());
				fileItem.setFileName(responseDTO.getFileName());
				fileItems.add(fileItem);
			}
			result.add(new BzContractFileTimeVO(DateUtil.format(date, TIME_FORMAT_PATTERN), fileItems));
		});
		return new Page<BzContractFileTimeVO>(page.getCurrent(), page.getSize(), page.getTotal()).setRecords(result);
	}

	public Page<BzContractFileTimeVO> pageView(PageQuery<BzContractQuery> pageQuery) {
		Page<BzContractFile> page = Page.of(pageQuery.getPage(), pageQuery.getSize());
		List<Map<String, Object>> mapList = baseMapper.selectByTime(page, pageQuery.getParam());

		List<String> ids = StrUtil.split(mapList.stream().map(map -> map.get("ids").toString()).collect(Collectors.joining(",")), ",");
		if (ids.isEmpty()) {
			return new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
		}

		List<BzContractFile> bzContractFiles = this.listByIds(ids);
		return convertToVO(page.setRecords(bzContractFiles));
	}
}