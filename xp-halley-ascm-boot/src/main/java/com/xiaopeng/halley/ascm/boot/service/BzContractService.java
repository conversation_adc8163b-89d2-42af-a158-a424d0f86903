package com.xiaopeng.halley.ascm.boot.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.entity.BzContract;
import com.xiaopeng.halley.ascm.boot.mapper.BzContractMapper;
import org.springframework.stereotype.Service;

@Service
public class BzContractService extends ServiceImpl<BzContractMapper, BzContract> {
	public void checkUnique(BzContract bzContract) {
		boolean exists = this.lambdaQuery()
				.not(wrapper ->
						wrapper.le(BzContract::getEndTime, bzContract.getEffectiveTime())
								.or()
								.gt(BzContract::getEffectiveTime, bzContract.getEndTime())
				)
				.eq(BzContract::getOperationWarehouse, bzContract.getOperationWarehouse())
				.ne(bzContract.getId() != null, BzContract::getId, bzContract.getId())
				.exists();
		if (exists) {
			throw new RuntimeException("该仓库有效时间段内已存在合同");
		}
	}
}
