package com.xiaopeng.halley.ascm.boot.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationVO;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import com.xiaopeng.halley.ascm.boot.mapper.BzErpReqMaterialRelMapper;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-5-16 10:18
 */
@Service
public class BzErpReqMaterialRelService extends ServiceImpl<BzErpReqMaterialRelMapper, BzErpReqMaterialRel> {

    @Resource
    private BzErpReqMaterialRelMapper bzErpReqMaterialRelMapper;

    /**
     * 运单箱号信息分页查询
     *
     * @param page
     * @return
     */
    public Page<BzWaybillBoxInformationVO> deliveryOrderPage(PageQuery<BzWaybillBoxInformationDto> page) {

        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        //查询总数
        long total = bzErpReqMaterialRelMapper.getdeliveryOrderPageTotal(page.getParam());

        List<BzWaybillBoxInformationVO> resultList = bzErpReqMaterialRelMapper.deliveryOrderPage(page.getParam(), startIndex, page.getSize());

        Page<BzWaybillBoxInformationVO> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(resultList);

        return returnPage;
    }
}
