package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaidi100.sdk.response.SubscribePushData;
import com.kuaidi100.sdk.response.SubscribeResp;
import com.kuaidi100.sdk.response.SubscribeWithMapPushParamResp;
import com.kuaidi100.sdk.response.SubscribeWithMapPushResult;
import com.xiaopeng.halley.ascm.boot.common.enums.SelfWaybillStatus;
import com.xiaopeng.halley.ascm.boot.dto.BzSelfWaybillDetail;
import com.xiaopeng.halley.ascm.boot.dto.SignSelfWaybillRequest;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.BzSelfWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillMaterialRel;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.mapper.BaseExpressTransshipmentMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzSelfWaybillMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BzSelfWaybillService extends ServiceImpl<BzSelfWaybillMapper, BzSelfWaybill> {
	@Resource
	private AscmLoginUserHelper ascmLoginUserHelper;
	@Resource
	private BzWaybillMaterialRelService bzWaybillMaterialRelService;
	@Resource
	private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;
	@Resource
	private BzWaybillTrackHistoryService bzWaybillTrackHistoryService;
	@Resource
	private BaseExpressTransshipmentMapper baseExpressTransshipmentMapper;

	public BzSelfWaybill getByWaybillCode(String waybillCode) {
		return this.lambdaQuery().eq(BzSelfWaybill::getWaybillCode, waybillCode).oneOpt()
				.orElseThrow(() -> new RuntimeException("运单不存在"));
	}

	public <T> Map<String, BzSelfWaybill> listMap(List<T> list, Function<T, String> mapper) {
		List<String> waybillCodes = list.stream().map(mapper).collect(Collectors.toList());
		return this.lambdaQuery().in(BzSelfWaybill::getWaybillCode, waybillCodes).list()
				.stream().collect(Collectors.toMap(BzSelfWaybill::getWaybillCode, Function.identity()));
	}

	@Transactional
	public SubscribeResp callBackMethod(SubscribeWithMapPushParamResp resp) {
		log.info("快递100订阅推送{}条物流节点: {}", resp.getLastResult().getData().size(), JSON.toJSONString(resp));
		SubscribeWithMapPushResult lastResult = resp.getLastResult();

		// 更新运单状态为运输中
		BzSelfWaybill bzSelfWaybill = this.lambdaQuery().eq(BzSelfWaybill::getLogisticsCode, lastResult.getNu()).oneOpt()
				.orElseThrow(() -> new RuntimeException("运单不存在"));

		// 删除旧物流节点信息
		bzWaybillTrackHistoryMapper.delete(new LambdaQueryWrapper<BzWaybillTrackHistory>()
				.eq(BzWaybillTrackHistory::getWaybillCode, bzSelfWaybill.getWaybillCode())
				.eq(BzWaybillTrackHistory::getTrackStatus, 4)
				.eq(BzWaybillTrackHistory::getIsDelete, 0)
		);

		// 全量新增物流节点信息
		String companyName = baseExpressTransshipmentMapper.getCompanyName(lastResult.getCom());
		List<BzWaybillTrackHistory> trackHistoryList = new ArrayList<>();
		for (SubscribePushData data : lastResult.getData()) {
			DateTime dateTime = DateUtil.parseDateTime(data.getFtime());
			List<String> addressParts = StrUtil.split(data.getAreaName(), ",");
			List<String> locationParts = StrUtil.split(data.getAreaCenter(), ",");

			BzWaybillTrackHistory bzWaybillTrackHistory = new BzWaybillTrackHistory();
			bzWaybillTrackHistory.setWaybillCode(bzSelfWaybill.getWaybillCode());
			bzWaybillTrackHistory.setProvince(!addressParts.isEmpty() ? addressParts.get(0) : "");
			bzWaybillTrackHistory.setCity(addressParts.size() > 1 ? addressParts.get(1) : "");
			bzWaybillTrackHistory.setAddress(data.getContext());
			bzWaybillTrackHistory.setLatitude(Double.parseDouble(locationParts.get(0)));
			bzWaybillTrackHistory.setLongitude(Double.parseDouble(locationParts.get(1)));
			bzWaybillTrackHistory.setTrackStatus(4);
			bzWaybillTrackHistory.setDriverName(companyName);
			bzWaybillTrackHistory.setDriverPhone("");
			bzWaybillTrackHistory.setCreateTime(dateTime);
			bzWaybillTrackHistory.setUpdateTime(dateTime);
			trackHistoryList.add(bzWaybillTrackHistory);
		}
		bzWaybillTrackHistoryService.saveBatch(trackHistoryList);

		// 更新运单信息
		BzWaybillTrackHistory lastTrackHistory = CollUtil.getLast(trackHistoryList);
		bzSelfWaybill.setLocationUpdateTime(lastTrackHistory.getCreateTime());
		bzSelfWaybill.setCurrentLocation(lastTrackHistory.getAddress());
		this.updateById(bzSelfWaybill);

		SubscribeResp subscribeResp = new SubscribeResp();
		subscribeResp.setResult(Boolean.TRUE);
		subscribeResp.setReturnCode("200");
		subscribeResp.setMessage("成功");
		return subscribeResp;
	}

	public BzSelfWaybillDetail getSelfWaybillDetail(Long id) {
		BzSelfWaybill bzSelfWaybill = this.getById(id);
		List<BzWaybillMaterialRel> materialList = bzWaybillMaterialRelService.lambdaQuery()
				.eq(BzWaybillMaterialRel::getWaybillCode, bzSelfWaybill.getWaybillCode()).list();
		List<BzWayBillTrackHistoryVO> trackHistoryList = bzWaybillTrackHistoryMapper.getWaybillTransportationDTO(bzSelfWaybill.getWaybillCode());
		return new BzSelfWaybillDetail(bzSelfWaybill, materialList, trackHistoryList);
	}

	@Transactional
	public void sign(SignSelfWaybillRequest request) {
		String waybillCode = CollUtil.getFirst(request.getWaybillCodes());
		BzSelfWaybill bzSelfWaybill = this.getByWaybillCode(waybillCode);
		AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
		if (!StrUtil.equals(loginUser.getPhone(), bzSelfWaybill.getDriverPhone())) {
			throw new RuntimeException("非本人的运单");
		}
		if (!bzWaybillTrackHistoryMapper.updateLatestTrackHistory(waybillCode)) {
			throw new RuntimeException("更新运单物流节点交付状态失败");
		}
		for (String fileId : request.getFileIdList()) {
//			BaseFile baseFile = new BaseFile();
//			baseFile.setFileName(picfileName);
//			baseFile.setFileUuid(buildName);
//			baseFile.setFileSaveName(saveFileName);
//			baseFile.setProject("ascm");
//			baseFile.setProjectPath(path);
			//
		}

		bzSelfWaybill.setActualArriveTime(new Date());
		bzSelfWaybill.setStatus(SelfWaybillStatus.COMPLETE.getValue());
		this.updateById(bzSelfWaybill);
	}
}