package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillFile;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillFileMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import com.xiaopeng.halley.ascm.boot.utils.Loadfont;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.BusinessException;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class BzWaybillFileService {

    @Resource
    private ImageService imageService;
    @Resource
    private BzWaybillFileMapper bzWaybillFileMapper;
    @Resource
    private BzWaybillMapper bzWaybillMapper;
    @Resource
    private BzWaybillService bzWaybillService;
    @Resource
    private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;
	@Resource
	private DragonService dragonService;

    public String getPictureFileId(MultipartFile file) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        String preName = fileName.substring(0, fileName.lastIndexOf("."));
        String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if (!fileName.contains(".")) {
            throw new BusinessException("文件名称异常！");
        }
        int indexOf = fileName.lastIndexOf(".");
        String fileType = fileName.substring(indexOf + 1);
        //图片格式
        String pictureType = "bmp,jpg,png,tif,gif,pcx,webp,jpeg,xlsx,xls";
        if (!pictureType.contains(fileType)) {
            throw new BusinessException("文件格式异常！");
        }
        //进行文件上传
        //TODO:斌哥--done
        final File excelFile = File.createTempFile(preName, suffixName);
        final String picfileName = preName + suffixName;
        file.transferTo(excelFile);
        ImageResponseDTO imageResponseDTO = imageService.putFileToServer(picfileName, excelFile);
        //删除关联临时文件
        {
            try {
                Arrays.stream(new File("uploads/").listFiles()).forEach(File::delete);
            } catch (Exception e) {
                log.warn("删除失败,e->{}", e.getMessage());
            }
        }

        return imageResponseDTO.getFileId();
    }

    public String putFileGetFileId(MultipartFile file, String fileNameFixed) throws IOException {
        //获取文件名
        String fileName = file.getOriginalFilename();
        String preName = fileName.substring(0, fileName.lastIndexOf("."));
        String suffixName = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
        if (!fileName.contains(".")) {
            throw new BusinessException("文件名称异常！");
        }
        int indexOf = fileName.lastIndexOf(".");
        String fileType = fileName.substring(indexOf + 1);
        //图片格式
        String pictureType = "bmp,jpg,png,tif,gif,pcx,webp,jpeg,xlsx,xls";
        if (!pictureType.contains(fileType)) {
            throw new BusinessException("文件格式异常！");
        }
        //进行文件上传
        //TODO:斌哥--done
        final File excelFile = File.createTempFile(preName, suffixName);
        final String picfileName = preName + suffixName;
        file.transferTo(excelFile);
        ImageResponseDTO imageResponseDTO = imageService.putFileToServer(picfileName, excelFile, fileNameFixed);
        //删除关联临时文件
        {
            try {
                Arrays.stream(new File("uploads/").listFiles()).forEach(File::delete);
            } catch (Exception e) {
                log.warn("删除失败,e->{}", e.getMessage());
            }
        }

        return imageResponseDTO.getFileId();
    }

    public JSONObject fileUploadWater(MultipartFile file, String waterText) {
        log.info("获取到的水印信息：e->{}", waterText);
        //获取文件名
        String fileName = file.getOriginalFilename();
        if (!fileName.contains(".")) {
            throw new BusinessException("文件名称异常！");
        }
        int indexOf = fileName.lastIndexOf(".");
        String fileType = fileName.substring(indexOf + 1);
        //图片格式
        String pictureType = "bmp,jpg,png,tif,gif,pcx,webp,jpeg";
        if (!pictureType.contains(fileType)) {
            throw new BusinessException("文件格式异常！");
        }

        String suffixName = fileName.substring(fileName.lastIndexOf("."));

        String preName = fileName.substring(0, fileName.lastIndexOf("."));

        try {
            final File excelFile = File.createTempFile(preName, suffixName);

            final File newFile = File.createTempFile(fileName + "new", suffixName);

            final String picfileName = preName + suffixName;

            file.transferTo(excelFile);

            BufferedImage targetImg = ImageIO.read(excelFile);

            int fontSize = 25;

            String text = waterText;

            int width = targetImg.getWidth(); //图片宽
            int height = targetImg.getHeight(); //图片高
            BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_BGR);
            Graphics2D g = bufferedImage.createGraphics();
            g.drawImage(targetImg, 0, 0, width, height, null);
            g.setColor(Color.GREEN); //水印颜色
            g.setFont(Loadfont.Font3(25f));
            // 水印内容放置在右下角
            int x = 40;
            int y = height - fontSize * 2;
            int y1 = height - fontSize;
            g.drawString(text.substring(0, 17), x, y);
            g.drawString(text.substring(17), x, y1);
            FileOutputStream outImgStream = new FileOutputStream(newFile);
            ImageIO.write(bufferedImage, "jpg", outImgStream);
            outImgStream.flush();
            outImgStream.close();
            g.dispose();

            //进行文件上传
            byte[] theSave = FileUtil.readBytes(newFile);
            //TODO:斌哥--done
//            String fileCode = xpFileOssFeign.uploadByte(theSave, "vscm", "xp-vscm-boot", fileName, 1, "vscm");
            ImageResponseDTO imageResponseDTO = imageService.putFileToServer(picfileName, newFile);

            //删除关联临时文件
            {
                try {
                    Arrays.stream(new File("uploads/").listFiles()).forEach(File::delete);
                } catch (Exception e) {
                    log.warn("删除失败,e->{}", e.getMessage());
                }
            }
            JSONObject returnObject = new JSONObject();
            returnObject.put("fileCode", imageResponseDTO.getFileId());
            returnObject.put("url", imageResponseDTO.getUrl());
            returnObject.put("fileName", imageResponseDTO.getFileName());

            return returnObject;

        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;

    }

    @Transactional
    public Result deliveryConfirm(AscmLoginUser ascmLoginUser, List<String> fileIdList, String... waybillCodes) throws ResultException {
        Result responseEntity = ResultUtil.success();
        for (int i = 0; i < waybillCodes.length; i++) {
            //检查该运单是否是该手机号下的
            LambdaQueryWrapper<BzWaybill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(BzWaybill::getWaybillCode, waybillCodes[i]).eq(BzWaybill::getIsDelete, 0);
            BzWaybill bzWaybill = bzWaybillMapper.selectOne(lambdaQueryWrapper);
            if (StrUtil.isBlank(ascmLoginUser.getPhone()) || !bzWaybill.getDriverPhone().equals(ascmLoginUser.getPhone())) {
                throw new ResultException(500, "运单：" + waybillCodes[i] + "已不属于您，请刷新运单，重新交付！");
            }
            //查询该运单下是否存在图片
            LambdaQueryWrapper<BzWaybillFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BzWaybillFile::getWaybillCode, waybillCodes[i]).eq(BzWaybillFile::getIsDelete, 0);
            List<BzWaybillFile> bzWaybillFiles = bzWaybillFileMapper.selectList(queryWrapper);
            //如果存在即删除
            if (bzWaybillFiles.size() > 0) {
                bzWaybillFiles.stream().forEach(e -> {
                    BzWaybillFile bzWaybillFile = new BzWaybillFile();
                    bzWaybillFile.setId(e.getId());
                    bzWaybillFile.setFileId(e.getFileId());
                    bzWaybillFile.setIsDelete(1);
                    bzWaybillFile.setUpdateUserId(ascmLoginUser.getUsername());
                    bzWaybillFileMapper.updateById(bzWaybillFile);
                });
            }
            //重新上传
            for (String fileId : fileIdList) {
                BzWaybillFile bzWaybillFile = new BzWaybillFile();
                bzWaybillFile.setFileId(fileId);
                bzWaybillFile.setUploadType("1");
                bzWaybillFile.setWaybillCode(waybillCodes[i]);
                if (bzWaybillFileMapper.insert(bzWaybillFile) != 1) {
                    throw new ResultException(500, "图片上传异常！");
                }
            }
            //最新的位置设置成交付状态
            LambdaQueryWrapper<BzWaybillTrackHistory> historyLambdaQueryWrapper = new LambdaQueryWrapper<>();
            historyLambdaQueryWrapper
                    .eq(BzWaybillTrackHistory::getWaybillCode, waybillCodes[i])
                    .eq(BzWaybillTrackHistory::getIsDelete, 0)
                    .orderByDesc(BzWaybillTrackHistory::getUpdateTime)
                    .last("limit 1");
            BzWaybillTrackHistory bzWaybillTrackHistory = bzWaybillTrackHistoryMapper.selectOne(historyLambdaQueryWrapper);
            if (ObjectUtils.isNotEmpty(bzWaybillTrackHistory)) {
                LambdaUpdateWrapper<BzWaybillTrackHistory> historyLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                historyLambdaUpdateWrapper.set(BzWaybillTrackHistory::getTrackStatus, 3).set(BzWaybillTrackHistory::getUpdateTime, new Date()).eq(BzWaybillTrackHistory::getId, bzWaybillTrackHistory.getId());
                bzWaybillTrackHistoryMapper.update(null, historyLambdaUpdateWrapper);
            }
            //完成运单
            LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BzWaybill::getStatus, "已完成").set(BzWaybill::getIsOntime, 1).set(BzWaybill::getSignTime, new Date()).eq(BzWaybill::getIsDelete, 0).eq(BzWaybill::getWaybillCode, waybillCodes[i]);
            if (bzWaybillMapper.update(null, updateWrapper) != 1) {
                throw new ResultException(500, "运单完成异常！");
            }
        }
        responseEntity.setMsg("运单完成成功！");
        dragonService.updateTrackSync(Arrays.asList(waybillCodes), true);
        //删除关联临时文件
        {
            try {
                Arrays.stream(new File("uploads/").listFiles()).forEach(File::delete);
            } catch (Exception e) {
                log.warn("删除失败,e->{}", e.getMessage());
            }
        }
        return responseEntity.setData(true);
    }
}
