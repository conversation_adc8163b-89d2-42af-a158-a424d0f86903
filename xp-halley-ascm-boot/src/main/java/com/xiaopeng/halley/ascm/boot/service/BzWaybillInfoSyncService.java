package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.lang.caller.CallerUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.enums.BusinessTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApi;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApiEnum;
import com.xiaopeng.halley.ascm.boot.dto.WaybillBoxListInfoDto;
import com.xiaopeng.halley.ascm.boot.dto.WaybillInfoSyncErpVO;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillInfoSync;
import com.xiaopeng.halley.ascm.boot.entity.EgressGatewayRequest;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillInfoSyncMapper;
import com.xiaopeng.halley.ascm.boot.utils.HttpUtils;
import com.xiaopeng.halley.ascm.boot.utils.POReqUtil;
import com.xpeng.athena.common.core.domain.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.service
 * @Date 2024/7/4 16:14
 */
@Slf4j
@Service
public class BzWaybillInfoSyncService extends ServiceImpl<BzWaybillInfoSyncMapper, BzWaybillInfoSync> {

    @Resource
    private PoRequestHelp egressGatewayFeign;
    @Resource
    private BzWaybillService bzWaybillService;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;

    /**
     * 实际发运时间同步erp  type为1是实际发运
     */
    public void dispatchTimeSync() {
        // 查询出所以待同步运单
        List<BzWaybill> bzWaybills = selectSyncData(1);
        if (CollUtil.isEmpty(bzWaybills)) {
            log.info("无待同步实际发运时间运单");
            return;
        }
        // 补充业务类型
        List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
        // 加锁调用ASCM0087接口
        log.info("BzWaybillInfoSyncService dispatchTimeSync size:{}", bzWaybills.size());
        for (BzWaybill bzWaybill : bzWaybills) {
            List<Integer> shopType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shopType)) {
                shopType.add(5);
            }
            waybillInfoSyncErp(bzWaybill, BusinessTypeEnum.getBusinessType(shopType.get(0)), 1);
        }
    }

    public List<BzWaybill> selectSyncData(int type, String ...waybillCodeList) {
        return bzWaybillService.getBaseMapper().selectSyncData(type, Arrays.asList(waybillCodeList));
    }

    /**
     * 实际签收时间同步erp  type为2是实际签收
     */
    public void signedTimeSync(List<BzWaybill> bzWaybills) {
        if (CollUtil.isEmpty(bzWaybills)) {
            log.info("无待同步实际签收时间运单");
            return;
        }
        // 补充业务类型
        List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
        log.info("url: {} BzWaybillInfoSyncService signedTimeSync size:{}", HttpUtils.getCurrentUrl(), bzWaybills.size());

        // 调用ASCM0087接口
        for (BzWaybill bzWaybill : bzWaybills) {
            List<Integer> shopType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shopType)) {
                shopType.add(5);
            }
            waybillInfoSyncErp(bzWaybill, BusinessTypeEnum.getBusinessType(shopType.get(0)), 2);
        }
    }

    /**
     * 获取同步运单状态
     *
     * @param bzWaybill
     * @return
     */
    @RemoteApi(RemoteApiEnum.ASCM0087)
    public void waybillInfoSyncErp(BzWaybill bzWaybill, String businessType, int type) {
        log.info("同步ERP的运单号 {}", bzWaybill.getWaybillCode());
        WaybillInfoSyncErpVO result = bzWaybillService.getBaseMapper().waybillTimeInfoSyncErp(bzWaybill.getWaybillCode());
        if (ObjectUtil.isNull(result)) {
            log.warn("本次同步时间erp发生异常，存在错误运单 {}", bzWaybill.getWaybillCode());
            return;
        }
        //进行数据封装
        String sendId = UUID.fastUUID().toString(true);
        JSONObject headParams = POReqUtil.getHeadParams("ASCM", "ERP", "ASCM0087", sendId);

        // 处理签收时间
        Date signTime = result.getSignTime();
        if (signTime != null && type == 1 && DateUtil.compare(signTime, new Date(0)) == 0) {
            log.warn("签收时间为初始时间，本次数据不同步");
            return;
        }
        // 处理发运时间
        Date departureTime = result.getDepartureTime();
        if (departureTime != null && type == 2 && DateUtil.compare(departureTime, new Date(0)) == 0) {
            log.warn("发运时间为初始时间，本次数据不同步");
            return;
        }

        // 封装DATA的数据
        JSONObject jsonObject = new JSONObject();
        List<WaybillBoxListInfoDto> boxList = result.getBoxList();
        JSONArray boxJsonArr = new JSONArray();
        for (WaybillBoxListInfoDto waybillBoxListInfoDto : boxList) {
            JSONObject boxInfo = new JSONObject();
            boxInfo.put("BOXCODE", waybillBoxListInfoDto.getBoxCode());
            List<String> deliveryOrderCodeList = waybillBoxListInfoDto.getDeliveryOrderCodeList();
            JSONArray deliveryOrderCodeListJson = new JSONArray();
            for (String deliveryOrderCode : deliveryOrderCodeList) {
                JSONObject deliveryOrderCodeJson = new JSONObject();
                deliveryOrderCodeJson.put("DELIVERYORDERCODE", deliveryOrderCode);
                deliveryOrderCodeListJson.add(deliveryOrderCodeJson);
            }
            boxInfo.put("DELIVERYORDERLIST", deliveryOrderCodeListJson);
            boxJsonArr.add(boxInfo);
        }
        jsonObject.put("BOXLIST", boxJsonArr);
        jsonObject.put("WAYBILLCODE", result.getWaybillCode());
        jsonObject.put("ZCLASS", businessType);
        if (null != departureTime) {
            jsonObject.put("ZSJFHDT", DateUtil.format(departureTime, "yyyyMMdd"));// 实际发货日期
            jsonObject.put("ZSJFHTM", DateUtil.format(departureTime, "HHmmss"));// 实际发货时间
        }
        if (null != signTime) {
            jsonObject.put("ZSJQSDT", DateUtil.format(signTime, "yyyyMMdd"));// 实际签收日期
            jsonObject.put("ZSJQSTM", DateUtil.format(signTime, "HHmmss"));// 实际签收时间
        }
        JSONObject params = new JSONObject();
        params.put("HEADER", headParams);
        params.put("DATA", jsonObject);

        log.info("同步ERP的数据 {}", params);
        // 调用WMS
        EgressGatewayRequest request = new EgressGatewayRequest();
        request.setAppId("xp-halley-ascm");
        request.setApiCode(POInvokeEnum.SI_ASCM0087_Syn_Out.getApiCode());
        request.setData(params);

        try {
            Result syncResult = egressGatewayFeign.invoke(request);
            log.info("BzWaybillInfoSyncService waybillInfoSyncErp 处理完成 result [{}]", JSONObject.toJSONString(syncResult));
            LambdaUpdateWrapper<BzWaybillInfoSync> luw = new LambdaUpdateWrapper<>();
            luw.set(type == 1, BzWaybillInfoSync::getDispatchTimeSync, 1);
            luw.set(type == 2, BzWaybillInfoSync::getSignedTimeSync, 1);
            luw.eq(BzWaybillInfoSync::getWaybillId, bzWaybill.getId());
            luw.eq(BzWaybillInfoSync::getWaybillCode, bzWaybill.getWaybillCode());
            update(luw);
        } catch (Exception e) {
            log.error(StrUtil.format("BzWaybillInfoSyncService waybillInfoSyncErp 同步ERP运单实际时间信息发生异常！{}", jsonObject), e);
        }
    }

}
