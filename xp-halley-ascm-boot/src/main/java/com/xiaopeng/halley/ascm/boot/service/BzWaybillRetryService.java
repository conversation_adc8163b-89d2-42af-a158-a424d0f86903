package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.common.enums.*;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryDto;
import com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryVo;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillRetry;
import com.xiaopeng.halley.ascm.boot.entity.EgressGatewayRequest;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillRetryMapper;
import com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant.WarehouseTypeEnum;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.service
 * @Date 2024/4/23 17:25
 */
@Slf4j
@Service
public class BzWaybillRetryService extends ServiceImpl<BzWaybillRetryMapper, BzWaybillRetry> {

    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Value("#{'${send.warehouseList}'.split(',')}")
    private List<Integer> sendWarehouse;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
    @Resource
    private BzWaybillService bzWaybillService;
    @Resource
    private PoRequestHelp egressGatewayFeign;

    /**
     * 补充字段内容
     *
     * @param args
     * @param code
     * @param userName
     * @param waybillRetry
     */
    private static void addEntityContent(JSONObject args, Integer code, String userName, BzWaybillRetry waybillRetry) {
        waybillRetry.setPayloadContent(args.toJSONString());
        waybillRetry.setFirstSendDate(new Date());
        waybillRetry.setFirstSendState(code == 200 ? 1 : 2);
        waybillRetry.setLastSendDate(new Date());
        waybillRetry.setLastSendName(userName);
        waybillRetry.setRequestCount(1);
        waybillRetry.setIsShow(1);
    }

    /**
     * 分页查询
     *
     * @param page 查询参数
     * @return 结果
     */
    public Page<BzWaybillRetryVo> getPage(PageQuery<BzWaybillRetryDto> page) {

        log.info("BzWaybillRetryService getPage start...");

        // 获取总条数
        long total = this.baseMapper.getPageTotal(page.getParam());
        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BzWaybillRetryVo> returnList = this.baseMapper.getPage(page.getParam(), startIndex, page.getSize());

        //  处理一下默认时间展示
        returnList.forEach(item -> {
            String firstDate = SDF.format(item.getFirstSendDate());
            String lastDate = SDF.format(item.getLastSendDate());
            if ("1970-01-01 08:00:00".equals(firstDate)) {
                item.setFirstSendDate(null);
            }
            if ("1970-01-01 08:00:00".equals(lastDate)) {
                item.setLastSendDate(null);
            }
            String targetSystem = item.getTargetSystem();
            String systemType = SendSystemEnum.getSystemName(Integer.valueOf(targetSystem));
            item.setTargetSystem(systemType);
        });

        Page<BzWaybillRetryVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(returnList);
        return returnPage;
    }

    /**
     * 创建条目
     *
     * @param waybillCode 运单编号
     */
    public void createRetryItem(String waybillCode) {
        log.info("BzWaybillRetryService createRetryItem waybillCode：{}", waybillCode);
        try {
            // 避免因为创建运单的事务未提交导致未读到数据，小休一会儿
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            log.error("BzWaybillRetryService createRetryItem 发生异常！");
        }
        // 先根据运单编号查询运单信息
        BzWaybill waybill = bzWaybillService.query().eq("waybill_code", JSONObject.parseObject(waybillCode, String.class)).last("limit 1").one();
        if (BeanUtil.isEmpty(waybill)) {
            log.warn("BzWaybillRetryService createRetryItem 运单不存在！ 运单编号：{}", waybillCode);
            return;
        }
        // 根据运单信息判断运单需要创建什么条目
        List<BaseWarehouse> baseWarehouseList = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
        List<BaseWarehouse> baseWarehouses = baseWarehouseList.stream().filter(item -> item.getLgort().equals(waybill.getShopCode())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(baseWarehouses)) {
            // 判断是否需要创建0083接口的条目
            if (sendWarehouse.contains(baseWarehouses.get(0).getWarehouseType())) {
                // 创建条目
                createEntity(waybill, baseWarehouses);
            }
        }

        List<BaseWarehouse> warehouseList = baseWarehouseList.stream().filter(item -> item.getLgort().equals(waybill.getLgort())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(warehouseList)) {
            // 判断是否需要创建0086接口的条目
            BaseWarehouse baseWarehouse = warehouseList.get(0);
            if (WarehouseTypeEnum.isWms(baseWarehouse.getWarehouseType()) || baseWarehouse.getWarehouseType() == 2) {
                // 创建条目
                if (baseWarehouse.getWarehouseType() != 1) {
                    baseWarehouse.setWarehouseType(1);
                }
                createEntity(waybill, warehouseList);
            }
        }

    }

    private void createEntity(BzWaybill waybill, List<BaseWarehouse> baseWarehouses) {
        String interfaceNumber = InterfaceNumberEnum.getInterfaceNumber(baseWarehouses.get(0).getWarehouseType());
        BzWaybillRetry waybillRetry = new BzWaybillRetry();
        waybillRetry.setWaybillCode(waybill.getWaybillCode());
        waybillRetry.setWarehouseNumber(waybill.getLgort());
        waybillRetry.setInterfaceNumber(interfaceNumber);
        waybillRetry.setInterfaceDescribe(InterfaceDescribeEnum.getInterfaceDescribe(baseWarehouses.get(0).getWarehouseType()));
        waybillRetry.setShopCode(waybill.getShopCode());
        waybillRetry.setShopDescribe(waybill.getShopName());
        waybillRetry.setTargetSystem(baseWarehouses.get(0).getWarehouseType().toString());
        waybillRetry.setPayloadContent(JSONObject.toJSONString("-"));
        if (interfaceNumber.equals(InterfaceNumberEnum.WMS.getType())) {
            for (int i = 1; i < 3; i++) {
                waybillRetry.setId(null);
                waybillRetry.setSendSource(i + "0");
                this.save(waybillRetry);
            }
            return;
        }
        this.save(waybillRetry);
    }

    /**
     * 查看报文
     *
     * @param id 条目id
     * @return 报文信息
     */
    public Result<String> getPayload(Long id) {
        log.info("BzWaybillRetryService getPayload id:{}", id);
        BzWaybillRetry waybillRetry = getById(id);
        if (BeanUtil.isEmpty(waybillRetry)) {
            return ResultUtil.failed("获取失败");
        }
        String payloadContent = waybillRetry.getPayloadContent();
        return ResultUtil.success(payloadContent);
    }

    /**
     * 重试
     *
     * @param id 条目id
     * @return 重试结果
     */
    public Result<String> retry(Long id) {
        log.info("BzWaybillRetryService retry id:{}", id);
        // 查询条目信息
        BzWaybillRetry waybillRetry = query().eq("id", id).eq("is_show", 1).one();
        // 判断条目是否允许重试
        if (BeanUtil.isEmpty(waybillRetry)) {
            return ResultUtil.failed("运单不存在！");
        }
        // 分发重试接口
        Result<String> payload = getPayload(waybillRetry.getId());
        String json = payload.getData();
        EgressGatewayRequest egressGatewayRequest = JSON.parseObject(json, EgressGatewayRequest.class);
        log.info("egressGatewayRequest = {}", egressGatewayRequest);
        try {
            egressGatewayFeign.invoke(egressGatewayRequest);
        } catch (Exception e) {
            log.error("BzWaybillRetryService retry {}", e.getMessage());
            return ResultUtil.failed("重推失败！");
        }
        return ResultUtil.success("重发成功！");
    }

    /**
     * 记录接口详情报文和结果
     *
     * @param eventData
     */
    public void apiDetails(Map<String, Object> eventData) {
        log.info("BzWaybillRetryService apiDetails {}", JSON.toJSONString(eventData));
        // 解析接口报文写入数据库
        String jsonString = (String) eventData.get("data");
        Map<String, Object> data = JSON.parseObject(jsonString, new TypeReference<Map<String, Object>>() {
        });
        // 获取请求参数
        JSONObject args = (JSONObject) data.get("args");
        log.info("args = {}", args);
        JSONObject payload = (JSONObject) args.get("data");
        // 解析报文运单号
        Object obj = payload.get("DATA");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(obj));
        String waybillCode = jsonObject.getString("waybillCode");
        // 解析请求结果
        Integer code = 500;
        if (ObjectUtil.isEmpty(data.get("result"))) {
            code = 500;
        } else {
            // TODO:注意解析是否会异常
            try {
                JSONObject result = (JSONObject) data.get("result");
                code = result.getInteger("code");
            } catch (Exception e) {
                log.error("解析请求报文发生异常 {}", e.getMessage());
            }
        }
        // 解析发运人
        String userName = (String) data.get("name");
        // 判断是那个接口的数据
        if (POInvokeEnum.SI_ASCM0083_Syn_Out.getApiCode().equals(args.get("apiCode"))) {
            distributeApi(waybillCode, InterfaceConstantEnum.ASCM0083.getName(), args, code, userName, null);
        } else if (POInvokeEnum.SI_ASCM0086_Syn_Out.getApiCode().equals(args.get("apiCode"))) {
            // ASCM0086接口还需要解析发送源
            String source = jsonObject.getString("ZSOURCE");
            // 查询之前是否有调用，如果有调用那就是更新最后一次发运相关的信息
            distributeApi(waybillCode, InterfaceConstantEnum.ASCM0086.getName(), args, code, userName, source);
        }
    }

    /**
     * 分发记录的类型
     *
     * @param waybillCode
     * @param interfaceNumber
     * @param args
     * @param code
     * @param userName
     */
    private void distributeApi(String waybillCode, String interfaceNumber, JSONObject args, Integer code, String userName, String source) {
        // 查询之前是否有调用，如果有调用那就是更新最后一次发运相关的信息
        BzWaybillRetry waybillRetry = query().eq("waybill_code", waybillCode)
                .eq("interface_number", interfaceNumber).eq(StrUtil.isNotEmpty(source), "send_source", source).one();
        // 是空的话就是拆单发运的
        if (BeanUtil.isEmpty(waybillRetry)) {
            OrderSplitSave(waybillCode, args, code, userName, interfaceNumber, source);
            return;
        }
        // 不是空的就判断接口请求的次数
        if (1 <= waybillRetry.getRequestCount()) {
            log.info("distributeApi retry");
            BzWaybillRetry bzWaybillRetry = new BzWaybillRetry();
            bzWaybillRetry.setId(waybillRetry.getId());
            retryUpdateApiDetails(bzWaybillRetry, userName, code);
            return;
        }
        // 首次处理0083的接口信息
        updateApiDetails(args, code, userName, interfaceNumber, source);
    }

    private void OrderSplitSave(String waybillCode, JSONObject args, Integer code, String userName, String interfaceConstant, String source) {
        log.info("BzWaybillRetryService apiDetails 拆单发运 {}", waybillCode);
        String[] split = waybillCode.split("-");
        String parentWaybillCode = split[0];
        log.info("parentWaybillCode = {}", parentWaybillCode);
        // 可直接找父运单
        BzWaybillRetry parentWaybill = query().eq("waybill_code", parentWaybillCode)
                .eq("interface_number", interfaceConstant).eq(StrUtil.isNotEmpty(source), "send_source", source).one();
        if (BeanUtil.isEmpty(parentWaybill)) {
            log.warn("BzWaybillRetryService OrderSplitSave 运单：{} 无父运单，记录接口信息发生异常！", waybillCode);
            return;
        }
        log.info("parentWaybill = {}", JSON.toJSONString(parentWaybill));
        BzWaybillRetry waybillRetry = new BzWaybillRetry();
        BeanUtils.copyProperties(parentWaybill, waybillRetry, "id", "waybillCode");
        waybillRetry.setWaybillCode(waybillCode);
        addEntityContent(args, code, userName, waybillRetry);
        save(waybillRetry);
    }

    /**
     * 重推更新
     *
     * @param bzWaybillRetry
     * @param lastSendName
     * @param code
     */
    private void retryUpdateApiDetails(BzWaybillRetry bzWaybillRetry, String lastSendName, Integer code) {
        bzWaybillRetry.setLastSendName(lastSendName);
        bzWaybillRetry.setLastSendDate(new Date());
        bzWaybillRetry.setRetryReturnMsg(code == 200 ? 1 : 2);
        bzWaybillRetry.setIsShow(1);
        updateById(bzWaybillRetry);
    }

    /**
     * 第一次更新
     *
     * @param args
     * @param code
     * @param lastSendName
     * @param interfaceNum
     */
    private void updateApiDetails(JSONObject args, Integer code, String lastSendName, String interfaceNum, String source) {
        JSONObject payload = (JSONObject) args.get("data");
        Object obj = payload.get("DATA");
        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(obj));
        String waybillCode = jsonObject.getString("waybillCode");
        log.info("waybillCode = {} ", waybillCode);
        // 根据运单编号和接口信息，更新运单重试信息
        BzWaybillRetry waybillRetry = new BzWaybillRetry();
        addEntityContent(args, code, lastSendName, waybillRetry);
        update(waybillRetry, new UpdateWrapper<BzWaybillRetry>().eq("waybill_code", waybillCode)
                .eq("interface_number", interfaceNum).eq(StrUtil.isNotBlank(source), "send_source", source));
    }

    /**
     * 删除过时数据
     *
     * @param jobParam 清理多长时间的数据
     */
    public void cleanOutdatedData(String jobParam) {
        if (StrUtil.isBlank(jobParam)) {
            log.error("任务参数错误！");
            return;
        }
        log.info("jobParam = {}", jobParam);
        DateTime dateTime = DateUtil.offsetDay(new Date(), -Integer.parseInt(jobParam));
        log.info("dateTime = {}", dateTime);
        // 30天前的数据清理掉
        remove(new QueryWrapper<BzWaybillRetry>().lt("create_time", dateTime));
    }
}
