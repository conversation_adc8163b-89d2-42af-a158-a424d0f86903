package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.util.concurrent.RateLimiter;
import com.google.gson.Gson;
import com.kuaidi100.sdk.api.QueryTrack;
import com.kuaidi100.sdk.api.Subscribe;
import com.kuaidi100.sdk.contant.ApiInfoConstant;
import com.kuaidi100.sdk.core.IBaseClient;
import com.kuaidi100.sdk.pojo.HttpResult;
import com.kuaidi100.sdk.request.*;
import com.kuaidi100.sdk.response.SubscribeResp;
import com.kuaidi100.sdk.utils.SignUtils;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaSyncDataTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.constant.KafkaConstant;
import com.xiaopeng.halley.ascm.boot.common.constant.WaybillStatusConstants;
import com.xiaopeng.halley.ascm.boot.common.enums.*;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqMaterialRelDto;
import com.xiaopeng.halley.ascm.boot.dto.importExport.ImportResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.importExport.TempResponseDto;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import com.xiaopeng.halley.ascm.boot.dto.waybill.*;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.listener.ActualTimeListener;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant.WarehouseTypeEnum;
import com.xiaopeng.halley.ascm.boot.page.bzErpReqPage.constant.BoxStatusEnum;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant.WaybillStatusEnum;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto.DeliveryImportInExcel;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.dto.LogisticsImportInExcel;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.listener.DeliveryImportDataListener;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.listener.LogisticsImportDataListener;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import com.xiaopeng.halley.ascm.boot.service.third.ThirdPartyService;
import com.xiaopeng.halley.ascm.boot.utils.HttpUtils;
import com.xiaopeng.halley.ascm.boot.utils.POReqUtil;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BoxVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.StatusVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.WayBillShopVO;
import com.xiaopeng.mqi.common.LoginUser;
import com.xiaopeng.mqi.helper.LoginUserHelper;
import com.xiaopeng.mqi.wx.util.RedisHelper;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.BusinessException;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BzWaybillService extends ServiceImpl<BzWaybillMapper, BzWaybill> {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    final Integer ONE = 1;
    @Resource
    private BaseWaybillRulesMapper baseWaybillRulesMapper;
    @Resource
    private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;
    @Resource
    private BzWaybillTrackHistoryService bzWaybillTrackHistoryService;
    @Resource
    private BzWaybillMapper bzWaybillMapper;
    @Resource
    private BaseTransportTypeMapper baseTransportTypeMapper;
    @Resource
    private BzWaybillFileMapper bzWaybillFileMapper;
    @Resource
    private BaseShopMapper baseShopMapper;
    @Resource
    private BzWaybillTrackTruckDriverMapper bzWaybillTrackTruckDriverMapper;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
    @Resource
    private BzWaybillBoxRelMapper bzWaybillBoxRelMapper;
    @Resource
    private BzErpReqBoxMapper bzErpReqBoxMapper;
    @Resource
    private BzErpReqMaterialRelMapper bzErpReqMaterialRelMapper;
    @Resource
    private ImageService imageService;
    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private RedissonClient redisson;
    @Resource
    private BzWaybillInfoSyncMapper bzWaybillInfoSyncMapper;
    @Resource
    private BzWaybillExportRecordMapper bzWaybillExportRecordMapper;
    @Value("${fileTemp.waybill.tempFileId}")
    private String tempFileId;
    @Value("${fileTemp.waybill.tempDeliveryFileId}")
    private String tempDeliveryFileId;
    @Value("${fileTemp.actualTime.tempFileId}")
    private String actualTimeTempFileId;
    @Value("#{'${send.warehouseList}'.split(',')}")
    private List<Integer> sendWarehouse;

    @Resource
    private BaseDemolitionCombineRecordMapper baseDemolitionCombineRecordMapper;
    @Resource
    private BasePrintOutTaskMapper basePrintOutTaskMapper;
    @Resource
    private AscmLockHelper ascmLockHelper;
    @Resource
    private PoRequestHelp egressGatewayFeign;
    @Value("${delivery.callbackUrl}")
    private String callbackUrl;
    @Value("${delivery.drivePhone}")
    private String drivePhone;
    @Value("${delivery.hddrivePhone}")
    private String hdDrivePhone;
    @Value("${delivery.whDrivePhone}")
    private String whDrivePhone;
    @Value("${delivery.key}")
    private String key;
    @Value("${delivery.customer}")
    private String customer;
    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;
    @Resource
    private BaseExpressTransshipmentMapper baseExpressTransshipmentMapper;
    @Resource
    private AscmEventKafkaProducer ascmEventKafkaProducer;
    @Resource
    private ThirdPartyService thirdPartyService;
    @Lazy
    @Resource
    private BzWaybillInfoSyncService bzWaybillInfoSyncService;
	@Resource
	private DragonService dragonService;
    @Resource
    private RedisHelper redisHelper;

    public void checkKuaidi100WaybillFinish(String waybillCode) {
        boolean exists = bzWaybillTrackHistoryService.lambdaQuery().eq(BzWaybillTrackHistory::getWaybillCode, waybillCode)
                .eq(BzWaybillTrackHistory::getTrackStatus, 4)
                .eq(BzWaybillTrackHistory::getIsDelete, 0)
                .exists();
        if (exists) {
            throw new RuntimeException("存在快递100的物流节点，不允许操作");
        }
    }

    public BzWaybill getByCode(String waybillCode) {
        BzWaybill bzWaybill = CollUtil.getFirst(this.lambdaQuery().eq(BzWaybill::getWaybillCode, waybillCode).list());
        return Assert.notNull(bzWaybill, "运单不存在！运单号: {}", waybillCode);
    }

    public void setRoute(String route) {
        AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
        String userId = loginUser.getUserId();
        String lgort = CollUtil.getFirst(StrUtil.split(loginUser.getAuth(), StrUtil.COMMA));
        String key = RedisKeyManager.PDA_ROUTE_CACHE.buildKey(userId, lgort);
        log.info("PDA设置路线 key: {}, route: {}", key, route);
        redisHelper.set(key, route, 30, TimeUnit.DAYS);
    }

    public String getRoute() {
        AscmLoginUser loginUser = ascmLoginUserHelper.getLoginUser();
        String userId = loginUser.getUserId();
        String lgort = CollUtil.getFirst(StrUtil.split(loginUser.getAuth(), StrUtil.COMMA));
        String key = RedisKeyManager.PDA_ROUTE_CACHE.buildKey(userId, lgort);
        return redisHelper.get(key, String.class);
    }

    public List<BzWaybillDetailDto> calVolumeWeight(Collection<String> calWaybillCodes, boolean isUpdate) {
        if (calWaybillCodes.isEmpty()) {
            return Collections.emptyList();
        }
        // 更新运单总体积、总重量
        List<BzWaybillDetailDto> bzWaybillDetails = bzWaybillMapper.calVolumeWeight(new ArrayList<>(calWaybillCodes));
        log.info("重新计算的运单 bzWaybills: {}", bzWaybillDetails);
        if (isUpdate) {
            List<BzWaybill> bzWaybills = BeanUtil.copyToList(bzWaybillDetails, BzWaybill.class);
            bzWaybills.forEach(e -> e.setUpdateTime(new Date()));
            SpringUtil.getBean(this.getClass()).updateBatchById(bzWaybills);
        }
        return bzWaybillDetails;
    }

    /**
     * 校验运单收货方是否为分仓
     */
    public Map<String, Boolean> verifierReceiverIsCentral(List<String> waybillCodeList) {
        if (CollUtil.isEmpty(waybillCodeList)) {
            return new HashMap<>();
        }

        Map<String, BaseWarehouse> warehouseMap = baseWarehouseMapper.selectList(new LambdaQueryWrapper<BaseWarehouse>()
                        .eq(BaseWarehouse::getIsDelete, 0))
                .stream().collect(Collectors.toMap(BaseWarehouse::getLgort, Function.identity()));
        Set<String> lgortList = warehouseMap.keySet();

        List<BzWaybill> bzWaybills = this.lambdaQuery()
                .in(BzWaybill::getWaybillCode, waybillCodeList)
                .list();

        Map<String, Boolean> mainWarehouse = new HashMap<>();
        for (BzWaybill bzWaybill : bzWaybills) {
            boolean contains = lgortList.contains(bzWaybill.getShopCode());
            mainWarehouse.put(bzWaybill.getWaybillCode(), contains);
        }
        return mainWarehouse;
    }

    /**
     * 日期格式校验
     *
     * @param date 源
     * @return 校验结果，正确 true ,错误 false
     */
    private static boolean isValidDate(String date) {
        try {
            LocalDate.parse(date, DATE_FORMATTER);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * 时间格式校验
     *
     * @param date 源
     * @return 校验结果，正确 true ,错误 false
     */
    private static boolean isValidTime(String date) {
        //时间格式校验
        String patternString = "^(?:[01]\\d|2[0-3])[0-5]\\d[0-5]\\d$";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(date);
        return matcher.matches();
    }

    /**
     * 运单详情
     */
    public BzWaybillDetailDto waybillDetail(String waybillCode, int type, int pageNo, int pageSize) throws ResultException {
        log.info("BzWaybillService waybillDetail [{}]", waybillCode);

        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<BzWaybill>().eq("waybill_code", waybillCode).eq("is_delete", 0).last("limit 1"));

        log.info("BzWaybillService bzWaybill [{}]", JSONObject.toJSONString(bzWaybill));

        if (null == bzWaybill) {
            throw new ResultException(500, "找不到运单！");
        }

        BzWaybillDetailDto bzWaybillDetailDTO = baseMapper.getDetailById(waybillCode, "", "", bzWaybill.getCarrierCode());

        log.info("BzWaybillService bzWaybillDetailDTO [{}]", JSONObject.toJSONString(bzWaybillDetailDTO));
        if (ObjectUtil.isNull(bzWaybillDetailDTO)) {
            throw new ResultException(500, "找不到运单！");
        }

        //取出所有箱号
        if (pageNo == 0) {
            pageNo = 0;
        }
        if (pageSize == 0) {
            pageSize = 1000000;//？？？？
        }
        Page<BzErpReqBoxRelDto> page = new Page<>(pageNo, pageSize);

        IPage<BzErpReqBoxRelDto> boxRelList = bzWaybillBoxRelMapper.getBoxesByWaybillCodePage(page, waybillCode, type);

        log.info("BzWaybillService boxRelList [{}]", JSONObject.toJSONString(boxRelList));

        //进行去重
        Set<BzErpReqBoxRelDto> playerSet = new TreeSet<>(Comparator.comparing(o -> (o.getDeliveryOrderCode() + o.getBoxCode())));

        log.info("BzWaybillService playerSet [{}]", JSONObject.toJSONString(playerSet));

        playerSet.addAll(boxRelList.getRecords());

        ArrayList<BzErpReqBoxRelDto> boxRelDtos = new ArrayList<>(playerSet);

        log.info("BzWaybillService boxRelDtos [{}]", JSONObject.toJSONString(boxRelDtos));

        for (int i = 0; i < boxRelDtos.size(); i++) {
            //箱号
            String boxCode = boxRelDtos.get(i).getBoxCode();
            //交货单号
            String deliveryOrderCode = boxRelDtos.get(i).getDeliveryOrderCode();
            List<BzErpReqMaterialRelDto> allByBoxCode = bzErpReqMaterialRelMapper.findAllByBoxCode(boxCode);

            log.info("BzWaybillService allByBoxCode [{}]", JSONObject.toJSONString(allByBoxCode));

            //进行去重
            allByBoxCode = allByBoxCode.stream().filter(e -> e.getDeliveryOrderCode().equals(deliveryOrderCode)).collect(Collectors.toList());
            Double totalMaterialVolume = 0.0;
            List<Double> collect = allByBoxCode.stream().map(BzErpReqMaterialRelDto::getMaterialVolume).map(Double::valueOf).collect(Collectors.toList());
            for (Double aDouble : collect) {
                totalMaterialVolume += aDouble;
            }
            boxRelDtos.get(i).setTotalMaterialVolume(totalMaterialVolume);
            boxRelDtos.get(i).setMaterialList(allByBoxCode);
        }
        if (null != boxRelList) {
            boxRelList.setTotal(boxRelDtos.size());
            bzWaybillDetailDTO.setBoxRelList(boxRelList.setRecords(boxRelDtos));
        }
        return bzWaybillDetailDTO;

    }

    /**
     * 物流模板文件
     */
    public TempResponseDto tempFile() {
        log.info("BzWaybillService tempFile 开始执行");
        ImageResponseDTO tempURL = imageService.getTempURL(tempFileId);
        TempResponseDto responseDTO = new TempResponseDto();
        responseDTO.setUrl(tempURL.getUrl());
        responseDTO.setFileName(tempURL.getFileName());
        return responseDTO;
    }

    /**
     * 快递模板文件
     */
    public TempResponseDto tempFileDelivery() {
        log.info("BzWaybillService tempFileDelivery 开始执行");
        ImageResponseDTO tempURL = imageService.getTempURL(tempDeliveryFileId);
        TempResponseDto responseDTO = new TempResponseDto();
        responseDTO.setUrl(tempURL.getUrl());
        responseDTO.setFileName(tempURL.getFileName());
        return responseDTO;
    }

    /**
     * 下载错误物流文件
     */
    public void failDownload(String operationCode, HttpServletResponse response) {

        RBucket<List<LogisticsImportInExcel>> bucketFail = redisson.getBucket(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(operationCode));
        List<LogisticsImportInExcel> failList = bucketFail.get();
//        List<LogisticsImportInExcel> failList = redisHelper.getList(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(operationCode), LogisticsImportInExcel.class);

        if (ObjectUtil.isNull(failList)) {
            log.warn("查询导入失败文件失败或者文件已失效");
            throw new BusinessException("查询导入失败文件失败或者文件已失效");
        }

        RBucket<List<LogisticsImportInExcel>> bucketSuccess = redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(operationCode));
        List<LogisticsImportInExcel> successList = bucketSuccess.get();
//        List<LogisticsImportInExcel> successList = redisHelper.getList(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(operationCode), LogisticsImportInExcel.class);
        if (ObjectUtil.isNotNull(successList) && !successList.isEmpty()) {
            failList.addAll(successList);
        }
        String fileName = "物流信息错误数据";
        try {
            fileName = URLEncoder.encode("物流信息错误数据", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("下载错误物流文件失败 url encode 异常 errorMessage:{}", e.getMessage());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        try {
            EasyExcel.write(response.getOutputStream(), LogisticsImportInExcel.class).excelType(ExcelTypeEnum.XLSX).sheet("sheet").doWrite(failList);
        } catch (IOException e) {
            log.error("下载错误物流文件失败 errorMessage:{}", e.getMessage());
        }
    }

    /**
     * 下载错误快递文件
     */
    public void failDeliveryDownload(String operationCode, HttpServletResponse response) {

        RBucket<List<DeliveryImportInExcel>> bucketFail = redisson.getBucket(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(operationCode));
        List<DeliveryImportInExcel> failList = bucketFail.get();

        if (ObjectUtil.isNull(failList)) {
            log.info("查询导入失败文件失败或者文件已失效");
            throw new BusinessException("查询导入失败文件失败或者文件已失效");
        }

        RBucket<List<DeliveryImportInExcel>> bucketSuccess = redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(operationCode));
        List<DeliveryImportInExcel> successList = bucketSuccess.get();
        if (ObjectUtil.isNotNull(successList) && !successList.isEmpty()) {
            failList.addAll(successList);
        }
        String fileName = "快递信息错误数据";
        try {
            fileName = URLEncoder.encode("快递信息错误数据", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("下载错误快递文件失败 url encode 异常 errorMessage:{}", e.getMessage());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

        try {
            EasyExcel.write(response.getOutputStream(), DeliveryImportInExcel.class).excelType(ExcelTypeEnum.XLSX).sheet("sheet").doWrite(failList);
        } catch (IOException e) {
            log.error("下载错误快递文件失败 errorMessage:{}", e.getMessage());
        }
    }

    /**
     * 导入物流校验
     */
    public ImportResponseDto importCheckList(MultipartFile file) {
        LogisticsImportDataListener dataListener = new LogisticsImportDataListener();
        //构建返回数据
        ImportResponseDto responseDTO = new ImportResponseDto();

        try {
            EasyExcel.read(file.getInputStream(), LogisticsImportInExcel.class, dataListener).sheet().doRead();

            //获取监听的导入数据
            List<LogisticsImportInExcel> logisticsImportInExcels = dataListener.getList();
            if (logisticsImportInExcels.size() > 200) {
                throw new BusinessException("导入数据不能超过200条");
            }

            //校验通过数据
            List<LogisticsImportInExcel> successLogistics = new ArrayList<>();

            //校验失败数据
            List<LogisticsImportInExcel> failLogistics = new ArrayList<>();

            List<String> waybillCodeList = logisticsImportInExcels.stream()
                    .map(LogisticsImportInExcel::getWaybillCode)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.toList());
            Map<String, String> statusMap = this.lambdaQuery().in(!waybillCodeList.isEmpty(), BzWaybill::getWaybillCode, waybillCodeList).list()
                    .stream().collect(Collectors.toMap(BzWaybill::getWaybillCode, BzWaybill::getStatus));

            List<String> allowStatusList = Arrays.asList(WaybillStatusConstants.PENDING_TRANSIT, WaybillStatusConstants.IN_TRANSIT,
                    WaybillStatusConstants.EXCEPTION_OCCURRED, WaybillStatusConstants.COMPLETED);
            Map<String, Boolean> mainWarehouse = verifierReceiverIsCentral(waybillCodeList);
            //处理校验逻辑
            for (LogisticsImportInExcel item : logisticsImportInExcels) {
                //地址编码
                if (StrUtil.isBlankIfStr(item.getWaybillCode())) {
                    item.setRemark("运单编码不能为空！");
                    failLogistics.add(item);
                    continue;
                } else {
                    BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setWaybillCode(item.getWaybillCode()).setIsDelete(0)));
                    if (null == bzWaybill) {
                        item.setRemark("找不到关联运单！");
                        failLogistics.add(item);
                        continue;
                    }
                }

                String status = statusMap.get(item.getWaybillCode());
                if (!allowStatusList.contains(status)) {
                    item.setRemark("只有【待运输、运输中、异常中、已完成】状态的运单可以导入！");
                    failLogistics.add(item);
                    continue;
                }

                if (Boolean.TRUE.equals(mainWarehouse.get(item.getWaybillCode()))) {
                    item.setRemark("分仓的运单不能导入！");
                    failLogistics.add(item);
                    continue;
                }

                //城市
                if (StrUtil.isBlankIfStr(item.getLocalInfo())) {
                    item.setRemark("城市不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //省份
                if (StrUtil.isBlankIfStr(item.getProvince())) {
                    item.setRemark("省份不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //具体位置
                if (StrUtil.isBlankIfStr(item.getAddress())) {
                    item.setRemark("具体位置不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //位置更新时间
                if (StrUtil.isBlankIfStr(item.getLocalUpdateTime())) {
                    item.setRemark("位置更新时间不能为空！");
                    failLogistics.add(item);
                    continue;
                } else {
                    try {
                        DateUtils.parseDate(item.getLocalUpdateTime(), DatePattern.PURE_DATETIME_PATTERN);
                    } catch (Exception e) {
                        log.warn("位置更新时间格式有误！inputParams:{} errorMessage:{}", item.getLocalUpdateTime(), e.getMessage());
                        item.setRemark("位置更新时间格式有误！");
                        failLogistics.add(item);
                        continue;
                    }
                }

                //物流单号
                if (StrUtil.isBlankIfStr(item.getLogisticsCode())) {
                    item.setRemark("物流单号不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //物流公司
                if (StrUtil.isBlankIfStr(item.getLogisticsCompany())) {
                    item.setRemark("物流公司不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                successLogistics.add(item);
            }

            String uuid = UUID.randomUUID().toString();
            if (!failLogistics.isEmpty()) {
//                redisHelper.set(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(uuid), failLogistics, 24, TimeUnit.HOURS);
                redisson.getBucket(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(uuid)).set(failLogistics, 24, TimeUnit.HOURS);
            }

            if (!successLogistics.isEmpty()) {
//                redisHelper.set(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(uuid), successLogistics, 24, TimeUnit.HOURS);
                redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(uuid)).set(successLogistics, 24, TimeUnit.HOURS);
            }

            responseDTO.setFailCount(failLogistics.size());
            responseDTO.setSuccessCount(successLogistics.size());
            responseDTO.setFileCode(uuid);
        } catch (IOException e) {
            dataListener.clear();
        } finally {
            dataListener.clear();
        }

        return responseDTO;
    }

    /**
     * 导入快递校验
     */
    public ImportResponseDto importCheckDeliveryList(MultipartFile file) {
        DeliveryImportDataListener dataListener = new DeliveryImportDataListener();
        //构建返回数据
        ImportResponseDto responseDTO = new ImportResponseDto();

        try {
            EasyExcel.read(file.getInputStream(), DeliveryImportInExcel.class, dataListener).sheet().doRead();

            //获取监听的导入数据
            List<DeliveryImportInExcel> deliveryImportInExcelList = dataListener.getList();

            //校验通过数据
            List<DeliveryImportInExcel> successLogistics = new ArrayList<>();

            //校验失败数据
            List<DeliveryImportInExcel> failLogistics = new ArrayList<>();

            //处理校验逻辑
            List<String> waybillList = deliveryImportInExcelList.stream().map(DeliveryImportInExcel::getWaybillCode).collect(Collectors.toList());
            //重复运单号集合
            HashMap<String, Integer> waybillCodeMap = new HashMap<>();
            ArrayList<String> repeatWaybillList = new ArrayList<>();
            //重复快递单号集合
            List<String> deliveryCodeList = deliveryImportInExcelList.stream().map(DeliveryImportInExcel::getLogisticsCode).collect(Collectors.toList());
            HashMap<String, Integer> deliveryCodeMap = new HashMap<>();
            ArrayList<String> repeatDeliveryCodeList = new ArrayList<>();
            for (int i = 0; i < waybillList.size(); i++) {
                if (waybillCodeMap.containsKey(waybillList.get(i))) {
                    repeatWaybillList.add(waybillList.get(i));
                    waybillCodeMap.put(waybillList.get(i), waybillCodeMap.get(waybillList.get(i)) + 1);
                } else {
                    waybillCodeMap.put(waybillList.get(i), 1);
                }
                if (deliveryCodeMap.containsKey(deliveryCodeList.get(i))) {
                    repeatDeliveryCodeList.add(deliveryCodeList.get(i));
                    deliveryCodeMap.put(deliveryCodeList.get(i), deliveryCodeMap.get(deliveryCodeList.get(i)) + 1);
                } else {
                    deliveryCodeMap.put(deliveryCodeList.get(i), 1);
                }
            }

            //空间换时间(运单)
            Set<String> waybillCodes = waybillCodeMap.keySet();
            QueryWrapper<BzWaybill> waybillCodeQueryWrapper = new QueryWrapper<>();
            waybillCodeQueryWrapper.in("waybill_code", waybillCodes);
            waybillCodeQueryWrapper.eq("is_delete", 0);
            List<BzWaybill> waybillCodeAndStatusList = bzWaybillMapper.selectList(waybillCodeQueryWrapper);
            Map<String, String> waybillCodeAndStatusMap = waybillCodeAndStatusList.stream().collect(Collectors.toMap(BzWaybill::getWaybillCode, BzWaybill::getStatus));

            //空间换时间(物流单)
            Set<String> deliveryCodes = deliveryCodeMap.keySet();
            QueryWrapper<BzWaybill> deliveryCodesQueryWrapper = new QueryWrapper<>();
            deliveryCodesQueryWrapper.in("logistics_code", deliveryCodes);
            deliveryCodesQueryWrapper.eq("is_delete", 0);
            List<BzWaybill> deliveryCodeAndWaybillCodeList = bzWaybillMapper.selectList(deliveryCodesQueryWrapper);
            Map<String, String> deliveryCodeAndWaybillCodeMap = deliveryCodeAndWaybillCodeList.stream().collect(Collectors.toMap(BzWaybill::getLogisticsCode, BzWaybill::getWaybillCode));

            for (DeliveryImportInExcel item : deliveryImportInExcelList) {
                //地址编码
                if (StrUtil.isBlankIfStr(item.getWaybillCode())) {
                    item.setRemark("运单编号不能为空！");
                    failLogistics.add(item);
                    continue;
                } else {
                    if (repeatWaybillList.contains(item.getWaybillCode())) {
                        item.setRemark("运单编号重复！");
                        failLogistics.add(item);
                        continue;
                    }
                    //BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setWaybillCode(item.getWaybillCode()).setIsDelete(0)));
                    if (!waybillCodeAndStatusMap.containsKey(item.getWaybillCode())) {
                        item.setRemark("找不到关联运单！");
                        failLogistics.add(item);
                        continue;
                    }
                    if (!("待运输".equals(waybillCodeAndStatusMap.get(item.getWaybillCode())) || "运输中".equals(waybillCodeAndStatusMap.get(item.getWaybillCode())) || "异常中".equals(waybillCodeAndStatusMap.get(item.getWaybillCode())))) {
                        item.setRemark("当前运单处于：" + waybillCodeAndStatusMap.get(item.getWaybillCode()) + "！");
                        failLogistics.add(item);
                        continue;
                    }
                }

                //寄件人联系电话
                if (StrUtil.isBlankIfStr(item.getDeliveryPhone())) {
                    item.setRemark("寄件人联系电话不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //物流单号
                if (StrUtil.isBlankIfStr(item.getLogisticsCode())) {
                    item.setRemark("物流单号不能为空！");
                    failLogistics.add(item);
                    continue;
                } else {
                    if (repeatDeliveryCodeList.contains(item.getLogisticsCode())) {
                        item.setRemark("物流单号重复！");
                        failLogistics.add(item);
                        continue;
                    }
                    //BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setLogisticsCode(item.getLogisticsCode()).setIsDelete(0)));
                    if (deliveryCodeAndWaybillCodeMap.containsKey(item.getLogisticsCode())) {
                        item.setRemark("物流单号" + item.getLogisticsCode() + "已被绑定在运单：" + deliveryCodeAndWaybillCodeMap.get(item.getLogisticsCode()) + "下！");
                        failLogistics.add(item);
                        continue;
                    }
                }

                //物流公司
                if (StrUtil.isBlankIfStr(item.getLogisticsCompany())) {
                    item.setRemark("物流公司不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                //物流公司编码
                if (StrUtil.isBlankIfStr(item.getDeliveryCompanyCode())) {
                    item.setRemark("物流公司编码不能为空！");
                    failLogistics.add(item);
                    continue;
                }

                successLogistics.add(item);
            }

            String uuid = UUID.randomUUID().toString();
            if (!failLogistics.isEmpty()) {
                redisson.getBucket(RedisKeyManager.ADDRESS_IN_FAIL_EXCEL.buildKey(uuid)).set(failLogistics, 24, TimeUnit.HOURS);
            }

            if (!successLogistics.isEmpty()) {
                redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(uuid)).set(successLogistics, 24, TimeUnit.HOURS);
            }

            responseDTO.setFailCount(failLogistics.size());
            responseDTO.setSuccessCount(successLogistics.size());
            responseDTO.setFileCode(uuid);
        } catch (IOException e) {
            dataListener.clear();
        } finally {
            dataListener.clear();
        }

        return responseDTO;
    }

    /**
     * 导入
     */
    public ImportResponseDto importList(String operationCode) {

        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        RBucket<List<LogisticsImportInExcel>> bucket = redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(operationCode));
        List<LogisticsImportInExcel> successList = bucket.get();

        int fail = 0;
        int success = 0;
        Set<String> successWaybillCodeSet = new HashSet<>();
        ImportResponseDto responseDTO = new ImportResponseDto();
        List<String> waybillCodeList = successList.stream().map(LogisticsImportInExcel::getWaybillCode).collect(Collectors.toList());
        List<BzWaybill> bzWaybillList = bzWaybillMapper.selectList(new QueryWrapper<BzWaybill>().in("waybill_code", waybillCodeList).eq("is_delete", 0));

        for (BzWaybill waybill : bzWaybillList) {
            List<LogisticsImportInExcel> selectLogistics = successList.stream().filter(l -> {
                return l.getWaybillCode().equalsIgnoreCase(waybill.getWaybillCode());
            }).collect(Collectors.toList());

            if (CollUtil.isEmpty(selectLogistics)) {
                fail++;
            } else {
                try {
                    LogisticsImportInExcel target = selectLogistics.get(0);

                    //司机姓名
                    if (StringUtils.isNotBlank(target.getDriverName())) {
                        waybill.setDriverName(target.getDriverName());
                    }

                    //联系电话
                    if (StringUtils.isNotBlank(target.getDriverPhone())) {
                        waybill.setDriverPhone(target.getDriverPhone());
                    }

                    //城市
                    if (StringUtils.isNotBlank(target.getLocalInfo())) {
                        waybill.setLocalInfo(target.getLocalInfo());
                    }

                    //位置更新时间
                    if (StringUtils.isNotBlank(target.getLocalUpdateTime())) {
                        Date parseDate = DateUtil.parse(target.getLocalUpdateTime(), DatePattern.PURE_DATETIME_PATTERN);
                        waybill.setLocalUpdateTime(parseDate);
                    }
                    DateTime earlyTime = selectLogistics.stream().map(e -> DateUtil.parse(e.getLocalUpdateTime(), DatePattern.PURE_DATETIME_PATTERN))
                            .min(Comparator.naturalOrder()).orElseThrow(() -> new RuntimeException("位置更新时间为空"));
                    log.info("excel中位置最早时间 {}", earlyTime);
                    if ("1970-01-01 08:00:00".equals(DateUtil.formatDateTime(waybill.getDepartureTime()))
                            || earlyTime.before(waybill.getDepartureTime())) {
                        waybill.setDepartureTime(earlyTime);
                        successWaybillCodeSet.add(waybill.getWaybillCode());
                    }

                    //物流单号
                    if (StringUtils.isNotBlank(target.getLogisticsCode())) {
                        waybill.setLogisticsCode(target.getLogisticsCode());
                    }

                    //物流公司
                    if (StringUtils.isNotBlank(target.getLogisticsCompany())) {
                        waybill.setLogisticsCompany(target.getLogisticsCompany());
                    }
                    waybill.setUpdateUserName(ascmLoginUser.getName());
                    waybill.setUpdateUserId(ascmLoginUser.getUsername());
                    waybill.setUpdateTime(new Date());

                    trackHistoryReset(selectLogistics, waybill);

                    int isSuccess = bzWaybillMapper.updateById(waybill);

                    if (isSuccess == 1) {
                        success++;
                    } else {
                        fail++;
                    }
                } catch (Exception e) {
                    log.error(StrUtil.format("导入运单发生异常 inputParams:{}", JSON.toJSONString(waybill), e.getMessage()), e);
                    fail++;
                }
            }
        }
        responseDTO.setSuccessCount(success);
        responseDTO.setFailCount(fail);
        BzWaybillService bzWaybillService = SpringUtil.getBean(this.getClass());
        bzWaybillService.syncWaybillTransportInfoByImport(successWaybillCodeSet);
        return responseDTO;
    }

    /**
     * 导入快递信息订阅
     */
    public ImportResponseDto importDeliveryList(String operationCode) throws Exception {

        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        RBucket<List<DeliveryImportInExcel>> bucket = redisson.getBucket(RedisKeyManager.ADDRESS_IN_SUCCESS_EXCEL.buildKey(operationCode));
        List<DeliveryImportInExcel> successList = bucket.get();
        int fail = 0;
        int success = 0;
        ImportResponseDto responseDTO = new ImportResponseDto();
        //进行订阅回填
        for (int i = 0; i < successList.size(); i++) {
            DeliveryImportInExcel deliveryImportInExcel = successList.get(i);
            //校验运单是否存在
            BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>()
                    .eq(BzWaybill::getWaybillCode, deliveryImportInExcel.getWaybillCode())
                    .eq(BzWaybill::getIsDelete, 0));
            if (ObjectUtil.isEmpty(bzWaybill)) {
                log.warn("导入快递信息订阅:运单号" + deliveryImportInExcel.getWaybillCode() + "不存在！");
                fail++;
                continue;
            }
            //运单存在进行快递100订阅
            Map result = this.subscribeDelivery(deliveryImportInExcel.getLogisticsCode(), deliveryImportInExcel.getDeliveryCompanyCode(), deliveryImportInExcel.getDeliveryPhone(), null);
            if ("200".equals(result.get("returnCode"))) {
                //进行回填
                Result responseEntity = this.backFillExpress(deliveryImportInExcel.getWaybillCode(), deliveryImportInExcel.getDeliveryCompanyCode(), deliveryImportInExcel.getLogisticsCode(), ascmLoginUser);
                if ((Boolean) responseEntity.getData()) {
                    //回填成功
                    success++;
                } else {
                    //回填失败
                    log.error("导入快递信息订阅:运单号" + deliveryImportInExcel.getWaybillCode() + "回填失败！");
                    fail++;
                }
            } else {
                //订阅失败
                fail++;
                log.warn("导入快递信息订阅:运单号" + deliveryImportInExcel.getWaybillCode() + "绑定快递失败！原因：" + result.get("message"));
            }
        }
        responseDTO.setSuccessCount(success);
        responseDTO.setFailCount(fail);
        return responseDTO;
    }

    @SuppressWarnings("all")
    private final RateLimiter rateLimiter = RateLimiter.create(5);
    @Getter
    private final Cache<String, Map<String, Object>> addressCache = CacheBuilder.newBuilder().maximumSize(200).build();

    @SuppressWarnings("all")
    public Map<String, Object> getURLContentCache(String address) {
        try {
            rateLimiter.acquire();
            return addressCache.get(address, () -> {
                return getURLContent(address);
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private void trackHistoryReset(List<LogisticsImportInExcel> selectLogistics, BzWaybill waybill) throws ParseException {
        //查询出这个运单的所有历史位置记录
        LambdaQueryWrapper<BzWaybillTrackHistory> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BzWaybillTrackHistory::getWaybillCode, selectLogistics.get(0).getWaybillCode()).eq(BzWaybillTrackHistory::getIsDelete, 0);
        List<BzWaybillTrackHistory> trackHistoryList = bzWaybillTrackHistoryMapper.selectList(lambdaQueryWrapper);
        for (LogisticsImportInExcel selectLogistic : selectLogistics) {
            //运单位置更新
            BzWaybillTrackHistory bzWaybillTrackHistory = new BzWaybillTrackHistory();
            //设置运单号
            bzWaybillTrackHistory.setWaybillCode(selectLogistic.getWaybillCode());
            //设置城市
            bzWaybillTrackHistory.setCity(selectLogistic.getLocalInfo());
            //具体位置
            if (StringUtils.isNotBlank(selectLogistic.getAddress())) {
                //解析位置的经纬度
                Map<String, Object> map = getURLContentCache(selectLogistic.getProvince() + "省" + selectLogistic.getLocalInfo() + "市" + selectLogistic.getAddress());
                //经纬度设置
                if (StringUtils.isNotBlank(map.get("lng").toString())) {
                    bzWaybillTrackHistory.setLongitude(Double.parseDouble(map.get("lng").toString()));
                }
                if (StringUtils.isNotBlank(map.get("lat").toString())) {
                    bzWaybillTrackHistory.setLatitude(Double.parseDouble(map.get("lat").toString()));
                }
                bzWaybillTrackHistory.setAddress(selectLogistic.getAddress());
            }
            //设置省份
            if (StringUtils.isNotBlank(selectLogistic.getProvince())) {
                bzWaybillTrackHistory.setProvince(selectLogistic.getProvince());
            }
            //设置位置更新时间
            if (StringUtils.isNotBlank(selectLogistic.getLocalUpdateTime())) {
                bzWaybillTrackHistory.setUpdateTime(DateUtils.parseDate(selectLogistic.getLocalUpdateTime(), DatePattern.PURE_DATETIME_PATTERN));
            }
            //设置司机手机号
            bzWaybillTrackHistory.setDriverPhone(selectLogistic.getDriverPhone());
            //设置司机姓名
            bzWaybillTrackHistory.setDriverName(selectLogistic.getDriverName());

            trackHistoryList.add(bzWaybillTrackHistory);
        }
        trackHistoryList = trackHistoryList.stream().sorted(Comparator.comparing(BzWaybillTrackHistory::getUpdateTime)).collect(Collectors.toList());
        if (trackHistoryList.size() == 1) {
            bzWaybillTrackHistoryMapper.insert(trackHistoryList.get(0));
            //最后设置运单手机号
            waybill.setDriverPhone(trackHistoryList.get(trackHistoryList.size() - 1).getDriverPhone());
            return;
        }
        for (int i = 0; i < trackHistoryList.size() - 1; i++) {
            if (i == 0) {
                trackHistoryList.get(0).setTrackStatus(0);
                if (trackHistoryList.get(0).getId() == null) {
                    bzWaybillTrackHistoryMapper.insert(trackHistoryList.get(0));
                } else {
                    bzWaybillTrackHistoryMapper.updateById(trackHistoryList.get(0));
                }
                continue;
            }
            if (!trackHistoryList.get(i).getDriverPhone().equals(trackHistoryList.get(i - 1).getDriverPhone())) {
                //转交
                trackHistoryList.get(i).setTrackStatus(1);
            } else {
                //在途中
                trackHistoryList.get(i).setTrackStatus(2);
            }
            if (trackHistoryList.get(i).getId() == null) {
                bzWaybillTrackHistoryMapper.insert(trackHistoryList.get(i));
            } else {
                bzWaybillTrackHistoryMapper.updateById(trackHistoryList.get(i));
            }
        }
        //判断对后一个与上一个是否为转交
        if (!trackHistoryList.get(trackHistoryList.size() - 1).getDriverPhone().equals(trackHistoryList.get(trackHistoryList.size() - 2).getDriverPhone())) {
            //设置为转交
            trackHistoryList.get(trackHistoryList.size() - 1).setTrackStatus(1);
            if (trackHistoryList.get(trackHistoryList.size() - 1).getId() == null) {
                bzWaybillTrackHistoryMapper.insert(trackHistoryList.get(trackHistoryList.size() - 1));
            } else {
                bzWaybillTrackHistoryMapper.updateById(trackHistoryList.get(trackHistoryList.size() - 1));
            }
        } else {
            //判断最后一个是否需要设置成交付状态
            LambdaQueryWrapper<BzWaybill> bzWaybillLambdaQueryWrapper = new LambdaQueryWrapper<>();
            bzWaybillLambdaQueryWrapper.eq(BzWaybill::getWaybillCode, selectLogistics.get(0).getWaybillCode()).eq(BzWaybill::getIsDelete, 0);
            BzWaybill bzWaybill = bzWaybillMapper.selectOne(bzWaybillLambdaQueryWrapper);
            if ("已完成".equals(bzWaybill.getStatus())) {
                trackHistoryList.get(trackHistoryList.size() - 1).setTrackStatus(3);
            } else {
                trackHistoryList.get(trackHistoryList.size() - 1).setTrackStatus(2);
            }
            if (trackHistoryList.get(trackHistoryList.size() - 1).getId() == null) {
                bzWaybillTrackHistoryMapper.insert(trackHistoryList.get(trackHistoryList.size() - 1));
            } else {
                bzWaybillTrackHistoryMapper.updateById(trackHistoryList.get(trackHistoryList.size() - 1));
            }
        }
        //最后设置运单手机号
        waybill.setDriverPhone(trackHistoryList.get(trackHistoryList.size() - 1).getDriverPhone());
        dragonService.updateTrackSync(Collections.singletonList(waybill.getWaybillCode()), true);
    }

    /**
     * 导出
     */
    @Async
    public void export(WaybillExportSearchDto exportDto) throws IOException {
        List<WaybillExportDto> list = this.baseMapper.export(exportDto);
        if (CollUtil.isNotEmpty(list)) {
            ExcelWriter excelWriter = null;
            String fileName = System.getProperty("user.dir") + "/运单明细导出" + System.currentTimeMillis() + ".xls";
            File excelFile = new File(fileName);
            try {
                ClassPathResource classPathResource = new ClassPathResource("/file/newExportTemp.xls");
                excelWriter = EasyExcel.write(excelFile).withTemplate(classPathResource.getInputStream()).excelType(ExcelTypeEnum.XLS).build();
                FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
                WriteSheet writeSheet = EasyExcel.writerSheet().build();
                list.forEach(str -> {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String departureTime = sdf.format(str.getDepartureTime());
                    String signTime = sdf.format(str.getSignTime());
                    String expiryTime = sdf.format(str.getExpiryTime());
                    if ("1970-01-01 08:00:00".equals(departureTime)) {
                        str.setNewDepartureTime("未发车");
                    } else {
                        str.setNewDepartureTime(sdf.format(str.getDepartureTime()));
                    }
                    if ("1970-01-01 08:00:00".equals(expiryTime)) {
                        str.setNewExpiryTime("未发车");
                    } else {
                        str.setNewExpiryTime(sdf.format(str.getExpiryTime()));
                    }
                    if ("1970-01-01 08:00:00".equals(signTime)) {
                        str.setNewSignTime("未签收");
                    } else {
                        str.setNewSignTime(sdf.format(str.getSignTime()));
                    }
                });
                excelWriter.fill(new FillWrapper(list), fillConfig, writeSheet);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("运单明细导出异常！【{}】", e.toString());
            } finally {
                if (excelWriter != null) {
                    excelWriter.close();
                }
            }
            byte[] bytesArray = fileToBinArray(excelFile);
            // 将生成的excel提交OSS
            //TODO:斌哥--done
//            fileId = xpFileOssFeign.uploadByte(bytesArray, "ascm", "primuss", "运单明细.xls", 1, "ascm");
            ImageResponseDTO imageResponseDTO = imageService.putFileToServer(excelFile.getName(), excelFile, "运单明细.xls");
            bzWaybillExportRecordMapper.updateStatus(exportDto.getExportId(), imageResponseDTO.getFileId());
            excelFile.delete();
        } else {
            LambdaUpdateWrapper<BzWaybillExportRecord> updateWrapper = new LambdaUpdateWrapper<>();
            //没有相应时间段的数据，设置状态值为2
            updateWrapper.set(BzWaybillExportRecord::getStatus, 2).eq(BzWaybillExportRecord::getIsDelete, 0).eq(BzWaybillExportRecord::getId, exportDto.getExportId());
            bzWaybillExportRecordMapper.update(null, updateWrapper);
        }
    }

    /**
     * 文件转字节
     *
     * @param excelFile
     * @return
     * @throws IOException
     */
    private byte[] fileToBinArray(File excelFile) throws IOException {

        byte[] bytesArray = new byte[(int) excelFile.length()];

        FileInputStream fis = new FileInputStream(excelFile);
        fis.read(bytesArray);
        fis.close();
        return bytesArray;
    }

    /**
     * PDA运单列表
     */
    @Transactional(readOnly = true)
    public Page<BzWaybillListDto> waybillList(PageQuery<PDAWaybillListPageRequestDto> page) {
        log.info("BzWaybillService waybillList PDA运单列表,page开始执行,page->{}", page);

        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        log.info("开始执行分页查询");
        // 构建分仓运单分页参数
        if (1 == page.getParam().getPageType()) {
            List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
            List<String> lgortList = baseWarehouses.stream().map(BaseWarehouse::getLgort).collect(Collectors.toList());
            page.getParam().setShopCodeList(lgortList);
        }

        List<BzWaybillListDto> waybillList = baseMapper.getPDAWaybillList(page.getParam(), startIndex, page.getSize());

        int totalRecords = this.baseMapper.getPADWaybillPageTotal(page.getParam());

        Page<BzWaybillListDto> returnPage = new Page<>();
        returnPage.setTotal(totalRecords);
        //总页数
        int pages = 0;
        if (totalRecords % page.getSize() == 0) {
            pages = (int) (totalRecords / page.getSize()) == 0 ? 1 : (int) (totalRecords / page.getSize());
        } else {
            pages = (int) (totalRecords / page.getSize()) + 1;
        }
        returnPage.setPages(pages);
        returnPage.setCurrent(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(waybillList);
        return returnPage;
    }

    /**
     * 小程序运单列表
     */
    @Transactional(readOnly = true)
    public Page<BzWaybillListDto> waybillMiniProgramList(PageQuery<PDAWaybillListPageRequestDto> page, AscmLoginUser ascmLoginUser) {
        log.info("BzWaybillService waybillList PDA运单列表,page开始执行,page->{}", page);

        // 分页开始的索引
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        log.info("开始执行分页查询");

        List<BzWaybillListDto> waybillList = baseMapper.getMiniProgramWaybills(page.getParam(), startIndex, page.getSize(), ascmLoginUser.getPhone());

        int totalRecords = this.baseMapper.getMiniProgramWaybillPageTotal(page.getParam(), ascmLoginUser.getPhone());

        // 获取门店信息为空的门店编码
        List<String> shopCodeList = waybillList.stream()
                .filter(showItem -> (StrUtil.isBlank(showItem.getShopCity()) || StrUtil.isBlank(showItem.getShopName())))
                .map(BzWaybillListDto::getShopCode).collect(Collectors.toList());

        Map<String, BaseShop> shopMap = getShopList(shopCodeList);

        waybillList.forEach(item -> {
            if (shopMap.keySet().contains(item.getShopCode())) {
                BaseShop baseShop = shopMap.get(item.getShopCode());
                if (ObjectUtil.isNotNull(baseShop)) {
                    // 为空，设置一下
                    item.setShopCity(StrUtil.isNotBlank(baseShop.getShopCity()) ? baseShop.getShopCity() : "");
                    item.setShopName(StrUtil.isNotBlank(baseShop.getShopName()) ? baseShop.getShopName() : "");
                }
            }
        });

        Page<BzWaybillListDto> returnPage = new Page<>();
        returnPage.setTotal(totalRecords);
        //总页数
        int pages = 0;
        if (totalRecords % page.getSize() == 0) {
            pages = (int) (totalRecords / page.getSize()) == 0 ? 1 : (int) (totalRecords / page.getSize());
        } else {
            pages = (int) (totalRecords / page.getSize()) + 1;
        }
        returnPage.setPages(pages);
        returnPage.setCurrent(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(waybillList);
        return returnPage;
    }

    /**
     * 获取订单状态数量
     **/
    public List<Map<String, String>> statusNum() {
        List<Map<String, String>> returnList = new ArrayList<>();

        Map<String, String> map = new HashMap<>();
        map.put("status", "已发布");
        map.put("num", "0");
        returnList.add(map);
        Map<String, String> mapTwo = new HashMap<>();
        mapTwo.put("status", "待提货");
        mapTwo.put("num", "0");
        returnList.add(mapTwo);
        Map<String, String> mapThree = new HashMap<>();
        mapThree.put("status", "待运输");
        mapThree.put("num", "0");
        returnList.add(mapThree);

        List<Map<String, String>> searchList = baseMapper.statusNum("", "");

        for (Map<String, String> returnItem : returnList) {

            for (Map<String, String> searchItem : searchList) {
                if (returnItem.get("status").equals(searchItem.get("status"))) {
                    returnItem.put("num", String.valueOf(searchItem.get("num")));
                }
            }

        }

        return returnList;
    }

    /**
     * 核箱扫描
     */
    public List<BzErpReqBoxRelDto> getBoxAllByCode(String boxCode) throws ResultException {
        if (StrUtil.isBlankIfStr(boxCode)) {
            throw new ResultException(500, "箱号不能为空！");
        }
        return baseMapper.getBoxAllByCode(boxCode, "", "");
    }

    /**
     * 获取运单状态
     */
    public WaybillStatusDto getWaybillStatus(String boxCode) {
        return baseMapper.getWaybillStatus(boxCode);
    }

    /**
     * 获取运单状态(通过运单号)
     */
    public WaybillStatusDto getWaybillStatusByNO(String waybillCode) {
        return baseMapper.getWaybillStatusByNo(waybillCode);
    }

    /**
     * 核箱提货
     */
    //TODO:斌哥--done
    @Transactional
    public Result verifyStatus(String waybillCode, String boxCode, AscmLoginUser newAscmLoginUser) throws ResultException {

        BzWaybillBoxRel bzWaybillBoxRel = bzWaybillBoxRelMapper.selectOne(new QueryWrapper<BzWaybillBoxRel>().eq("is_delete", 0).eq("box_code", boxCode).eq("is_devanning", 0));
        if (null == bzWaybillBoxRel) {
            throw new ResultException(41001, "找不到箱号信息");
        }
        if (!waybillCode.equals(bzWaybillBoxRel.getWaybillCode())) {
            throw new ResultException(41002, "来自不同运单,无法完成扫描");
        }

        Map status = baseMapper.verifyStatus(waybillCode, boxCode, "", "");
        Result responseEntity = ResultUtil.success();
        if (null == status || status.size() <= 0) {
            throw new ResultException(41003, "运单不存在！");
        }
        if (!status.get("waybillStatus").equals(WaybillStatusConstants.PUBLISHED)) {
            if (!status.get("waybillStatus").equals(WaybillStatusConstants.PENDING_PICKUP)) {
                throw new ResultException(41004, "当前运单状态不能核箱！");
            }
        }
        // pda扫描路线检查
        String route = this.getRoute();
        if (StrUtil.isNotBlank(route) && !route.equals(MapUtil.getStr(status, "path"))) {
            throw new BusinessException("扫描的线路不一致！");
        }
        // 检查WMS箱数量
        thirdPartyService.checkBoxMaterialCount(boxCode, waybillCode);

        //更该运单状态
        if (!WaybillStatusConstants.PENDING_PICKUP.equals(status.get("waybillStatus"))) {
            bzWaybillMapper.update(null, new LambdaUpdateWrapper<BzWaybill>()
                    .set(BzWaybill::getStatus, WaybillStatusConstants.PENDING_PICKUP)
                    .set(BzWaybill::getPickupTime, new Date())
                    .eq(BzWaybill::getWaybillCode, waybillCode)
                    .eq(BzWaybill::getIsDelete, 0));
        }
        if (status.get("status") == (BoxStatusEnum.NOT_SCANNED.getValue())) {
            //更新操作
            LambdaUpdateWrapper<BzWaybillBoxRel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BzWaybillBoxRel::getStatus, 1);
            updateWrapper.set(BzWaybillBoxRel::getUpdateUserId, newAscmLoginUser.getUsername());
            updateWrapper.set(BzWaybillBoxRel::getUpdateUserName, newAscmLoginUser.getName());
            updateWrapper.set(BzWaybillBoxRel::getUpdateTime, new Date());
            updateWrapper.eq(BzWaybillBoxRel::getId, bzWaybillBoxRel.getId());
            int update = bzWaybillBoxRelMapper.update(null, updateWrapper);
            boolean flag = update == 1;
            if (flag) {
                //修改运单的操作时间
                bzWaybillMapper.update(null, new LambdaUpdateWrapper<BzWaybill>()
                        .eq(BzWaybill::getWaybillCode, waybillCode)
                        .set(BzWaybill::getUpdateTime, new Date())
                        .set(BzWaybill::getUpdateUserId, newAscmLoginUser.getUsername()));
                responseEntity.setMsg("扫描成功");
            } else {
                throw new ResultException(41005, "核箱失败");
            }
        } else {
            //抛出状态异常
            throw new ResultException(41006, "当前状态为：" + BoxStatusEnum.getDescriptor((Integer) status.get("status")) + ",无法扫描！");
        }
        return responseEntity;
    }

    /**
     * 完成运单
     */
    //TODO:斌哥--done
    @Transactional
    public Result completeWaybill(String waybillCode, AscmLoginUser newAscmLoginUser, String transportType) throws ResultException {
        Result responseEntity = ResultUtil.success();
        //运单状态健壮性校验
        LambdaQueryWrapper<BzWaybill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BzWaybill::getWaybillCode, waybillCode).eq(BzWaybill::getIsDelete, 0);
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(lambdaQueryWrapper);
        if (bzWaybill == null) {
            return ResultUtil.failed("运单不存在").setData(false);
        }
        if (1 != bzWaybill.getIsCompletePacking()) {
            return ResultUtil.failed("该运单尚未包装完成").setData(false);
        }
        if (!WaybillStatusConstants.PENDING_PICKUP.equals(bzWaybill.getStatus())) {
            responseEntity.setMsg("当前运单不处于待提货状态！");
            return responseEntity;
        }
        int noCount = baseMapper.getCountByCode(waybillCode);
        if (noCount > 0) {
            //未全部扫描完成无需更新运单状态
            return ResultUtil.failed("箱号未全部扫描完成").setData(false);
        } else {
            bzWaybill.setUpdateTime(new Date());
            bzWaybill.setUpdateUserId(newAscmLoginUser.getUsername());
            bzWaybill.setUpdateUserName(newAscmLoginUser.getName());
            bzWaybill.setPdaShippingTime(new Date());
            int count = 0;
            //状态值的修改
            LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BzWaybill::getStatus, WaybillStatusConstants.PENDING_TRANSIT);
            updateWrapper.set(BzWaybill::getPdaShippingTime, new Date());
            updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
            // 如果运输类型有更改，就重新计算过期时间和时效相关字段
            if (StrUtil.isNotBlank(transportType) && !bzWaybill.getTransportType().equals(transportType)) {
                changeTransportInfo(bzWaybill, transportType, updateWrapper);
            }
            int update = bzWaybillMapper.update(null, updateWrapper);
            boolean flag = update == 1;
            if (flag) {
                //更新其他信息
                count = baseMapper.updateByWaybillCode(bzWaybill);
                if (count > 0) {
                    try {
                        Boolean tryLock = ascmLockHelper.tryLock("LOCK" + bzWaybill.getWaybillCode(), 30, 120, TimeUnit.SECONDS);
                        if (tryLock) {
                            //同步发运信息
                            List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
                            List<Integer> businessType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
                            List<String> lgort = baseWarehouses.stream().filter(item -> WarehouseTypeEnum.isWms(item.getWarehouseType()) || Objects.equals(item.getWarehouseType(), WarehouseTypeEnum.JFK.getValue())).map(BaseWarehouse::getLgort).collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(baseWarehouses) && lgort.contains(bzWaybill.getLgort())) {
                                syncWaybillTransportInfo(bzWaybill.getWaybillCode(), "10", CollectionUtil.isEmpty(businessType) ? "50" : BusinessTypeEnum.getBusinessType(businessType.get(0)));
                            }
                        }
                    } catch (Exception e) {
                        log.info("分布式锁异常");
                        log.error("同步运单发生异常 inputParams:{} errorMessage:{}", bzWaybill.getWaybillCode(), e.getMessage());
                    } finally {
                        ascmLockHelper.unlock("LOCK" + bzWaybill.getWaybillCode());
                    }
                    //生成一个打印记录
                    LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BzWaybillBoxRel::getWaybillCode, waybillCode).eq(BzWaybillBoxRel::getIsDevanning, 0).eq(BzWaybillBoxRel::getStatus, 1).eq(BzWaybillBoxRel::getIsDelete, 0);
                    List<String> collect = bzWaybillBoxRelMapper.selectList(queryWrapper).stream().map(BzWaybillBoxRel::getBoxCode).collect(Collectors.toList());
                    printInsert(collect, newAscmLoginUser, bzWaybill);
                    responseEntity.setMsg("已发单！");
                } else {
                    throw new ResultException(500, "任务完成失败");
                }
            } else {
                throw new ResultException(500, "任务完成失败");
            }
            return responseEntity;
        }
    }

    /**
     * 发运时用户修改运输类型，更新相关运输信息
     *
     * @param bzWaybill
     * @param transportType
     * @param updateWrapper
     */
    private void changeTransportInfo(BzWaybill bzWaybill, String transportType, LambdaUpdateWrapper<BzWaybill> updateWrapper) {
        // 查询出新的时效 根据运输类型 发运城市 门店城市 运单类型重新匹配
        String lgort = bzWaybill.getLgort();
        String shopCode = bzWaybill.getShopCode();
        LambdaQueryWrapper<BaseWaybillRules> lqw = new LambdaQueryWrapper<>();
        lqw.eq(BaseWaybillRules::getLgort, lgort).eq(BaseWaybillRules::getShopCode, shopCode)
                .eq(BaseWaybillRules::getTransportType, transportType).last("limit 1");
        BaseWaybillRules baseWaybillRules = baseWaybillRulesMapper.selectOne(lqw);
        // 更新新的时效
        updateWrapper.set(BzWaybill::getTransportType, transportType);
        if (BeanUtil.isNotEmpty(baseWaybillRules) && 0 < baseWaybillRules.getPathExpiry()) {
            updateWrapper.set(BzWaybill::getPathExpiry, baseWaybillRules.getPathExpiry());
            updateWrapper.set(BzWaybill::getPath, baseWaybillRules.getPath());
            updateWrapper.set(BzWaybill::getPathRemark, baseWaybillRules.getPathRemark());
            updateWrapper.set(BzWaybill::getCarrierCode, baseWaybillRules.getCarrierCode());
            //updateWrapper.set(BzWaybill::getExpiryTime, DateUtil.offsetHour(new Date(), baseWaybillRules.getPathExpiry()));
        } else {
            updateWrapper.set(BzWaybill::getPathExpiry, 0);
            updateWrapper.set(BzWaybill::getCarrierCode, "");
            updateWrapper.set(BzWaybill::getPath, "");
            updateWrapper.set(BzWaybill::getPathRemark, "");
            if (BeanUtil.isNotEmpty(baseWaybillRules)) {
                updateWrapper.set(BzWaybill::getCarrierCode, baseWaybillRules.getCarrierCode());
                updateWrapper.set(BzWaybill::getPath, baseWaybillRules.getPath());
                updateWrapper.set(BzWaybill::getPathRemark, baseWaybillRules.getPathRemark());
            }
        }
    }

    /**
     * 各状态信息
     */
    public StatusVO statuNum(String driverPhone) {
        StatusVO stateNum = bzWaybillMapper.findStateNum(driverPhone);
        return stateNum;
    }

    public String checkWaybill(String waybillCode) throws ResultException {
        if (StringUtils.isBlank(waybillCode)) {
            throw new ResultException(500, "运单号为空！");
        }
        String status = bzWaybillMapper.findWaybill(waybillCode);
        if (StringUtils.isBlank(status)) {
            throw new ResultException(500, "不存在该运单！");
        }
        return status;
    }

    public List<WayBillShopVO> getWayBillListByCode(String... wayBillCode) {
        List<WayBillShopVO> wayBillShopVOList = new ArrayList<>();
        List<WayBillShopVO> wayBillListByCode = bzWaybillMapper.getWayBillListByCode(wayBillCode);
        wayBillListByCode.stream().forEach(e -> {
            //做时间的解析
            e.setParseSignTime(com.xiaopeng.halley.ascm.boot.utils.DateUtils.format(e.getSignTime(), com.xiaopeng.halley.ascm.boot.utils.DateUtils.DateTimeFormatterEnum.FORMAT_DATE_TIME_STYLE_1));
            String waybillCode = e.getWaybillCode();
            //处理箱号
            List<BoxVO> boxVOS = fillWayBillShop(waybillCode);
            if (CollUtil.isNotEmpty(boxVOS)) {
                e.setBoxList(boxVOS);
            }
            wayBillShopVOList.add(e);

            //处理图片信息
            LambdaQueryWrapper<BzWaybillFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BzWaybillFile::getWaybillCode, waybillCode).eq(BzWaybillFile::getIsDelete, 0);
            List<BzWaybillFile> bzWaybillFiles = bzWaybillFileMapper.selectList(queryWrapper);
            ArrayList<String> pictures = new ArrayList<>();
            if (bzWaybillFiles.size() > 0) {
                for (BzWaybillFile bzWaybillFile : bzWaybillFiles) {
                    //TODO:斌哥--done
//                    WaterMarkDTO waterMarkDTO = new WaterMarkDTO();
//                    waterMarkDTO.setFileId(bzWaybillFile.getFileId());
//                    HashMap<String, String> map = xpFileOssFeign.download(waterMarkDTO);
//                    String url = map.get("url");
                    ImageResponseDTO tempURL = imageService.getTempURL(bzWaybillFile.getFileId());
                    if (ObjectUtils.isNotEmpty(tempURL)) {
                        pictures.add(tempURL.getUrl());
                    }
                }
            }
            e.setPictures(pictures);
        });
        return wayBillShopVOList;
    }

    private List<BoxVO> fillWayBillShop(String wayBillCode) {
        return bzErpReqBoxMapper.getListByWayBillCode(wayBillCode);
    }

    public boolean updateWaybillInfo(WaybillStatusDto dto, String driverPhone) throws ResultException {
        log.info("BzWaybillService updateWaybillInfo 更新的运单数据为：[{}] 司机手机号为：[{}]", dto.getWaybillCode(), dto.getDriverPhone());
        //获取当前时间
        Date date = new Date();
        //获取该运单
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, dto.getWaybillCode()).eq(BzWaybill::getIsDelete, 0));
        if (ObjectUtils.isEmpty(bzWaybill)) {
            throw new ResultException(500, "查无此运单");
        }
        thirdPartyService.checkBoxMaterialCount(dto.getWaybillCode());
        if (StringUtils.isNotBlank(bzWaybill.getDriverPhone())) {//不为空即转交
            BzWaybillTrackTruckDriver bzWaybillTrackTruckDriver = new BzWaybillTrackTruckDriver();
            //运单编号记录
            bzWaybillTrackTruckDriver.setWaybillCode(dto.getWaybillCode());
            //接收方司机手机号
            bzWaybillTrackTruckDriver.setReceiveDriverPhone(dto.getDriverPhone());
            //送出方司机手机号
            bzWaybillTrackTruckDriver.setSendDriverPhone(bzWaybill.getDriverPhone());
            //送出方司机姓名
            if (StringUtils.isNotBlank(bzWaybill.getDriverName())) {
                bzWaybillTrackTruckDriver.setSendDriverName(bzWaybill.getDriverName());
            }
            //获取司机最近的一次历史提交位置
            LambdaQueryWrapper<BzWaybillTrackHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BzWaybillTrackHistory::getWaybillCode, dto.getWaybillCode())
                    .eq(BzWaybillTrackHistory::getDriverPhone, bzWaybill.getDriverPhone())
                    .eq(BzWaybillTrackHistory::getIsDelete, 0)
                    .orderByDesc(BzWaybillTrackHistory::getUpdateTime)
                    .last("limit 1");
            BzWaybillTrackHistory lastTrack = bzWaybillTrackHistoryMapper.selectOne(queryWrapper);
            if (ObjectUtils.isNotEmpty(lastTrack)) {
                //交接省份
                if (StringUtils.isNotBlank(lastTrack.getProvince())) {
                    bzWaybillTrackTruckDriver.setProvince(lastTrack.getProvince());
                }
                //交接城市
                if (StringUtils.isNotBlank(lastTrack.getCity())) {
                    bzWaybillTrackTruckDriver.setCity(lastTrack.getCity());
                }
                //交接地址
                if (StringUtils.isNotBlank(lastTrack.getAddress())) {
                    bzWaybillTrackTruckDriver.setAddress(lastTrack.getAddress());
                }
            }
            //交接时间
            bzWaybillTrackTruckDriver.setCreateTime(date);
            bzWaybillTrackTruckDriverMapper.insert(bzWaybillTrackTruckDriver);
        } else {
            BzWaybillTrackTruckDriver bzWaybillTrackTruckDriver = new BzWaybillTrackTruckDriver();
            bzWaybillTrackTruckDriver.setWaybillCode(dto.getWaybillCode());
            bzWaybillTrackTruckDriver.setReceiveDriverPhone(dto.getDriverPhone());
            bzWaybillTrackTruckDriver.setCreateTime(date);
            bzWaybillTrackTruckDriverMapper.insert(bzWaybillTrackTruckDriver);
        }

        //更新发车时间(第一次设置)
        Date departureTime = date;
        if ("1970-01-01 08:00:00".equals(com.xiaopeng.halley.ascm.boot.utils.DateUtils.format(bzWaybill.getDepartureTime(), com.xiaopeng.halley.ascm.boot.utils.DateUtils.DateTimeFormatterEnum.FORMAT_DATE_TIME_STYLE_1)) || null == bzWaybill.getDepartureTime()) {
            LambdaUpdateWrapper<BzWaybill> bzWaybillLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            bzWaybillLambdaUpdateWrapper.set(BzWaybill::getDepartureTime, date).eq(BzWaybill::getId, bzWaybill.getId());
            bzWaybillMapper.update(null, bzWaybillLambdaUpdateWrapper);
        } else {
            departureTime = null;
        }
//        String driverName = bzDriverMapper.findByBzDriverName(driverPhone);
        UpdateWrapper<BzWaybill> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.set("driver_name", driverName);
        updateWrapper.set("driver_phone", dto.getDriverPhone());
        updateWrapper.set("car_plate", dto.getCarPlate());
        updateWrapper.set("car_type", dto.getCarType());
        updateWrapper.set("status", "运输中");
        updateWrapper.set("driver_name", dto.getDriverName());
        //过期时间（第一次设置）
        Date expiryTime = date;
        if ("1970-01-01 08:00:00".equals(com.xiaopeng.halley.ascm.boot.utils.DateUtils.format(bzWaybill.getExpiryTime(), com.xiaopeng.halley.ascm.boot.utils.DateUtils.DateTimeFormatterEnum.FORMAT_DATE_TIME_STYLE_1)) || null == bzWaybill.getExpiryTime()) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.HOUR, bzWaybill.getPathExpiry());//这里是增加3*60分钟，即3小时，也可以在第一个形参选择所需单位
            date = calendar.getTime();
            updateWrapper.set("expiry_time", dto.getExpiryTime());//过期时间
            if (null == dto.getExpiryTime()) {
                updateWrapper.set("expiry_time", date);//过期时间
            }
        } else {
            expiryTime = null;
        }
        updateWrapper.eq("waybill_code", dto.getWaybillCode());
        int count = bzWaybillMapper.update(null, updateWrapper);
        log.info("BzWaybillService updateWaybillInfo 更新的数据条数为：[{}]", count);
        if (count > 0) {
            dragonService.updateTrackSync(Collections.singletonList(dto.getWaybillCode()), true);
            log.info("BzWaybillService updateWaybillInfo bzWaybill [{}]", JSONObject.toJSONString(bzWaybill));
            // 如果是总仓到分仓的运单
            // 如果原来状态为待提货，变为运输中，就发给83接口
            List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
            log.info("BzWaybillService updateWaybillInfo baseWarehouses [{}]", JSONObject.toJSONString(baseWarehouses));
            if (CollectionUtil.isNotEmpty(baseWarehouses)) {
                List<Integer> shopType = baseWarehouses.stream()
                        .filter(item -> item.getLgort().equals(bzWaybill.getShopCode()))
                        .map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());

                List<String> wmsCodes = getWmsCodes(baseWarehouses);
                log.info("BzWaybillService updateWaybillInfo shopType [{}]", JSONObject.toJSONString(shopType));
                // ASCM0083接口同步
                if (CollectionUtil.isNotEmpty(shopType) && sendWarehouse.contains(shopType.get(0)) && wmsCodes.contains(bzWaybill.getLgort())) {
                    // 如果原来状态为待提货，变为运输中，就发给83接口
                    bzWaybill.setDriverPhone(dto.getDriverPhone());
                    bzWaybill.setDriverName(dto.getDriverName());
                    bzWaybill.setCarPlate(dto.getCarPlate());
                    if (null != expiryTime) {
                        bzWaybill.setExpiryTime(dto.getExpiryTime());
                    }
                    if (null != departureTime) {
                        bzWaybill.setDepartureTime(departureTime);
                    }
                    sendToJFK(bzWaybill, shopType.get(0));
                }
                List<Integer> warehouseType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getLgort())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
                log.info("BzWaybillService updateWaybillInfo warehouseType [{}]", JSONObject.toJSONString(warehouseType));
                // ASCM0086接口同步
                if (CollectionUtil.isNotEmpty(warehouseType) && (WarehouseTypeEnum.isWms(warehouseType.get(0))) || Objects.equals(warehouseType.get(0), WarehouseTypeEnum.JFK.getValue())) {
                    if (CollectionUtil.isEmpty(shopType)) {
                        shopType.add(5);
                    }
                    syncWaybillTransportInfo(dto.getWaybillCode(), "20", BusinessTypeEnum.getBusinessType(shopType.get(0)));
                }
            }
            return true;
        }
        return false;
    }

    public List<String> getWmsCodes(List<BaseWarehouse> baseWarehouses) {
        return baseWarehouses.stream()
                .filter(e -> WarehouseTypeEnum.isWms(e.getWarehouseType()))
                .map(BaseWarehouse::getLgort)
                .collect(Collectors.toList());
    }

    /**
     * 运单同步给捷富凯
     *
     * @param bzWaybill
     */
    private void sendToJFK(BzWaybill bzWaybill, Integer warehouseType) {
        log.info("BzWaybillService sendToJFK running [{}]", JSONObject.toJSONString(bzWaybill));
        try {
            sendMain(bzWaybill, warehouseType);
        } catch (Exception e) {
            log.info("BzWaybillService Exception [{}]", e.getMessage());
        }
    }

    @RemoteApi(RemoteApiEnum.ASCM0083)
    private void sendMain(BzWaybill bzWaybill, Integer warehouseType) {
        log.info("updateWaybillInfo sendToJFK running [{}]", JSONObject.toJSONString(bzWaybill));

        bzWaybill.setStatus("运输中");

        //进行数据封装
        String sendId = UUID.fastUUID().toString(true);

        JSONObject requestObject = getBoxJson(bzWaybill);

        log.info("updateWaybillInfo sendToJFK build JSON [{}]", requestObject.toJSONString());

        JSONObject headParams = POReqUtil.getHeadParams("ASCM", SendSystemEnum.getSystemType(warehouseType), "ASCM0083", sendId);

        JSONObject params = new JSONObject();
        params.put("HEADER", headParams);
        params.put("DATA", requestObject);

        //TODO:调用WMS接口
        EgressGatewayRequest request = new EgressGatewayRequest();
        request.setAppId("xp-halley-ascm");
        request.setApiCode(POInvokeEnum.SI_ASCM0083_Syn_Out.getApiCode());
        request.setData(params);

        Result result = egressGatewayFeign.invoke(request);

        log.info("updateWaybillInfo sendToJFK 处理完成 result [{}]", JSONObject.toJSONString(result));

    }

    @NotNull
    private JSONObject getBoxJson(BzWaybill bzWaybill) {
        JSONObject requestObject = new JSONObject();

        requestObject.put("orderType", bzWaybill.getOrderType());
        requestObject.put("transportType", bzWaybill.getTransportType());
        requestObject.put("status", bzWaybill.getStatus());
        requestObject.put("waybillCode", bzWaybill.getWaybillCode());
        requestObject.put("lgort", bzWaybill.getLgort());
        requestObject.put("shopCode", bzWaybill.getShopCode());
        requestObject.put("driverContactNum", bzWaybill.getDriverPhone());
        requestObject.put("driverName", bzWaybill.getDriverName());
        requestObject.put("carPlate", bzWaybill.getCarPlate());
        requestObject.put("totalVolume", bzWaybill.getTotalVolume());
        requestObject.put("receiveTime", DateUtil.format(bzWaybill.getReceivedTime(), "yyyy-MM-dd HH:mm:ss"));
        requestObject.put("sendTime", DateUtil.format(bzWaybill.getReceivedTime(), "yyyy-MM-dd HH:mm:ss"));
        requestObject.put("dispatchTime", DateUtil.format(bzWaybill.getDepartureTime(), "yyyy-MM-dd HH:mm:ss"));
        requestObject.put("expiryTime", ObjectUtil.isNull(bzWaybill.getExpiryTime()) ? DateUtil.now() : DateUtil.format(bzWaybill.getExpiryTime(), "yyyy-MM-dd HH:mm:ss"));
        requestObject.put("path", (StrUtil.isNotBlank(bzWaybill.getWarehouseCity()) ? bzWaybill.getWarehouseCity() : "无") + (StrUtil.isNotBlank(bzWaybill.getShopCity()) ? bzWaybill.getShopCity() : "无"));

        getBox(bzWaybill, requestObject);
        return requestObject;
    }

    private void getBox(BzWaybill bzWaybill, JSONObject requestObject) {
        LambdaQueryWrapper<BzWaybillBoxRel> lqwbwbr = new LambdaQueryWrapper<>();
        lqwbwbr.eq(BzWaybillBoxRel::getWaybillCode, bzWaybill.getWaybillCode());
        lqwbwbr.eq(BzWaybillBoxRel::getIsDelete, 0);
        lqwbwbr.eq(BzWaybillBoxRel::getIsDevanning, 0);

        List<BzWaybillBoxRel> theBoxList = bzWaybillBoxRelMapper.selectList(lqwbwbr);

        if (theBoxList.size() > 0) {
            List<String> boxCodeList = theBoxList.stream()
                    .map(BzWaybillBoxRel::getBoxCode)
                    .collect(Collectors.toList());

            LambdaQueryWrapper<BzErpReqBox> lqwberb = new LambdaQueryWrapper<>();
            lqwberb.in(BzErpReqBox::getBoxCode, boxCodeList);
            lqwberb.eq(BzErpReqBox::getIsDelete, 0);
            lqwberb.eq(BzErpReqBox::getWaybillCode, bzWaybill.getWaybillCode());

            List<BzErpReqBox> boxList = bzErpReqBoxMapper.selectList(lqwberb);

            Set<String> deliveryOrderCodeList = new HashSet<>();

            List<JSONObject> boxJSONObjectList = new ArrayList<>();

            boxList.forEach(boxItem -> {
                // 包装类型(0-单独包装,1-混合包装,2-虚拟包装)
                JSONObject boxJsonObject = new JSONObject();
                boxJsonObject.put("boxCode", boxItem.getBoxCode());
                boxJsonObject.put("packageType", PackageTypeEnum.getPackageType(boxItem.getPackageType()));

                /*LambdaQueryWrapper<BzErpReqMaterialRel> lqwbermr = new LambdaQueryWrapper<>();
                lqwbermr.eq(BzErpReqMaterialRel::getBoxCode, boxItem.getBoxCode());
                lqwbermr.eq(BzErpReqMaterialRel::getIsDelete, 0);

                List<BzErpReqMaterialRel> materialList = bzErpReqMaterialRelMapper.selectList(lqwbermr);*/
                //把物料聚合后发送给JFK
                List<BzErpReqMaterialRel> materialList = bzErpReqMaterialRelMapper.selectMaterialList(boxItem);
                log.info("BzWaybillService getBox {}", JSON.toJSONString(materialList));

                List<JSONObject> buildMaterialList = new ArrayList<>();

                materialList.forEach(materialItem -> {
                    JSONObject materialObjectItem = new JSONObject();

                    deliveryOrderCodeList.add(materialItem.getDeliveryOrderCode());

                    materialObjectItem.put("deliveryOrderCode", materialItem.getDeliveryOrderCode());
                    materialObjectItem.put("materialCode", materialItem.getMatnr());
                    materialObjectItem.put("materialName", materialItem.getMaktx());
                    materialObjectItem.put("materialCount", materialItem.getQuantity());
                    materialObjectItem.put("zzvbeln", StrUtil.nullToDefault(materialItem.getOriginDeliveryOrderCode(), ""));
                    buildMaterialList.add(materialObjectItem);
                });

                boxJsonObject.put("materialList", buildMaterialList);

                boxJSONObjectList.add(boxJsonObject);

            });

            requestObject.put("totalBox", theBoxList.size());
            requestObject.put("deliveryOrderCodeList", deliveryOrderCodeList);
            requestObject.put("boxList", boxJSONObjectList);
        }

    }

    /**
     * 发布全部运单
     */
    public Map<String, Integer> publishAllWaybill() {
        log.info("BzWaybillService publishAllWaybill,开始发布全部运单");
        List<BzWaybill> waybillList = bzWaybillMapper.selectList(new QueryWrapper<BzWaybill>()
                .eq("status", WaybillStatusEnum.UNPUBLISHED.getValue()).eq("is_hang_up", 0).ne("transport_type", "").eq("is_delete", 0));
        log.info("BzWaybillService publishAllWaybill,共查到有【{}】条，状态为待发布且有运输方式的可发布运单", waybillList.size());
        int count = 0;
        if (CollUtil.isNotEmpty(waybillList)) {
            Set<Long> waybillIdList = waybillList.stream().map(BzWaybill::getId).collect(Collectors.toSet());
            // todo:状态机控制
            LambdaUpdateWrapper<BzWaybill> wrapper = new LambdaUpdateWrapper<>();
            wrapper.set(BzWaybill::getStatus, WaybillStatusEnum.PUBLISHED.getValue()).set(BzWaybill::getCirculationTime, new Date());
            wrapper.in(BzWaybill::getId, waybillIdList);
            count = bzWaybillMapper.update(null, wrapper);
        }
        Map<String, Integer> result = new HashMap();
        result.put("total", waybillList.size());
        result.put("succeed", count);
        log.info("BzWaybillService publishAllWaybill,处理完成，result->{}", result);

        return result;
    }

    /**
     * 匹配运输
     */
    public Map matchWaybillRule(List<Map> waybillList) {
        int updateCount = 0;
        String errorInfo = "";
        String multipleRule = "";
        for (Map waybill : waybillList) {
            if (waybill == null) {
                continue;
            }

            if (StringUtils.isNotBlank(MapUtil.getStr(waybill, "lgort"))
                    && StringUtils.isNotBlank(MapUtil.getStr(waybill, "shop_code"))
                    && StringUtils.isNotBlank(MapUtil.getStr(waybill, "order_type"))) {
                List<BaseWaybillRules> waybillRules = baseWaybillRulesMapper.selectList(new QueryWrapper<BaseWaybillRules>().eq("status", 0).eq("lgort", waybill.get("lgort").toString()).eq("shop_code", waybill.get("shop_code").toString()).eq("order_type", waybill.get("order_type").toString()));

                if (CollUtil.isEmpty(waybillRules)) {
                    log.error("BzWaybillService publishAllWaybill,找不到匹配规则，waybill->{}", waybill);

                    // 记录无法匹配规则运单
                    if (StringUtils.isBlank(errorInfo)) {
                        errorInfo = waybill.get("waybill_code").toString();
                    } else {
                        errorInfo = errorInfo + "," + waybill.get("waybill_code");
                    }
                } else {
                    if (waybillRules.size() > 1) {
                        log.error("BzWaybillService publishAllWaybill,找到多条匹配规则，waybill->{}", waybill);

                        // 记录有重复运单规则运单
                        if (StringUtils.isBlank(multipleRule)) {
                            multipleRule = waybill.get("waybill_code").toString();
                        } else {
                            multipleRule = multipleRule + "," + waybill.get("waybill_code");
                        }
                    }

//                    baseTransportTimelinessService.lambdaQuery().eq(BaseTransportTimeliness::getShopCity, )
                    BaseWaybillRules waybillRule = waybillRules.get(0);
                    log.info("BzWaybillService publishAllWaybill,找到匹配规则，waybillRule->{}", waybillRule);

                    // 更新逻辑
                    {
                        LambdaUpdateWrapper<BzWaybill> wrapper = new LambdaUpdateWrapper<>();
                        wrapper.set(BzWaybill::getTransportType, waybillRule.getTransportType());//运输类型
                        wrapper.set(BzWaybill::getPath, waybillRule.getPath());//线路
                        wrapper.set(BzWaybill::getPathExpiry, waybillRule.getPathExpiry());//线路时效
                        wrapper.set(BzWaybill::getCarrierCode, waybillRule.getCarrierCode());//承运商编码
                        //TODO:线路描述
                        if (waybillRule.getPathRemark() != null) {
                            wrapper.set(BzWaybill::getPathRemark, waybillRule.getPathRemark());//线路描述
                        }
                        wrapper.eq(BzWaybill::getId, waybill.get("id"));
                        updateCount = updateCount + bzWaybillMapper.update(null, wrapper);
                    }

                    log.info("BzWaybillService publishAllWaybill,匹配规则成功，waybill->{},waybillRule->{}", waybill, waybillRule);
                }
            } else {
                log.error("BzWaybillService publishAllWaybill,匹配规则失败，参数错误，有为空，waybill->{}", waybill);
            }
        }

        Map result = new HashMap();
        result.put("total", waybillList.size());
        result.put("succeed", updateCount);
        result.put("errorInfo", errorInfo);
        result.put("multipleRule", multipleRule);
        return result;
    }

    public Result checkBzWaybillExpiryTime() {
        Result responseEntity = ResultUtil.success();
        LambdaQueryWrapper<BzWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybill::getStatus, "运输中").eq(BzWaybill::getIsDelete, 0);
        List<BzWaybill> bzWaybills = bzWaybillMapper.selectList(queryWrapper);
        if (!bzWaybills.isEmpty()) {
            Date date = new Date();
            bzWaybills.forEach(e -> {
                Date expiryDate = e.getExpiryTime();
                if (expiryDate == null || "1970-01-01 08:00:00".equals(com.xiaopeng.halley.ascm.boot.utils.DateUtils.format(expiryDate, com.xiaopeng.halley.ascm.boot.utils.DateUtils.DateTimeFormatterEnum.FORMAT_DATE_TIME_STYLE_1))) {
                } else {
                    //超时，状态改为异常
                    if (date.getTime() > expiryDate.getTime()) {
                        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(BzWaybill::getStatus, "异常中").eq(BzWaybill::getId, e.getId());
                        bzWaybillMapper.update(null, updateWrapper);
                    }
                }
            });
        } else {
        }
        return responseEntity;
    }

    /**
     * pad运单详情
     * 原因：PDA去除重复箱号
     */
    public BzWaybillDetailDto waybillDetailByPDA(String waybillCode, int type, int pageNo, int pageSize) throws ResultException {
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<BzWaybill>().eq("waybill_code", waybillCode).eq("is_delete", 0).last("limit 1"));
        if (null == bzWaybill) {
            throw new ResultException(500, "找不到运单！");
        }

        BzWaybillDetailDto bzWaybillDetailDTO = baseMapper.getDetailById(waybillCode, "", "", bzWaybill.getCarrierCode());
        if (ObjectUtil.isNull(bzWaybillDetailDTO)) {
            throw new ResultException(500, "找不到运单！");
        }

        //取出所有箱号
        if (pageNo == 0) {
            pageNo = 0;
        }
        if (pageSize == 0) {
            pageSize = 1000000;//？？？？
        }
        Page<BzErpReqBoxRelDto> page = new Page<>(pageNo, pageSize);

        //箱号规则排序
        IPage<BzErpReqBoxRelDto> boxRelList = bzErpReqBoxMapper.getBoxesByWaybillCodePagePDA(page, waybillCode, type);
        bzWaybillDetailDTO.setBoxRelList(boxRelList);
        return bzWaybillDetailDTO;
    }

    /**
     * @param boxList 需要拆离的箱号
     * @param isPrint 是否打印
     * @param type    端
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean demolitionBox(List<String> boxList, boolean isPrint, String type, AscmLoginUser ascmLoginUser, String transportType) {
        //设置事务回滚点
        Object savePoint = TransactionAspectSupport.currentTransactionStatus().createSavepoint();
        try {
            //获取出箱号对应的对象
            LambdaQueryWrapper<BzErpReqBox> boxQueryWrapper = new LambdaQueryWrapper<>();
            boxQueryWrapper.in(BzErpReqBox::getBoxCode, boxList).eq(BzErpReqBox::getIsDelete, 0);
            List<BzErpReqBox> erpReqBoxes = bzErpReqBoxMapper.selectList(boxQueryWrapper);
            //箱号的总体积
            Double totalVolume = 0.0;
            Double totalWeight = 0.0;
            for (BzErpReqBox bzErpReqBox : erpReqBoxes) {
                totalVolume += bzErpReqBox.getActualVolume();
                totalWeight += bzErpReqBox.getBoxWeight();
            }
            //查询出当前箱号与运单关系的对象
            LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(BzWaybillBoxRel::getBoxCode, boxList).eq(BzWaybillBoxRel::getIsDevanning, 0).eq(BzWaybillBoxRel::getIsDelete, 0);
            List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(queryWrapper);
            //查找旧运单
            BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>()
                    .eq(BzWaybill::getWaybillCode, bzWaybillBoxRels.get(0).getWaybillCode())
                    .eq(BzWaybill::getIsDelete, 0));
            List<BzWaybillBoxRel> boxScanInfo = bzWaybillBoxRels.stream().filter(item -> item.getStatus().equals(0)).collect(Collectors.toList());
            //生成新的运单
            int count = bzWaybill.getDemolitionCount() + 1;
            String newWaybillCode = bzWaybill.getWaybillCode() + "-" + count;
            //新旧运单基本信息同步
            BzWaybill newWaybill = new BzWaybill();
            BeanUtils.copyProperties(bzWaybill, newWaybill, "id", "waybillCode", "pathExpiry", "path", "carrierCode", "isDevanning", "combineCount", "demolitionCount");
            //修改运单号
            newWaybill.setWaybillCode(newWaybillCode);
            //拆箱字数默认0
            newWaybill.setDemolitionCount(0);
            //设置成包装完成
            newWaybill.setIsCompletePacking(1);
            //修改箱号
            newWaybill.setTotalBox(erpReqBoxes.size());
            //修改体积
            newWaybill.setTotalVolume(totalVolume);
            //修改体积
            newWaybill.setTotalWeight(totalWeight);
            //如果是打印的话，状态变更为待运输
            if (isPrint) {
                newWaybill.setStatus("待运输");
            }
            if (CollectionUtil.isNotEmpty(boxScanInfo)) {
                newWaybill.setStatus("待提货");
                newWaybill.setPickupTime(new Date());
            }
            //修改创建时间、更新时间
            newWaybill.setCreateTime(new Date()).setUpdateTime(new Date());
            //制空合单次数
            newWaybill.setCombineCount(0);
            //创建操作人
            newWaybill.setCreateUserId(ascmLoginUser.getUsername());
            newWaybill.setCreateUserName(ascmLoginUser.getName());
            // 设置pda发运时间
            if (isPrint) {
                newWaybill.setPdaShippingTime(new Date());
            }
            //原本有值进行置空
            newWaybill.setUpdateUserName(ascmLoginUser.getName());
            newWaybill.setUpdateUserId(ascmLoginUser.getUsername());
            // 设置运输类型
            if (StrUtil.isNotBlank(transportType)) {
                newWaybill.setTransportType(transportType);
                LambdaQueryWrapper<BaseWaybillRules> lqw = new LambdaQueryWrapper<>();
                lqw.eq(BaseWaybillRules::getLgort, bzWaybill.getLgort()).eq(BaseWaybillRules::getShopCode, bzWaybill.getShopCode())
                        .eq(BaseWaybillRules::getTransportType, transportType).last("limit 1");
                BaseWaybillRules baseWaybillRules = baseWaybillRulesMapper.selectOne(lqw);
                if (BeanUtil.isNotEmpty(baseWaybillRules) && 0 < baseWaybillRules.getPathExpiry()) {
                    newWaybill.setPathExpiry(baseWaybillRules.getPathExpiry());
                    newWaybill.setPath(baseWaybillRules.getPath());
                    newWaybill.setCarrierCode(baseWaybillRules.getCarrierCode());
                }
            } else {
                newWaybill.setTransportType(bzWaybill.getTransportType());
                newWaybill.setPath(bzWaybill.getPath());
                newWaybill.setPathExpiry(bzWaybill.getPathExpiry());
                newWaybill.setCarrierCode(bzWaybill.getCarrierCode());
            }
            bzWaybillMapper.insert(newWaybill);
            // 记录运单信息重推表
            BzWaybillInfoSync bzWaybillInfoSync = new BzWaybillInfoSync();
            bzWaybillInfoSync.setWaybillId(newWaybill.getId());
            bzWaybillInfoSync.setWaybillCode(newWaybill.getWaybillCode());
            bzWaybillInfoSyncMapper.insert(bzWaybillInfoSync);
            //修改箱号对应的运单
            erpReqBoxes.forEach(e -> {
                LambdaUpdateWrapper<BzErpReqBox> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BzErpReqBox::getWaybillCode, newWaybillCode).eq(BzErpReqBox::getId, e.getId());
                bzErpReqBoxMapper.update(null, updateWrapper);
            });
            //运单对应箱号关联关系
            for (BzWaybillBoxRel bzWaybillBoxRel : bzWaybillBoxRels) {
                //生成新的箱号运单对应关系
                BzWaybillBoxRel boxRel = new BzWaybillBoxRel();
                BeanUtils.copyProperties(bzWaybillBoxRel, boxRel);
                //修改运单号
                boxRel.setWaybillCode(newWaybillCode);
                boxRel.setCreateUserId(ascmLoginUser.getUsername());
                boxRel.setCreateUserName(ascmLoginUser.getName());
                boxRel.setUpdateUserId(ascmLoginUser.getUsername());
                boxRel.setUpdateUserName(ascmLoginUser.getName());
                boxRel.setCreateTime(new Date());
                boxRel.setUpdateTime(new Date());
                //进行入库
                boxRel.setId(null);
                bzWaybillBoxRelMapper.insert(boxRel);
                //当前信息变更为已拆离
                LambdaUpdateWrapper<BzWaybillBoxRel> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BzWaybillBoxRel::getIsDevanning, 1)
                        .set(BzWaybillBoxRel::getUpdateUserId, ascmLoginUser.getUsername())
                        .set(BzWaybillBoxRel::getUpdateUserName, ascmLoginUser.getName())
                        .set(BzWaybillBoxRel::getUpdateTime, new Date())
                        .eq(BzWaybillBoxRel::getId, bzWaybillBoxRel.getId());
                bzWaybillBoxRelMapper.update(null, updateWrapper);
            }
            //生成一条拆单记录
            BaseDemolitionCombineRecord record = new BaseDemolitionCombineRecord();
            record.setParentWaybillCode(bzWaybill.getWaybillCode());//父运单
            record.setChildWaybillCode(newWaybillCode);//子运单
            record.setAction("拆");
            record.setItem(type);
            //创建操作人
            record.setCreateUserId(ascmLoginUser.getUsername());
            record.setCreateUserName(ascmLoginUser.getName());
            baseDemolitionCombineRecordMapper.insert(record);

            if (isPrint) {
                //生成打印记录
                printInsert(boxList, ascmLoginUser, newWaybill);
            }
            //修改旧运单的相关信息
            LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
            //总箱数
            updateWrapper.set(BzWaybill::getTotalBox, bzWaybill.getTotalBox() - erpReqBoxes.size());
            //总体积
            updateWrapper.set(BzWaybill::getTotalVolume, bzWaybill.getTotalVolume() - totalVolume);
            //总重量
            updateWrapper.set(BzWaybill::getTotalWeight, bzWaybill.getTotalWeight() - totalWeight);
            //修改时间
            updateWrapper.set(BzWaybill::getUpdateTime, new Date());
            //拆单次数
            updateWrapper.set(BzWaybill::getDemolitionCount, bzWaybill.getDemolitionCount() + 1);
            //是否拆箱
            updateWrapper.set(BzWaybill::getIsDevanning, 1);
            //修改人
            updateWrapper.set(BzWaybill::getUpdateUserId, ascmLoginUser.getUsername());
            updateWrapper.set(BzWaybill::getUpdateUserName, ascmLoginUser.getName());
            //分布式锁进行总箱数以及体积的控制
            try {
                Boolean tryLock = ascmLockHelper.tryLock("LOCK" + bzWaybill.getWaybillCode(), 30, 120, TimeUnit.SECONDS);
                if (tryLock) {
                    bzWaybillMapper.update(null, updateWrapper);
                    // 拆单发运同步数据
                    List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
                    // TODO: 拆单，查询父运单应该也是没有问题的  待验证
                    List<Integer> warehouseType = baseWarehouses.stream().filter(warehouse -> warehouse.getLgort().equals(bzWaybill.getLgort())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
                    List<Integer> shopType = baseWarehouses.stream().filter(warehouse -> warehouse.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());

                    if (CollectionUtil.isNotEmpty(warehouseType) && (WarehouseTypeEnum.isWms(warehouseType.get(0))) || Objects.equals(warehouseType.get(0), WarehouseTypeEnum.JFK.getValue()) && isPrint) {
                        syncWaybillTransportInfo(newWaybillCode, "10", CollectionUtil.isEmpty(shopType) ? "50" : BusinessTypeEnum.getBusinessType(shopType.get(0)));
                    }
                    this.calVolumeWeight(Collections.singletonList(bzWaybill.getWaybillCode()), true);
                }
            } catch (Exception e) {
                log.error("拆单发运发生异常", e);
                //手工回滚异常
                TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savePoint);
                return false;
            } finally {
                ascmLockHelper.unlock("LOCK" + bzWaybill.getWaybillCode());
            }
        } catch (Exception e) {
            log.error("拆单发运发生异常", e);
            //手工回滚异常
            TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savePoint);
            return false;
        }
        return true;
    }

    private void setPrintInfo(BasePrintOutTask basePrintOutTask, BzWaybill bzWaybill, String triggerClient) {
        basePrintOutTask.setRoute(bzWaybill.getPath());
        basePrintOutTask.setTriggerClient(triggerClient);
        BaseWarehouse baseWarehouse = baseWarehouseMapper.selectByLgort(bzWaybill.getLgort());
        if (baseWarehouse != null) {
            basePrintOutTask.setWarehouseType(baseWarehouse.getWarehouseType());
        }
    }

    /**
     * 新增打印记录
     *
     * @param boxList          用于查询交货单号
     * @param newAscmLoginUser 操作的用户
     * @param bzWaybill        操作的运单
     */
    private void printInsert(List<String> boxList, AscmLoginUser newAscmLoginUser, BzWaybill bzWaybill) {
        BasePrintOutTask printOutTask = new BasePrintOutTask();
        setPrintInfo(printOutTask, bzWaybill, TriggerClient.PDA.name());
	    printOutTask.setCount(0);//第一次打印
        printOutTask.setStatus(0);//待打印状态
        printOutTask.setWaybillCode(bzWaybill.getWaybillCode());//PDA过来子运单进行打印
        printOutTask.setLgobe(bzWaybill.getLgobe());
        printOutTask.setLgort(bzWaybill.getLgort());
        printOutTask.setShopName(bzWaybill.getShopName());
        printOutTask.setShopCode(bzWaybill.getShopCode());
        //查找交货单号
        LambdaQueryWrapper<BzErpReqMaterialRel> materialQueryWrapper = new LambdaQueryWrapper<>();
        materialQueryWrapper.in(BzErpReqMaterialRel::getBoxCode, boxList).eq(BzErpReqMaterialRel::getIsDelete, 0);
        List<BzErpReqMaterialRel> materialRels = bzErpReqMaterialRelMapper.selectList(materialQueryWrapper);
        Set<String> collect = materialRels.stream().map(BzErpReqMaterialRel::getDeliveryOrderCode).collect(Collectors.toSet());
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : collect) {
            stringBuilder.append(s + "、");
        }
        String substring = stringBuilder.substring(0, stringBuilder.length() - 1);
        printOutTask.setDeliveryOrderCode(substring);//交货单号
        printOutTask.setOrderType(bzWaybill.getOrderType());
        //创建操作人
        printOutTask.setCreateUserId(newAscmLoginUser.getUsername());
        printOutTask.setCreateUserName(newAscmLoginUser.getName());
        basePrintOutTaskMapper.insert(printOutTask);
    }

    public Map<String, Object> getURLContent(String address) {
        //这里需要使用你的key值
        String urlStr = "https://apis.map.qq.com/ws/geocoder/v1/?address=" + URLUtil.encode(address) + "&key=YNQBZ-XWHLU-77PVQ-BZKFO-3GKKJ-YQF2U";
        //请求的url
        URL url = null;
        //请求的输入流
        BufferedReader in = null;
        //输入流的缓冲
        StringBuffer sb = new StringBuffer();
        try {
            url = new URL(urlStr);
            in = new BufferedReader(new InputStreamReader(url.openStream(), StandardCharsets.UTF_8));
            String str = null;
            //一行一行进行读入
            while ((str = in.readLine()) != null) {
                sb.append(str);
            }
        } catch (Exception ex) {
            throw new BusinessException("地图解析异常");
        } finally {
            try {
                if (in != null) {
                    in.close(); //关闭流
                }
            } catch (IOException ex) {

            }
        }
        String result = sb.toString();
        log.info("getURLContent address: {} result: {}", address, result);
        int status = JSONObject.parseObject(result).getIntValue("status");
        if (status == 120) {
            throw new BusinessException(JSONObject.parseObject(result).getString("message"));
        }
        String r = JSONObject.parseObject(result).getString("result");
        String location = JSONObject.parseObject(r).getString("location");
        String lng = JSONObject.parseObject(location).getString("lng");
        String lat = JSONObject.parseObject(location).getString("lat");
        Map<String, Object> map = new HashMap<>();
        map.put("lng", lng);
        map.put("lat", lat);
        return map;
    }

    /**
     * @param waybillCode   运单编号
     * @param boxCode       箱号
     * @param ascmLoginUser 用户信息
     * @return
     */
    public Result sendBack(String waybillCode, String boxCode, AscmLoginUser ascmLoginUser) {
        if (StringUtils.isBlank(waybillCode)) {
            return ResultUtil.failed("运单号为空").setData(false);
        }
        if (StringUtils.isBlank(boxCode)) {
            return ResultUtil.failed("箱号为空").setData(false);
        }
        if (ObjectUtils.isEmpty(ascmLoginUser)) {
            return ResultUtil.failed("未获取到用户信息").setData(false);
        }
        LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybillBoxRel::getBoxCode, boxCode).eq(BzWaybillBoxRel::getWaybillCode, waybillCode).eq(BzWaybillBoxRel::getIsDelete, 0);
        BzWaybillBoxRel bzWaybillBoxRel = bzWaybillBoxRelMapper.selectOne(queryWrapper);
        if (null == bzWaybillBoxRel) {
            return ResultUtil.failed("未查询到运单:" + waybillCode + "下存在箱号" + boxCode).setData(false);
        }
        if (bzWaybillBoxRel.getStatus() == 0) {
            return ResultUtil.failed("查询到运单:" + waybillCode + "下存在箱号" + boxCode + "为未扫描状态，无需退回").setData(false);
        }
        if (bzWaybillBoxRel.getStatus() == 1) {
            LambdaUpdateWrapper<BzWaybillBoxRel> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BzWaybillBoxRel::getStatus, 0).eq(BzWaybillBoxRel::getId, bzWaybillBoxRel.getId());
            bzWaybillBoxRelMapper.update(null, updateWrapper);
            return ResultUtil.success().setMsg("运单:" + waybillCode + "下箱号" + boxCode + "状态成功退回！");
        } else {
            return ResultUtil.failed("运单:" + waybillCode + "下存在箱号" + boxCode + "状态异常！").setData(false);
        }
    }

    /**
     * @param company       快递公司编号
     * @param number        快递单号
     * @param ascmLoginUser 用户信息
     * @return
     */
    public Result backFillExpress(String waybillCode, String company, String number, AscmLoginUser ascmLoginUser) {
        log.info("BzWaybillService backFillExpress waybillCode [{}] company [{}]", waybillCode, company);

        if (StringUtils.isBlank(waybillCode)) {
            return ResultUtil.failed("运单号为空").setData(false);
        }
        if (StringUtils.isBlank(company)) {
            return ResultUtil.failed("快递公司编号为空").setData(false);
        }
        if (StringUtils.isBlank(number)) {
            return ResultUtil.failed("快递单号为空").setData(false);
        }
        if (ObjectUtils.isEmpty(ascmLoginUser)) {
            return ResultUtil.failed("未获取到用户信息").setData(false);
        }
        LambdaQueryWrapper<BzWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybill::getWaybillCode, waybillCode).eq(BzWaybill::getIsDelete, 0);
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(queryWrapper);
        if (null == bzWaybill) {
            return ResultUtil.failed("未查询到运单:" + waybillCode).setData(false);
        }
        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
        //修改人信息
        LambdaQueryWrapper<BaseExpressTransshipment> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(BaseExpressTransshipment::getCompanyCode, company).eq(BaseExpressTransshipment::getIsDelete, 0);
        BaseExpressTransshipment baseExpressTransshipment = baseExpressTransshipmentMapper.selectOne(lambdaQueryWrapper);
        String companyName = null;
        if (null != baseExpressTransshipment) {
            companyName = baseExpressTransshipment.getCompanyName();
        } else {
            companyName = company;
        }
        // 如果原运输类型为快递就直接拿，不是的话就重新匹配运输时效
        Integer pathExpiry = bzWaybill.getPathExpiry();
        if (!"快递".equals(bzWaybill.getTransportType())) {
            LambdaQueryWrapper<BaseWaybillRules> lqw = new LambdaQueryWrapper<>();
            lqw.eq(BaseWaybillRules::getLgort, bzWaybill.getLgort()).eq(BaseWaybillRules::getShopCode, bzWaybill.getShopCode())
                    .eq(BaseWaybillRules::getTransportType, "快递").last("limit 1");
            BaseWaybillRules baseWaybillRules = baseWaybillRulesMapper.selectOne(lqw);
            if (BeanUtil.isNotEmpty(baseWaybillRules) && 0 < baseWaybillRules.getPathExpiry()) {
                pathExpiry = baseWaybillRules.getPathExpiry();
                bzWaybill.setPathExpiry(baseWaybillRules.getPathExpiry());
                bzWaybill.setPath(baseWaybillRules.getPath());
                bzWaybill.setCarrierCode(baseWaybillRules.getCarrierCode());
                updateWrapper.set(BzWaybill::getPathExpiry, baseWaybillRules.getPathExpiry());
                updateWrapper.set(BzWaybill::getPath, baseWaybillRules.getPath());
                updateWrapper.set(BzWaybill::getCarrierCode, baseWaybillRules.getCarrierCode());
            } else {
                pathExpiry = 0;
                bzWaybill.setPathExpiry(0);
                bzWaybill.setPath("");
                bzWaybill.setCarrierCode("");
                updateWrapper.set(BzWaybill::getPath, "");
                updateWrapper.set(BzWaybill::getCarrierCode, "");
                if (BeanUtil.isNotEmpty(baseWaybillRules)) {
                    bzWaybill.setPath(baseWaybillRules.getPath());
                    bzWaybill.setCarrierCode(baseWaybillRules.getCarrierCode());
                    updateWrapper.set(BzWaybill::getPath, baseWaybillRules.getPath());
                    updateWrapper.set(BzWaybill::getCarrierCode, baseWaybillRules.getCarrierCode());
                }
                updateWrapper.set(BzWaybill::getPathExpiry, 0);
            }
        }
        //设置异常过期时间
        Date expiryTime = new Date(System.currentTimeMillis() + pathExpiry * 60 * 60 * 1000);
        if (WaybillStatusConstants.PENDING_TRANSIT.equals(bzWaybill.getStatus())) {
            log.info("分仓调拨单 运单号：{} 发运仓库：{} 接收仓库：{}", waybillCode, bzWaybill.getLgort(), bzWaybill.getShopCode());
            // 如果是总仓发分仓，调用83接口同步给捷富凯
            List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
            List<Integer> shopType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
            List<String> wmsCodes = getWmsCodes(baseWarehouses);
            if (CollectionUtil.isNotEmpty(shopType) && sendWarehouse.contains(shopType.get(0)) && wmsCodes.contains(bzWaybill.getLgort())) {
                bzWaybill.setTransportType("快递");
                bzWaybill.setExpiryTime(expiryTime);
                bzWaybill.setDepartureTime(new Date());
                sendToJFK(bzWaybill, shopType.get(0));
            }
            // 如果是总仓发出，也需要调用86接口同步数据给我WMS
            List<String> lgort = baseWarehouses.stream().filter(item -> WarehouseTypeEnum.isWms(item.getWarehouseType()) || Objects.equals(item.getWarehouseType(), WarehouseTypeEnum.JFK.getValue())).map(BaseWarehouse::getLgort).collect(Collectors.toList());
            if (lgort.contains(bzWaybill.getLgort())) {
                syncWaybillTransportInfo(bzWaybill.getWaybillCode(), "20", CollectionUtil.isEmpty(shopType) ? "50" : BusinessTypeEnum.getBusinessType(shopType.get(0)));
            }
            updateWrapper.set(BzWaybill::getTransportType, "快递");
            updateWrapper.set(BzWaybill::getStatus, "运输中");
            updateWrapper.set(BzWaybill::getExpiryTime, expiryTime);
        }
        updateWrapper.set(BzWaybill::getLogisticsCompany, companyName)
                .set(BzWaybill::getUpdateUserName, ascmLoginUser.getName())
                .set(BzWaybill::getLogisticsCode, number)
                .set(BzWaybill::getDepartureTime, new Date())
                .set(BzWaybill::getTransportType, "快递")
                .set(BzWaybill::getUpdateUserId, ascmLoginUser.getUsername())
                .eq(BzWaybill::getId, bzWaybill.getId());
        int count = bzWaybillMapper.update(null, updateWrapper);
        if (count == 1) {
            dragonService.updateTrackSync(Collections.singletonList(bzWaybill.getWaybillCode()), true);
            return ResultUtil.success().setMsg("快递信息回填成功！").setData(true);
        } else {
            return ResultUtil.failed("快递信息回填失败！").setData(false);
        }
    }

    /**
     * 转运快递列表查询
     *
     * @param page
     * @param pageSize
     * @param keyWord
     * @return
     */
    public Result searchDeliveryCompany(Integer page, Integer pageSize, String keyWord) {
        if (null == page) {
            page = 0;
        }
        if (null == pageSize) {
            pageSize = 10;
        }
        IPage myPage = new Page<BaseExpressTransshipment>(page, pageSize);
        //构建查询条件
        IPage iPage = null;
        if (StringUtils.isNotBlank(keyWord)) {
            LambdaQueryWrapper<BaseExpressTransshipment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(BaseExpressTransshipment::getCompanyName, "%" + keyWord + "%");
            iPage = baseExpressTransshipmentMapper.selectPage(myPage, queryWrapper);
        } else {
            iPage = baseExpressTransshipmentMapper.selectPage(myPage, null);
        }
        return ResultUtil.success(iPage);
    }

    /**
     * 确定快递单号真实
     *
     * @param company
     * @param number
     * @return
     */
    public Result confirmDeliveryCode(String company, String number) {

        QueryTrackReq queryTrackReq = new QueryTrackReq();
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom(company);
        queryTrackParam.setNum(number);
        String param = new Gson().toJson(queryTrackParam);

        queryTrackReq.setParam(param);
        queryTrackReq.setCustomer(customer);
        queryTrackReq.setSign(SignUtils.querySign(param, key, customer));

        IBaseClient baseClient = new QueryTrack();

        HttpResult result = null;
        try {
            result = baseClient.execute(queryTrackReq);
        } catch (Exception e) {
            return ResultUtil.failed("调用快递100接口出错").setData(false);
        }
        String body = result.getBody();
        Map map = JSON.parseObject(body, Map.class);
        String message = (String) map.get("message");
        if ("ok".equals(message)) {
            return ResultUtil.success(true);
        } else {
            return ResultUtil.failed("查询无结果，请隔段时间再查").setData(false);
        }
    }

    /**
     * 订阅接口
     */
    public Map subscribeDelivery(String deliveryCode, String companyCode, String deliveryPhone, String callbackUrl) throws Exception {
        SubscribeParameters subscribeParameters = new SubscribeParameters();
        subscribeParameters.setCallbackurl(callbackUrl == null ? this.callbackUrl : callbackUrl);
        if (StringUtils.isNotBlank(deliveryPhone)) {
            subscribeParameters.setPhone(deliveryPhone);
        } else {
            setSubscribePhoneNum(subscribeParameters);
            //subscribeParameters.setPhone(drivePhone);
        }
        subscribeParameters.setResultv2("4");

        SubscribeParam subscribeParam = new SubscribeParam();
        subscribeParam.setParameters(subscribeParameters);
        subscribeParam.setCompany(companyCode);
        subscribeParam.setNumber(deliveryCode);
        subscribeParam.setKey(key);

        SubscribeReq subscribeReq = new SubscribeReq();
        subscribeReq.setSchema(ApiInfoConstant.SUBSCRIBE_SCHEMA);
        subscribeReq.setParam(new Gson().toJson(subscribeParam));

        IBaseClient subscribe = new Subscribe();
        HttpResult execute = subscribe.execute(subscribeReq);
        log.info("快递100订阅返回->{}", execute);
        Map returnMap = JSON.parseObject(execute.getBody(), Map.class);

        return returnMap;
    }

    /**
     * 设置订阅手机号
     *
     * @param subscribeParameters
     */
    private void setSubscribePhoneNum(SubscribeParameters subscribeParameters) {
        try {
            // 获取登录的用户信息
            AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();
            if (ascmLoginUser == null) {
                return;
            }

            // 根据不同的用户，去导向不同的统一管理员电话，这样后续快递100才可以获取到对应的快递信息
            if ("2101".equals(ascmLoginUser.getAuth())) {
                log.info("BzWaybillService setSubscribePhoneNum 肇庆总仓");
                subscribeParameters.setPhone(drivePhone);
            } else if ("2021".equals(ascmLoginUser.getAuth())) {
                log.info("BzWaybillService setSubscribePhoneNum 华东分仓");
                subscribeParameters.setPhone(hdDrivePhone);
            } else if ("2201".equals(ascmLoginUser.getAuth())) {
                log.info("BzWaybillService setSubscribePhoneNum 武汉总仓");
                subscribeParameters.setPhone(whDrivePhone);
            } else {
                // 都不是的话就设置成肇庆总仓的
                subscribeParameters.setPhone(drivePhone);
            }

        } catch (Exception e) {
            log.info("订阅快递100设置订阅手机号时发生异常！{}", e.getMessage());
        }
    }

    /**
     * 快递100回调
     *
     * @param request
     * @return
     */
    public SubscribeResp callBackMethod(HttpServletRequest request) {
        // 构建响应对象
        SubscribeResp subscribeResp = new SubscribeResp();
        subscribeResp.setResult(Boolean.TRUE);
        subscribeResp.setReturnCode("200");
        subscribeResp.setMessage("成功");
        // 获取快递单号
        String param = request.getParameter("param");
        log.info("快递100订阅推送回调结果 {}", param);
        JSONObject jsonObject = JSON.parseObject(param);
        JSONObject lastResult = jsonObject.getJSONObject("lastResult");
        String deliveryCode = (String) lastResult.get("nu");
        //通过快递单号查询绑定的运单信息
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>()
                .eq(BzWaybill::getLogisticsCode, deliveryCode).eq(BzWaybill::getIsDelete, 0));
        if (ObjectUtil.isNotEmpty(bzWaybill)) {
            lastResult.put("waybillCode", bzWaybill.getWaybillCode());
            lastResult.put("waybillCodeId", bzWaybill.getId());
            log.info("callBackMethod {}", JSON.toJSONString(lastResult));
            // 消费发送kafka
            ascmEventKafkaProducer.pushEvent(KafkaConstant.EVENT_DATA_SYNC, MapUtil.builder()
                    .put("data", JSONObject.toJSONString(lastResult))
                    .put("key", AscmKafkaSyncDataTypeEnum.KUAIDI100_DATA_SYNC.getKey())
                    .put("ts", System.currentTimeMillis())
                    .build());
            // 响应结果
            return subscribeResp;
        }
        log.warn("快递100订阅推送回调，快递单号：" + deliveryCode + "未匹配到运单！");
        subscribeResp.setResult(Boolean.FALSE);
        subscribeResp.setReturnCode("500");
        subscribeResp.setMessage("快递单号：" + deliveryCode + "未订阅！");
        return subscribeResp;
    }

    /**
     * kafka异步保存快递100的数据
     */
    @Transactional
    public void kuaidiAsyncSaveData(String data) {
        // 解析一些数据
        JSONObject lastResult = JSON.parseObject(data);
        String waybillCode = (String) lastResult.get("waybillCode");
        Integer id = (Integer) lastResult.get("waybillCodeId");
        long waybillCodeId = id.longValue();
        //快递公司
        String deliveryCompany = (String) lastResult.get("com");
        //获取快递公司中文名
        BaseExpressTransshipment transshipment = baseExpressTransshipmentMapper.selectOne(
                new LambdaQueryWrapper<BaseExpressTransshipment>()
                        .eq(BaseExpressTransshipment::getCompanyCode, deliveryCompany)
                        .eq(BaseExpressTransshipment::getIsDelete, 0));
        //将原来的数据进行逻辑删除
        bzWaybillTrackHistoryMapper.update(null, new LambdaUpdateWrapper<BzWaybillTrackHistory>()
                .eq(BzWaybillTrackHistory::getWaybillCode, waybillCode)
                .eq(BzWaybillTrackHistory::getTrackStatus, 4)//4代表快递转运
                .eq(BzWaybillTrackHistory::getIsDelete, 0)
                .set(BzWaybillTrackHistory::getIsDelete, 1)
        );
        //记录集合
        JSONArray jsonArray = lastResult.getJSONArray("data");
        log.info("快递100订阅推送回调结果data数组->{}", jsonArray);
        List<BzWaybillTrackHistory> trackHistoryList = new ArrayList<>();
        for (Object array : jsonArray) {
            Map map = JSON.parseObject(array.toString(), Map.class);
            //提前预设
            String province = "";
            String city = "";
            String address = "";
            if (null != map.get("areaName") && StringUtils.isNotBlank(map.get("areaName").toString())) {      //location为空才会执行
                //获取省份城市地点
                String areaName = (String) map.get("areaName");
                int index1 = areaName.indexOf(",");
                if (index1 != -1) {
                    province = areaName.substring(0, index1);//省份
                    //将省份去除
                    String newAreaName1 = areaName.substring(index1 + 1);
                    int index2 = newAreaName1.indexOf(",");
                    if (index2 != -1) {
                        city = newAreaName1.substring(0, index2);//城市
                    } else {
                        //如果不存在说明只剩城市了
                        city = newAreaName1;
                    }
                }
            }
            String context = (String) map.get("context");
            //context作为详细地址
            if (StringUtils.isNotBlank(context)) {
                address = context;
            }
            BzWaybillTrackHistory trackHistory = new BzWaybillTrackHistory();
            //获取经纬度
            if (null != map.get("areaCenter")) {
                String areaCenter = (String) map.get("areaCenter");
                int index = areaCenter.indexOf(",");
                String longitude = areaCenter.substring(0, index);//经度
                String latitude = areaCenter.substring(index + 1);//纬度
                trackHistory.setLatitude(Double.parseDouble(latitude));
                trackHistory.setLongitude(Double.parseDouble(longitude));
            }
            //创建一条历史记录
            if (StringUtils.isNotBlank(address)) {
                log.info("快递100订阅推送回调结果，开始创建历史记录");
                trackHistory.setAddress(address);
                trackHistory.setCity(city);
                trackHistory.setTrackStatus(4);
                trackHistory.setProvince(province);
                trackHistory.setDriverName(transshipment.getCompanyName());
                //快递时间
                String dateString = (String) map.get("ftime");
                if (StringUtils.isNotBlank(dateString)) {
                    DateTime dateTime = DateUtil.parseDateTime(dateString);
                    trackHistory.setCreateTime(dateTime);
                    trackHistory.setUpdateTime(dateTime);
                }
                trackHistory.setWaybillCode(waybillCode);
                trackHistoryList.add(trackHistory);
            } else {
                log.info("快递100订阅推送回调结果，未获取到省份、城市，不进行添加数据->{}", trackHistory);
            }
        }
        // 批量插入
        if (CollUtil.isNotEmpty(trackHistoryList)) {
            bzWaybillTrackHistoryService.saveBatch(trackHistoryList);
            log.info("快递100订阅推送回调结果，落库成功！ waybillCode:{}", waybillCode);
        }
        //判断快递是否已经签收
        String isCheck = (String) lastResult.get("ischeck");
        if ("1".equals(isCheck)) {
            //签收将运单状态进行变更
            BzWaybill waybill = new BzWaybill();
            waybill.setId(waybillCodeId);
            waybill.setStatus(WaybillStatusConstants.COMPLETED);
            waybill.setUpdateTime(new Date());
            waybill.setSignTime(new Date());
            waybill.setUpdateUserName("快递100");
            waybill.setUpdateUserId("kuaidi100");
            bzWaybillMapper.updateById(waybill);
        }
        dragonService.updateTrackSync(Collections.singletonList(waybillCode), true);
    }

    /**
     * 合单操作
     *
     * @param args
     * @param waybillId
     */
    @Transactional(rollbackFor = Exception.class)
    public Map combineBzWaybill(List<Object> args, Long waybillId) {
        BzWaybill parentBzWaybill = bzWaybillMapper.selectById(waybillId);
        //判断包装是否完成
        List<BzWaybill> bzWaybills = bzWaybillMapper.selectList(new LambdaQueryWrapper<BzWaybill>()
                .in(BzWaybill::getId, args)
//                        .eq(BzWaybill::getIsCompletePacking, 1)  搜索列表就校验过了
                .eq(BzWaybill::getIsDelete, 0));
        //
        StringBuilder successMsg = new StringBuilder();
        //总箱数
        Integer totalBox = 0;
        //总体积
        Double totalVolume = 0.0;
        Double totalWeight = 0.0;
        for (BzWaybill bzWaybill : bzWaybills) {
            Object savePoint = null;
            try {
                Boolean tryLock = ascmLockHelper.tryLock("LOCK" + bzWaybill.getWaybillCode(), 0, 5, TimeUnit.MINUTES);
                if (tryLock) {
                    //设置回滚点
                    savePoint = TransactionAspectSupport.currentTransactionStatus().createSavepoint();
                    //运单与箱号的关系交替
                    List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(new LambdaQueryWrapper<BzWaybillBoxRel>()
                            .eq(BzWaybillBoxRel::getWaybillCode, bzWaybill.getWaybillCode())
                            .eq(BzWaybillBoxRel::getIsDevanning, 0)
                            .eq(BzWaybillBoxRel::getIsDelete, 0));
                    //添加集合
                    ArrayList<BzWaybillBoxRel> insertList = new ArrayList<>();
                    bzWaybillBoxRels.stream().forEach(e -> {
                        //当前记录进行逻辑删除
                        BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
                        bzWaybillBoxRel.setId(e.getId());
                        bzWaybillBoxRel.setIsDelete(1);
                        bzWaybillBoxRelMapper.updateById(bzWaybillBoxRel);
                        //查询该箱是否曾经由此拆离
                        LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(BzWaybillBoxRel::getWaybillCode, parentBzWaybill.getWaybillCode())
                                .eq(BzWaybillBoxRel::getBoxCode, e.getBoxCode())
                                .eq(BzWaybillBoxRel::getIsDevanning, 1)
                                .eq(BzWaybillBoxRel::getIsDelete, 0);
                        BzWaybillBoxRel waybillBoxRel = bzWaybillBoxRelMapper.selectOne(queryWrapper);
                        if (ObjectUtil.isEmpty(waybillBoxRel)) {
                            //构建新的记录
                            e.setId(null);
                            e.setWaybillCode(parentBzWaybill.getWaybillCode());
                            e.setCreateTime(new Date());
                            insertList.add(e);
                        } else {
                            waybillBoxRel.setIsDevanning(0);
                            bzWaybillBoxRelMapper.updateById(waybillBoxRel);
                        }
                    });
                    if (!insertList.isEmpty()) {
                        bzWaybillBoxRelMapper.insertBatch(insertList);
                    }

                    //箱号上的运单字段
                    List<String> list = bzWaybillBoxRels.stream().map(BzWaybillBoxRel::getBoxCode).collect(Collectors.toList());
                    List<BzErpReqBox> bzErpReqBoxes = bzErpReqBoxMapper.selectList(new LambdaQueryWrapper<BzErpReqBox>().in(BzErpReqBox::getBoxCode, list).eq(BzErpReqBox::getIsDelete, 0));
                    for (BzErpReqBox bzErpReqBox : bzErpReqBoxes) {
                        BzErpReqBox newBzBox = new BzErpReqBox();
                        newBzBox.setId(bzErpReqBox.getId());
                        newBzBox.setWaybillCode(parentBzWaybill.getWaybillCode());
                        newBzBox.setUpdateTime(new Date());
                        bzErpReqBoxMapper.updateById(newBzBox);
                    }

                    //叠加
                    totalBox += bzWaybill.getTotalBox();
                    totalVolume += bzWaybill.getTotalVolume();
                    totalWeight += bzWaybill.getTotalWeight();

                    //查看子运单是否传输完成，完成则删除，未完成则保留
                    if (bzWaybill.getIsCompletePacking() == 1) {
                        //子运单进行逻辑删除
                        BzWaybill updateBzWaybill = new BzWaybill();
                        updateBzWaybill.setId(bzWaybill.getId());
                        updateBzWaybill.setTotalBox(0);
                        updateBzWaybill.setTotalVolume(0.0);
                        updateBzWaybill.setTotalWeight(0.0);
                        updateBzWaybill.setUpdateTime(new Date());
                        bzWaybillMapper.updateById(updateBzWaybill);
//                        bzWaybillMapper.deleteById(bzWaybill.getId());
                    } else {
                        //保留并修改相关信息
                        BzWaybill updateBzWaybill = new BzWaybill();
                        updateBzWaybill.setId(bzWaybill.getId());
                        updateBzWaybill.setTotalBox(0);
                        updateBzWaybill.setTotalVolume(0.0);
                        updateBzWaybill.setTotalWeight(0.0);
                        updateBzWaybill.setUpdateTime(new Date());
                        bzWaybillMapper.updateById(updateBzWaybill);
                    }

                    //生成一个拆合记录
                    BaseDemolitionCombineRecord record = new BaseDemolitionCombineRecord();
                    record.setParentWaybillCode(parentBzWaybill.getWaybillCode());
                    record.setItem("PC");
                    record.setChildWaybillCode(bzWaybill.getWaybillCode());
                    record.setAction("合");
                    baseDemolitionCombineRecordMapper.insert(record);
                    successMsg.append(bzWaybill.getWaybillCode() + "、");
                } else {
                    //未获取到锁（不进行自旋），进行信息记录
                    log.error(bzWaybill.getWaybillCode() + "未获取到分布式锁");
                }
            } catch (Exception e) {
                log.error("合单操作发生异常 errorMessage:{}", e.getMessage());
                //手工回滚异常,回滚到savePoint
                TransactionAspectSupport.currentTransactionStatus().rollbackToSavepoint(savePoint);
            } finally {
                ascmLockHelper.unlock("LOCK" + bzWaybill.getWaybillCode());
            }
        }
        //父运单总体积、总箱数的变更
        int update = bzWaybillMapper.update(null, new LambdaUpdateWrapper<BzWaybill>()
                .set(BzWaybill::getTotalBox, parentBzWaybill.getTotalBox() + totalBox)
                .set(BzWaybill::getTotalVolume, parentBzWaybill.getTotalVolume() + totalVolume)
                .set(BzWaybill::getTotalWeight, parentBzWaybill.getTotalWeight() + totalWeight)
                .set(BzWaybill::getUpdateTime, new Date())
                .set(BzWaybill::getCombineCount, parentBzWaybill.getCombineCount() + 1)
                .eq(BzWaybill::getId, parentBzWaybill.getId()));

        HashMap<String, String> map = new HashMap<>();
        map.put("isSuccess", String.valueOf(update));
        map.put("successMsg", successMsg.substring(0, successMsg.length() - 1));
        return map;
    }

    /**
     * 总箱数查询接口
     *
     * @param vo
     * @return
     */
    public IPage<BoxParamDto> boxesByWaybill(BoxParamVo vo) {
        if (vo.getPage() == null) {
            vo.setPage(1);
        }
        if (vo.getPageSize() == null) {
            vo.setPageSize(10);
        }
        Page<BoxParamDto> page = new Page<>();
        page.setSize(vo.getPageSize());
        page.setCurrent(vo.getPage());
        page.setOptimizeCountSql(false);
        //查询运单是为取消状态
        Long bzWaybillId = vo.getBzWaybillId();
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getId, bzWaybillId));
        IPage<BoxParamDto> list = null;
        if ("已取消".equals(bzWaybill.getStatus())) {
            list = bzWaybillMapper.boxesByWaybill(page, vo, 1);
        } else {
            list = bzWaybillMapper.boxesByWaybill(page, vo, 0);
        }
        for (BoxParamDto record : list.getRecords()) {
            Date updateTimeGet = record.getUpdateTimeGet();
            SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            record.setUpdateTime(sdf3.format(updateTimeGet));
        }
        return list;
    }

    public Result driverDeliveryHistory(String drivePhone, Integer page, Integer pageSize) {
        if (StringUtils.isBlank(drivePhone)) {
            return ResultUtil.failed("输入手机号为空！").setData(false);
        }
        if (null == ascmLoginUserHelper.getLoginUser()) {
            return ResultUtil.failed("请登录！").setData(false);
        }
        if (!drivePhone.equals(ascmLoginUserHelper.getLoginUser().getPhone())) {
            return ResultUtil.failed("请使用登录手机查看！").setData(false);
        }
        if (page == null || page <= 0) {
            page = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        IPage<BzWaybillListDto> queryPage = new Page(page, pageSize);
        LambdaQueryWrapper<BzWaybillTrackTruckDriver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybillTrackTruckDriver::getReceiveDriverPhone, drivePhone)
                .eq(BzWaybillTrackTruckDriver::getIsDelete, 0);
        List<BzWaybillTrackTruckDriver> truckDriverList = bzWaybillTrackTruckDriverMapper.selectList(queryWrapper);
        if (ObjectUtil.isEmpty(truckDriverList)) {
            return ResultUtil.success(queryPage);
        }
        Set<String> waybillCodes = truckDriverList.stream().filter(e -> StringUtils.isNotBlank(e.getWaybillCode())).map(BzWaybillTrackTruckDriver::getWaybillCode).collect(Collectors.toSet());
        if (ObjectUtil.isEmpty(waybillCodes)) {
            return ResultUtil.success(queryPage);
        }
        queryPage = bzWaybillMapper.getMiniProgramWaybillList(queryPage, waybillCodes);
        return ResultUtil.success(queryPage);
    }

    /**
     * 获取所有运单状态
     */
    public Result getWaybillAllStatusCountList() {
        List<Map> statusList = new ArrayList<>();
        Map<String, Map> statusMap = bzWaybillMapper.getAllWaybillStatusCount(null, null, null);

        //全部
        {
            Map option = new HashMap<>();
            option.put("label", "全部");
            option.put("value", "全部");
            int count = 0;
            if (null != statusMap.get("待发布")) {
                count = count + Optional.of(statusMap.get("待发布").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("已发布")) {
                count = count + Optional.of(statusMap.get("已发布").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("待提货")) {
                count = count + Optional.of(statusMap.get("待提货").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("待运输")) {
                count = count + Optional.of(statusMap.get("待运输").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("运输中")) {
                count = count + Optional.of(statusMap.get("运输中").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("异常中")) {
                count = count + Optional.of(statusMap.get("异常中").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("已取消")) {
                count = count + Optional.of(statusMap.get("已取消").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            if (null != statusMap.get("已完成")) {
                count = count + Optional.of(statusMap.get("已完成").get("count")).map(String::valueOf).map(Integer::valueOf).orElse(0);
            }
            option.put("count", count);
            statusList.add(option);
        }

        //待发布
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("待发布")) {
                option.put("label", "待发布");
                option.put("value", "待发布");
                option.put("count", statusMap.get("待发布").get("count"));
            } else {
                option.put("label", "待发布");
                option.put("value", "待发布");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //已发布
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("已发布")) {
                option.put("label", "已发布");
                option.put("value", "已发布");
                option.put("count", statusMap.get("已发布").get("count"));
            } else {
                option.put("label", "已发布");
                option.put("value", "已发布");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //待提货
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("待提货")) {
                option.put("label", "待提货");
                option.put("value", "待提货");
                option.put("count", statusMap.get("待提货").get("count"));
            } else {
                option.put("label", "待提货");
                option.put("value", "待提货");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //待运输
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("待运输")) {
                option.put("label", "待运输");
                option.put("value", "待运输");
                option.put("count", statusMap.get("待运输").get("count"));
            } else {
                option.put("label", "待运输");
                option.put("value", "待运输");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //运输中
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("运输中")) {
                option.put("label", "运输中");
                option.put("value", "运输中");
                option.put("count", statusMap.get("运输中").get("count"));
            } else {
                option.put("label", "运输中");
                option.put("value", "运输中");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //异常中
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("异常中")) {
                option.put("label", "异常中");
                option.put("value", "异常中");
                option.put("count", statusMap.get("异常中").get("count"));
            } else {
                option.put("label", "异常中");
                option.put("value", "异常中");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //已取消
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("已取消")) {
                option.put("label", "已取消");
                option.put("value", "已取消");
                option.put("count", statusMap.get("已取消").get("count"));
            } else {
                option.put("label", "已取消");
                option.put("value", "已取消");
                option.put("count", 0);
            }
            statusList.add(option);
        }

        //已完成
        {
            Map option = new HashMap<>();
            if (null != statusMap.get("已完成")) {
                option.put("label", "已完成");
                option.put("value", "已完成");
                option.put("count", statusMap.get("已完成").get("count"));
            } else {
                option.put("label", "已完成");
                option.put("value", "已完成");
                option.put("count", 0);
            }
            statusList.add(option);
        }

//        return statusList;
        return ResultUtil.success(statusList);
    }

    /**
     * 获取运单号下所有已扫描的箱号
     *
     * @param waybillCode 运单编号
     * @return
     */
    public Result getDemolitionBoxList(String waybillCode) {
        if (StringUtils.isBlank(waybillCode)) {
            return ResultUtil.failed("运单号为空！").setData(false);
        }
        List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(new LambdaQueryWrapper<BzWaybillBoxRel>()
                .eq(BzWaybillBoxRel::getWaybillCode, waybillCode)
                .eq(BzWaybillBoxRel::getStatus, 1)
                .eq(BzWaybillBoxRel::getIsDevanning, 0)
                .eq(BzWaybillBoxRel::getIsDelete, 0));
        if (ObjectUtils.isEmpty(bzWaybillBoxRels)) {
            return ResultUtil.failed("请先扫箱，再执行拆箱发运！").setData(false);
        }
        List<String> boxCodes = bzWaybillBoxRels.stream().map(BzWaybillBoxRel::getBoxCode).collect(Collectors.toList());
        return ResultUtil.success(boxCodes);
    }

    public List<Map<String, Object>> getAllTransportType() {
        QueryWrapper<BaseTransportType> transportTypeQueryWrapper = Wrappers.query();

        transportTypeQueryWrapper.select(BaseTransportType.class, e -> "transport_type".equals(e.getColumn()));
        List<Map<String, Object>> maps = baseTransportTypeMapper.selectMaps(transportTypeQueryWrapper);
        return maps;
    }

    /**
     * 运单总览分页查询
     *
     * @param page
     * @return
     */
    public Page<BzWayBillPageVo> getPage(PageQuery<BzWayBillPageDto> page) {
        long startTime = System.currentTimeMillis();
        log.info("BzWaybillService getPage 运单分页查询总开始时间 {}", startTime);

        List<String> stringList = new ArrayList<>();
        stringList.add("待发布");
        stringList.add("已发布");
        stringList.add("待提货");
        stringList.add("待运输");

        log.info("BzWaybillService tempFile 开始执行 [{}]", JSONObject.toJSONString(page.getParam()));
        if (ONE.equals(page.getParam().getSeparateWarehouseFlag()) && CollUtil.isEmpty(page.getParam().getShopCodeList())) {
            // 分仓调拨，查询出所有仓库，然后都添加到目标地址
            LambdaQueryWrapper<BaseWarehouse> allLQW = new LambdaQueryWrapper<>();
            allLQW.eq(BaseWarehouse::getIsDelete, 0);

            List<BaseWarehouse> allBaseWarehouseList = baseWarehouseMapper.selectList(allLQW);
            log.info("BzWaybillService getPage 分仓调拨运单分页查询获取总数耗时时间 {}", System.currentTimeMillis() - startTime);

            List<String> shopCodeList = allBaseWarehouseList.stream()
                    .map(BaseWarehouse::getLgort)
                    .collect(Collectors.toList());

            page.getParam().setShopCodeList(shopCodeList);
        }
        //获取总数
        long total = this.baseMapper.getPageTotal(page.getParam());
        log.info("BzWaybillService getPage 运单分页查询获取总数耗时时间 {}", System.currentTimeMillis() - startTime);

        log.info("BzWaybillService tempFile 开始执行 total [{}]", total);

        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BzWayBillPageVo> bzWaybillPage = bzWaybillMapper.getConditionPage(page.getParam(), startIndex, page.getSize());
        log.info("BzWaybillService getPage 运单分页查询获取主数据耗时时间 {}", System.currentTimeMillis() - startTime);

        log.info("BzWaybillService tempFile 开始执行 bzWaybillPage [{}]", JSONObject.toJSONString(bzWaybillPage));

        if (!CollUtil.isEmpty(bzWaybillPage)) {
            // 获取出所有的运单编号
            List<String> waybillCodes = bzWaybillPage.stream().map(BzWayBillPageVo::getWaybillCode).collect(Collectors.toList());

            // 获取门店信息为空的门店编码
            List<String> shopCodeList = bzWaybillPage.stream()
                    .filter(showItem -> (StrUtil.isBlank(showItem.getShopCity()) || StrUtil.isBlank(showItem.getShopName()) || StrUtil.isBlank(showItem.getShopProvince())))
                    .map(BzWayBillPageVo::getShopCode).collect(Collectors.toList());

            Map<String, BaseShop> shopMap = getShopList(shopCodeList);

            //根据运单编号查询出所有的交货单号和箱子是否完整信息
            List<Map<String, Object>> deliveryOrderCodes = bzWaybillMapper.getDeliveryOrderCodes(waybillCodes);
            log.info("BzWaybillService getPage 根据运单编号查询出所有的交货单号和箱子是否完整信息 {}", System.currentTimeMillis() - startTime);
            // 获取当前位置
            List<BzWaybillTrackHistory> trackHistoryList = bzWaybillTrackHistoryMapper.getAddress(waybillCodes);
            log.info("BzWaybillService getPage 获取当前位置 {}", System.currentTimeMillis() - startTime);
            //查询出来的结果，用并行流填充给主数据，保证线程安全
            List<BzWayBillPageVo> eachList = Collections.synchronizedList(bzWaybillPage);
            eachList.parallelStream().forEach(item -> {

                // 填充门店信息
                if (shopMap.keySet().contains(item.getShopCode())) {
                    BaseShop baseShop = shopMap.get(item.getShopCode());
                    if (ObjectUtil.isNotNull(baseShop)) {
                        item.setShopCity(StrUtil.isNotBlank(baseShop.getShopCity()) ? baseShop.getShopCity() : "");
                        item.setShopName(StrUtil.isNotBlank(baseShop.getShopName()) ? baseShop.getShopName() : "");
                        item.setShopProvince(StrUtil.isNotBlank(baseShop.getShopProvince()) ? baseShop.getShopProvince() : "");
                    }
                }

                // 拿到运单编号
                String waybillCode = item.getWaybillCode();
                // 过滤数据,拿到当前运单的数据
                List<Map<String, Object>> waybillCodeList = deliveryOrderCodes.stream().filter(deliveryOrderCode -> waybillCode.equals(deliveryOrderCode.get("waybillCode"))).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(waybillCodeList)) {
                    Map<String, Object> stringObjectMap = waybillCodeList.get(0);
                    // 设置交货单号信息
                    item.setDeliveryOrderCode((String) stringObjectMap.get("deliveryOrderCode"));
                    item.setOriginDeliveryOrderCode((String) stringObjectMap.get("originDeliveryOrderCode"));
                    // 设置是否完整信息
                    // 判断箱子是否完整或有缺失
                    String receiveState = (String) stringObjectMap.get("receiveState");
                    // 如果有缺失的，就都是缺失的，否则都是完整的
                    if (StrUtil.isNotBlank(receiveState)) {
                        if (receiveState.contains("2")) {
                            item.setReceiveState(2);
                        } else {
                            item.setReceiveState(1);
                        }
                    }
                }
                // 填充位置信息
                List<BzWaybillTrackHistory> collect = trackHistoryList.stream().filter(trackHistory -> trackHistory.getWaybillCode().equals(waybillCode)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    BzWaybillTrackHistory bzWaybillTrackHistory = collect.get(0);
                    item.setLocalInfo(bzWaybillTrackHistory.getAddress());
                    item.setLocalUpdateTime(bzWaybillTrackHistory.getCreateTime());
                }
                // 判断是否能拆箱
                // 1挂起 0没挂起
                if (item.getIsHangUp() == 1 || ((item.getIsHangUp() == 0) && !stringList.contains(item.getStatus()))) {
                    item.setDevanning(1);
                } else {
                    item.setDevanning(0);
                }
                // 对签收时间和过期时间为空的不做超时判断
                if (item.getSignTime() == null || item.getExpiryTime() == null) {
                    item.setIsTimeout("");
                }
            });

            log.info("BzWaybillService getPage 运单分页查询获取其他需补全数据耗时时间 {}", System.currentTimeMillis() - startTime);
        }

        Page<BzWayBillPageVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(bzWaybillPage);
        log.info("BzWaybillService getPage 运单分页查询最终耗时时间 {}", System.currentTimeMillis() - startTime);
        return returnPage;
    }

    private Map<String, BaseShop> getShopList(List<String> shopCodeList) {

        // 如果为空，直接返回一个空集合
        if (shopCodeList.size() <= 0) {
            return new HashMap<>();
        }

        LambdaQueryWrapper<BaseShop> queryWrapper = new LambdaQueryWrapper<>();

        queryWrapper.in(BaseShop::getShopCode, shopCodeList);
        queryWrapper.eq(BaseShop::getIsDelete, 0);

        List<BaseShop> baseShops = baseShopMapper.selectList(queryWrapper);

        Map<String, BaseShop> returnMap = baseShops.stream()
                .collect(Collectors.toMap(BaseShop::getShopCode, baseShop -> baseShop));

        return returnMap;
    }

    public void finishWaybill(WaybillSuccessDto requestVO) throws BusinessException {
        List<String> waybillCodeList = requestVO.getWaybillCodeList();
        log.info("准备结束运单 waybillCodeList: {}", waybillCodeList);
        if (CollUtil.isEmpty(waybillCodeList)) {
            return;
        }
        waybillCodeList.forEach(this::checkKuaidi100WaybillFinish);

        // 查询【分仓调拨】运单是否属于主仓库数据
        List<String> list = this.lambdaQuery()
                .select(BzWaybill::getShopCode)
                .in(BzWaybill::getWaybillCode, waybillCodeList)
                .list().stream().map(BzWaybill::getShopCode).collect(Collectors.toList());
        List<String> lgortList = baseWarehouseMapper.selectList(new QueryWrapper<>())
                .stream().map(BaseWarehouse::getLgort).collect(Collectors.toList());
        log.info("运单所属目标编码: {}, 目前主仓库数据编码: {}", list, lgortList);

        Set<String> set = CollUtil.intersectionDistinct(list, lgortList);
        if (!set.isEmpty()) {
            throw new BusinessException("分仓调拨运单，不允许直接结束运单！");
        }

        LoginUser loginUser = LoginUserHelper.getLoginUser();

        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(BzWaybill::getWaybillCode, waybillCodeList);
        updateWrapper.set(BzWaybill::getStatus, "已完成");
        updateWrapper.set(BzWaybill::getSignTime, new Date());
        updateWrapper.set(BzWaybill::getUpdateTime,new Date());

        dragonService.updateTrackSync(waybillCodeList, true);
        updateWrapper.set(BzWaybill::getUpdateUserName, (loginUser != null && StrUtil.isNotBlank(loginUser.getUsername())) ? loginUser.getUsername() : null);
        bzWaybillMapper.update(null, updateWrapper);
        bzWaybillInfoSyncService.signedTimeSync(bzWaybillInfoSyncService.selectSyncData(2));
    }

    public void updateSort() {
        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(BzWaybill::getSortNum, 1);
        updateWrapper.eq(BzWaybill::getStatus, "运输中");

        int updateCount = bzWaybillMapper.update(null, updateWrapper);

        log.info("BzWaybillService updateSort 开始执行 [{}]", updateCount);
    }

    public JSONObject getBuildJson(String waybill) {
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, waybill).eq(BzWaybill::getIsDelete, 0));
        return getBoxJson(bzWaybill);
    }

    /**
     * 批量导入实际到达时间校验
     *
     * @param file 导入的文件
     * @return 校验的结果
     */
    public ImportResponseDto importActualTime(MultipartFile file) {
        ActualTimeListener actualTimeListener = new ActualTimeListener();

        //存放读取的数据
        List<ActualTimeDto> importDTOList = new ArrayList<>();

        //校验成功的数据
        List<ActualTimeDto> successList = new Vector<>();

        //校验失败的数据
        List<ActualTimeDto> failList = new Vector<>();

        try {
            //读取 excel 内容
            EasyExcel.read(file.getInputStream(), ActualTimeDto.class, actualTimeListener).sheet().doRead();
            //获取读取到的内容
            importDTOList = actualTimeListener.getList();
            //检验
            if (CollUtil.isEmpty(importDTOList)) {
                throw new ResultException(500, "导入内容为空！");
            }
            //用来判断是否存在重复
            List<String> waybillCodes = new Vector<>();
            //并行校验
            Map<String, Boolean> mainWarehouseMap = verifierReceiverIsCentral(
                    importDTOList.stream().map(ActualTimeDto::getWaybillCode).collect(Collectors.toList())
            );
            importDTOList.parallelStream().forEach(item -> {
                if (Boolean.TRUE.equals(mainWarehouseMap.get(item.getWaybillCode()))) {
                    BzWaybill waybill = this.lambdaQuery().eq(BzWaybill::getWaybillCode, item.getWaybillCode()).last("limit 1").one();
                    if (waybill.getShopCity().equals("")) {

                    }
                    item.setFailedReason("分仓的运单不能导入");
                    failList.add(item);
                    return;
                }
                // 判断运单是否重复
                if (waybillCodes.contains(item.getWaybillCode())) {
                    item.setFailedReason("运单编号重复");
                    failList.add(item);
                    return;
                } else {
                    waybillCodes.add(item.getWaybillCode());
                }
                // 运单非空校验
                if (StrUtil.isBlank(item.getWaybillCode())) {
                    item.setFailedReason("运单号不能为空！");
                    failList.add(item);
                    return;
                }
                // 运单真实性校验
                BzWaybill bzWaybill = this.bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, item.getWaybillCode()));
                if (BeanUtil.isEmpty(bzWaybill)) {
                    item.setFailedReason("运单号不存在！");
                    failList.add(item);
                    return;
                }
                // 日期格式校验
                if (!isValidDate(item.getDate())) {
                    item.setFailedReason("日期格式错误！");
                    failList.add(item);
                    return;
                }
                //时间格式校验
                if (!isValidTime(item.getTime())) {
                    item.setFailedReason("时间格式错误！");
                    failList.add(item);
                    return;
                }
                //时间转换
                String string = item.getDate() + item.getTime();
                DateTime time = DateUtil.parse(string, "yyyyMMddHHmmss");
                item.setSignTime(time);
                successList.add(item);
            });

            //构造响应体
            ImportResponseDto accountImportResponseDTO = new ImportResponseDto();
            accountImportResponseDTO.setFailCount(failList.size());
            accountImportResponseDTO.setSuccessCount(successList.size());

            String uuid = UUID.randomUUID().toString();
            accountImportResponseDTO.setFileCode(uuid);

            if (!failList.isEmpty()) {
                log.info("创建错误导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("fail", uuid), JSON.toJSON(failList), 16, TimeUnit.HOURS);
            }

            if (!successList.isEmpty()) {
                log.info("创建成功导出码 [{}]", uuid);
                // 存入redis
                ascmRedisHelper.set(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("success", uuid), JSON.toJSON(successList), 16, TimeUnit.HOURS);
            }

            return accountImportResponseDTO;
        } catch (IOException | ResultException e) {
            throw new RuntimeException(e);
        } finally {
            actualTimeListener.clear();
        }
    }

    /**
     * 实际时间模板下载
     *
     * @return 模板的下载路径名称等
     */
    public ImageResponseDTO getTempFile() {
        ImageResponseDTO returnVO = imageService.getTempURL(actualTimeTempFileId);
        return returnVO;
    }

    /**
     * 下载错误的文件
     *
     * @param dto      错误文件的UUID
     * @param response 响应文件
     */
    public void downloadFailFile(AccountSuccessRequestDTO dto, HttpServletResponse response) throws ResultException {
        log.info("BzWaybillService downloadFailFile 开始导出 {}", dto.getOperationCode());
        String operationCode = dto.getOperationCode();
        //获取redis中存储的失败数据
        String json = ascmRedisHelper.get(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("fail", operationCode), String.class);
        List<ActualTimeDto> failResult = JSON.parseArray(json, ActualTimeDto.class);
        //响应出去
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");

        try {
            String fileName = URLEncoder.encode("实际时间导入错误数据", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            //把自定义头暴漏给前端 让其获取到导出的文件名称
            response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
            EasyExcel.write(response.getOutputStream(), ActualTimeDto.class).sheet("sheet").doWrite(failResult);
        } catch (Exception e) {
            log.error("BzWaybillService downloadFailFile 导出异常！[{}]", e.toString());
        }
    }

    /**
     * 批量导入更新运单状态
     *
     * @param operationCode 校验成功的文件UUID
     * @return 更新的结果
     */
    @Transactional
    public Result updateActualTime(String operationCode) throws ResultException {
        log.info("BzWaybillService updateActualTime 开始执行");
        List<ActualTimeDto> successResult = ascmRedisHelper.getList(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("success", operationCode), ActualTimeDto.class);

        AtomicInteger successCount = new AtomicInteger();
        AtomicInteger failCount = new AtomicInteger();

        //不能并行，多租户会失效
        successResult.forEach(item -> {
            LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .set(BzWaybill::getSignTime, item.getSignTime())
                    .set(BzWaybill::getStatus, "已完成").eq(BzWaybill::getWaybillCode, item.getWaybillCode()).ne(BzWaybill::getStatus, "已取消");
            int update = this.bzWaybillMapper.update(null, updateWrapper);
            if (update == 1) {
                successCount.addAndGet(update);
            } else {
                failCount.addAndGet(1);
                item.setFailedReason("您无权操作此仓库或此运单已取消！");
            }
        });
        ascmRedisHelper.set(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("success", operationCode), JSON.toJSONString(successResult), 1, TimeUnit.HOURS);
        log.info("BzWaybillService updateActualTime 更新成功{}个 更新失败{}个", successCount.get(), failCount.get());
        if (failCount.get() != 0) {
            return ResultUtil.failed(String.format("本次导入更新成功%d个 更新失败%d个", successCount.get(), failCount.get()));
        }
        BzWaybillService bzWaybillService = SpringUtil.getBean(this.getClass());
        dragonService.updateTrackSync(successResult.stream().map(ActualTimeDto::getWaybillCode).collect(Collectors.toList()), true);
        bzWaybillService.signedTimeSync(successResult.stream().map(ActualTimeDto::getWaybillCode).collect(Collectors.toList()));
        return ResultUtil.success(String.format("本次导入更新成功%d个 更新失败%d个", successCount.get(), failCount.get()));
    }

    @Async
    public void signedTimeSync(List<String> waybillCodes) {
        if (!waybillCodes.isEmpty()) {
            List<BzWaybill> bzWaybills = bzWaybillInfoSyncService.selectSyncData(2);
            bzWaybillInfoSyncService.signedTimeSync(bzWaybills);
        }
    }

    /**
     * 获取校验成功的数据分页
     *
     * @param page 分页查询条件
     * @return 分页结果
     */
    public Page<ActualTimeDto> getSuccessPage(PageQuery<AccountSuccessRequestDTO> page) {
        log.info("BzWaybillController getSuccessPage 开始执行");
        AccountSuccessRequestDTO accountSuccessRequestDTO = page.getParam();

        List<ActualTimeDto> successResult = null;
        try {
            String json = ascmRedisHelper.get(RedisKeyManager.IMPORT_ACTUAL_TIME.buildKey("success", accountSuccessRequestDTO.getOperationCode()), String.class);
            successResult = JSON.parseArray(json, ActualTimeDto.class);
            log.info("redis中获取到的值：{}", json);
        } catch (ResultException e) {
            throw new RuntimeException(e);
        }
        Page<ActualTimeDto> returnPage = page.convertPage();
        returnPage.setTotal(successResult.size());
        if (page.getPage() > returnPage.getPages()) {
            return returnPage;
        }
        List<ActualTimeDto> subList;

        if (successResult.size() > page.getSize()) {
            int thePage = (int) page.getPage();
            int theSize = (int) page.getSize();

            int startIndex = theSize * (thePage - 1);

            int endIndex = startIndex + theSize;

            if (endIndex > successResult.size()) {
                endIndex = successResult.size();
            }

            subList = successResult.subList(startIndex, endIndex);

        } else {
            subList = successResult;
        }

        returnPage.setRecords(subList);
        return returnPage;
    }

    /**
     * 运单详情页 —— 详情+箱子分页
     *
     * @param page 查询分页参数
     * @return 详情页结果
     * @throws ResultException 异常信息
     */
    public BzWaybillDetailDto getWaybillDetail(PageQuery<String> page) throws ResultException {

        log.info("BzWaybillService waybillDetail [{}]", page.getParam());

        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<BzWaybill>().eq("waybill_code", page.getParam()).eq("is_delete", 0).last("limit 1"));

        log.info("BzWaybillService bzWaybill [{}]", JSONObject.toJSONString(bzWaybill));

        if (null == bzWaybill) {
            throw new ResultException(500, "找不到运单！");
        }

        BzWaybillDetailDto bzWaybillDetailDTO = baseMapper.getDetailById(page.getParam(), "", "", bzWaybill.getCarrierCode());

        log.info("BzWaybillService bzWaybillDetailDTO [{}]", JSONObject.toJSONString(bzWaybillDetailDTO));
        if (ObjectUtil.isNull(bzWaybillDetailDTO)) {
            throw new ResultException(500, "找不到运单！");
        }

        Page<BzErpReqBoxRelDto> pageParameter = new Page<>(page.getPage(), page.getSize());

        IPage<BzErpReqBoxRelDto> boxRelList = bzWaybillBoxRelMapper.getBoxes(pageParameter, page.getParam());

        //计算总体积
        List<BzErpReqBoxRelDto> records = boxRelList.getRecords();
        for (BzErpReqBoxRelDto record : records) {
            Double totalMaterialVolume = 0.000;
            List<BzErpReqMaterialRelDto> allByBoxCode = bzErpReqMaterialRelMapper.findAllByBoxCode(record.getBoxCode());
            List<Double> collect = allByBoxCode.stream().map(BzErpReqMaterialRelDto::getMaterialVolume).map(Double::valueOf).collect(Collectors.toList());
            for (Double aDouble : collect) {
                totalMaterialVolume += aDouble;
            }
            record.setTotalMaterialVolume(totalMaterialVolume);
            Double actualVolume = Double.parseDouble(record.getActualVolume());
            //设置装载率
            if (Math.abs(totalMaterialVolume) > 0 && Math.abs(actualVolume) > 0) {
                BigDecimal bigDecimal = new BigDecimal(totalMaterialVolume / actualVolume).setScale(4, RoundingMode.DOWN);
                record.setLoadingRate(bigDecimal.doubleValue());
            } else {
                record.setLoadingRate(null);
            }
        }

        log.info("BzWaybillService boxRelList [{}]", JSONObject.toJSONString(boxRelList));
        bzWaybillDetailDTO.setBoxRelList(boxRelList);
        return bzWaybillDetailDTO;
    }

    /**
     * 分仓调拨明细详情分页
     *
     * @param page 分页参数
     * @return 分页结果
     */
    public Page<BzWaybillMaterialDetailPageVo> getMaterialDetailPage(PageQuery<BzWayBillPageDto> page) {
        log.info("BzWaybillService getMaterialDetailPage {}", page);

        if (ONE.equals(page.getParam().getSeparateWarehouseFlag()) && CollUtil.isEmpty(page.getParam().getShopCodeList())) {
            // 分仓调拨，查询出所有仓库，然后都添加到目标地址
            LambdaQueryWrapper<BaseWarehouse> allLQW = new LambdaQueryWrapper<>();
            allLQW.eq(BaseWarehouse::getIsDelete, 0);

            List<BaseWarehouse> allBaseWarehouseList = baseWarehouseMapper.selectList(allLQW);

            List<String> shopCodeList = allBaseWarehouseList.stream()
                    .map(BaseWarehouse::getLgort)
                    .collect(Collectors.toList());

            page.getParam().setShopCodeList(shopCodeList);
        }

        //获取总数
        long total = this.baseMapper.getMaterialDetailPageTotal(page.getParam());

        log.info("BzWaybillService getMaterialDetailPage 开始执行 total [{}]", total);

        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();

        List<BzWaybillMaterialDetailPageVo> bzWaybillPage = bzWaybillMapper.getMaterialDetailPage(page.getParam(), startIndex, page.getSize());

        log.info("BzWaybillService getMaterialDetailPage 开始执行 bzWaybillPage [{}]", JSONObject.toJSONString(bzWaybillPage));

        //获取出所有的运单编号
        List<String> waybillCodes = bzWaybillPage.stream().map(BzWaybillMaterialDetailPageVo::getWaybillCode).collect(Collectors.toList());
        HashSet<String> newWaybillCodes = new HashSet<>(waybillCodes);
        waybillCodes = new ArrayList<>(newWaybillCodes);
        log.info("BzWaybillService getMaterialDetailPage list {}", JSON.toJSONString(waybillCodes));
        // 获取当前位置
        if (!waybillCodes.isEmpty()) {
            List<BzWaybillTrackHistory> trackHistoryList = bzWaybillTrackHistoryMapper.getAddress(waybillCodes);
            log.info("BzWaybillService getMaterialDetailPage {}", JSON.toJSONString(trackHistoryList));
            for (BzWaybillMaterialDetailPageVo bzWaybillMaterialDetailPageVo : bzWaybillPage) {
                String waybillCode = bzWaybillMaterialDetailPageVo.getWaybillCode();
                log.info("BzWaybillService getMaterialDetailPage waybillCode{}", waybillCode);
                // 填充位置信息
                List<BzWaybillTrackHistory> collect = trackHistoryList.stream().filter(trackHistory -> trackHistory.getWaybillCode().equals(waybillCode)).collect(Collectors.toList());
                log.info("BzWaybillService getMaterialDetailPage trackHistory{}", JSON.toJSONString(collect));
                if (CollUtil.isNotEmpty(collect)) {
                    BzWaybillTrackHistory bzWaybillTrackHistory = collect.get(0);
                    bzWaybillMaterialDetailPageVo.setLocalInfo(bzWaybillTrackHistory.getAddress());
                    bzWaybillMaterialDetailPageVo.setLocalUpdateTime(bzWaybillTrackHistory.getCreateTime());
                }
            }
        }

        Page<BzWaybillMaterialDetailPageVo> returnPage = new Page<>();
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(bzWaybillPage);

        return returnPage;
    }

    /**
     * 分仓调拨明细详情分页V2
     */
    public Page<BzWaybillMaterialDetailPageVo> getMaterialDetailPageV2(PageQuery<BzWayBillPageDto> page) {
        Page<BzWaybillMaterialDetailPageVo> bzWaybillPage = bzWaybillMapper.getMaterialDetailPageV2(page.getParam(), page.convertPage());

        //获取出所有的运单编号
        List<String> waybillCodes = bzWaybillPage.getRecords().stream().map(BzWaybillMaterialDetailPageVo::getWaybillCode).distinct().collect(Collectors.toList());
        // 获取当前位置
        if (!waybillCodes.isEmpty()) {
            List<BzWaybillTrackHistory> trackHistoryList = bzWaybillTrackHistoryMapper.getAddress(waybillCodes);
            for (BzWaybillMaterialDetailPageVo record : bzWaybillPage.getRecords()) {
                List<BzWaybillTrackHistory> trackHistories = trackHistoryList.stream()
                        .filter(trackHistory -> trackHistory.getWaybillCode().equals(record.getWaybillCode()))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(trackHistories)) {
                    BzWaybillTrackHistory bzWaybillTrackHistory = trackHistories.get(0);
                    record.setLocalInfo(bzWaybillTrackHistory.getAddress());
                    record.setLocalUpdateTime(bzWaybillTrackHistory.getCreateTime());
                }
            }
        }

        return bzWaybillPage;
    }


    /**
     * 重新发JFK
     *
     * @param waybillCode 运单编号
     */
    public void retry(String waybillCode) {
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new QueryWrapper<BzWaybill>().eq("waybill_code", waybillCode));
        List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
        List<Integer> shopType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(shopType)) {
            return;
        }
        this.sendToJFK(bzWaybill, shopType.get(0));
    }

    /**
     * 获取同步运单状态
     *
     * @param waybillCode
     * @return
     */
    public void syncWaybillTransportInfo(String waybillCode, String source, String businessType) {
        log.info("url: {} 同步WMS的运单号 {}", HttpUtils.getCurrentUrl(), waybillCode);
        SyncWaybillInfoDto result = bzWaybillMapper.syncWaybillTransportInfo(waybillCode);
        log.info("同步WMS的运单信息 {}", JSON.toJSONString(result));
        try {
            sendToWMS(result, source, businessType, new Date());
        } catch (Exception e) {
            log.error("同步WMS时发生异常 {}", e.getMessage());
        }
    }

    @Async
    public void syncWaybillTransportInfoByImport(Set<String> waybillCodeList) {
        log.info("syncWaybillTransportInfoByImport waybillCodeList: {}", waybillCodeList);
        if (CollUtil.isEmpty(waybillCodeList)) {
            return;
        }
        List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new QueryWrapper<BaseWarehouse>().eq("is_delete", 0));
        List<BzWaybill> bzWaybills = this.lambdaQuery().in(BzWaybill::getWaybillCode, waybillCodeList).list();

        for (BzWaybill bzWaybill : bzWaybills) {
            List<Integer> businessType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
            List<String> lgort = baseWarehouses.stream().filter(item -> WarehouseTypeEnum.isWms(item.getWarehouseType()) || Objects.equals(item.getWarehouseType(), WarehouseTypeEnum.JFK.getValue())).map(BaseWarehouse::getLgort).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(baseWarehouses) && lgort.contains(bzWaybill.getLgort())) {
                SyncWaybillInfoDto result = bzWaybillMapper.syncWaybillTransportInfo(bzWaybill.getWaybillCode());
                log.info("import 同步WMS的运单信息 {}", JSON.toJSONString(result));
                try {
                    sendToWMS(result, "20", CollectionUtil.isEmpty(businessType) ? "50" : BusinessTypeEnum.getBusinessType(businessType.get(0)), bzWaybill.getLocalUpdateTime());
                } catch (Exception e) {
                    log.error("import 同步WMS时发生异常 {}", e.getMessage());
                }
            }
        }

        // ASCM0087接口
        BzWaybillInfoSyncService waybillInfoSyncService = SpringUtil.getBean(BzWaybillInfoSyncService.class);
        log.info("BzWaybillInfoSyncService dispatchTimeSync size:{}", bzWaybills.size());
        for (BzWaybill bzWaybill : bzWaybills) {
            List<Integer> shopType = baseWarehouses.stream().filter(item -> item.getLgort().equals(bzWaybill.getShopCode())).map(BaseWarehouse::getWarehouseType).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(shopType)) {
                shopType.add(5);
            }
            waybillInfoSyncService.waybillInfoSyncErp(bzWaybill, BusinessTypeEnum.getBusinessType(shopType.get(0)), 1);
        }
    }

    @RemoteApi(RemoteApiEnum.ASCM0086)
    private void sendToWMS(SyncWaybillInfoDto result, String source, String businessType, Date date) {
        //进行数据封装
        String sendId = UUID.fastUUID().toString(true);
        JSONObject headParams = POReqUtil.getHeadParams("ASCM", "WMS", "ASCM0086", sendId);

        // 封装DATA的数据
        String jsonString = JSON.toJSONString(result);
        JSONObject requestObject = JSONObject.parseObject(jsonString);
        requestObject.put("ZSENDTIME", DateUtil.format(date, "yyyyMMdd"));
        requestObject.put("ZCLASS", businessType);
        requestObject.put("ZSOURCE", source);
        JSONObject params = new JSONObject();
        params.put("HEADER", headParams);
        params.put("DATA", requestObject);

        log.info("同步wms的数据 {}", params);
        // 调用WMS
        EgressGatewayRequest request = new EgressGatewayRequest();
        request.setAppId("xp-halley-ascm");
        request.setApiCode(POInvokeEnum.SI_ASCM0086_Syn_Out.getApiCode());
        request.setData(params);

        Result syncResult = egressGatewayFeign.invoke(request);

        log.info("updateWaybillInfo sendToWMS 处理完成 result [{}]", JSONObject.toJSONString(syncResult));
    }

    /**
     * 编辑运单
     *
     * @param waybillDto
     * @return
     */
    public Result<String> updateWaybill(BzWaybillDto waybillDto) {
        BzWaybill bzWaybill = new BzWaybill();
        BeanUtils.copyProperties(waybillDto, bzWaybill);
        // 判断运单是否修改
        BzWaybill oldBzWaybill = this.baseMapper.selectById(waybillDto.getId());
        if (BeanUtil.isEmpty(oldBzWaybill)) {
            return ResultUtil.failed("运单不存在");
        }
        if (!oldBzWaybill.getTransportType().equals(waybillDto.getTransportType())) {
            LambdaQueryWrapper<BaseWaybillRules> lqw = new LambdaQueryWrapper<>();
            lqw.eq(BaseWaybillRules::getLgort, bzWaybill.getLgort()).eq(BaseWaybillRules::getShopCode, bzWaybill.getShopCode())
                    .eq(BaseWaybillRules::getTransportType, waybillDto.getTransportType()).last("limit 1");
            BaseWaybillRules baseWaybillRules = baseWaybillRulesMapper.selectOne(lqw);
            if (BeanUtil.isNotEmpty(baseWaybillRules) && 0 < baseWaybillRules.getPathExpiry()) {
                bzWaybill.setPathExpiry(baseWaybillRules.getPathExpiry());
                bzWaybill.setPath(baseWaybillRules.getPath());
                bzWaybill.setCarrierCode(baseWaybillRules.getCarrierCode());
            } else {
                bzWaybill.setPathExpiry(0);
                bzWaybill.setPath("");
                bzWaybill.setCarrierCode("");
                if (BeanUtil.isNotEmpty(baseWaybillRules)) {
                    bzWaybill.setPath(baseWaybillRules.getPath());
                    bzWaybill.setCarrierCode(baseWaybillRules.getCarrierCode());
                }
            }
        }
        // 优先修改人工填写的，而非匹配规则
        bzWaybill.setPath(waybillDto.getRoute());
        if (null == waybillDto.getCarrierCode()) {
            bzWaybill.setCarrierCode("");
        }
        boolean result = updateById(bzWaybill);
        if (result) {
            return ResultUtil.success("更新成功");
        }
        return ResultUtil.failed("更新失败");
    }

    /**
     * 运单总览分页查询(新)
     *
     * @param page
     * @return
     */
    public Page<BzWayBillNewPageVo> getNewPage(PageQuery<BzWayBillPageDto> page) throws ExecutionException, InterruptedException {
        // 构建响应对象
        Page<BzWayBillNewPageVo> returnPage = new Page<>();
        // 获取总数
        long total = bzWaybillMapper.getNewPageTotal(page.getParam());
        // 如果是0条，后面就不查询了，直接响应结果
        if (0 == total) {
            returnPage.setTotal(total);
            returnPage.setPages(page.getPage());
            returnPage.setSize(page.getSize());
            returnPage.setRecords(new ArrayList<BzWayBillNewPageVo>());
            return returnPage;
        }
        // 获取分页数据
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();
        List<BzWayBillNewPageVo> bzWaybillPage = bzWaybillMapper.getNewPageItem(page.getParam(), startIndex, page.getSize());
        // 补充字段需要的查询 -- 使用CompletableFuture来做并发查询
        CompletableFuture<Map<String, BaseShop>> shopCodeMaps = CompletableFuture.supplyAsync(() -> {
            log.info("查询城市的信息");
            List<String> shopCodeList = bzWaybillPage.stream().map(BzWayBillNewPageVo::getShopCode).distinct().collect(Collectors.toList());
            List<BaseShop> baseShops = baseShopMapper.selectList(new LambdaQueryWrapper<BaseShop>()
                    .eq(BaseShop::getIsDelete, 0).in(BaseShop::getShopCode, shopCodeList));
            return baseShops.stream().collect(Collectors.toMap(BaseShop::getShopCode, Function.identity()));
        });
        CompletableFuture<Map<String, String>> baseWarehouseMaps = CompletableFuture.supplyAsync(() -> {
            log.info("查询仓库的信息");
            List<String> warehouseList = bzWaybillPage.stream().map(BzWayBillNewPageVo::getLgort).distinct().collect(Collectors.toList());
            List<BaseWarehouse> baseWarehouses = baseWarehouseMapper.selectList(new LambdaQueryWrapper<BaseWarehouse>()
                    .eq(BaseWarehouse::getIsDelete, 0).in(BaseWarehouse::getLgort, warehouseList));
            return baseWarehouses.stream().collect(Collectors.toMap(BaseWarehouse::getLgort, BaseWarehouse::getWarehouseRemark));
        });
        CompletableFuture<Map<String, Map<String, Object>>> deliveryOrderCodeMaps = CompletableFuture.supplyAsync(() -> {
            log.info("查询交货单的信息");
            List<String> waybillCodeList = bzWaybillPage.stream().map(BzWayBillNewPageVo::getWaybillCode).distinct().collect(Collectors.toList());
            List<Map<String, Object>> deliveryOrderCodes = bzWaybillMapper.getDeliveryOrderCodes(waybillCodeList);
            return deliveryOrderCodes.stream()
                    .collect(Collectors.toMap(innerMap -> (String) innerMap.get("waybillCode"), Function.identity()));
        });
        CompletableFuture<Map<String, BzWaybillTrackHistory>> addressInfo = CompletableFuture.supplyAsync(() -> {
            log.info("查询地址的信息");
            List<String> waybillCodeList = bzWaybillPage.stream().map(BzWayBillNewPageVo::getWaybillCode).distinct().collect(Collectors.toList());
            List<BzWaybillTrackHistory> address = bzWaybillTrackHistoryMapper.getAddress(waybillCodeList);

            return address.stream().collect(Collectors.toMap(BzWaybillTrackHistory::getWaybillCode, Function.identity(), (v1, v2) -> v1));
        });
        // 补充字段
        Map<String, BaseShop> shopCodeToBaseShop = shopCodeMaps.get();
        Map<String, String> lgortToRemark = baseWarehouseMaps.get();
        Map<String, Map<String, Object>> deliveryOrder = deliveryOrderCodeMaps.get();
        Map<String, BzWaybillTrackHistory> stringBzWaybillTrackHistoryMap = addressInfo.get();
        bzWaybillPage.forEach(item -> {
            // 设置仓库简称
            item.setWarehouseRemark(lgortToRemark.get(item.getLgort()));
            // 设置门店信息
            BaseShop baseShop = shopCodeToBaseShop.get(item.getShopCode());
            if (BeanUtil.isNotEmpty(baseShop)) {
                item.setShopName(baseShop.getShopName());
                item.setShopProvince(baseShop.getShopProvince());
                item.setShopCity(baseShop.getShopCity());
                item.setShopAddress(baseShop.getShopAddress());
                item.setShopContactPerson(baseShop.getContactPerson());
                item.setShopContactNum(baseShop.getContactNum());
            }
            // 设置交货单信息
            String waybillCode = item.getWaybillCode();
            Map<String, Object> deliveryOrderEntity = deliveryOrder.get(waybillCode);
            if (null != deliveryOrderEntity) {
                item.setOriginDeliveryOrderCode((String) deliveryOrderEntity.get("originDeliveryOrderCode"));
                item.setDeliveryOrderCode((String) deliveryOrderEntity.get("deliveryOrderCode"));
                item.setDeliveryOrderType((String) deliveryOrderEntity.get("deliveryOrderType"));
                // 设置计划发运时间信息
                LocalDateTime planShippingTime = (LocalDateTime) deliveryOrderEntity.get("planShippingTime");
                if (null != planShippingTime) {
                    Date time = Date.from(planShippingTime.atZone(ZoneId.systemDefault()).toInstant());
                    if (DateUtil.compare(time, new Date(0)) == 0) {
                        item.setPlanShippingTime(null);
                    } else {
                        item.setPlanShippingTime(time);
                    }
                }
            }
            // 设置地址信息
            if (CollectionUtil.isNotEmpty(stringBzWaybillTrackHistoryMap)) {
                BzWaybillTrackHistory bzWaybillTrackHistory = stringBzWaybillTrackHistoryMap.get(item.getWaybillCode());
                if (BeanUtil.isNotEmpty(bzWaybillTrackHistory)) {
                    item.setLocalInfo(bzWaybillTrackHistory.getAddress());
                    item.setLocalUpdateTime(bzWaybillTrackHistory.getCreateTime());
                }
            }
        });
        // 响应结果
        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(bzWaybillPage);
        return returnPage;
    }

    /**
     * 匹配运输
     *
     * @return
     */
    public Result<String> matchTransport(List<Long> ids) {

        if (CollectionUtil.isEmpty(ids)) {
            return ResultUtil.failed("请选择需要匹配的运单！");
        }
        // 查询出对应的运单
        List<Map> bzWaybillList = this.baseMapper.matchTransport(ids);
        // 仅已发布、待发布可以匹配
        for (Map stringObjectMap : bzWaybillList) {
            if (!stringObjectMap.get("status").equals(WaybillStatusEnum.UNPUBLISHED.getValue())
                    && !stringObjectMap.get("status").equals(WaybillStatusEnum.PUBLISHED.getValue())
                    && !stringObjectMap.get("status").equals(WaybillStatusEnum.NOT_PICK_UP.getValue())) {
                return ResultUtil.failed("匹配运输失败，仅待发布、已发布、待提货可以匹配");
            }
        }

        if (CollUtil.isEmpty(bzWaybillList)) {
            return ResultUtil.failed("找不到关联运单无法进行匹配运输");
        } else {
            // 匹配运输
            Map result = matchWaybillRule(bzWaybillList);
            if (result.get("total").equals(0)) {
                return ResultUtil.failed("匹配运输失败，无符合条件的运单进行匹配");
            } else if (!Objects.equals(result.get("total"), result.get("succeed"))) {
                if (StrUtil.isBlank(result.get("multipleRule").toString())) {
                    return ResultUtil.failed("匹配运输失败，共" + result.get("total") + "条，匹配" + result.get("succeed") + "条。失败运单号：" + result.get("errorInfo"));
                } else {
                    return ResultUtil.failed("匹配运输失败，共" + result.get("total") + "条，匹配" + result.get("succeed") + "条。失败运单号：" + result.get("errorInfo") + "，存在重复规则运单号：" + result.get("multipleRule"));
                }
            } else {
                if (StrUtil.isBlank(result.get("multipleRule").toString())) {
                    return ResultUtil.success("匹配运输成功，共" + result.get("total") + "条，成功匹配" + result.get("succeed") + "条。");
                } else {
                    return ResultUtil.success("匹配运输成功，共" + result.get("total") + "条，成功匹配" + result.get("succeed") + "条。存在重复规则运单号：" + result.get("multipleRule"));
                }
            }
        }
    }

    /**
     * 批量打印
     *
     * @param ids
     * @return
     */
    public Result<String> batchPrint(List<Long> ids) {
        // 根据id查询运单
        List<BzWaybill> bzWaybills = baseMapper.selectBatchIds(ids);
        for (BzWaybill bzWaybill : bzWaybills) {
            if (!bzWaybill.getStatus().equals(WaybillStatusEnum.IN_TRANSIT.getValue())
                    && !bzWaybill.getStatus().equals(WaybillStatusEnum.IN_THE_ABNORMAL.getValue())
                    && !bzWaybill.getStatus().equals(WaybillStatusEnum.WAIT_TRANSIT.getValue())
                    && !bzWaybill.getStatus().equals(WaybillStatusEnum.COMPLETE.getValue())) {
                return ResultUtil.failed("操作失败，仅【待运输、运输中、异常中、已完成】可以打印");
            }
        }

        for (BzWaybill bzWaybill : bzWaybills) {
            QueryWrapper<BasePrintOutTask> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("waybill_code", bzWaybill.getWaybillCode());
            Long aLong = basePrintOutTaskMapper.selectCount(queryWrapper);
            if (aLong > 0) {
                UpdateWrapper<BasePrintOutTask> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("status", 0);
                updateWrapper.eq("waybill_code", bzWaybill.getWaybillCode());
                updateWrapper.and(wq -> wq.eq("status", PrintTaskStatusEnum.SUCCESS.getValue())
                        .or().eq("status", PrintTaskStatusEnum.FAIL.getValue()));
                updateWrapper.set("trigger_client", TriggerClient.PC.name());
                updateWrapper.set("retry_count", 0);
                updateWrapper.set("base_file_id", null);
                updateWrapper.set("create_time", new Date());
                updateWrapper.set("update_time", new Date());
                updateWrapper.set("update_user_id", ascmLoginUserHelper.getLoginUser().getUserId());
                updateWrapper.set("update_user_name", ascmLoginUserHelper.getLoginUser().getName());
                basePrintOutTaskMapper.update(null, updateWrapper);
            } else {
                BasePrintOutTask basePrintOutTask = new BasePrintOutTask();
                setPrintInfo(basePrintOutTask, bzWaybill, TriggerClient.PC.name());
                //copy时剔除id字段
                BeanUtils.copyProperties(bzWaybill, basePrintOutTask, "id", "createUserId", "createUserName", "createTime", "updateUserId", "updateUserName", "updateTime");
                //查询交货单号
                String deliveryOrderCode = bzWaybillMapper.getDeliveryOrderCode(bzWaybill.getWaybillCode());
                basePrintOutTask.setCreateUserId(ascmLoginUserHelper.getLoginUser().getUserId());
                basePrintOutTask.setCreateUserName(ascmLoginUserHelper.getLoginUser().getName());
                basePrintOutTask.setUpdateUserId(ascmLoginUserHelper.getLoginUser().getUserId());
                basePrintOutTask.setUpdateUserName(ascmLoginUserHelper.getLoginUser().getName());
                basePrintOutTask.setCreateTime(new Date());
                basePrintOutTask.setDeliveryOrderCode(deliveryOrderCode);
                basePrintOutTask.setStatus(0);
                basePrintOutTaskMapper.insert(basePrintOutTask);
            }
        }
        return ResultUtil.success("批量操作成功!");
    }

    /**
     * 修改运单状态（取消、挂起、确认收货）
     *
     * @return
     */
    @Transactional
    public Result<String> changeWaybillState(Integer action, Long id) {
        if (ObjectUtil.isEmpty(action)) {
            return ResultUtil.failed("参数有误");
        }
        BzWaybill bzWaybill = bzWaybillMapper.selectById(id);
        if (BeanUtil.isEmpty(bzWaybill)) {
            return ResultUtil.failed("运单不存在！");
        }
        // 取消运单
        if (1 == action) {
            // 获取关联运单信息
            if (bzWaybill.getIsCompletePacking() == 1 && bzWaybill.getTotalBox() == 0) {
                return ResultUtil.failed("运单已传输完成，并且箱数为0，不可进行任何操作！");
            }

            boolean flag = false;
            String msg = "";
            if (bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.UNPUBLISHED.getValue()) ||
                    bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.PUBLISHED.getValue()) ||
                    bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.NOT_PICK_UP.getValue()) ||
                    bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.WAIT_TRANSIT.getValue())) {
                LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BzWaybill::getStatus, "已取消");
                updateWrapper.set(BzWaybill::getUpdateTime, new Date());//设置修改时间，便于查询
                updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
                int update = bzWaybillMapper.update(null, updateWrapper);
                flag = update == 1;
                msg = "取消";
            }

            if (flag) {
                // 状态机无法字段刷新更新人，需要手动刷新更新人
                {
                    LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(BzWaybill::getUpdateUserId, ascmLoginUserHelper.getLoginUser().getUserId());
                    updateWrapper.set(BzWaybill::getUpdateUserName, ascmLoginUserHelper.getLoginUser().getUsername());
                    updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
                    bzWaybillMapper.update(null, updateWrapper);
                }

                // 运单取消需要回退所有箱号状态为为未扫描
                String waybillCode = bzWaybill.getWaybillCode();
                //获取运单下下的箱并将扫描状态修改为未扫描
                bzWaybillBoxRelMapper.selectList(new LambdaQueryWrapper<BzWaybillBoxRel>().eq(BzWaybillBoxRel::getWaybillCode, waybillCode).eq(BzWaybillBoxRel::getIsDelete, 0)).stream().forEach(e -> {
                    e.setStatus(0);
                    e.setIsDelete(1);
                    bzWaybillBoxRelMapper.updateById(e);
                });
                return ResultUtil.success("您已成功将" + bzWaybill.getWaybillCode() + "更改为" + msg + "状态.");
            }
        }

        // 挂起运单
        if (2 == action) {
            if (bzWaybill.getIsCompletePacking() == 1 && bzWaybill.getTotalBox() == 0) {
                return ResultUtil.failed("运单已传输完成，并且箱数为0，不可进行任何操作！");
            }

            if (bzWaybill.getIsHangUp().equals(1)) { //挂起
                bzWaybill.setIsHangUp(0);
                Date date = new Date();
                date.setTime(0);
                bzWaybill.setHangUpTime(date);
            } else {
                bzWaybill.setIsHangUp(1);
                bzWaybill.setHangUpTime(new Date());
            }
            bzWaybill.updateById();
            return ResultUtil.success("您已成功将" + bzWaybill.getWaybillCode() + "挂起.");
        }

        // 确认收货
        if (3 == action) {
            this.checkKuaidi100WaybillFinish(bzWaybill.getWaybillCode());
            if (bzWaybill.getIsCompletePacking() == 1 && bzWaybill.getTotalBox() > 0) {
                boolean flag = false;
                String msg = "";

                if (bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.IN_TRANSIT.getValue()) || bzWaybill.getStatus().equalsIgnoreCase(WaybillStatusEnum.IN_THE_ABNORMAL.getValue())) {

                    //关闭后计算是否准时送达
                    {
                        Date expiryDate = DateUtils.addDays(bzWaybill.getDepartureTime(), bzWaybill.getPathExpiry());
                        Date signDate = bzWaybill.getSignTime();

                        if (expiryDate.before(signDate)) {
                            //是否为同一条
                            SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
                            if (fmt.format(expiryDate).equals(fmt.format(signDate))) { //为同一天为准确
                                bzWaybill.setIsOntime(1);
                            } else {
                                bzWaybill.setIsOntime(0);
                            }
                        } else {
                            bzWaybill.setIsOntime(1);
                        }
                        bzWaybill.updateById();
                    }
                    LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(BzWaybill::getStatus, "已完成");
                    updateWrapper.set(BzWaybill::getSignTime, new Date());
                    updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
                    int update = bzWaybillMapper.update(null, updateWrapper);
                    flag = update == 1;
                    msg = "关闭";
                }

                if (flag) {
                    // 状态机无法字段刷新更新人，需要手动刷新更新人
                    {
                        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(BzWaybill::getUpdateUserId, ascmLoginUserHelper.getLoginUser().getUserId());
                        updateWrapper.set(BzWaybill::getUpdateUserName, ascmLoginUserHelper.getLoginUser().getUsername());
                        updateWrapper.eq(BzWaybill::getId, bzWaybill.getId());
                        bzWaybillMapper.update(null, updateWrapper);
                    }
                    //PC端确认收获讲最新的物流信息改为完成状态
                    BzWaybillTrackHistory bzWaybillTrackHistory = bzWaybillTrackHistoryMapper.selectOne(new LambdaQueryWrapper<BzWaybillTrackHistory>()
                            .eq(BzWaybillTrackHistory::getWaybillCode, bzWaybill.getWaybillCode())
                            .eq(BzWaybillTrackHistory::getIsDelete, 0)
                            .orderByDesc(BzWaybillTrackHistory::getCreateTime)
                            .last("limit 1"));
                    if (ObjectUtil.isNotEmpty(bzWaybillTrackHistory)) {
                        bzWaybillTrackHistoryMapper.updateById(bzWaybillTrackHistory.setTrackStatus(3));
                    }

                    dragonService.updateTrackSync(Collections.singletonList(bzWaybill.getWaybillCode()), true);
                    List<BzWaybill> bzWaybills = bzWaybillInfoSyncService.selectSyncData(2);
                    bzWaybillInfoSyncService.signedTimeSync(bzWaybills);
                    return ResultUtil.success("您已成功将" + bzWaybill.getWaybillCode() + "更改为" + msg + "状态.");
                }

            } else {
                return ResultUtil.failed("运单：" + bzWaybill.getWaybillCode() + "尚未包装完成或该运单下不存在箱子！");
            }
        }
        return ResultUtil.failed("请指定您所需要修改的运单数据.");
    }

    /**
     * 合单列表查询
     *
     * @param page
     * @return
     */
    public Page<Map<String, Object>> mergeWaybillList(PageQuery<Long> page) {

        Long id = page.getParam();
        if (ObjectUtil.isEmpty(id)) {
            throw new BusinessException("请选择需要合单的运单数据.");
        }
        BzWaybill bzWaybill = bzWaybillMapper.selectById(id);
        if (bzWaybill.getIsCompletePacking() == 1 && bzWaybill.getTotalBox() == 0) {
            throw new BusinessException("运单已传输完成，并且箱数为0，不可进行任何操作！");
        }
        if (bzWaybill.getIsDelete() == 1) {
            throw new BusinessException("合单失败，请稍后重试！");
        }
        // 获取总数
        long total = bzWaybillMapper.waybillJoinListTotal(bzWaybill);
        // 构建分页参数
        long startIndex = page.getPage() == 1 ? 0 : (page.getPage() - 1) * page.getSize();
        // 查询出分页数据
        List<Map<String, Object>> waybillJoinList = bzWaybillMapper.waybillJoinList(bzWaybill, startIndex, page.getSize());

        // 构建响应对象
        Page<Map<String, Object>> returnPage = new Page<>();

        returnPage.setTotal(total);
        returnPage.setPages(page.getPage());
        returnPage.setSize(page.getSize());
        returnPage.setRecords(waybillJoinList);

        return returnPage;
    }
}
