package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayImageVO;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BzWaybillTrackHistoryService extends ServiceImpl<BzWaybillTrackHistoryMapper, BzWaybillTrackHistory> {

    @Resource
    private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;

    @Resource
    private AscmLoginUserHelper loginUserHelper;

    @Resource
    private BzWaybillMapper bzWaybillMapper;

    @Resource
    private DragonService dragonService;

    public List<Map<String, Object>> getMapData(String waybillCode) {
        List<Map<String, Object>> mapDataByWaybillCode = bzWaybillTrackHistoryMapper.getMapDataByWaybillCode(waybillCode);
        if (CollUtil.isEmpty(mapDataByWaybillCode)) {
            return null;
        }
        return mapDataByWaybillCode;
    }

    public List<BzWayBillTrackHistoryVO> getTransportationList(String waybillCode) {
        List<BzWayBillTrackHistoryVO> waybillTransportationDTO = bzWaybillTrackHistoryMapper.getWaybillTransportationDTO(waybillCode);
        if (CollUtil.isEmpty(waybillTransportationDTO)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<BzWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybill::getWaybillCode, waybillCode).eq(BzWaybill::getIsDelete, 0).last("limit 1");
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(queryWrapper);

        for (BzWayBillTrackHistoryVO bzWayBillTrackHistoryVO : waybillTransportationDTO) {
            //bzWayBillTrackHistoryVO.setUpdateTime(DateUtil.offsetHour(bzWayBillTrackHistoryVO.getUpdateTime(), 8));
            bzWayBillTrackHistoryVO.setWaybillCreateTime(DateUtil.offsetHour(bzWaybill.getCreateTime(), 8));
        }
        //再并行处理一下坐标0,0的问题
        waybillTransportationDTO = waybillTransportationDTO.stream().filter(item -> {
            if (item.getWaybillCode().startsWith("JD")) {
                return true;
            }
            return StrUtil.isNotBlank(item.getCity());
        }).collect(Collectors.toList());

        return waybillTransportationDTO;
    }

    public List<BzWayImageVO> getImageList(String waybillCode) {
        List<BzWayImageVO> waybillTransportationDTO = bzWaybillTrackHistoryMapper.getWaybillImage(waybillCode);
        if (CollUtil.isEmpty(waybillTransportationDTO)) {
            return new ArrayList<>();
        }
        return waybillTransportationDTO;
    }

    @Transactional
    public Result locationReporting(BzWaybillTrackHistory bzWaybillTrackHistory) {
        log.info("获取到前端定位数据 start one->{}", bzWaybillTrackHistory);
        String phone = loginUserHelper.getLoginUser().getPhone();
        //当前运输司机
        String driverPhone = bzWaybillTrackHistory.getDriverPhone();
        if (!driverPhone.equals(phone)) {
            return ResultUtil.failed("请使用登录手机同步位置信息！");
        }
        //获取司机当前运输的运单
        List<BzWaybill> bzWaybills = bzWaybillMapper.selectList(new LambdaQueryWrapper<BzWaybill>()
                .eq(BzWaybill::getDriverPhone, driverPhone)
                .in(BzWaybill::getStatus, "运输中", "异常中")
                .eq(BzWaybill::getIsDelete, 0));
        //给运单更新时间
        Date date = new Date();
        DateTime dateTime = DateUtil.offsetHour(date, 8);
        List<Long> waybillIds = bzWaybills.stream().filter(item -> {
            return DateUtil.compare(item.getLocalUpdateTime(), dateTime) < 0;
        }).map(BzWaybill::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(waybillIds)) {
            LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BzWaybill::getLocalUpdateTime, dateTime).in(BzWaybill::getId, waybillIds);
            bzWaybillMapper.update(null, updateWrapper);
        }
        //获取运单号
        Set<String> collect = bzWaybills.stream().map(BzWaybill::getWaybillCode).collect(Collectors.toSet());
        //搜集位置变更信息
        int count = 0;
        ArrayList<BzWaybillTrackHistory> list = new ArrayList<>();
        if (!collect.isEmpty()) {
            for (String waybillCode : collect) {
                BzWaybillTrackHistory waybillTrackHistory = new BzWaybillTrackHistory();
                waybillTrackHistory.setWaybillCode(waybillCode);//运单号
                if (StrUtil.isBlank(bzWaybillTrackHistory.getProvince())) {
                    Result<Object> responseEntity = ResultUtil.failed("获取运单异常：未获取到运单信息!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setProvince(bzWaybillTrackHistory.getProvince());//省份
                if (StrUtil.isBlank(bzWaybillTrackHistory.getCity())) {
                    Result<Object> responseEntity = ResultUtil.failed("获取位置异常：未获取到城市信息!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setCity(bzWaybillTrackHistory.getCity());//城市
                if (StrUtil.isBlank(bzWaybillTrackHistory.getAddress())) {
                    Result<Object> responseEntity = ResultUtil.failed("获取位置异常：未获取到地址信息!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setAddress(bzWaybillTrackHistory.getAddress());//地址
                if (bzWaybillTrackHistory.getLatitude() == null) {
                    Result<Object> responseEntity = ResultUtil.failed("获取位置异常：未获取到经纬度信息!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setLatitude(bzWaybillTrackHistory.getLatitude());
                if (bzWaybillTrackHistory.getLongitude() == null) {
                    Result<Object> responseEntity = ResultUtil.failed("获取位置异常：未获取到经纬度信息!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setLongitude(bzWaybillTrackHistory.getLongitude());
                if (StrUtil.isBlank(bzWaybillTrackHistory.getDriverPhone())) {
                    Result<Object> responseEntity = ResultUtil.failed("获取用户异常：未获取到手机号!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
                waybillTrackHistory.setDriverPhone(driverPhone);
                waybillTrackHistory.setUpdateTime(new Date());
                waybillTrackHistory.setCreateTime(new Date());
                BzWaybillTrackHistory history = bzWaybillTrackHistoryMapper.selectOne(new LambdaQueryWrapper<BzWaybillTrackHistory>().eq(BzWaybillTrackHistory::getWaybillCode, waybillCode).eq(BzWaybillTrackHistory::getIsDelete, 0).last("limit 1"));
                BzWaybillTrackHistory historyLatest = bzWaybillTrackHistoryMapper.selectOne(new LambdaQueryWrapper<BzWaybillTrackHistory>().eq(BzWaybillTrackHistory::getWaybillCode, waybillCode).eq(BzWaybillTrackHistory::getIsDelete, 0).orderByDesc(BzWaybillTrackHistory::getUpdateTime).last("limit 1"));
                if (ObjectUtils.isEmpty(history)) {
                    log.info("获取到前端定位数据 start two->{}", bzWaybillTrackHistory);
                    //初始点
                    waybillTrackHistory.setTrackStatus(0);
                    log.info("准备执行入库操作，该位置为起始点，数据->{}", waybillTrackHistory);
                    bzWaybillTrackHistoryMapper.insert(waybillTrackHistory);
                } else if (!historyLatest.getDriverPhone().equals(driverPhone)) {
                    log.info("获取到前端定位数据 start two->{}", bzWaybillTrackHistory);
                    //转交
                    waybillTrackHistory.setTrackStatus(1);
                    log.info("准备执行入库操作，该位置为起始点，数据->{}", waybillTrackHistory);
                    bzWaybillTrackHistoryMapper.insert(waybillTrackHistory);
                } else {
                    //在途中
                    waybillTrackHistory.setTrackStatus(2);
                    list.add(waybillTrackHistory);
                }
            }
            dragonService.updateTrackSync(collect, true);
            if (!list.isEmpty()) {
                try {
                    log.info("准备执行入库操作，该位置为在途中，批量数据->{}", list);
                    count = bzWaybillTrackHistoryMapper.insertList(list);
                } catch (Exception e) {
                    Result<Object> responseEntity = ResultUtil.failed("入库失败!");
                    responseEntity.setData(false);
                    return responseEntity;
                }
            }
        } else {
            Result<Object> responseEntity = ResultUtil.failed("您手机号下没有正在运输中运单!");
            responseEntity.setData(false);
            return responseEntity;
        }
        if (count == list.size()) {
            return ResultUtil.success(true);
        } else {
            Result<Object> responseEntity = ResultUtil.failed("上传地点异常!");
            responseEntity.setData(false);
            return responseEntity;
        }
    }
}
