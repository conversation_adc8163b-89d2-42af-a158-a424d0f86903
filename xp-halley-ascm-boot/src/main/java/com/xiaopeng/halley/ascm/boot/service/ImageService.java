package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaopeng.halley.ascm.boot.dto.ImageResponseDTO;
import com.xiaopeng.halley.ascm.boot.entity.BaseFile;
import com.xiaopeng.halley.ascm.boot.mapper.BaseFileMapper;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ImageService {

    private static OSS ossClient;

    private static String ALI_ENDPOINT;

    private static String ALI_ACCESS_KEYID;

    private static String ALI_ACCESS_KEY_SECRET;

    private static String ALI_OSS_BUCKET;

    public static String ALI_OSS_PATH;

    private static String STS_ENDPOINT;

    private static String ROLE_ARN;

    private static String ROLE_SESSION_NAME;

    private static String DURATION_SECONDS;

    @Resource
    private BaseFileMapper baseFileMapper;

    @Resource
    private AscmRedisHelper ascmRedisHelper;

    /**
     * 初始化cos客户端
     */
    @PostConstruct
    static public void ini() {
        log.info("------- 初始化COS -------");
        log.info("初始化COS对象管理客户端");
        // Endpoint以杭州为例，其它Region请按实际情况填写。
        // 阿里云主账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM账号进行API访问或日常运维，请登录RAM控制台创建RAM账号。
        // 创建OSSClient实例。

        // 初始化两个，一个走内网，一个走外网，
        // 内网较快，服务器提交到OSS服务器走内网
        // 外界要访问，走外网
        ossClient = new OSSClientBuilder().build(ALI_ENDPOINT, ALI_ACCESS_KEYID, ALI_ACCESS_KEY_SECRET);

        log.info("...初始化COS完成");
        log.info("------------------------");
    }

    @Value("${cos.ali.path}")
    public void setAliOssPath(String aliOssPath) {
        ALI_OSS_PATH = aliOssPath;
    }

    @Value("${cos.ali.endpoint}")
    public void setAliEndpoint(String aliEndpoint) {
        ALI_ENDPOINT = aliEndpoint;
    }

    @Value("${cos.ali.accessKeyId}")
    public void setAliAccessKeyid(String aliAccessKeyid) {
        ALI_ACCESS_KEYID = aliAccessKeyid;
    }

    @Value("${cos.ali.accessKeySecret}")
    public void setAliAccessKeySecret(String aliAccessKeySecret) {
        ALI_ACCESS_KEY_SECRET = aliAccessKeySecret;
    }

    @Value("${cos.ali.bucket}")
    public void setAliOssBucket(String aliOssBucket) {
        ALI_OSS_BUCKET = aliOssBucket;
    }

    @Value("${cos.ali.stsEndpoint}")
    public void setStsEndpoint(String stsEndpoint) {
        STS_ENDPOINT = stsEndpoint;
    }

    @Value("${cos.ali.roleArn}")
    public void setRoleArn(String roleArn) {
        ROLE_ARN = roleArn;
    }

    @Value("${cos.ali.roleSessionName}")
    public void setRoleSessionName(String roleSessionName) {
        ROLE_SESSION_NAME = roleSessionName;
    }

    @Value("${cos.ali.durationSeconds}")
    public void setDurationSeconds(String durationSeconds) {
        DURATION_SECONDS = durationSeconds;
    }

    public BaseFile putFileToServer(String filename, InputStream inputStream) {
        log.info("同步提交图片开始");

        String suffixName = filename.substring(filename.lastIndexOf("."));
        String uuid = UUID.fastUUID().toString(true);
        String saveFileName = uuid + suffixName;

        String path = ALI_OSS_PATH;

        //设置文件名

        PutObjectResult putObjectResult = ossClient.putObject(ALI_OSS_BUCKET, path + saveFileName, inputStream);

        // TODO 判断是否成功
        log.info("putObjectResult {}", JSONObject.toJSONString(putObjectResult));

        // 保存到数据库中
        BaseFile baseFile = new BaseFile();
        baseFile.setFileName(filename);
        baseFile.setFileUuid(uuid);
        baseFile.setFileSaveName(saveFileName);
        baseFile.setProject("ascm");
        baseFile.setProjectPath(path);
        baseFile.setCreateTime(new Date());
        baseFile.setCreateUserId("");
        baseFile.setCreateUserName("");
        baseFile.setUpdateTime(new Date());
        baseFile.setUpdateUserId("");
        baseFile.setUpdateUserName("");
        baseFileMapper.insert(baseFile);
        return baseFile;
    }


    public ImageResponseDTO putFileToServer(String picfileName, File excelFile) {
        log.info("同步提交图片开始");

        String suffixName = picfileName.substring(picfileName.lastIndexOf("."));

        String buildName = UUID.fastUUID().toString(true);

        String saveFileName = buildName + suffixName;

        String path = ALI_OSS_PATH;

        //设置文件名

        PutObjectResult putObjectResult = ossClient.putObject(ALI_OSS_BUCKET, path + saveFileName, excelFile);

        // TODO 判断是否成功
        log.info("putObjectResult {}", JSONObject.toJSONString(putObjectResult));

        // 获取临时访问路径，由于图片刚提交不会存在缓存的问题，可以忽略一次缓存查询

        // 暂时需要审核的时候打开来
        // 这里由于大小的问题，无法解决

        long fileSize = excelFile.length();
        log.info("fileSize {}", fileSize);

        // 1mb = 1048576 byte
        // 1mb 以下，直接检测
        // 超过10m的图片处理不了，通过压缩会导致数据丢失，无法识别，有可能需要使用剪切一块一块去处理，先限制10M以内吧
        // nginx服务器限制是最大50M的图片，这样处理应该可以了

        // 所有用户都需要进行内容检测

        // 保存到数据库中
        BaseFile baseFile = new BaseFile();
        baseFile.setFileName(picfileName);
        baseFile.setFileUuid(buildName);
        baseFile.setFileSaveName(saveFileName);
        baseFile.setProject("ascm");
        baseFile.setProjectPath(path);

        baseFile.setCreateTime(new Date());
        baseFile.setCreateUserId("");
        baseFile.setCreateUserName("");

        baseFile.setUpdateTime(new Date());
        baseFile.setUpdateUserId("");
        baseFile.setUpdateUserName("");

        baseFileMapper.insert(baseFile);

        ImageResponseDTO imageResponseDTO = getImage(path + saveFileName);
        imageResponseDTO.setFileName(picfileName);
        imageResponseDTO.setFileId(baseFile.getFileUuid());

        return imageResponseDTO;
    }

    public ImageResponseDTO putFileToServer(String picfileName, File excelFile, String exportFileName) throws UnsupportedEncodingException {
        log.info("同步提交图片开始");

        String suffixName = picfileName.substring(picfileName.lastIndexOf("."));

        String buildName = UUID.fastUUID().toString(true);

        String saveFileName = buildName + suffixName;

        String path = ALI_OSS_PATH;

        //设置文件名
        // 创建上传Object的Metadata
        ObjectMetadata meta = new ObjectMetadata();

        //设置文件上传时的请求头信息
        meta.setContentDisposition("attachment;filename=\"" + URLEncoder.encode(exportFileName, "utf-8") + "\"");

        // 设置内容被下载时的编码格式。
        meta.setContentEncoding("utf-8");

        PutObjectResult putObjectResult = ossClient.putObject(ALI_OSS_BUCKET, path + saveFileName, excelFile, meta);

        // TODO 判断是否成功
        log.info("putObjectResult {}", JSONObject.toJSONString(putObjectResult));

        // 获取临时访问路径，由于图片刚提交不会存在缓存的问题，可以忽略一次缓存查询

        // 暂时需要审核的时候打开来
        // 这里由于大小的问题，无法解决

        long fileSize = excelFile.length();
        log.info("fileSize {}", fileSize);

        // 1mb = 1048576 byte
        // 1mb 以下，直接检测
        // 超过10m的图片处理不了，通过压缩会导致数据丢失，无法识别，有可能需要使用剪切一块一块去处理，先限制10M以内吧
        // nginx服务器限制是最大50M的图片，这样处理应该可以了

        // 所有用户都需要进行内容检测

        // 保存到数据库中
        BaseFile baseFile = new BaseFile();
        baseFile.setFileName(picfileName);
        baseFile.setFileUuid(buildName);
        baseFile.setFileSaveName(saveFileName);
        baseFile.setProject("ascm");
        baseFile.setProjectPath(path);

        baseFile.setCreateTime(new Date());
        baseFile.setCreateUserId("");
        baseFile.setCreateUserName("");

        baseFile.setUpdateTime(new Date());
        baseFile.setUpdateUserId("");
        baseFile.setUpdateUserName("");

        baseFileMapper.insert(baseFile);

        ImageResponseDTO imageResponseDTO = getImage(path + saveFileName);
        imageResponseDTO.setFileName(picfileName);
        imageResponseDTO.setFileId(baseFile.getFileUuid());

        return imageResponseDTO;
    }

    public ImageResponseDTO getTempURL(String fileId) {
        log.info("获取临时访问路径 {} ", fileId);
        // 先redis获取，如果没有再使用腾讯接口获取
        // todo 优化项 优化对应的编码，这样直接传JSON不知道对不对

        // 缓存查询不到，使用腾讯COS接口获取

        // fileId 换取一个获取路径

        LambdaQueryWrapper<BaseFile> fileIdQ = new LambdaQueryWrapper();
        fileIdQ.eq(BaseFile::getFileUuid, fileId);

        BaseFile target = baseFileMapper.selectOne(fileIdQ);

        if (ObjectUtil.isNotNull(target)) {
            ImageResponseDTO returnVO = getImage(target.getProjectPath() + target.getFileSaveName());
            returnVO.setFileName(target.getFileName());
            returnVO.setFileId(target.getFileUuid());
            return returnVO;
        } else {
            log.error("查询不到图片");
            return null;
        }

    }

    public List<ImageResponseDTO> getTempURLList(List<Long> fileIds) {
        if (fileIds.isEmpty()) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<BaseFile> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BaseFile::getId, fileIds);
        List<BaseFile> baseFiles = baseFileMapper.selectList(wrapper);
        return baseFiles.stream().map(baseFile -> {
            ImageResponseDTO returnVO = getImage(baseFile.getProjectPath() + baseFile.getFileSaveName());
            returnVO.setFileName(baseFile.getFileName());
            returnVO.setFileId(String.valueOf(baseFile.getId()));
            return returnVO;
        }).collect(Collectors.toList());
    }

    @NotNull
    private ImageResponseDTO getImage(String path) {
        Date expirationDate = new Date(System.currentTimeMillis() + 60L * 60L * 1000L);

        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = ossClient.generatePresignedUrl(ALI_OSS_BUCKET, path, expirationDate);

        // TODO 这里获取不到图片怎么办
        if (ObjectUtil.isNull(url)) {
            log.error("获取图片访问路径失败");
        }

        log.info("获取临时访问路径返回 {}", url.toString());

        ImageResponseDTO imageResponseDTO = new ImageResponseDTO();
        String urlTemp = url.toString();
        String realURL = urlTemp.replace("http://", "https://");
        log.info("替换后的url:" + realURL);
        imageResponseDTO.setUrl(realURL);
        return imageResponseDTO;
    }

    public String getOssUrl(String path) {
        if (StringUtils.isNotEmpty(path)) {
            Date expirationDate = new Date(System.currentTimeMillis() + 24L * 60L * 60L * 1000L);

            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(ALI_OSS_BUCKET, path, expirationDate);
            if (ObjectUtil.isNull(url)) {
                log.error("获取图片访问路径失败");
            }

            log.info("获取原始文件临时访问路径返回 {}", url.toString());
            String urlTemp = url.toString();
            return urlTemp.replace("http://", "https://");

        }
        return "";
    }

    public Map<String, Object> generateTempCredentials() throws ClientException, ResultException {
        String stsToken = (String) ascmRedisHelper.get("oss:sts-token");
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(stsToken)) {
            log.info("获取缓存的stsToken:{}", stsToken);
            return JSONObject.parseObject(stsToken);
        }
        DefaultProfile.addEndpoint("", "", "Sts", STS_ENDPOINT);

        IClientProfile profile = DefaultProfile.getProfile("", ALI_ACCESS_KEYID, ALI_ACCESS_KEY_SECRET);

        DefaultAcsClient client = new DefaultAcsClient(profile);
        final AssumeRoleRequest request = new AssumeRoleRequest();

        request.setSysMethod(MethodType.POST);

        request.setRoleArn(ROLE_ARN);
        request.setRoleSessionName(ROLE_SESSION_NAME);
        request.setDurationSeconds(Long.valueOf(DURATION_SECONDS));
        final AssumeRoleResponse response = client.getAcsResponse(request);
        log.info("Expiration: " + response.getCredentials().getExpiration());
        log.info("Access Key Id: " + response.getCredentials().getAccessKeyId());
        log.info("Access Key Secret: " + response.getCredentials().getAccessKeySecret());
        log.info("Security Token: " + response.getCredentials().getSecurityToken());
        log.info("RequestId: " + response.getRequestId());
        AssumeRoleResponse.Credentials credentials = response.getCredentials();
        Map<String, Object> map = BeanUtil.beanToMap(response.getCredentials());
        map.put("bucket", ALI_OSS_BUCKET);
        map.put("path", ALI_OSS_PATH);
        ascmRedisHelper.set("oss:sts-token", JSONObject.toJSONString(map), 23, TimeUnit.MINUTES);
        return map;
    }

    public String getNewOssUrl(String path) {
        if (StringUtils.isNotEmpty(path)) {
            // 创建请求。
            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(ALI_OSS_BUCKET, path);
            // 设置HttpMethod为GET。
            generatePresignedUrlRequest.setMethod(HttpMethod.GET);
            Date expirationDate = new Date(System.currentTimeMillis() + 2L * 60L * 60L * 1000L);
            // 将图片按照比例缩放30%°。
            String image = "image/resize,p_30";
            generatePresignedUrlRequest.setProcess(image);
            generatePresignedUrlRequest.setExpiration(expirationDate);

            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
            if (ObjectUtil.isNull(url)) {
                log.error("获取图片访问路径失败");
            }

            log.info("获取压缩图片临时访问路径返回 {}", url.toString());
            String urlTemp = url.toString();
            return urlTemp.replace("http://", "https://");

        }
        return "";
    }
}
