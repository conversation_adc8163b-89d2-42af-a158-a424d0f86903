package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.dto.PORequest;
import com.xiaopeng.halley.ascm.boot.entity.EgressGatewayRequest;
import com.xiaopeng.halley.ascm.boot.utils.POReqUtil;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.util.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PoRequestHelp {

    @Value("${po.requestPrefixUrl}")
    private String requestPrefixUrl;

    @Value("${po.userName}")
    private String userName;

    @Value("${po.password}")
    private String password;

    public Result invoke(EgressGatewayRequest request) {
        log.info("invoke 提交内容 [{}]", JSONObject.toJSONString(request));
        POInvokeEnum targetEnm = POInvokeEnum.getDescriptor(request.getApiCode());
        if (ObjectUtil.isNull(targetEnm)) {
            return ResultUtil.failed("apiCode有误");
        }

        TimeInterval timeInterval = new TimeInterval();
        timeInterval.start();

        String encodeString = Base64.encode(userName.trim() + ":" + password.trim());
        HttpResponse httpResponse = HttpRequest.post(requestPrefixUrl + targetEnm.getSuffixUrl())
                .header("Authorization", "Basic " + encodeString)
                .body(request.getData().toJSONString())
                .execute();
        String poResult = httpResponse.body();
        httpResponse.close();

        if (!httpResponse.isOk()) {
            log.error("PO请求出现异常 stauts: {}, poResult: {}", httpResponse.getStatus(), poResult);
            throw new RuntimeException(JSON.toJSONString(poResult));
        }
        log.info("invoke poResult [{}], 耗时: {}", poResult, timeInterval.intervalPretty());
        return ResultUtil.success(JSON.parseObject(poResult));
    }

    public <T> T invoke(PORequest request, Class<T> clazz) {
        return JSONObject.parseObject(this.invoke(request), clazz);
    }

    public String invoke(PORequest request, String path) {
        JSONObject jsonObject = JSONObject.parseObject(this.invoke(request));
        Object eval = JSONPath.eval(jsonObject, path);
        if (ObjectUtil.isNull(eval)) {
            throw new RuntimeException("表达式有误 path:" + path);
        }
        return eval.toString();
    }

    private String invoke(PORequest request) {
        POInvokeEnum poInvokeEnum = request.getPoInvokeEnum();
        String requestURL = requestPrefixUrl + poInvokeEnum.getSuffixUrl();

        JSONObject data = new JSONObject();
        data.put("HEADER", POReqUtil.getHeadParams(request.getReceiver(), request.getBusId()));
        data.put("DATA", request.getData());
        log.info("po请求 request: {}", data);

        String encodeString = Base64.encode(userName.trim() + ":" + password.trim());
        HttpResponse response = HttpRequest.post(requestURL)
                .header("Authorization", "Basic " + encodeString)
                .body(data.toJSONString())
                .execute();
        String result = response.body();
        response.close();
        log.info("po请求 result:[{}]", result);
        if (StrUtil.isBlank(result)) {
            log.error("PO请求获取不到结果! PORequest: {}", JSON.toJSONString(request));
            throw new RuntimeException("PO请求获取不到结果");
        }
        return result;
    }

}