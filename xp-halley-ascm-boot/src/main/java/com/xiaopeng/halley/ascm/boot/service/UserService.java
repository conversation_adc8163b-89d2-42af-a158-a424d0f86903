package com.xiaopeng.halley.ascm.boot.service;

import cn.hutool.core.util.ObjectUtil;
import com.xiaopeng.halley.ascm.boot.dto.user.AscmLoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @Date 2022/5/13 9:49 AM
 */
@SuppressWarnings("rawtypes")
@Service
@Slf4j
public class UserService {

    @Resource
    private AscmLoginUserHelper ascmLoginUserHelper;

    /**
     * 获取当前登录用户姓名
     *
     * @return
     */
    public String getName() {

        AscmLoginUser ascmLoginUser = ascmLoginUserHelper.getLoginUser();

        if (ObjectUtil.isNotNull(ascmLoginUser)) {
            return ascmLoginUser.getName();
        } else {
            return null;
        }
    }

}
