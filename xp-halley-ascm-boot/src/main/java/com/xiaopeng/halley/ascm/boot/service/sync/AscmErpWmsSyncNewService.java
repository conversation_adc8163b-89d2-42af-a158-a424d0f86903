package com.xiaopeng.halley.ascm.boot.service.sync;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.google.common.base.CaseFormat;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaSyncDataTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.constant.KafkaConstant;
import com.xiaopeng.halley.ascm.boot.common.enums.OrderTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.TransportTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.config.ShopCodeMapperConfig;
import com.xiaopeng.halley.ascm.boot.dto.ErpSyncShopDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.*;
import com.xiaopeng.halley.ascm.boot.dto.syn.SynReturnDTo;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant.WaybillStatusEnum;
import com.xiaopeng.halley.ascm.boot.service.*;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * WMS同步运单新接口
 *
 * <AUTHOR>
 * @Date 2022/9/28 3:09 PM
 */
@Slf4j
@Service
public class AscmErpWmsSyncNewService {

    final String FLGORT = "2021";
    @Resource
    private ApplicationContext context;
    @Resource
    private BzWaybillMapper bzWaybillMapper;
    @Resource
    private BzErpReqMaterialRelMapper bzErpReqMaterialRelMapper;
    @Resource
    private BzWaybillService waybillService;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
    @Resource
    private BzWaybillBoxRelMapper bzWaybillBoxRelMapper;

    @Resource
    private BzErpReqBoxMapper bzErpReqBoxMapper;
    @Resource
    private BaseShopMapper baseShopMapper;
    @Resource
    private AscmLockHelper ascmLockHelper;
    @Resource
    private BzWaybillInfoSyncService bzWaybillInfoSyncService;
    @Resource
    private AscmEventKafkaProducer ascmEventKafkaProducer;
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private BzErpReqMaterialRelService bzErpReqMaterialRelService;
	@Resource
	private BzErpReqBoxService bzErpReqBoxService;

    // 数据设置
    public static BzErpReqMaterialRel setData(BzErpReqMaterialRel bzErpReqMaterialRel, ErpSyncPTAndDTDto erpSyncPTAndDTDto) {

        BzErpReqMaterialRel result = new BzErpReqMaterialRel();
        result.setId(bzErpReqMaterialRel.getId());

        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZXDRQ())) {
            result.setPurchaseDate(erpSyncPTAndDTDto.getZXDRQ());
        }

        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZXDSJ())) {
            result.setPurchaseTime(erpSyncPTAndDTDto.getZXDSJ());
        }

        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZPHRQ())) {
            result.setDeliveryDate(erpSyncPTAndDTDto.getZPHRQ());
        }

        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZPHSJ())) {
            result.setDeliveryTime(erpSyncPTAndDTDto.getZPHSJ());
        }
        // 写入下单时间
        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZXDRQ()) &&
                StrUtil.isNotBlank(erpSyncPTAndDTDto.getZXDSJ()) &&
                !"00000000".equals(erpSyncPTAndDTDto.getZXDRQ()) &&
                !"000000".equals(erpSyncPTAndDTDto.getZXDSJ())) {
            try {
                Date date = com.xiaopeng.halley.ascm.boot.utils.DateUtils.joinDateAndTime(erpSyncPTAndDTDto.getZXDRQ(), erpSyncPTAndDTDto.getZXDSJ());
                result.setPurchaseDateTime(date);
            } catch (ParseException e) {
                log.error("日期时间格式化异常 inputParams: {}", JSON.toJSONString(erpSyncPTAndDTDto));
                log.error(e.getMessage(), e);
            }
        }
        // 写入配货时间
        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZPHRQ()) &&
                StrUtil.isNotBlank(erpSyncPTAndDTDto.getZPHSJ()) &&
                !"00000000".equals(erpSyncPTAndDTDto.getZPHRQ()) &&
                !"000000".equals(erpSyncPTAndDTDto.getZPHSJ())) {
            try {
                Date date = com.xiaopeng.halley.ascm.boot.utils.DateUtils.joinDateAndTime(erpSyncPTAndDTDto.getZPHRQ(), erpSyncPTAndDTDto.getZPHSJ());
                result.setDeliveryDateTime(date);
            } catch (ParseException e) {
                log.error("日期时间格式化异常 inputParams: {}", JSON.toJSONString(erpSyncPTAndDTDto));
                log.error(e.getMessage(), e);
            }
        }
        // 写入计划发运时间
        if (StrUtil.isNotBlank(erpSyncPTAndDTDto.getZYL01()) &&
                StrUtil.isNotBlank(erpSyncPTAndDTDto.getZYL02()) &&
                !"00000000".equals(erpSyncPTAndDTDto.getZYL01()) &&
                !"000000".equals(erpSyncPTAndDTDto.getZYL02())) {
            try {
                Date date = com.xiaopeng.halley.ascm.boot.utils.DateUtils.joinDateAndTime(erpSyncPTAndDTDto.getZYL01(), erpSyncPTAndDTDto.getZYL02());
                result.setPlanShippingTime(date);
            } catch (ParseException e) {
                log.error("日期时间格式化异常 inputParams: {}", JSON.toJSONString(erpSyncPTAndDTDto));
                log.error(e.getMessage(), e);
            }
        }
        return result;
    }

    /**
     * 同步处理(0030)
     */
    public SynReturnDTo wmsWaybillSyn(JSONObject params) {
        log.info("AscmErpWmsSyncNewService wmsWaybillSyn, 收到WMS运单信息同步指令，开始执行 [{}]", params.toJSONString());

        // 整理数据
        JSONArray data = params.getJSONArray("DATA");
        log.info("DATA---- [{}]", JSON.toJSONString(data));

        List<WmsWaybillDto> dataList = JSONObject.parseArray(JSONObject.toJSONString(data), WmsWaybillDto.class);
        System.out.println("dataList----->" + dataList);
        String waybillCodes = null;
        try {
            Date date = new Date();
            waybillCodes = context.getBean(AscmErpWmsSyncNewService.class).saveWaybill(dataList, date);
        } catch (Throwable t) {
            log.error(t.getMessage(), t);
            return SynReturnDTo.getError("failed", t.getMessage());
        }
        if (StrUtil.isNotBlank(waybillCodes)) {
            SynReturnDTo synReturnDTo = SynReturnDTo.getSuccess("success");
            synReturnDTo.setZMESSAGE(waybillCodes.substring(0, waybillCodes.length() - 1) + "：运单已经存在！");
            return synReturnDTo;
        } else {
            return SynReturnDTo.getSuccess("success");
        }

    }

    /**
     * WMS运单信息传输ASCM
     */
    @Transactional(rollbackFor = Exception.class)
    public String saveWaybill(List<WmsWaybillDto> wmsWaybillDtoList, Date date) throws Exception {
        log.info("AscmErpWmsSyncNewService saveWaybill WMS订单状态传ASCM开始执行 wmsWaybillDtoList [{}]", wmsWaybillDtoList);

        //记录重复的运单
        StringBuilder stringBuilder = new StringBuilder();
        for (WmsWaybillDto dto : wmsWaybillDtoList) {
            BzWaybill waybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setWaybillCode(dto.getZYDBH())));

            //增量判断
            if (null == waybill && ("30".equals(dto.getStatusValue()) || "40".equals(dto.getStatusValue()))) {
                throw new ResultException(500, "运单不存在，不能进行增量操作");
            }
            if (null != waybill && waybill.getIsDelete() == 1 && ("30".equals(dto.getStatusValue()) || "40".equals(dto.getStatusValue()))) {
                throw new ResultException(500, "运单处于取消状态，不能进行增量操作");
            }

            //物料增量操作
            if (null != waybill && waybill.getIsDelete() == 0 && "40".equals(dto.getStatusValue())) {
                //取出增量箱
                List<WmsWaybillItemDto> wmsWaybillItemDtos = dto.getItems();
                if (CollUtil.isEmpty(wmsWaybillItemDtos)) {
                    log.error("saveWaybill 运单无关联箱，waybill->{}", waybill);
                    throw new ResultException(500, "运单无关联箱");
                }
                //添加物料
                addMaterialRels(wmsWaybillItemDtos);
                continue;
            }

            //箱子增量操作
            if (null != waybill && waybill.getIsDelete() == 0 && "30".equals(dto.getStatusValue())) {
                //取出增量箱
                List<WmsWaybillItemDto> wmsWaybillItemDtos = dto.getItems();
                if (CollUtil.isEmpty(wmsWaybillItemDtos)) {
                    log.error("saveWaybill 运单无关联箱，waybill->{}", waybill);
                    throw new ResultException(500, "运单无关联箱");
                }
                //添加箱子
                addBoxes(waybill, wmsWaybillItemDtos);
                continue;
            }

            if (null != waybill && !"20".equals(dto.getStatusValue())) {
                log.info("WMS同步运单失败，重复运单->{}", dto.getZYDBH());
                stringBuilder.append(dto.getZYDBH() + "、");
                continue;
            }

            if (null == waybill) {
                waybill = new BzWaybill();
                if (FLGORT.equals(dto.getLgort())) {
                    waybill.setIsCompletePacking(1);
                }
                waybill.setWaybillCode(dto.getZYDBH());
            }

            if ("10".equals(dto.getStatusValue())) {
                // todo:状态机控制
                waybill.setStatus(WaybillStatusEnum.UNPUBLISHED.getValue());
            } else if ("20".equals(dto.getStatusValue())) {
                //执行过拆单、合单操作的运单不能取消
                if (waybill.getCombineCount() > 0 || waybill.getDemolitionCount() > 0) {
                    throw new ResultException(500, waybill.getWaybillCode() + "运单执行过拆单或者合单操作，无法取消");
                }
                //运输中、异常中、已完成不可以进行取消
                if ("运输中".equals(waybill.getStatus()) || "异常中".equals(waybill.getStatus()) || "已完成".equals(waybill.getStatus())) {
                    throw new ResultException(500, "运输中、异常中、已完成运单不可以进行取消，无法取消");
                }
                // todo:状态机控制
                waybill.setStatus(WaybillStatusEnum.CANCEL.getValue());
                //进行取消逻辑
                if (null != waybill.getId() && waybill.getId() > 0) {
                    //waybill.updateById();
                    LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.set(BzWaybill::getStatus, "已取消").set(BzWaybill::getUpdateTime, new Date()).eq(BzWaybill::getId, waybill.getId());
                    bzWaybillMapper.update(null, updateWrapper);
                    //逻辑删除运单下箱号的关联关系
                    LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(BzWaybillBoxRel::getWaybillCode, waybill.getWaybillCode()).eq(BzWaybillBoxRel::getIsDelete, 0);
                    List<BzWaybillBoxRel> waybillBoxRels = bzWaybillBoxRelMapper.selectList(queryWrapper);
                    for (BzWaybillBoxRel waybillBoxRel : waybillBoxRels) {
                        bzWaybillBoxRelMapper.update(null, new LambdaUpdateWrapper<BzWaybillBoxRel>()
                                .eq(BzWaybillBoxRel::getId, waybillBoxRel.getId())
                                .set(BzWaybillBoxRel::getIsDelete, 1)
                                .set(BzWaybillBoxRel::getUpdateTime, new Date()));
                    }
                    continue;
                } else {
                    throw new ResultException(500, "WMS同步运单失败，找不到关联运单，无法取消");
                }
            }

            // null说明没有运单信息，是新增的运单
            boolean insertResult = false;
            if (null == waybill.getId()) {
                waybill.setReceivedTime(date);
                insertResult = waybill.insert();
                // 新增重推记录信息
                BzWaybillInfoSync bzWaybillInfoSync = new BzWaybillInfoSync();
                bzWaybillInfoSync.setWaybillId(waybill.getId());
                bzWaybillInfoSync.setWaybillCode(waybill.getWaybillCode());
                bzWaybillInfoSyncService.save(bzWaybillInfoSync);
                // waybill = bzWaybillMapper.selectById(waybill.getId());////
            }

            //TODO:插入门店
            // insertOrUpdateShop(dto);

            //TODO:插入仓库
            // insertOrUpdateWarehouse(dto);

            // 获取所有箱号信息
            List<WmsWaybillItemDto> wmsWaybillItemDtos = dto.getItems();

            if (CollUtil.isEmpty(wmsWaybillItemDtos)) {
                log.error("saveWaybill 运单无关联箱，waybill->{}", waybill);
            } else {

                // 查询是否存在该箱号
                Set<String> relBoxCodes = wmsWaybillItemDtos.stream().map(WmsWaybillItemDto::getCarton).collect(Collectors.toSet());
                int totalBox = relBoxCodes.size();
                List<BzErpReqBox> relBoxList = bzErpReqBoxMapper.selectList(new QueryWrapper<BzErpReqBox>()
                        .in("box_code", relBoxCodes)
                        .eq("is_delete", 0));

                log.info("relBoxList->{}", relBoxList);

                List<String> listBox = relBoxList.stream().map(BzErpReqBox::getBoxCode).collect(Collectors.toList());

                boolean flag = true;
                //所有箱子体积之和
                double totalVolume = 0.0;
                // 箱号存在不允许操作
                if (!CollUtil.isEmpty(relBoxList)) {
                    //查看关联关系是否为空
                    LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
//                    queryWrapper.in(BzWaybillBoxRel::getBoxCode,relBoxCodes).eq(BzWaybillBoxRel::getIsDelete,0);
                    queryWrapper.in(BzWaybillBoxRel::getBoxCode, relBoxCodes);
                    List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(queryWrapper);
                    log.info("bzWaybillBoxRels->{}", bzWaybillBoxRels);
                    if (!CollUtil.isEmpty(bzWaybillBoxRels)) {
                        for (BzErpReqBox bzErpReqBox : relBoxList) {
                            //重新构建关联关系
                            BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
                            bzWaybillBoxRel.setWaybillCode(dto.getZYDBH());//设置运单编号
                            bzWaybillBoxRel.setBoxCode(bzErpReqBox.getBoxCode());//设置箱号
                            bzWaybillBoxRel.setStatus(0);
                            bzWaybillBoxRel.setIsDevanning(0);
                            bzWaybillBoxRelMapper.insert(bzWaybillBoxRel);
                        }
                        for (BzErpReqBox bzErpReqBox : relBoxList) {
                            totalVolume += bzErpReqBox.getActualVolume();
                        }
                        if (relBoxCodes.size() > listBox.size()) {
                            for (String box : listBox) {
                                relBoxCodes.remove(box);
                            }
                            log.info("relBoxCodes->{}", relBoxCodes);
                        } else {
                            flag = false;
                        }
                    } else {
                        log.info("BzWaybillBoxRel->null");
                    }

                }
                log.info("flag {}", flag);

                if (flag) {
                    // 箱号不存在进行添加入库
                    for (String boxCode : relBoxCodes) {
                        BzErpReqBox bzErpReqBox = new BzErpReqBox();
                        List<WmsWaybillItemDto> collect = wmsWaybillItemDtos.stream().filter(e -> e.getCarton().equals(boxCode)).collect(Collectors.toList());
                        WmsWaybillItemDto itemDto = collect.get(0);
                        bzErpReqBox.setBoxCode(boxCode);//设置箱号
                        //TODO:取消长宽高体积的校验
                        /*if (StrUtil.isBlank(itemDto.getZcd())) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材长不能为空");
                        }*/
                        bzErpReqBox.setBoxLong(itemDto.getZcd());//长

                        /*if (StrUtil.isBlank(itemDto.getZkd())) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材宽不能为空");
                        }*/
                        bzErpReqBox.setBoxWidth(itemDto.getZkd());//宽

                        /*if (StrUtil.isBlank(itemDto.getZgd())) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材高不能为空");
                        }*/
                        bzErpReqBox.setBoxHeight(itemDto.getZgd());//高

                        /*if (itemDto.getZbztj() < 0) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材体积不能小于0");
                        }*/
                        bzErpReqBox.setBoxVolume(itemDto.getZbztj());//包材体积(立方米)

                        /*if (itemDto.getZdxzl() <= 0) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,单箱重量不能小于等于0");
                        }*/
                        bzErpReqBox.setBoxWeight(itemDto.getZdxzl());//单箱重量（kg）

                        /*if (itemDto.getZsjtj() <= 0) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,实际体积不能小于等于0");
                        }*/
                        bzErpReqBox.setActualVolume(itemDto.getZsjtj());//实际体积(立方米)

                        //包装类型
                        if ("P001".equals(itemDto.getPmat())) { //单独包装
                            bzErpReqBox.setPackageType("0");//包装类型(0-单独包装,1-混合包装,2-虚拟包装)
                        } else if ("XNZX001".equals(itemDto.getPmat())) { //虚拟包装
                            bzErpReqBox.setPackageType("2");
                        } else { // 混合包装
                            bzErpReqBox.setPackageType("1");
                        }

                        if (StrUtil.isBlank(itemDto.getPmat())) {
                            log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                            throw new ResultException(500, "箱号：" + boxCode + "入库失败,包装材料编号不能为空");
                        }
                        bzErpReqBox.setPackageCode(itemDto.getPmat());//包装材料编号

                        //箱子体积之和
                        totalVolume = totalVolume + (null == itemDto.getZsjtj() ? 0 : itemDto.getZsjtj());
                        bzErpReqBox.setPackageTime(DateUtils.parseDate(itemDto.getZbzrq() + itemDto.getZbzsj(), "yyyyMMddHHmmss"));//包装时间
                        bzErpReqBox.setWaybillCode(dto.getZYDBH());//运单编号
                        bzErpReqBox.insert();//箱子进行入库操作

                        //TODO:建立箱子与运单的关联关系
                        BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
                        bzWaybillBoxRel.setWaybillCode(dto.getZYDBH());//设置运单编号
                        bzWaybillBoxRel.setBoxCode(boxCode);//设置箱号
                        bzWaybillBoxRel.setStatus(0);
                        bzWaybillBoxRel.setIsDevanning(0);
                        bzWaybillBoxRel.insert();
                    }

                    materialRelsMethod(wmsWaybillItemDtos, relBoxCodes);
                }
                // 补充运单信息
                //TODO:争议点
                String lprio = wmsWaybillItemDtos.get(0).getLprio();
                if ("00".equals(lprio)) {
                    lprio = "常规";
                } else if ("01".equals(lprio)) {
                    lprio = "常规";
                } else if ("02".equals(lprio) || "06".equals(lprio)) {
                    lprio = "紧急";
                } else if ("03".equals(lprio)) {
                    lprio = "火急";
                }
                waybill.setOrderType(lprio);//订单类型
                waybill.setLgort(dto.getLgort());//仓库编码
                waybill.setShopCode(dto.getKunnr());//门店编码
                waybill.setWaybillCreateTime(new Date());//创建时间
                waybill.setTotalBox(totalBox);//总箱数量
                if (StrUtil.isBlank(dto.getWerks())) {
                    log.error("saveWaybill 发货工厂编码为空");
                    throw new ResultException(500, "发货工厂编码为空");
                }
                waybill.setWerks(dto.getWerks());//发货工厂编码
                waybill.setFactoryDesc(dto.getName1() == null ? "" : dto.getName1());//工厂描述
                //TODO:总体积与预估总体积
                //预估总体积对我们没用
                waybill.setEstimatedVolume(0.0);
                //总体积 = 所有箱子体积之和
                waybill.setTotalVolume(totalVolume);
                waybill.setLgobe(dto.getLgobe());//发货仓库描述
                waybill.setWarehouseContactPerson(dto.getZfhr());//发货仓库联系人
                waybill.setWarehouseContactNum(dto.getZfhnum());//发货仓库联系人电话
                waybill.setWarehouseProvince(dto.getZfhsf());//发货仓库省份
                waybill.setWarehouseCity(dto.getZfhcty());//发货仓库城市
                waybill.setWarehouseAddress(dto.getZfhadd());//发货仓库地址
                waybill.setShopName(dto.getNameOrg1());//客户描述
                waybill.setShopContactPerson(dto.getZjsr());//门店联系人
                waybill.setShopContactNum(dto.getZjcrnum());//门店联系人电话
                waybill.setShopProvince(dto.getZsdsf());//送达省份
                waybill.setShopCity(dto.getZsdcty());//送达城市
                waybill.setShopAddress(dto.getZscadd());//送达地址

                ShopCodeMapperConfig shopCodeMapperConfig = SpringUtil.getBean(ShopCodeMapperConfig.class);
                if (shopCodeMapperConfig.isEnable() && shopCodeMapperConfig.getMapperInfos() != null) {
                    Optional<ShopCodeMapperConfig.MapperInfo> optional = shopCodeMapperConfig.getMapperInfos().stream()
                            .filter(e -> e.getOriginShopCode().equals(dto.getKunnr()) && e.getShopCity().equals(dto.getZsdcty()))
                            .findFirst();
                    if (optional.isPresent()) {
                        ShopCodeMapperConfig.MapperInfo mapperInfo = optional.get();
                        waybill.setShopCode(mapperInfo.getMapperShopCode());
                        log.info("映射shopCode originShopCode: {}, mapperShopCode: {}", mapperInfo.getOriginShopCode(), mapperInfo.getMapperShopCode());
                    }
                }
            }

            Integer updateCount = bzWaybillMapper.updateById(waybill);

            // 自动匹配规则
            {
                log.info("saveWaybill 开始匹配规则");

                Map<String, Object> waybillMap = new LinkedHashMap<String, Object>();
                Field[] fields = waybill.getClass().getDeclaredFields();
                for (Field field : fields) {
                    field.setAccessible(true);
                    waybillMap.put(CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE).convert(field.getName()), field.get(waybill));
                }

                List<Map> waybillMapList = new ArrayList<>();
                waybillMapList.add(waybillMap);
                Map result = waybillService.matchWaybillRule(waybillMapList);

                log.info("saveWaybill 规则匹配完成，匹配结果->{}", result);
            }

            // 移到后面，避免事务为提交导致无法生成条目
            if (insertResult) {
                ascmEventKafkaProducer.pushEvent(KafkaConstant.EVENT_DATA_SYNC, MapUtil.builder()
                        .put("data", JSONObject.toJSONString(waybill.getWaybillCode()))
                        .put("key", AscmKafkaSyncDataTypeEnum.CREATE_WAYBILL_RETRY_SYNC.getKey())
                        .put("ts", System.currentTimeMillis()).map());
            }
            log.info("saveWaybill 完成运单创建 updateState [{}]", updateCount);
            log.info("saveWaybill 完成运单创建 waybill [{}]", JSONObject.toJSONString(waybill));
        }
        return stringBuilder.toString();
    }

    /**
     * @param wmsWaybillItemDtos
     */
    private void addMaterialRels(List<WmsWaybillItemDto> wmsWaybillItemDtos) throws ResultException {
        Set<String> relBoxCodes = wmsWaybillItemDtos.stream().map(WmsWaybillItemDto::getCarton).collect(Collectors.toSet());
        //查询是否存在该箱子
        for (String relBoxCode : relBoxCodes) {
            Double changeVolume = 0.0; //加物料后变化的体积
            BzWaybillBoxRel bzWaybillBoxRel = bzWaybillBoxRelMapper.selectOne(new LambdaQueryWrapper<BzWaybillBoxRel>()
                    .eq(BzWaybillBoxRel::getBoxCode, relBoxCode)
                    .eq(BzWaybillBoxRel::getIsDevanning, 0).eq(BzWaybillBoxRel::getIsDelete, 0));
            if (null == bzWaybillBoxRel) {
                throw new ResultException(500, "箱号：" + relBoxCode + "不存在关联关系！");
            }
            //TODO 先注释了，生产上有问题，看看后面需不需要加逻辑
            /*if(bzWaybillBoxRel.getStatus() == 1){
                throw new ResultException(500,"箱号："+relBoxCode+"已扫描，不能进行添加物料！");
            }*/
            LambdaQueryWrapper<BzErpReqBox> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BzErpReqBox::getBoxCode, relBoxCode).eq(BzErpReqBox::getIsDelete, 0);
            BzErpReqBox bzErpReqBox = bzErpReqBoxMapper.selectOne(queryWrapper);
            if (null == bzErpReqBox) {
                throw new ResultException(500, "不存在箱号：" + bzErpReqBox.getBoxCode());
            } else {
                //获取该条记录
                List<WmsWaybillItemDto> collect = wmsWaybillItemDtos.stream().filter(e -> e.getCarton().equals(bzErpReqBox.getBoxCode())).collect(Collectors.toList());
                WmsWaybillItemDto wmsWaybillItemDto = collect.get(0);
                changeVolume = wmsWaybillItemDto.getZsjtj() - bzErpReqBox.getActualVolume();
                //修改箱号信息
                BzErpReqBox erpReqBox = new BzErpReqBox();
                erpReqBox.setId(bzErpReqBox.getId());
                erpReqBox.setBoxLong(wmsWaybillItemDto.getCd());//箱子长
                erpReqBox.setBoxHeight(wmsWaybillItemDto.getGd());//箱子高
                erpReqBox.setBoxWeight(wmsWaybillItemDto.getZdxzl());
                erpReqBox.setBoxWidth(wmsWaybillItemDto.getKd());
                erpReqBox.setActualVolume(wmsWaybillItemDto.getZsjtj());
                erpReqBox.setBoxVolume(wmsWaybillItemDto.getZbztj());
                erpReqBox.setUpdateTime(new Date());
                bzErpReqBoxMapper.updateById(erpReqBox);
            }
            //修改运单的体积(存在拆箱的可能---查询出最新的运单)
            BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, bzWaybillBoxRel.getWaybillCode()).eq(BzWaybill::getIsDelete, 0));
            bzWaybillMapper.update(null, new LambdaUpdateWrapper<BzWaybill>()
                    .eq(BzWaybill::getId, bzWaybill.getId())
                    .set(BzWaybill::getTotalVolume, bzWaybill.getTotalVolume() + changeVolume).set(BzWaybill::getUpdateTime, new Date()));
        }
        //全部存在
        materialRelsMethod(wmsWaybillItemDtos, relBoxCodes);
    }

    private void materialRelsMethod(List<WmsWaybillItemDto> wmsWaybillItemDtos, Set<String> relBoxCodes) throws ResultException {
        List<WmsWaybillItemDto> collect = wmsWaybillItemDtos.stream().filter(e -> relBoxCodes.contains(e.getCarton())).collect(Collectors.toList());

        //新建物料数据
        for (WmsWaybillItemDto itemDto : collect) {
            //TODO:交货单+箱号+物料号+交货单行号唯一,需要吗？
//            LambdaQueryWrapper<BzErpReqMaterialRel> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(BzErpReqMaterialRel::getDeliveryOrderCode,itemDto.getZzvbeln())//交货单号
//                    .eq(BzErpReqMaterialRel::getBoxCode,itemDto.getCarton())//箱号
//                    .eq(BzErpReqMaterialRel::getMatnr,itemDto.getProductno())//物料号
//                    .eq(BzErpReqMaterialRel::getLineNumber,itemDto.getPosnr()==null?"":itemDto.getPosnr())//行号
//                    .last("limit 1");
//            List<BzErpReqMaterialRel> bzErpReqMaterialRels = bzErpReqMaterialRelMapper.selectList(queryWrapper);
//            if(bzErpReqMaterialRels.size()>0){
//                throw new ResultException(500,"交货单+箱号+物料号+交货单行号已存在");
//            }
            BzErpReqMaterialRel bzErpReqMaterialRel = new BzErpReqMaterialRel();
            if (StrUtil.isBlank(itemDto.getZzvbeln())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,交货单号不能为空");
            }
            bzErpReqMaterialRel.setDeliveryOrderCode(itemDto.getZzvbeln());//交货单号
            bzErpReqMaterialRel.setOriginDeliveryOrderCode(itemDto.getZYL02());//原始交货单号

            if (StrUtil.isBlank(itemDto.getPosnr())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,交货单行号不能为空");
            }
            bzErpReqMaterialRel.setLineNumber(itemDto.getPosnr());//交货单行号

            if (StrUtil.isBlank(itemDto.getCarton())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,箱号不能为空");
            }
            bzErpReqMaterialRel.setBoxCode(itemDto.getCarton());//箱号

            if (StrUtil.isBlank(itemDto.getVtext())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,ERP交货单类型描述不能为空");
            }
            bzErpReqMaterialRel.setDeliveryOrderType(itemDto.getVtext());//交货单类型描述

            if (StrUtil.isBlank(itemDto.getProductno())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,物料号不能为空");
            }
            bzErpReqMaterialRel.setMatnr(itemDto.getProductno());//物料号

            if (StrUtil.isBlank(itemDto.getMaktx())) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,物料描述不能为空");
            }
            bzErpReqMaterialRel.setMaktx(itemDto.getMaktx());//物料描述

            if (itemDto.getQty() <= 0) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,数量不能小于等于0");
            }
            bzErpReqMaterialRel.setQuantity(itemDto.getQty());//数量

            //TODO:单位是否全为PC
            bzErpReqMaterialRel.setUnit("PC");//单位

            //TODO:暂时不用管
            bzErpReqMaterialRel.setEstimatedVolume(0.0);//预估总体积

            if (itemDto.getLfimg() <= 0) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,订单数量不能小于等于0");
            }
            bzErpReqMaterialRel.setOrderCount(itemDto.getLfimg());//订单数量

            if (itemDto.getQty() <= 0) {
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500, "物料：" + itemDto.getProductno() + "入库失败,包装数量不能小于等于0");
            }
            bzErpReqMaterialRel.setPackageCount(itemDto.getQty());//包装数量

            /*if(StrUtil.isBlank(itemDto.getCd())){
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500,"物料："+itemDto.getProductno()+"入库失败,物料长不能为空");
            }*/
            bzErpReqMaterialRel.setMaterialLong(itemDto.getCd());//物料长(毫米)

            /*if(StrUtil.isBlank(itemDto.getKd())){
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500,"物料："+itemDto.getProductno()+"入库失败,物料宽不能为空");
            }*/
            bzErpReqMaterialRel.setMaterialWidth(itemDto.getKd());//物料宽(毫米)

            /*if(StrUtil.isBlank(itemDto.getGd())){
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500,"物料："+itemDto.getProductno()+"入库失败,物料高不能为空");
            }*/
            bzErpReqMaterialRel.setMaterialHeight(itemDto.getGd());//物料高(毫米)

            /*if(itemDto.getZwltj()<=0){
                log.error("saveWaybill 物料信息错误，boxCode->{}", itemDto.getProductno());
                throw new ResultException(500,"物料："+itemDto.getProductno()+"入库失败,物料体积不能小于等于0");
            }*/
            bzErpReqMaterialRel.setMaterialVolume(itemDto.getZwltj());//物料体积(立方米)

            bzErpReqMaterialRel.setOriginDeliveryOrderCode(itemDto.getZYL02());
            bzErpReqMaterialRel.insert();

            //ERP需求入库
//                    bzErpReq.setErpOrderType(itemDto.getVtext());//ERP订单类型
//                    bzErpReq.setOrderType(itemDto.getLprio());//订单类型
//                    bzErpReq.setDeliveryOrderCode(itemDto.getZzvbeln());//交货单号
//                    String zzvbeln = itemDto.getZzvbeln();
//                    bzErpReq.setRowNum(wmsWaybillItemDtos.stream().filter(e->e.getZzvbeln().equals(zzvbeln)).collect(Collectors.toList()).size());//行数
//                    String wave = itemDto.getWave();
//                    if(StrUtil.isBlank(wave)){
//                        wave = "0";
//                    }
//                    bzErpReq.setWaveNum(Integer.parseInt(wave));//波次
//                    bzErpReq.setErpCreateTime(new Date());//创建时间
//                    bzErpReq.insert();
        }
    }

    /**
     * 箱子增量操作
     *
     * @param waybill
     * @param wmsWaybillItemDtos
     */
    private void addBoxes(BzWaybill waybill, List<WmsWaybillItemDto> wmsWaybillItemDtos) throws ResultException, ParseException {
        Integer totalBox = waybill.getTotalBox();//当前运单箱数
        Double totalVolume = waybill.getTotalVolume();//当前运单的总体积
        // 查询是否存在该箱号
        Set<String> relBoxCodes = wmsWaybillItemDtos.stream().map(WmsWaybillItemDto::getCarton).collect(Collectors.toSet());
        int boxSize = relBoxCodes.size();
        totalBox += boxSize;
        //查询表中是否存在该箱号
        List<BzErpReqBox> relBoxList = bzErpReqBoxMapper.selectList(new QueryWrapper<BzErpReqBox>()
                .in("box_code", relBoxCodes)
                .eq("is_delete", 0));
        //箱号已存在不允许进行添加操作
        if (relBoxList.size() > 0) {
            log.error("存在重复箱号：->{}", relBoxList);
            throw new ResultException(500, "存在重复箱号");
        }
        // 箱号不存在进行添加入库
        for (String boxCode : relBoxCodes) {
            BzErpReqBox bzErpReqBox = new BzErpReqBox();
            List<WmsWaybillItemDto> collect = wmsWaybillItemDtos.stream().filter(e -> e.getCarton().equals(boxCode)).collect(Collectors.toList());
            WmsWaybillItemDto itemDto = collect.get(0);
            bzErpReqBox.setBoxCode(boxCode);//设置箱号
            if (StrUtil.isBlank(itemDto.getZcd())) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材长不能为空");
            }
            bzErpReqBox.setBoxLong(itemDto.getZcd());//长

            if (StrUtil.isBlank(itemDto.getZkd())) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材宽不能为空");
            }
            bzErpReqBox.setBoxWidth(itemDto.getZkd());//宽

            if (StrUtil.isBlank(itemDto.getZgd())) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材高不能为空");
            }
            bzErpReqBox.setBoxHeight(itemDto.getZgd());//高

            if (itemDto.getZbztj() < 0) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,包材体积不能小于0");
            }
            bzErpReqBox.setBoxVolume(itemDto.getZbztj());//包材体积(立方米)

            if (itemDto.getZdxzl() <= 0) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,单箱重量不能小于等于0");
            }
            bzErpReqBox.setBoxWeight(itemDto.getZdxzl());//单箱重量（kg）

            if (itemDto.getZsjtj() <= 0) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,实际体积不能小于等于0");
            }
            bzErpReqBox.setActualVolume(itemDto.getZsjtj());//实际体积(立方米)

            //包装类型
            if ("P001".equals(itemDto.getPmat())) { //单独包装
                bzErpReqBox.setPackageType("0");//包装类型(0-单独包装,1-混合包装,2-虚拟包装)
            } else if ("XNZX001".equals(itemDto.getPmat())) { //虚拟包装
                bzErpReqBox.setPackageType("2");
            } else { // 混合包装
                bzErpReqBox.setPackageType("1");
            }

            if (StrUtil.isBlank(itemDto.getPmat())) {
                log.error("saveWaybill 箱子信息错误，boxCode->{}", boxCode);
                throw new ResultException(500, "箱号：" + boxCode + "入库失败,包装材料编号不能为空");
            }
            bzErpReqBox.setPackageCode(itemDto.getPmat());//包装材料编号

            //箱子体积之和
            totalVolume = totalVolume + itemDto.getZsjtj();
            bzErpReqBox.setPackageTime(DateUtils.parseDate(itemDto.getZbzrq() + itemDto.getZbzsj(), "yyyyMMddHHmmss"));//包装时间
            bzErpReqBox.setWaybillCode(waybill.getWaybillCode());//运单编号
            bzErpReqBox.insert();//箱子进行入库操作

            //TODO:建立箱子与运单的关联关系
            BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
            bzWaybillBoxRel.setWaybillCode(waybill.getWaybillCode());//设置运单编号
            bzWaybillBoxRel.setBoxCode(boxCode);//设置箱号
            bzWaybillBoxRel.setStatus(0);
            bzWaybillBoxRel.setIsDevanning(0);
            bzWaybillBoxRel.insert();
        }
        //进行物料表的添加
        materialRelsMethod(wmsWaybillItemDtos, relBoxCodes);

        //进行运单的更新
        //获取redis锁
        try {
            Boolean tryLock = ascmLockHelper.tryLock("LOCK" + waybill.getWaybillCode(), 5, 5, TimeUnit.MINUTES);
            if (tryLock) {
                LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(BzWaybill::getTotalVolume, totalVolume).set(BzWaybill::getTotalBox, totalBox).set(BzWaybill::getUpdateTime, new Date()).eq(BzWaybill::getId, waybill.getId());
                bzWaybillMapper.update(null, updateWrapper);
                bzWaybillService.calVolumeWeight(Collections.singletonList(waybill.getWaybillCode()), true);
            }
        } catch (Exception e) {
            log.error("tryLock异常", e);
            throw new ResultException(500, "tryLock异常");
        } finally {
            ascmLockHelper.unlock("LOCK" + waybill.getWaybillCode());
        }
    }

    /**
     * 插入或更新门店
     */
    private void insertOrUpdateShop(WmsWaybillDto wmsWaybillDto) throws ResultException {
        //门店编码
        String shopCode = wmsWaybillDto.getKunnr();

        if (StrUtil.isNotBlank(shopCode)) {
            BaseShop targetShop = baseShopMapper.selectOne(new QueryWrapper<>(new BaseShop().setShopCode(shopCode).setIsDelete(0)).last("limit 1"));
            log.info("AscmErpWmsSyncNewService insertOrUpdateShop,插入或更新门店，匹配到门店，wmsWaybillDto->{}，targetShop->{}", wmsWaybillDto, targetShop);
            if (null == targetShop) {
                targetShop = new BaseShop();
                targetShop.setShopCode(shopCode);

                if (StrUtil.isBlank(wmsWaybillDto.getNameOrg1())) {      //客户描述
                    throw new ResultException(500, "客户描述为空");
                }
                targetShop.setShopName(wmsWaybillDto.getNameOrg1());

                if (StrUtil.isBlank(wmsWaybillDto.getZsdcty())) {        //送达城市
                    throw new ResultException(500, "送达城市为空");
                }
                targetShop.setShopCity(wmsWaybillDto.getZsdcty());

                if (StrUtil.isBlank(wmsWaybillDto.getZsdsf())) {         //送达省份
                    throw new ResultException(500, "送达省份为空");
                }
                targetShop.setShopProvince(wmsWaybillDto.getZsdsf());

                if (StrUtil.isBlank(wmsWaybillDto.getZscadd())) {        //送达地址
                    throw new ResultException(500, "送达地址为空");
                }
                targetShop.setShopAddress(wmsWaybillDto.getZscadd());

                if (StrUtil.isBlank(wmsWaybillDto.getZjsr())) {          //接车人
                    throw new ResultException(500, "接车人为空");
                }
                targetShop.setContactPerson(wmsWaybillDto.getZjsr());

                if (StrUtil.isBlank(wmsWaybillDto.getZjcrnum())) {       //接车人联系方式
                    throw new ResultException(500, "接车人联系方式为空");
                }
                targetShop.setContactNum(wmsWaybillDto.getZjcrnum());

                targetShop.insert();
                log.info("AscmErpWmsSyncNewService insertOrUpdateShop,插入新门店，wmsWaybillDto->{}，targetShop->{}", wmsWaybillDto, targetShop);
            } else {
                if (StrUtil.isBlank(wmsWaybillDto.getNameOrg1())) {
                    throw new ResultException(500, "客户描述为空");
                }
                targetShop.setShopName(wmsWaybillDto.getNameOrg1());

                if (StrUtil.isBlank(wmsWaybillDto.getZsdcty())) {
                    throw new ResultException(500, "送达城市为空");
                }
                targetShop.setShopCity(wmsWaybillDto.getZsdcty());

                if (StrUtil.isBlank(wmsWaybillDto.getZsdsf())) {
                    throw new ResultException(500, "送达省份为空");
                }
                targetShop.setShopProvince(wmsWaybillDto.getZsdsf());

                if (StrUtil.isBlank(wmsWaybillDto.getZscadd())) {
                    throw new ResultException(500, "送达地址为空");
                }
                targetShop.setShopAddress(wmsWaybillDto.getZscadd());

                if (StrUtil.isBlank(wmsWaybillDto.getZjsr())) {
                    throw new ResultException(500, "接车人为空");
                }
                targetShop.setContactPerson(wmsWaybillDto.getZjsr());

                if (StrUtil.isBlank(wmsWaybillDto.getZjcrnum())) {
                    throw new ResultException(500, "接车人联系方式为空");
                }
                targetShop.setContactNum(wmsWaybillDto.getZjcrnum());

                targetShop.updateById();
                log.info("AscmErpWmsSyncNewService insertOrUpdateShop,更新门店，wmsWaybillDto->{}，targetShop->{}", wmsWaybillDto, targetShop);
            }
        } else {
            throw new ResultException(500, "门店编码为空");
        }
    }

    /**
     * 插入或更新仓库
     */
    private void insertOrUpdateWarehouse(WmsWaybillDto wmsWaybillDto) throws ResultException {
        //仓库编码
        String lgort = wmsWaybillDto.getLgort();

        if (StrUtil.isNotBlank(lgort)) {
            BaseWarehouse targetWarehouse = baseWarehouseMapper.selectOne(new QueryWrapper<>(new BaseWarehouse().setLgort(lgort).setIsDelete(0)).last("limit 1"));
            log.info("AscmErpWmsSyncNewService insertOrUpdateWarehouse,插入或更新仓库，匹配到仓库，wmsWaybillDto->{}，targetWarehouse->{}", wmsWaybillDto, targetWarehouse);
            if (null == targetWarehouse) {
                targetWarehouse = new BaseWarehouse();
                targetWarehouse.setLgort(wmsWaybillDto.getLgort());

                if (StrUtil.isBlank(wmsWaybillDto.getLgobe())) {         //发货仓库描述
                    throw new ResultException(500, "发货仓库描述为空");
                }
                targetWarehouse.setLgobe(wmsWaybillDto.getLgobe());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhr())) {          //发货仓库联系人
                    throw new ResultException(500, "发货仓库联系人为空");
                }
                targetWarehouse.setContactPerson(wmsWaybillDto.getZfhr());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhnum())) {        //发货仓库联系人电话
                    throw new ResultException(500, "发货仓库联系人电话为空");
                }
                targetWarehouse.setContactNum(wmsWaybillDto.getZfhnum());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhsf())) {         //发货仓库省份
                    throw new ResultException(500, "发货仓库省份为空");
                }
                targetWarehouse.setWarehouseProvince(wmsWaybillDto.getZfhsf());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhcty())) {        //发货仓库城市
                    throw new ResultException(500, "发货仓库城市为空");
                }
                targetWarehouse.setWarehouseCity(wmsWaybillDto.getZfhcty());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhadd())) {        //发货仓库地址
                    throw new ResultException(500, "发货仓库地址为空");
                }
                targetWarehouse.setWarehouseAddress(wmsWaybillDto.getZfhadd());

                targetWarehouse.insert();
                log.info("AscmErpWmsSyncNewService insertOrUpdateWarehouse,插入新仓库，wmsWaybillDto->{}，targetWarehouse->{}", wmsWaybillDto, targetWarehouse);
            } else {
                if (StrUtil.isBlank(wmsWaybillDto.getLgobe())) {
                    throw new ResultException(500, "发货仓库描述为空");
                }
                targetWarehouse.setLgobe(wmsWaybillDto.getLgobe());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhr())) {
                    throw new ResultException(500, "发货仓库联系人为空");
                }
                targetWarehouse.setContactPerson(wmsWaybillDto.getZfhr());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhnum())) {
                    throw new ResultException(500, "发货仓库联系人电话为空");
                }
                targetWarehouse.setContactNum(wmsWaybillDto.getZfhnum());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhsf())) {
                    throw new ResultException(500, "发货仓库省份为空");
                }
                targetWarehouse.setWarehouseProvince(wmsWaybillDto.getZfhsf());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhcty())) {
                    throw new ResultException(500, "发货仓库城市为空");
                }
                targetWarehouse.setWarehouseCity(wmsWaybillDto.getZfhcty());

                if (StrUtil.isBlank(wmsWaybillDto.getZfhadd())) {
                    throw new ResultException(500, "发货仓库地址为空");
                }
                targetWarehouse.setWarehouseAddress(wmsWaybillDto.getZfhadd());

                targetWarehouse.updateById();
                log.info("AscmErpWmsSyncNewService insertOrUpdateWarehouse,更新新仓库，wmsWaybillDto->{}，targetWarehouse->{}", wmsWaybillDto, targetWarehouse);
            }
        } else {
            throw new ResultException(500, "仓库编码为空");
        }
    }

    /**
     * WMS包装完成传ASCM
     *
     * @param map
     * @return
     */
    public SynReturnDTo packCompleted(Map map) {
        log.info("BzErpReqController packCompleted 开始执行 传入数据 [{}]", JSONObject.toJSONString(map));

        String bzWaybillCode = map.get("ZYDBH").toString();
        String statusCode = map.get("ZPACSTATUS").toString();

        log.info("packCompleted Map [{}]", map);

        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>()
                .eq(BzWaybill::getWaybillCode, bzWaybillCode)
                .eq(BzWaybill::getIsDelete, 0));

        if (ObjectUtil.isEmpty(bzWaybill)) {
            new SynReturnDTo();
            return SynReturnDTo.getError("error", "不存在该运单！");
        }
        if (bzWaybill.getIsCompletePacking() == 1) {
            //return new SynReturnDTo().getError("error","不可重复确认完成！");
            return SynReturnDTo.getSuccess("success");
        }
        if (!"10".equals(statusCode)) {    //取消状态
            new SynReturnDTo();
            return SynReturnDTo.getError("error", "ZPACSTATUS不为10！");
        }
        int update = bzWaybillMapper.update(null, new LambdaUpdateWrapper<BzWaybill>()
                .eq(BzWaybill::getId, bzWaybill.getId())
                .set(BzWaybill::getIsCompletePacking, 1)
        );
        if (update != 1) {
            new SynReturnDTo();
            return SynReturnDTo.getError("error", "确认包装完成失败！");
        }
        return SynReturnDTo.getSuccess("success");
    }

    /**
     * 门店数据同步
     *
     * @param params
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SynReturnDTo shopDataSync(JSONObject params) {
        log.info("AscmErpWmsSyncService shopDataSync, 收到WMS门店同步指令，开始执行 [{}]", params.toJSONString());
        // 整理数据
        JSONArray data = params.getJSONArray("DATA");
        log.info("DATA---- [{}]", JSON.toJSONString(data));
        log.info("DATA---- [{}]", JSON.toJSONString(data));
        List<ErpSyncShopDto> erpShopList = JSONObject.parseArray(JSONObject.toJSONString(data), ErpSyncShopDto.class);

        List<ErpSyncShopDto> eachList = Collections.synchronizedList(erpShopList);

        // 采用并行操作，提高效率

        //记录本次更新数量
        AtomicInteger updateNum = new AtomicInteger(0);

        //记录本次新增数量
        AtomicInteger insertNum = new AtomicInteger(0);

        eachList.parallelStream().forEach(erpSyncShopDto -> {

            //需要更新的实体
            BaseShop baseShop = new BaseShop();
            baseShop.syncData(erpSyncShopDto);
            //数据库中的实体
            BaseShop shop = baseShopMapper.selectOne(new LambdaQueryWrapper<BaseShop>().eq(BaseShop::getShopCode, baseShop.getShopCode()));
            baseShop.setUpdateTime(new Date());
            baseShop.setUpdateUserName("ERP");
            baseShop.setUpdateUserId("ERP");
            if (BeanUtil.isEmpty(shop)) {
                //新增操作
                baseShop.setCreateUserId("ERP");
                baseShop.setCreateUserName("ERP");
                baseShop.setCreateTime(new Date());
                int insert = baseShopMapper.insert(baseShop);
                insertNum.addAndGet(insert);
            } else {
                //非动态更新的门店不做更新处理
                if (shop.getDynamicUpdate() == 1) {
                    //动态更新的门店做更新处理
                    int update = baseShopMapper.update(baseShop, new LambdaUpdateWrapper<BaseShop>().eq(BaseShop::getShopCode, baseShop.getShopCode()));
                    updateNum.addAndGet(update);
                }
            }

        });

        //做本次同步的日志记录
        log.info("AscmErpWmsSyncService shopDataSync SyncResult update{},insert{}", updateNum, insertNum);

        return SynReturnDTo.getSuccess("success");
    }

    /**
     * ERP同步交货单下单及配货时间数据
     *
     * @param params
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SynReturnDTo pTAndDTSync(JSONObject params) {
        log.info("AscmErpWmsSyncNewService pTAndDTSync, 收到WMS同步指令，开始执行 [{}]", params.toJSONString());

        // 整理数据
        JSONArray data = params.getJSONArray("DATA");
        log.info("DATA---- [{}]", JSON.toJSONString(data));
        List<ErpSyncPTAndDTDto> erpSyncPTAndDTDtos = JSONObject.parseArray(JSONObject.toJSONString(data), ErpSyncPTAndDTDto.class);
        //对不存在的运单做记录
        List<ErpSyncPTAndDTDto> failData = new ArrayList<>();

        erpSyncPTAndDTDtos.forEach(erpSyncPTAndDTDto -> {
            //根据交货单号查询出对应的实体
            List<BzErpReqMaterialRel> bzErpReqMaterialRel = bzErpReqMaterialRelMapper.selectList(new LambdaQueryWrapper<BzErpReqMaterialRel>().eq(BzErpReqMaterialRel::getDeliveryOrderCode, erpSyncPTAndDTDto.getVBELN()));
            for (BzErpReqMaterialRel erpReqMaterialRel : bzErpReqMaterialRel) {
                //给实体对应的属性赋值
                BzErpReqMaterialRel result = setData(erpReqMaterialRel, erpSyncPTAndDTDto);
                //更新
                bzErpReqMaterialRelMapper.updateById(result);
            }
        });

        //不存在的交货单做异常日志记录
        if (CollUtil.isNotEmpty(failData)) {
            log.error("更新是遇到非法数据 {}", JSON.toJSONString(failData));
        }

        return SynReturnDTo.getSuccess("success");
    }

    /**
     * JD同步运单
     *
     * @param syncWaybillForJDDto JD同步过来的运单数据
     * @return
     */
    public SynReturnDTo syncWaybillforJD(SyncWaybillForJDDto syncWaybillForJDDto) {

        //查询出运单
        BzWaybill waybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setWaybillCode(syncWaybillForJDDto.getWaybillCode())));
        //如果找不到对应的运单就新增
        if (null == waybill) {
            context.getBean(AscmErpWmsSyncNewService.class).insertWaybill(syncWaybillForJDDto);
        } else {
            //有运单就更新
            //context.getBean(AscmErpWmsSyncNewService.class).updateWaybill(syncWaybillForJDDto,waybill.getId());
            return SynReturnDTo.getError("error", "重复的运单");
        }

        return SynReturnDTo.getSuccess("success!");
    }

    /**
     * 更新运单
     *
     * @param syncWaybillForJDDto JD同步过来的运单数据
     * @param id                  需要更新的运单的id
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWaybill(SyncWaybillForJDDto syncWaybillForJDDto, Long id) {
        BzWaybill bzWaybill = setParameter(syncWaybillForJDDto);
        bzWaybill.setId(id);
        int i = bzWaybillMapper.updateById(bzWaybill);
    }

    /**
     * 新增运单
     *
     * @param syncWaybillForJDDto JD同步过来的运单数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertWaybill(SyncWaybillForJDDto syncWaybillForJDDto) {
        //运单新增
        BzWaybill bzWaybill = setParameter(syncWaybillForJDDto);
        bzWaybill.setStatus(WaybillStatusEnum.WAIT_TRANSIT.getValue());
        //设置门店
        bzWaybill = setShop(syncWaybillForJDDto, bzWaybill);
        //插入仓库
        bzWaybill = setWarehouse(syncWaybillForJDDto, bzWaybill);
        //落库
        bzWaybill.insert();
        //int insert = bzWaybillMapper.insert(bzWaybill);
        //箱子新增
        List<BoxListDto> boxList = syncWaybillForJDDto.getBoxList();
        if (CollUtil.isEmpty(boxList)) {
            log.error("insertWaybill 运单无关联箱，waybill --->{}", JSON.toJSONString(syncWaybillForJDDto));
        } else {
            //本次数据同步的箱子编码集合
            Set<String> boxCodes = boxList.stream().map(BoxListDto::getBoxCode).collect(Collectors.toSet());
            int totalBox = boxCodes.size();
            List<BzErpReqBox> bzErpReqBoxes = bzErpReqBoxMapper.selectList(new QueryWrapper<BzErpReqBox>()
                    .in("box_code", boxCodes)
                    .eq("is_delete", 0));
            //根据本次同步过来的箱子的编码集合，获取到数据库中的箱子编码集合
            List<String> listBox = bzErpReqBoxes.stream().map(BzErpReqBox::getBoxCode).collect(Collectors.toList());

            boolean flag = true;
            //所有箱子的体积总和
            double totalVolume = 0.0;
            // 箱号存在不允许操作
            if (CollUtil.isNotEmpty(bzErpReqBoxes)) {
                //查看关联关系是否为空
                LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(BzWaybillBoxRel::getBoxCode, boxCodes);
                //查询出本次的数据库中已有箱子和运单之间的关系
                List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(queryWrapper);
                if (CollUtil.isNotEmpty(bzWaybillBoxRels)) {
                    for (BzErpReqBox bzErpReqBox : bzErpReqBoxes) {
                        //重新构建关联关系
                        BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
                        bzWaybillBoxRel.setWaybillCode(syncWaybillForJDDto.getWaybillCode());//设置运单编号
                        bzWaybillBoxRel.setBoxCode(bzErpReqBox.getBoxCode());//设置箱号
                        bzWaybillBoxRel.setStatus(1);//京东默认已扫描
                        bzWaybillBoxRel.setIsDevanning(0);
                        //查询出重复的箱子和运单的关联关系，做逻辑删除处理
                        List<BzWaybillBoxRel> bzWaybillBoxRelList = bzWaybillBoxRelMapper.selectList(new LambdaQueryWrapper<BzWaybillBoxRel>().eq(BzWaybillBoxRel::getBoxCode, bzErpReqBox.getBoxCode()).eq(BzWaybillBoxRel::getIsDelete, 0));
                        for (BzWaybillBoxRel waybillBoxRel : bzWaybillBoxRelList) {
                            waybillBoxRel.setIsDelete(1);
                            waybillBoxRel.updateById();
                        }
                        //新增一个箱子和运单的关联关联
                        bzWaybillBoxRelMapper.insert(bzWaybillBoxRel);

                    }
                    for (BzErpReqBox bzErpReqBox : bzErpReqBoxes) {
                        totalVolume += bzErpReqBox.getActualVolume();
                    }
                    bzWaybill.setTotalVolume(totalVolume);
                    bzWaybillMapper.updateById(bzWaybill);
                    //去除已经录入的箱子
                    if (boxCodes.size() > listBox.size()) {
                        for (String box : listBox) {
                            boxCodes.remove(box);
                        }
                        log.info("relBoxCodes->{}", boxCodes);
                    } else {
                        flag = false;
                    }
                } else {
                    log.info("BzWaybillBoxRel->null");
                }
            }

            if (flag) {
                // 箱号不存在进行添加入库
                for (String boxCode : boxCodes) {
                    BzErpReqBox bzErpReqBox = new BzErpReqBox();
                    List<BoxListDto> collect = boxList.stream().filter(e -> e.getBoxCode().equals(boxCode)).collect(Collectors.toList());
                    BoxListDto itemDto = collect.get(0);
                    bzErpReqBox.setBoxCode(boxCode);//设置箱号

                    //包装类型
                    if ("P001".equals(itemDto.getPackageType())) { //单独包装
                        bzErpReqBox.setPackageType("0");//包装类型(0-单独包装,1-混合包装,2-虚拟包装)
                    } else if ("XNZX001".equals(itemDto.getPackageType())) { //虚拟包装
                        bzErpReqBox.setPackageType("2");
                    } else { // 混合包装
                        bzErpReqBox.setPackageType("1");
                    }
                    bzErpReqBox.setBoxVolume(itemDto.getBoxvolume());// 箱子的包装材料体积
                    bzErpReqBox.setActualVolume(itemDto.getBoxvolume());// 箱子的实际体积
                    bzErpReqBox.setBoxWeight(itemDto.getBoxweight());// 箱子的重量
                    bzErpReqBox.setWaybillCode(syncWaybillForJDDto.getWaybillCode());//运单编号
                    bzErpReqBox.insert();//箱子进行入库操作

                    //建立箱子与运单的关联关系
                    BzWaybillBoxRel bzWaybillBoxRel = new BzWaybillBoxRel();
                    bzWaybillBoxRel.setWaybillCode(syncWaybillForJDDto.getWaybillCode());//设置运单编号
                    bzWaybillBoxRel.setBoxCode(boxCode);//设置箱号
                    bzWaybillBoxRel.setStatus(1);//京东默认已扫描
                    bzWaybillBoxRel.setIsDevanning(0);
                    bzWaybillBoxRel.insert();

                    //体积累加计算总体积
                    totalVolume += itemDto.getBoxvolume();
                }
                //设置总体积
                for (BzErpReqBox bzErpReqBox : bzErpReqBoxes) {
                    log.info("boxvolume:{}", bzErpReqBox.getActualVolume());
                    totalVolume += bzErpReqBox.getActualVolume();
                }
                log.info("boxtotalvolume:{}", totalVolume);
                bzWaybill.setTotalVolume(totalVolume);
                bzWaybillMapper.updateById(bzWaybill);
                //关联物料关系
                associatedMaterials(boxList, boxCodes);

            }

            BzWaybillInfoSync bzWaybillInfoSync = new BzWaybillInfoSync();
            bzWaybillInfoSync.setWaybillId(bzWaybill.getId());
            bzWaybillInfoSync.setWaybillCode(bzWaybill.getWaybillCode());
            bzWaybillInfoSyncService.save(bzWaybillInfoSync);

        }

    }

    /**
     * 新建物料关联关系
     *
     * @param boxList  箱子的集合
     * @param boxCodes 箱子的编号
     */
    private void associatedMaterials(List<BoxListDto> boxList, Set<String> boxCodes) {
        List<BoxListDto> collect = boxList.stream().filter(e -> boxCodes.contains(e.getBoxCode())).collect(Collectors.toList());

        for (BoxListDto boxListDto : collect) {
            List<MaterialListDto> materialList = boxListDto.getMaterialList();
            for (MaterialListDto materialListDto : materialList) {
                BzErpReqMaterialRel bzErpReqMaterialRel = new BzErpReqMaterialRel();
                bzErpReqMaterialRel.setBoxCode(boxListDto.getBoxCode());
                bzErpReqMaterialRel.setDeliveryOrderCode(materialListDto.getDeliveryOrderCode());
                bzErpReqMaterialRel.setMatnr(materialListDto.getMaterialCode());
                bzErpReqMaterialRel.setMaktx(materialListDto.getMaterialName());
                bzErpReqMaterialRel.setPackageCount(new Integer(materialListDto.getMaterialCount()));
                bzErpReqMaterialRel.setMaterialVolume(materialListDto.getMaterialvolume());
                bzErpReqMaterialRel.insert();
            }
        }
    }

    /**
     * 设置门店
     *
     * @param bzWaybill 运单信息
     * @return 构造好的门店数据
     */
    private BzWaybill setShop(SyncWaybillForJDDto syncWaybillForJDDto, BzWaybill bzWaybill) {
        BaseShop baseShop = baseShopMapper.selectOne(new LambdaQueryWrapper<BaseShop>().eq(BaseShop::getShopCode, syncWaybillForJDDto.getShopCode()));
        if (null == baseShop) {
            log.error("京东同步运单失败！无对应门店信息！门店编码为：[{}]", syncWaybillForJDDto.getShopCode());
            throw new RuntimeException("找不到对应门店信息！");
        }
        bzWaybill.setShopProvince(baseShop.getShopProvince());
        bzWaybill.setShopCity(baseShop.getShopCity());
        bzWaybill.setShopContactPerson(baseShop.getContactPerson());
        bzWaybill.setShopContactNum(baseShop.getContactNum());
        return bzWaybill;
    }

    /**
     * 设置仓库
     *
     * @param bzWaybill 运单信息
     * @return 构造好的仓库数据
     */
    private BzWaybill setWarehouse(SyncWaybillForJDDto syncWaybillForJDDto, BzWaybill bzWaybill) {
        BaseWarehouse baseWarehouse = baseWarehouseMapper.selectOne(new LambdaQueryWrapper<BaseWarehouse>().eq(BaseWarehouse::getLgort, syncWaybillForJDDto.getLgort()));
        if (null == baseWarehouse) {
            log.error("京东同步运单失败！无对应仓库信息！仓库编码为：[{}]", syncWaybillForJDDto.getLgort());
            throw new RuntimeException("找不到对应仓库信息！");
        }
        bzWaybill.setLgort(baseWarehouse.getLgort());
        bzWaybill.setLgobe(baseWarehouse.getLgobe());
        bzWaybill.setWarehouseProvince(baseWarehouse.getWarehouseProvince());
        bzWaybill.setWarehouseCity(baseWarehouse.getWarehouseCity());
        bzWaybill.setWarehouseAddress(baseWarehouse.getWarehouseAddress());
        bzWaybill.setWarehouseContactPerson(baseWarehouse.getContactPerson());
        bzWaybill.setWarehouseContactNum(baseWarehouse.getContactNum());
        return bzWaybill;
    }

    /**
     * 设置运单实体的参数
     *
     * @param syncWaybillForJDDto JD同步过来的运单数据
     * @return 运单实体
     */
    private BzWaybill setParameter(SyncWaybillForJDDto syncWaybillForJDDto) {
        BzWaybill bzWaybill = new BzWaybill();
        bzWaybill.setWaybillCode(syncWaybillForJDDto.getWaybillCode());
        bzWaybill.setShopCode(syncWaybillForJDDto.getShopCode());
        bzWaybill.setShopName(syncWaybillForJDDto.getShopName());
        bzWaybill.setShopAddress(syncWaybillForJDDto.getShopAddress());
        bzWaybill.setOrderType(OrderTypeEnum.getTypeValue(new Integer(syncWaybillForJDDto.getOrderType())));
        bzWaybill.setTransportType(TransportTypeEnum.getTypeValue(new Integer(syncWaybillForJDDto.getTransportType())));
        bzWaybill.setLogisticsCompany(syncWaybillForJDDto.getLogisticsCompany());
        bzWaybill.setReceivedTime(syncWaybillForJDDto.getReceiveTime());
        bzWaybill.setDepartureTime(syncWaybillForJDDto.getDispatchTime());
        bzWaybill.setTotalBox(syncWaybillForJDDto.getTotalBox());
        bzWaybill.setIsCompletePacking(1);//京东接口默认包装完成
        bzWaybill.setCirculationTime(syncWaybillForJDDto.getSendTime());
        return bzWaybill;
    }

    @Transactional(rollbackFor = Exception.class)
    public void receiveBoxMaterial(List<BoxMaterialRequestDto> materialRequestDtos) {
        log.info("materialRequestDtos: {}", materialRequestDtos);
        if (CollUtil.isEmpty(materialRequestDtos)) {
            return;
        }

        List<String> boxCodes = materialRequestDtos.stream().map(BoxMaterialRequestDto::getBoxCode).collect(Collectors.toList());
        Map<String, BzErpReqBox> boxMap = new LambdaQueryChainWrapper<>(bzErpReqBoxMapper)
                .in(BzErpReqBox::getBoxCode, boxCodes)
                .eq(BzErpReqBox::getIsDelete, 0)
                .list().stream().collect(Collectors.toMap(BzErpReqBox::getBoxCode, Function.identity()));

        Map<String, List<BzErpReqMaterialRel>> materialRelMap = new LambdaQueryChainWrapper<>(bzErpReqMaterialRelMapper)
                .eq(BzErpReqMaterialRel::getIsDelete, 0)
                .in(BzErpReqMaterialRel::getBoxCode, boxCodes)
                .list().stream().collect(Collectors.groupingBy(e -> e.getBoxCode() + "@" + e.getMatnr()));

        Set<String> calWaybillCodes = new HashSet<>();
        for (BoxMaterialRequestDto request : materialRequestDtos) {
            // 除以1000的处理
            if (request.getBoxWeight() != null) {
                request.setBoxWeight(NumberUtil.div(request.getBoxWeight(), Double.valueOf(1000)));
            }
            if (request.getMaterialWeight() != null) {
                request.setMaterialWeight(NumberUtil.div(request.getMaterialWeight(), Double.valueOf(1000)));
            }

            String boxCode = request.getBoxCode();
            // 箱号不存在
            BzErpReqBox bzErpReqBox = boxMap.get(boxCode);
            if (bzErpReqBox == null) {
                throw new RuntimeException("箱号不存在 boxCode: {}" + boxCode);
            }
            // 物料不存在或者物料关联的箱号不一致
            List<BzErpReqMaterialRel> bzErpReqMaterialRels = materialRelMap.get(request.getBoxCode() + "@" + request.getMaterialCode());
            if (CollUtil.isEmpty(bzErpReqMaterialRels)) {
                log.info("物料不存在或者物料关联的箱号不一致 boxCode: {}, materialCode: {}", boxCode, request.getMaterialCode());
                continue;
            }

            for (BzErpReqMaterialRel bzErpReqMaterialRel : bzErpReqMaterialRels) {
                bzErpReqMaterialRel.setMaterialWidth(request.getMaterialWidth())
                        .setMaterialLong(request.getMaterialLength())
                        .setMaterialWidth(request.getMaterialWidth())
                        .setMaterialHeight(request.getMaterialHeight())
                        .setMaterialVolume(request.getMaterialVolume())
                        .setMaterialWeight(request.getMaterialWeight())
                        .setUpdateTime(new Date());
            }

            bzErpReqBox.setBoxLong(request.getBoxLength())
                    .setBoxWidth(request.getBoxWidth())
                    .setBoxHeight(request.getBoxHeight())
                    .setBoxWeight(request.getBoxWeight())
                    .setActualVolume(request.getBoxVolume())
                    .setUpdateTime(new Date());
            calWaybillCodes.add(bzErpReqBox.getWaybillCode());
        }

        try {
            bzErpReqMaterialRelService.updateBatchById(materialRelMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
            bzErpReqBoxService.updateBatchById(boxMap.values());
            bzWaybillService.calVolumeWeight(calWaybillCodes, true);
        } catch (Exception e) {
            throw new RuntimeException(StrUtil.format("更新数据库异常, boxCode: {}, materialCode: {}"));
        }
    }

    /**
     //     * WMS运单信息传输ASCM
     //     */
//    @Transactional(rollbackFor = Exception.class)
//    public boolean saveWaybill(List<WmsWaybillDto> wmsWaybillDtoList) throws Exception {
//       log.info("AscmErpWmsSyncNewService saveWaybill, WMS订单状态传ASCM开始执行, wmsWaybillDtoList->{}", wmsWaybillDtoList);
//
//        for (WmsWaybillDto dto : wmsWaybillDtoList) {
//            BzWaybill waybill = bzWaybillMapper.selectOne(new QueryWrapper<>(new BzWaybill().setWaybillCode(dto.getZYDBH())));
//            if(null != waybill && !dto.getStatusValue().equals("20")){
//                throw new ResultException(500,"WMS同步运单失败，重复运单");
//            }
//
//            if (null == waybill) {
//                waybill = new BzWaybill();
//                waybill.setWaybillCode(dto.getZYDBH());
//            }
//
//            if (dto.getStatusValue().equals("10")) {
//                // todo:状态机控制
//                waybill.setStatus(WaybillStatusEnum.UNPUBLISHED.getValue());
//            } else if (dto.getStatusValue().equals("20")) {
//                // todo:状态机控制
//                waybill.setStatus(WaybillStatusEnum.CANCEL.getValue());
//                if (null != waybill.getId() && waybill.getId() > 0) {
//                    waybill.updateById();
//                    continue;
//                } else {
//                    throw new ResultException(500,"WMS同步运单失败，找不到关联运单，无法取消");
//                }
//            }
//
//            if (null == waybill.getId()) {
//                waybill.insert();
//                waybill = bzWaybillMapper.selectById(waybill.getId());
//            }
//
//            // 获取所有关联需求
//            List<WmsWaybillItemDto> wmsWaybillItemDtos = dto.getItems();
//            if (CollUtil.isEmpty(wmsWaybillItemDtos)) {
//                log.error("saveWaybill 运单无关联需求，waybill->{}", waybill);
//            } else {
//                // 查询所有关联需求
//                Set<String> deliveryOrderCodes = wmsWaybillItemDtos.stream().map(WmsWaybillItemDto::getZzvbeln).collect(Collectors.toSet());
//                List<BzErpReq> relErpReqList = bzErpReqMapper.selectList(new QueryWrapper<BzErpReq>()
//                        .in("delivery_order_code", deliveryOrderCodes)
//                        .eq("is_delete", 0));
//                if (CollUtil.isEmpty(relErpReqList)) {
//                    throw new ResultException(500,"WMS同步运单失败，找不到关联需求");
//                }
//
//                // 查询所有关联箱号
//                Set<String> relBoxCodes = wmsWaybillItemDtos.stream().map(WmsWaybillItemDto::getCarton).collect(Collectors.toSet());
//                List<BzErpReqBox> relBoxList = bzErpReqBoxMapper.selectList(new QueryWrapper<BzErpReqBox>()
//                        .in("box_code", relBoxCodes)
//                        .eq("is_delete", 0));
////                if (CollUtil.isEmpty(relBoxList)) {
////                    throw new ResultException(500,"WMS同步运单失败，找不到关联箱号");
////                }
//
//                // 查询所有箱号与需要关联关系
//                List<BzErpReqBoxRel> relErpReqBoxRelList = bzErpReqBoxRelMapper.selectList(new QueryWrapper<BzErpReqBoxRel>()
//                        .in("box_code", relBoxCodes)
//                        .eq("is_delete", 0));
//
//                // 查询所有关联物料
//                List<BzErpReqMaterialRel> relReqMaterialRelList = bzErpReqMaterialRelMapper.selectList(new QueryWrapper<BzErpReqMaterialRel>()
//                        .in("delivery_order_code", deliveryOrderCodes)
//                        .eq("is_delete", 0));
//                if (CollUtil.isEmpty(relReqMaterialRelList)) {
//                    throw new ResultException(500,"WMS同步运单失败，找不到关联物料");
//                }
//
//                // 补充运单信息
//                {
//                    // 选择第一个运单进行补充
//                    BzErpReq req = relErpReqList.get(0);
//                    waybill.setOrderType(req.getOrderType());//订单类型
//                    waybill.setLgort(req.getLgort());//仓库编码
//                    waybill.setShopCode(req.getShopCode());//门店编码
//                    waybill.setWaybillCreateTime(new Date());//创建时间
//                }
//
//                // 对每个需求进行处理
//                for (BzErpReq req : relErpReqList) {
//                    //处理预计体积-需求预计体积 = 所有需求预计体积总和
//                    waybill.setEstimatedVolume(waybill.getEstimatedVolume() + req.getEstimatedVolume());
//
//                    BzWaybillReqRel bzWaybillReqRel = new BzWaybillReqRel();
//                    bzWaybillReqRel.setErpReqId(req.getId());
//                    bzWaybillReqRel.setWaybillId(waybill.getId());
//                    bzWaybillReqRel.insert();
//                }
//
//                // 对每个item进行处理
//                for (WmsWaybillItemDto item : wmsWaybillItemDtos) {
//
//                    // 运单补充
//                    {
//                        // 总体积 - 所有想实体体积
//                        waybill.setTotalVolume(waybill.getTotalVolume() + item.getZsjtj());
//
//                        // 总箱数
//                        waybill.setTotalBox(waybill.getTotalBox() + 1);
//                    }
//
//                    // 物料处理
//                    {
//                        List<BzErpReqMaterialRel> bzErpReqMaterialRelList = relReqMaterialRelList.stream().filter(i -> {
//                            if (i.getMatnr().equals(item.getProductno())) {
//                                return true;
//                            } else {
//                                return false;
//                            }
//                        }).collect(Collectors.toList());
//                        if (CollUtil.isEmpty(bzErpReqMaterialRelList)) {
//                            throw new ResultException(500,"WMS同步运单失败，找不到交货单关联物料，物料号：" + item.getProductno());
//                        } else {
//                            BzErpReqMaterialRel material = bzErpReqMaterialRelList.get(0);
//                            material.setMaterialLong(item.getCd());
//                            material.setMaterialWidth(item.getKd());
//                            material.setMaterialHeight(item.getGd());
//                            material.setMaterialVolume(item.getZwltj());
//                            material.setOrderCount(item.getLfimg());
//                            material.setPackageCount(item.getQty());
//                            material.setBoxCode(item.getCarton());
//                            material.updateById();
//                        }
//                    }
//
//                    // 箱号处理
//                    {
//                        List<BzErpReqBox> bzErpReqBoxList = relBoxList.stream().filter(i -> {
//                            if (i.getBoxCode().equals(item.getCarton())) {
//                                return true;
//                            } else {
//                                return false;
//                            }
//                        }).collect(Collectors.toList());
//
//                        BzErpReqBox box;
//                        if (CollUtil.isEmpty(bzErpReqBoxList)) { //没有箱号，创建新箱号
//                            if (StrUtil.isEmpty(item.getCarton())) {
//                                throw new ResultException(500,"WMS同步运单失败，箱号为空，item：" + item);
//                            }
//                            box = new BzErpReqBox();
//                        } else { //有箱号进行更新
//                            box = bzErpReqBoxList.get(0);
//                        }
//
//                        box.setBoxCode(item.getCarton());
//                        box.setBoxLong(item.getZcd());
//                        box.setBoxWidth(item.getZkd());
//                        box.setBoxHeight(item.getZgd());
//                        box.setBoxWeight(item.getZdxzl());
//                        box.setBoxVolume(item.getZbztj());
//                        box.setActualVolume(item.getZsjtj());
//                        box.setPackageCode(item.getPmat());
//                        //包装类型
//                        if (StrUtil.isNotBlank(item.getPmat())) {
//                            if (item.getPmat().equals("P001")) { //单独包装
//                                box.setPackageType("0");
//                            } else if (item.getPmat().equals("XNZX001")) { //虚拟包装
//                                box.setPackageType("2");
//                            } else { // 混合包装
//                                box.setPackageType("1");
//                            }
//                        }
//                        box.setPackageTime(DateUtils.parseDate(item.getZbzrq() + item.getZbzsj(), "yyyyMMddHHmmss"));
//                        box.setWaybillCode(waybill.getWaybillCode());
//
//                        if (null != box.getId() && box.getId() > 0) {
//                            box.updateById();
//                        } else {
//                            box.insert();
//                        }
//
//                        //箱号关联处理
//                        {
//                            //添加箱号与需求关联
//                            List<BzErpReq> bzErpReqList = relErpReqList.stream().filter(i -> {
//                                if (i.getDeliveryOrderCode().equals(item.getZzvbeln())) {
//                                    return true;
//                                } else {
//                                    return false;
//                                }
//                            }).collect(Collectors.toList());
//                            if (CollUtil.isEmpty(bzErpReqList)) {
//                                throw new ResultException(500,"WMS同步运单失败，找不到箱号关联需求，交货单号：" + item.getZzvbeln());
//                            }
//                            //查看是否有需求与箱号关联关系
//                            List<BzErpReqBoxRel> bzErpReqBoxRelList = relErpReqBoxRelList.stream().filter(i -> {
//                                if (i.getAscmCode().equals(bzErpReqList.get(0).getAscmCode()) &&
//                                        i.getBoxCode().equals(item.getCarton())) {
//                                    return true;
//                                } else {
//                                    return false;
//                                }
//                            }).collect(Collectors.toList());
//                            if (CollUtil.isEmpty(bzErpReqBoxRelList)) { //没有关联关系，添加新关联关系
//                                BzErpReqBoxRel bzErpReqBoxRel = new BzErpReqBoxRel();
//                                bzErpReqBoxRel.setAscmCode(bzErpReqList.get(0).getAscmCode());
//                                bzErpReqBoxRel.setBoxCode(box.getBoxCode());
//                                bzErpReqBoxRel.insert();
//                            }
//                        }
//                    }
//
//
////                        List<BzErpReqBox> bzErpReqBoxList = relBoxList.stream().filter(i -> {
////                            if (i.getBoxCode().equals(item.getCarton())) {
////                                return true;
////                            } else {
////                                return false;
////                            }
////                        }).collect(Collectors.toList());
////
////                        if (CollUtil.isEmpty(bzErpReqBoxList)) { // 箱号不存在，添加新箱号
////                            if (StrUtil.isEmpty(item.getCarton())) {
////                                throw new ResultException(500,"WMS同步运单失败，箱号为空，item：" + item);
////                            } else {
////                                // 添加箱号相关
////                                BzErpReqBox box = new BzErpReqBox();
////                                box.setBoxCode(item.getCarton());
////                                box.setBoxLong(item.getZcd());
////                                box.setBoxWidth(item.getZkd());
////                                box.setBoxHeight(item.getZgd());
////                                box.setBoxVolume(item.getZbztj());
////                                box.setActualVolume(item.getZsjtj());
////                                box.setPackageCode(item.getPmat());
////                                //包装类型
////                                if (StrUtil.isNotBlank(item.getPmat())) {
////                                    if (item.getPmat().equals("P001")) { //单独包装
////                                        box.setPackageType("0");
////                                    } else if (item.getPmat().equals("XNZX001")) { //虚拟包装
////                                        box.setPackageType("2");
////                                    } else { // 混合包装
////                                        box.setPackageType("1");
////                                    }
////                                }
////                                box.setPackageTime(DateUtils.parseDate(item.getZbzrq() + item.getZbzsj(), "yyyyMMddHHmmss"));
////                                box.setWaybillCode(waybill.getWaybillCode());
////                                box.insert();
////
////                                //添加箱号与需求关联
////                                List<BzErpReq> bzErpReqList = relErpReqList.stream().filter(i -> {
////                                    if (i.getDeliveryOrderCode().equals(item.getZzvbeln())) {
////                                        return true;
////                                    } else {
////                                        return false;
////                                    }
////                                }).collect(Collectors.toList());
////                                if (CollUtil.isEmpty(bzErpReqList)) {
////                                    throw new ResultException(500,"WMS同步运单失败，找不到箱号关联需求，交货单号：" + item.getZzvbeln());
////                                }
////                                BzErpReqBoxRel bzErpReqBoxRel = new BzErpReqBoxRel();
////                                bzErpReqBoxRel.setAscmCode(bzErpReqList.get(0).getAscmCode());
////                                bzErpReqBoxRel.setBoxCode(box.getBoxCode());
////                                bzErpReqBoxRel.insert();
////                            }
////                        }
////                    }
//                }
//            }
//            waybill.updateById();
//           log.info("saveWaybill 完成运单创建，waybill->{}", waybill);
//
//            // 自动匹配规则
//            {
//               log.info("saveWaybill 开始匹配规则");
//
//                Map<String,Object> waybillMap = new LinkedHashMap<String, Object>();
//                Field[] fields = waybill.getClass().getDeclaredFields();
//                for(Field field : fields){
//                    field.setAccessible(true);
//                    waybillMap.put(CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE).convert(field.getName()) , field.get(waybill));
//                }
//
//                List<Map> waybillMapList = new ArrayList<>();
//                waybillMapList.add(waybillMap);
//                Map result = waybillService.matchWaybillRule(waybillMapList);
//
//               log.info("saveWaybill 规则匹配完成，匹配结果->{}", result);
//            }
//        }
//
//
//        return true;
//    }
}
