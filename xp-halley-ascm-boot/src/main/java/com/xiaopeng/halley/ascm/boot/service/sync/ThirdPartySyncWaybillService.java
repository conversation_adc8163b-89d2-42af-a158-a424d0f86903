package com.xiaopeng.halley.ascm.boot.service.sync;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaopeng.halley.ascm.boot.common.constant.AscmKafkaSyncDataTypeEnum;
import com.xiaopeng.halley.ascm.boot.common.constant.KafkaConstant;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.dto.erp.*;
import com.xiaopeng.halley.ascm.boot.dto.syn.SynReturnDTo;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.page.bzWaybillPage.constant.WaybillStatusEnum;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Author：huqizhi
 * @Date：2023/7/4 15:07
 */
@Slf4j
@Service
public class ThirdPartySyncWaybillService {
    @Resource
    private BzWaybillMapper bzWaybillMapper;
    @Resource
    private BzErpReqBoxMapper bzErpReqBoxMapper;
    @Resource
    private BzErpReqMaterialRelMapper bzErpReqMaterialRelMapper;
    @Resource
    private AscmEventKafkaProducer ascmEventKafkaProducer;
    @Resource
    private BaseMaterialsMapper baseMaterialsMapper;
    @Resource
    private BaseSuppliersMapper baseSuppliersMapper;
    @Resource
    private BzSupplierMaterialMapper bzSupplierMaterialMapper;
    @Resource
    private BzSupplierPackageInfoMapper bzSupplierPackageInfoMapper;
    @Resource
    private BzDeliveryOrderMaterialMapper bzDeliveryOrderMaterialMapper;
    @Resource
    private BzDeliveryOrdersMapper bzDeliveryOrdersMapper;
	@Resource
	private DragonService dragonService;

    /**
     * ASCM0084（收货情况）的接口
     *
     * @param syncWaybillResultDto 同步的数据实体
     * @return 响应本次同步的结果
     */
    public SynReturnDTo syncWaybillResult(SyncWaybillResultDto syncWaybillResultDto) {
        //查询出对应的运单
        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, syncWaybillResultDto.getWaybillCode()).eq(BzWaybill::getIsDelete, 0));
        //判断运单是否存在
        if (null == bzWaybill) {
            log.error("ThirdPartySyncWaybillService syncWaybillResult, 同步的运单信息不存在 [{}]", syncWaybillResultDto);
            return SynReturnDTo.getError("error", "同步的运单不存在！");
        }
        // 发送kafka异步处理
        ascmEventKafkaProducer.pushEvent(KafkaConstant.EVENT_DATA_SYNC, MapUtil.builder()
                .put("data", JSONObject.toJSONString(syncWaybillResultDto))
                .put("key", AscmKafkaSyncDataTypeEnum.ASCM0084_DATA_SYNC.getKey())
                .put("ts", System.currentTimeMillis())
                .map());
        //给参数设置值
        //bzWaybill = setParameter(syncWaybillResultDto, bzWaybill);
        return SynReturnDTo.getSuccess("success!");
    }

    /**
     * kafka异步处理ascm0084接口的业务逻辑
     *
     * @param syncWaybillResultDtoStr
     * @param time
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWaybillData(String syncWaybillResultDtoStr, String time) {
        // 处理一下时间
        long updateTime = Long.parseLong(time);
        // json转对象
        SyncWaybillResultDto syncWaybillResultDto = JSON.parseObject(syncWaybillResultDtoStr, SyncWaybillResultDto.class);
        //更新运单联系人信息
        LambdaUpdateWrapper<BzWaybill> luw = new LambdaUpdateWrapper<>();
        luw.set(BzWaybill::getUpdateTime, new Date(updateTime));
        if ("已完成".equals(syncWaybillResultDto.getStatus())) {
            luw.set(BzWaybill::getStatus, syncWaybillResultDto.getStatus());
        }
        luw.eq(BzWaybill::getWaybillCode, syncWaybillResultDto.getWaybillCode());
        luw.eq(BzWaybill::getIsDelete, 0);
        int updateWaybillInfoResult = bzWaybillMapper.update(null, luw);
        if (updateWaybillInfoResult <= 0) {
            log.error("kafka异步处理ascm0084接口运单发生异常 WaybillCode = {} 不存在！", syncWaybillResultDto.getWaybillCode());
        }
        //更新箱子的收货信息
        List<BoxListDto> boxList = syncWaybillResultDto.getBoxList();
        //拿出所有的箱子的箱号
        //List<String> boxCodeList = boxList.stream().map(BoxListDto::getBoxCode).collect(Collectors.toList());
        //拿箱号去批量查询对应的箱子
        //List<BzErpReqBox> bzErpReqBoxes = bzErpReqBoxMapper.selectList(new QueryWrapper<BzErpReqBox>().in("box_code", boxCodeList));
        //批量查询出对应的交货单集合
        /*List<BzErpReqMaterialRel> deliveryOrderCode = bzErpReqMaterialRelMapper.selectList(new QueryWrapper<BzErpReqMaterialRel>().in("delivery_order_code", syncWaybillResultDto.getDeliveryOrderCodeList()));
        //给对应的箱子设置是否收货的参数
        for (BzErpReqBox bzErpReqBox : bzErpReqBoxes) {
            List<BoxListDto> collect = boxList.stream().filter(e -> e.getBoxCode().equals(bzErpReqBox.getBoxCode())).collect(Collectors.toList());
            BoxListDto boxListDto = collect.get(0);
            bzErpReqBox.setReceiveState(boxListDto.getGetState());
            //取出箱子里面的物料
            List<MaterialListDto> materialList = boxListDto.getMaterialList();
            for (MaterialListDto materialListDto : materialList) {
                List<BzErpReqMaterialRel> materialRelList = deliveryOrderCode.stream().filter(e -> e.getDeliveryOrderCode().equals(materialListDto.getDeliveryOrderCode())).collect(Collectors.toList());
                BzErpReqMaterialRel bzErpReqMaterialRel = materialRelList.get(0);
                bzErpReqMaterialRel.setReceiveCount(materialListDto.getActualGetMaterialCount());
            }
        }*/
        for (BoxListDto boxListDto : boxList) {
            BzErpReqBox bzErpReqBox = bzErpReqBoxMapper.selectOne(new LambdaQueryWrapper<BzErpReqBox>().eq(BzErpReqBox::getBoxCode, boxListDto.getBoxCode()));
            bzErpReqBox.setReceiveState(boxListDto.getGetState());
            bzErpReqBox.setUpdateTime(new Date());
            //更新物料的收货信息
            List<MaterialListDto> materialList = boxListDto.getMaterialList();
            //TODO:完全相同的物料会导致覆盖前面的物料
            for (MaterialListDto materialListDto : materialList) {
                //获取物料实际的收货数量(聚合的结果)，然后记录
                Integer actualGetMaterialCount = materialListDto.getActualGetMaterialCount();
                QueryWrapper<BzErpReqMaterialRel> bzErpReqMaterialRelQueryWrapper = new QueryWrapper<>();
                bzErpReqMaterialRelQueryWrapper.eq("delivery_order_code", materialListDto.getDeliveryOrderCode())
                        .eq("box_code", boxListDto.getBoxCode())
                        .eq("matnr", materialListDto.getMaterialCode());
                //.eq("quantity", materialListDto.getMaterialCount());
                List<BzErpReqMaterialRel> materialRelList = bzErpReqMaterialRelMapper.selectList(bzErpReqMaterialRelQueryWrapper);
                if (materialRelList.size() > 1) {
                    for (BzErpReqMaterialRel bzErpReqMaterialRel : materialRelList) {
                        //获取物料的实际发货数量
                        Integer quantity = bzErpReqMaterialRel.getQuantity();
                        //如果实际收货数量大于实际发货数量，就设置实际发货数量
                        if (actualGetMaterialCount > quantity) {
                            bzErpReqMaterialRel.setReceiveCount(quantity);
                            actualGetMaterialCount = actualGetMaterialCount - quantity;
                        } else {
                            //如果不大于，就设置剩余的值
                            bzErpReqMaterialRel.setReceiveCount(actualGetMaterialCount);
                            actualGetMaterialCount = actualGetMaterialCount - quantity;
                            //减完后小于0，就直接设置成0
                            if (actualGetMaterialCount < 0) {
                                actualGetMaterialCount = 0;
                            }
                        }
                        bzErpReqMaterialRel.setUpdateTime(new Date());
                        bzErpReqMaterialRelMapper.updateById(bzErpReqMaterialRel);
                    }
                } else if (materialRelList.size() == 1) {
                    for (BzErpReqMaterialRel bzErpReqMaterialRel : materialRelList) {
                        bzErpReqMaterialRel.setReceiveCount(materialListDto.getActualGetMaterialCount());
                        bzErpReqMaterialRel.setUpdateTime(new Date());
                        bzErpReqMaterialRelMapper.updateById(bzErpReqMaterialRel);
                    }
                }
            }
            bzErpReqBoxMapper.updateById(bzErpReqBox);
        }
    }

    /**
     * 设置参数
     *
     * @param syncWaybillResultDto 源
     * @param bzWaybill            结果
     * @return 设置好的结果
     */
    private BzWaybill setParameter(SyncWaybillResultDto syncWaybillResultDto, BzWaybill bzWaybill) {
        bzWaybill.setOrderType(syncWaybillResultDto.getOrderType());
        bzWaybill.setTransportType(syncWaybillResultDto.getTransportType());
        bzWaybill.setStatus(syncWaybillResultDto.getStatus());
        bzWaybill.setWaybillCode(syncWaybillResultDto.getWaybillCode());
        bzWaybill.setLgort(syncWaybillResultDto.getLgort());
        bzWaybill.setShopCity(syncWaybillResultDto.getShopCode());
        bzWaybill.setDriverPhone(syncWaybillResultDto.getDriverContactNum());
        bzWaybill.setTotalVolume(syncWaybillResultDto.getTotalVolume());
        bzWaybill.setReceivedTime(syncWaybillResultDto.getReceiveTime());
        bzWaybill.setPath(syncWaybillResultDto.getPath());
        bzWaybill.setTotalBox(syncWaybillResultDto.getTotalBox());
        return bzWaybill;
    }

    /**
     * ASCM0085-1 (在途跟踪) 的接口
     *
     * @param syncWaybillDetailforJDDto 同步过来的数据
     * @return 同步的结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SynReturnDTo syncWaybillDetailforJD(syncWaybillDetailforJDDto syncWaybillDetailforJDDto) {

        BzWaybill bzWaybill = bzWaybillMapper.selectOne(new LambdaQueryWrapper<BzWaybill>().eq(BzWaybill::getWaybillCode, syncWaybillDetailforJDDto.getTrackingNumber()));
        if (ObjectUtil.isEmpty(bzWaybill)) {
            return SynReturnDTo.getError("500", "同步数据有误！");
        }

        //做轨迹记录
        BzWaybillTrackHistory bzWaybillTrackHistory = new BzWaybillTrackHistory();
        bzWaybillTrackHistory.setWaybillCode(syncWaybillDetailforJDDto.getTrackingNumber());
        bzWaybillTrackHistory.setDriverName(syncWaybillDetailforJDDto.getOpeName());
        String stringBuffer = syncWaybillDetailforJDDto.getOpeTitle() + "--" +
                syncWaybillDetailforJDDto.getOpeRemark();
        bzWaybillTrackHistory.setAddress(stringBuffer);
        bzWaybillTrackHistory.setTrackStatus(2);
        bzWaybillTrackHistory.setUpdateTime(new Date());
        bzWaybillTrackHistory.insert();
        //更新运单状态
        String trackingNumber = syncWaybillDetailforJDDto.getTrackingNumber();
        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
        if (null != syncWaybillDetailforJDDto.getOpeTitle()) {
            if (syncWaybillDetailforJDDto.getOpeTitle().contains("揽收")) {
                updateWrapper
                        .set(BzWaybill::getStatus, WaybillStatusEnum.IN_TRANSIT.getValue())
                        .set(BzWaybill::getDepartureTime, syncWaybillDetailforJDDto.getOpeTime())
                        .set(BzWaybill::getUpdateTime, new Date())
                        .eq(BzWaybill::getWaybillCode, trackingNumber);
            } else if ("妥投".equals(syncWaybillDetailforJDDto.getOpeTitle())) {
                updateWrapper
                        .set(BzWaybill::getStatus, WaybillStatusEnum.COMPLETE.getValue())
                        .set(BzWaybill::getSignTime, syncWaybillDetailforJDDto.getOpeTime())
                        .set(BzWaybill::getUpdateTime, new Date())
                        .eq(BzWaybill::getWaybillCode, trackingNumber);
            } else if (syncWaybillDetailforJDDto.getOpeTitle().contains("异常") || "拒收、已取消、退回".contains(syncWaybillDetailforJDDto.getOpeTitle())) {
                updateWrapper
                        .set(BzWaybill::getStatus, WaybillStatusEnum.IN_THE_ABNORMAL.getValue())
                        .set(BzWaybill::getAbnormalCause, syncWaybillDetailforJDDto.getOpeRemark())//异常原因
                        .set(BzWaybill::getUpdateTime, new Date())
                        .eq(BzWaybill::getWaybillCode, trackingNumber);
            } else {
                updateWrapper.set(BzWaybill::getStatus, WaybillStatusEnum.IN_TRANSIT.getValue()).set(BzWaybill::getUpdateTime, new Date()).eq(BzWaybill::getWaybillCode, trackingNumber);
            }
            bzWaybillMapper.update(null, updateWrapper);
            dragonService.updateTrackSync(Collections.singletonList(trackingNumber), true);
            return SynReturnDTo.getSuccess("Success!");
        }
        return SynReturnDTo.getError("error", "error");
    }

    /**
     * ERP下发物料和供应商信息
     *
     * @param materialInfoDto 同步过来的数据
     * @return 同步的结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SynReturnDTo materialInfo(MaterialInfoDto materialInfoDto) {
        BaseMaterials baseMaterials = BeanUtil.copyProperties(materialInfoDto, BaseMaterials.class);
        BaseMaterials baseMaterials1 = baseMaterialsMapper.selectOne(new LambdaQueryWrapper<BaseMaterials>().eq(BaseMaterials::getMaterialCode, baseMaterials.getMaterialCode()));
        List<SupplyItem> zsuppliers = materialInfoDto.getZSUPPLIERS();
        if (CollectionUtils.isNotEmpty(zsuppliers)) {
            SupplyItem supplyItem = zsuppliers.get(0);
            baseMaterials.setSupplierPackage(supplyItem.getSupplierPackage());
            if (ObjectUtils.isEmpty(baseMaterials1)) {
                baseMaterialsMapper.insert(baseMaterials);
            } else {
                baseMaterials.setId(baseMaterials1.getId());
                baseMaterialsMapper.updateById(baseMaterials);
            }

            for (SupplyItem zsupplier : zsuppliers) {
                BaseSuppliers baseSuppliers = BeanUtil.copyProperties(zsupplier, BaseSuppliers.class);
                BaseSuppliers baseSuppliers1 = baseSuppliersMapper.selectOne(new LambdaQueryWrapper<BaseSuppliers>().eq(BaseSuppliers::getSupplierCode, baseSuppliers.getSupplierCode()));
                if (ObjectUtils.isEmpty(baseSuppliers1)) {
                    baseSuppliersMapper.insert(baseSuppliers);
                } else {
                    baseSuppliers.setId(baseSuppliers1.getId());
                }

                BzSupplierMaterial bzSupplierMaterial1 = bzSupplierMaterialMapper.selectOne(new LambdaQueryWrapper<BzSupplierMaterial>().eq(BzSupplierMaterial::getMaterialId, baseMaterials.getId()).eq(BzSupplierMaterial::getSupplierId, baseSuppliers.getId()));
                if (ObjectUtils.isEmpty(bzSupplierMaterial1)) {
                    BzSupplierMaterial bzSupplierMaterial = new BzSupplierMaterial();
                    bzSupplierMaterial.setMaterialId(baseMaterials.getId());
                    bzSupplierMaterial.setSupplierId(baseSuppliers.getId());
                    bzSupplierMaterial.setSupplierSnp(zsupplier.getSupplierSnp());
                    bzSupplierMaterialMapper.insert(bzSupplierMaterial);
                } else {
                    BzSupplierMaterial bzSupplierMaterial = new BzSupplierMaterial();
                    bzSupplierMaterial.setId(bzSupplierMaterial1.getId());
                    bzSupplierMaterial.setSupplierSnp(zsupplier.getSupplierSnp());
                    bzSupplierMaterialMapper.updateById(bzSupplierMaterial);
                }
                List<SupplyPackPlanItem> zwrapinfo = zsupplier.getZWRAPINFO();
                if (CollectionUtils.isNotEmpty(zwrapinfo)) {
                    // 如果这个供应商之前有包装信息就删除了
                    LambdaQueryWrapper<BzSupplierPackageInfo> lqw = new LambdaQueryWrapper<>();
                    lqw.eq(BzSupplierPackageInfo::getMaterialId, baseMaterials.getId());
                    lqw.eq(BzSupplierPackageInfo::getSupplierId, baseSuppliers.getId());
                    bzSupplierPackageInfoMapper.delete(lqw);
                    for (SupplyPackPlanItem supplyPackPlanItem : zwrapinfo) {
                        supplyPackPlanItem.setRecycle("是".equals(supplyPackPlanItem.getRecycle()) ? "1" : "0");
                        BzSupplierPackageInfo bzSupplierPackageInfo = BeanUtil.copyProperties(supplyPackPlanItem, BzSupplierPackageInfo.class);
                        bzSupplierPackageInfo.setMaterialId(baseMaterials.getId());
                        bzSupplierPackageInfo.setSupplierId(baseSuppliers.getId());
                        bzSupplierPackageInfo.setSupplierSnp(zsupplier.getSupplierSnp());
                        bzSupplierPackageInfoMapper.insert(bzSupplierPackageInfo);
                    }
                } else {
                    LambdaQueryWrapper<BzSupplierPackageInfo> lqw = new LambdaQueryWrapper<>();
                    lqw.eq(BzSupplierPackageInfo::getMaterialId, baseMaterials.getId());
                    lqw.eq(BzSupplierPackageInfo::getSupplierId, baseSuppliers.getId());
                    bzSupplierPackageInfoMapper.delete(lqw);
                }
            }
        } else {
            if (ObjectUtils.isEmpty(baseMaterials1)) {
                baseMaterialsMapper.insert(baseMaterials);
            } else {
                baseMaterials.setId(baseMaterials1.getId());
                baseMaterialsMapper.updateById(baseMaterials);
            }
        }

        return SynReturnDTo.getSuccess("Success!");
    }

    /**
     * ERP下发物料和入库订单信息
     *
     * @param materialDeliveryNoteDto 同步过来的数据
     * @return 同步的结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SynReturnDTo materialDeliveryNote(MaterialDeliveryNoteDto materialDeliveryNoteDto) {
        BzDeliveryOrders bzDeliveryOrders = BeanUtil.copyProperties(materialDeliveryNoteDto, BzDeliveryOrders.class);
        // 先查询，如果存在就更新，不存在就新增
        List<BzDeliveryOrders> bzDeliveryOrdersList = bzDeliveryOrdersMapper.selectList(new QueryWrapper<BzDeliveryOrders>()
                .eq("delivery_order_code", bzDeliveryOrders.getDeliveryOrderCode()).eq("asn_code", bzDeliveryOrders.getAsnCode()));
        if (CollUtil.isEmpty(bzDeliveryOrdersList)) {
            bzDeliveryOrdersMapper.insert(bzDeliveryOrders);
            List<String> matnrs = materialDeliveryNoteDto.getMATNR();
            for (String matnr : matnrs) {
                BzDeliveryOrderMaterial bzDeliveryOrderMaterial = setInfo(bzDeliveryOrders, matnr);
                // 先删除原来的物料关联关系，后在新增关联关系
                bzDeliveryOrderMaterialMapper.insert(bzDeliveryOrderMaterial);
            }
        } else {
            BzDeliveryOrders bzDeliveryOrders1 = bzDeliveryOrdersList.get(0);
            List<String> matnrs = materialDeliveryNoteDto.getMATNR();
            // 先删除原来的物料关联关系，后在新增关联关系
            bzDeliveryOrderMaterialMapper.delete(new QueryWrapper<BzDeliveryOrderMaterial>()
                    .eq("order_id", bzDeliveryOrders1.getId()));
            for (String matnr : matnrs) {
                BzDeliveryOrderMaterial bzDeliveryOrderMaterial = setInfo(bzDeliveryOrders1, matnr);
                bzDeliveryOrderMaterialMapper.insert(bzDeliveryOrderMaterial);
            }
        }

        return SynReturnDTo.getSuccess("Success!");
    }

    private BzDeliveryOrderMaterial setInfo(BzDeliveryOrders bzDeliveryOrders1, String matnr) {
        BzDeliveryOrderMaterial bzDeliveryOrderMaterial = new BzDeliveryOrderMaterial();
        bzDeliveryOrderMaterial.setOrderId(bzDeliveryOrders1.getId());
        BaseMaterials baseMaterials = baseMaterialsMapper.selectOne(new LambdaQueryWrapper<BaseMaterials>().select(BaseMaterials::getId).eq(BaseMaterials::getMaterialCode, matnr));
        if (ObjectUtil.isNull(baseMaterials)) {
            // 没有查出来就新增一个物料
            baseMaterials = new BaseMaterials();
            baseMaterials.setMaterialCode(matnr);
            baseMaterialsMapper.insert(baseMaterials);
        }
        bzDeliveryOrderMaterial.setMaterialId(baseMaterials.getId());
        return bzDeliveryOrderMaterial;
    }
}
