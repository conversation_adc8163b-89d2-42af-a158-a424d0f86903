package com.xiaopeng.halley.ascm.boot.service.third;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xiaopeng.halley.ascm.boot.dto.AsmWayBillReq;
import com.xiaopeng.halley.ascm.boot.dto.AsmWaybillFreshReq;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DragonService {
	@Lazy
	@Resource
	private BzWaybillService bzWaybillService;
	@Resource
	private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;

	public void updateSync(Collection<String> waybillCodes) {
		if (waybillCodes.isEmpty()) {
			return;
		}
		if (bzWaybillService.lambdaUpdate().in(BzWaybill::getWaybillCode, waybillCodes)
				.set(BzWaybill::getSync, 0)
				.update()) {
			log.info("成功更新运单sync waybillCodes: {}", waybillCodes);
		}
	}

	/**
	 * 运单状态或者规则发生改变
	 */
	public void updateTrackSync(Collection<String> waybillCodes, boolean needSync) {
		if (waybillCodes.isEmpty()) {
			return;
		}
		if (bzWaybillService.lambdaUpdate().in(BzWaybill::getWaybillCode, waybillCodes)
				.set(BzWaybill::getTrackSync, needSync ? 1 : 0)
				.update()) {
			log.info("成功更新运单trackSync waybillCodes: {}, isSync: {}", waybillCodes, needSync);
		}
	}

	public Map<String, List<AsmWayBillReq>> queryStoreWaybill(List<String> filterWaybillCodes) {
		Map<String, List<AsmWayBillReq>> resultMap = new HashMap<>();
		List<AsmWayBillReq> asmWayBillReqs = bzWaybillService.getBaseMapper().queryStoreWaybill(filterWaybillCodes);
		List<String> waybillCodes = asmWayBillReqs.stream().map(AsmWayBillReq::getWaybillCode).collect(Collectors.toList());
		if (waybillCodes.isEmpty()) {
			return resultMap;
		}

		for (AsmWayBillReq waybill : asmWayBillReqs) {
			for (String line : StrUtil.split(waybill.getConcatStr(), ",")) {
				List<String> parts = StrUtil.split(line, "@");
				if (parts.size() != 4) {
					continue;
				}
				String deliverOrderCode = parts.get(0);
				String boxCode = parts.get(1);
				String materialCode = parts.get(2);
				String orderCount = parts.get(3);

				// 设置交货单号和状态流
				List<AsmWayBillReq> list = resultMap.getOrDefault(deliverOrderCode, new ArrayList<>());
				AsmWayBillReq item = BeanUtil.copyProperties(waybill, AsmWayBillReq.class);

				// 若交货单下有重复的运单，把箱子内容进行合并
				Optional<AsmWayBillReq> optional = list.stream().filter(e -> e.getWaybillCode().equals(waybill.getWaybillCode())).findFirst();
				if (optional.isPresent()) {
					item = optional.get();
				} else {
					list.add(item);
				}

				resultMap.put(deliverOrderCode, list);
				item.setOemOutCode(deliverOrderCode);

				// 添加箱物料
				AsmWayBillReq.BoxInfo boxInfo = new AsmWayBillReq.BoxInfo();
				boxInfo.setBoxCode(boxCode);
				boxInfo.setPartNo(materialCode);
				boxInfo.setQty(Integer.parseInt(orderCount));
				item.getBoxList().add(boxInfo);

				// 放到结果map中
				item.setConcatStr(null);
			}
		}
		return resultMap;
	}

	public List<AsmWaybillFreshReq> queryFreshWaybill(List<String> filterWaybillCodes) {
		List<AsmWaybillFreshReq> asmWaybillFreshReqs = bzWaybillService.getBaseMapper().queryFreshWaybill(filterWaybillCodes);
		if (asmWaybillFreshReqs.isEmpty()) {
			return Collections.emptyList();
		}

		List<String> waybillCodes = asmWaybillFreshReqs.stream().map(AsmWaybillFreshReq::getWayBillCode).collect(Collectors.toList());
		Map<String, List<BzWayBillTrackHistoryVO>> trackMap = bzWaybillTrackHistoryMapper.getWaybillTransportationList(waybillCodes)
				.stream().collect(Collectors.groupingBy(BzWayBillTrackHistoryVO::getWaybillCode));
		// 查询运单状态流
		Map<String, Set<AsmWaybillFreshReq.LogisticsTrack>> statusTrackMap = bzWaybillService.lambdaQuery().in(BzWaybill::getWaybillCode, waybillCodes).list().stream()
				.collect(Collectors.toMap(BzWaybill::getWaybillCode, bzWaybill -> {
					Set<AsmWaybillFreshReq.LogisticsTrack> logisticsTrack = new LinkedHashSet<>();
					if (isInitDate(bzWaybill.getCirculationTime())) {
						logisticsTrack.add(new AsmWaybillFreshReq.LogisticsTrack("已发布", bzWaybill.getCirculationTime()));
					}
					if (isInitDate(bzWaybill.getPickupTime())) {
						logisticsTrack.add(new AsmWaybillFreshReq.LogisticsTrack("待提货", bzWaybill.getPickupTime()));
					}
					if (isInitDate(bzWaybill.getPdaShippingTime())) {
						logisticsTrack.add(new AsmWaybillFreshReq.LogisticsTrack("待运输", bzWaybill.getPdaShippingTime()));
					}
					if (isInitDate(bzWaybill.getDepartureTime())) {
						logisticsTrack.add(new AsmWaybillFreshReq.LogisticsTrack("运输中", bzWaybill.getDepartureTime()));
					}
					if (isInitDate(bzWaybill.getSignTime())) {
						logisticsTrack.add(new AsmWaybillFreshReq.LogisticsTrack("已完成", bzWaybill.getSignTime()));
					}
					return logisticsTrack;
				}));
		log.info("statusTrackMap {}", JSON.toJSONString(statusTrackMap));

		for (AsmWaybillFreshReq asmWaybillFreshReq : asmWaybillFreshReqs) {
			List<AsmWaybillFreshReq.LogisticsTrack> trackList = trackMap.getOrDefault(asmWaybillFreshReq.getWayBillCode(), new ArrayList<>())
					.stream()
					.map(e -> new AsmWaybillFreshReq.LogisticsTrack(e.getAddress(), e.getUpdateTime()))
					.collect(Collectors.toList());

			// 加入状态流并进行倒序排序
			if (statusTrackMap.containsKey(asmWaybillFreshReq.getWayBillCode())) {
				Set<AsmWaybillFreshReq.LogisticsTrack> logisticsTracks = statusTrackMap.get(asmWaybillFreshReq.getWayBillCode());
				trackList.addAll(logisticsTracks);
				trackList = trackList.stream().sorted((o1, o2) -> o2.getLogisticsTime().compareTo(o1.getLogisticsTime())).collect(Collectors.toList());
			}
			asmWaybillFreshReq.setLogisticsTrack(trackList);
		}
		return asmWaybillFreshReqs;
	}

	private boolean isInitDate(Date date) {
		return !"1970-01-01 08:00:00".equals(DateUtil.formatDateTime(date));
	}
}
