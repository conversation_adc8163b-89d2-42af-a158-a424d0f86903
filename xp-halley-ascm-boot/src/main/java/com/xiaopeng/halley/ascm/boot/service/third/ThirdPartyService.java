package com.xiaopeng.halley.ascm.boot.service.third;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;

import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApi;
import com.xiaopeng.halley.ascm.boot.common.enums.RemoteApiEnum;
import com.xiaopeng.halley.ascm.boot.dto.PORequest;
import com.xiaopeng.halley.ascm.boot.entity.BaseWarehouse;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillBoxRel;
import com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillBoxRelMapper;
import com.xiaopeng.halley.ascm.boot.page.baseWarehousePage.constant.WarehouseTypeEnum;
import com.xiaopeng.halley.ascm.boot.service.BzErpReqMaterialRelService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xiaopeng.halley.ascm.boot.service.PoRequestHelp;
import com.xpeng.athena.common.core.domain.ResultEnums;
import com.xpeng.athena.common.core.exception.ResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 第三方请求交互接口
 */
@Slf4j
@Service
public class ThirdPartyService {
    @Value("${waybill.enableCheckBoxCount}")
    private boolean enableCheckBoxCount;
    @Resource
    private PoRequestHelp poRequestHelp;
    @Lazy
    @Resource
    private BzWaybillService bzWaybillService;
    @Resource
    private BaseWarehouseMapper baseWarehouseMapper;
	@Resource
	private BzWaybillBoxRelMapper bzWaybillBoxRelMapper;
    @Resource
    private BzErpReqMaterialRelService bzErpReqMaterialRelService;

    /**
     * 请求wms箱号物料数量
     *
     * @param boxCode     箱号
     * @param waybillCode 运单号
     */
    @RemoteApi(RemoteApiEnum.ASCM0088)
    public void checkBoxMaterialCount(String boxCode, String waybillCode) throws ResultException {
        if (ignoreCheck(waybillCode)) return;

        PORequest poRequest = buildPORequest(waybillCode, Collections.singletonList(boxCode));
        Integer total = Convert.toInt(poRequestHelp.invoke(poRequest, "ZTOTAL"));

        int targetCount = bzErpReqMaterialRelService.getBaseMapper().findAllByBoxCode(boxCode)
                .stream().mapToInt(e -> Integer.parseInt(e.getPackageCount())).sum();

        log.info("ASCM箱号数量: {}, WMS箱号数量: {}", targetCount, total);
        if (!Objects.equals(targetCount, total)) {
            throw new ResultException(ResultEnums.BUSINESS_ERROR.getCode(), "箱数量不一致，请查看WMS包装是否发送！箱号: " + boxCode);
        }
    }

    @RemoteApi(RemoteApiEnum.ASCM0088)
    public void checkBoxMaterialCount(String waybillCode) throws ResultException {
        if (ignoreCheck(waybillCode)) {
            return;
        }
        // 发运时额外的校验条件，分仓才需要校验
	    if (Boolean.FALSE.equals(bzWaybillService.verifierReceiverIsCentral(Collections.singletonList(waybillCode)).get(waybillCode))) {
            log.info("非分仓运单，无需校验 waybillCode: {}", waybillCode);
		    return;
	    }

        //查询出当前箱号与运单关系的对象
        LambdaQueryWrapper<BzWaybillBoxRel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BzWaybillBoxRel::getWaybillCode, waybillCode)
                .eq(BzWaybillBoxRel::getIsDevanning, 0)
                .eq(BzWaybillBoxRel::getIsDelete, 0);
        List<String> boxList = bzWaybillBoxRelMapper.selectList(wrapper)
                .stream().map(BzWaybillBoxRel::getBoxCode)
                .collect(Collectors.toList());

        PORequest poRequest = buildPORequest(waybillCode, boxList);
        Integer total = Convert.toInt(poRequestHelp.invoke(poRequest, "ZTOTAL"));
        int targetCount = bzErpReqMaterialRelService.getBaseMapper().countBoxPackageCount(boxList);

        log.info("ASCM箱号数量: {}, WMS箱号数量: {}", targetCount, total);
        if (!Objects.equals(targetCount, total)) {
            throw new ResultException(ResultEnums.BUSINESS_ERROR.getCode(), "箱数量不一致，请查看WMS包装是否发送！");
        }
    }

    private PORequest buildPORequest(String waybillCode, List<String> boxList) {
        WarehouseTypeEnum warehouseType = WarehouseTypeEnum.of(baseWarehouseMapper.selectWarehouseTypeByWaybill(waybillCode));
        String receiver = warehouseType == WarehouseTypeEnum.XL_WMS ? "XDRAGON" : "EWM";

        List<JSONObject> data = boxList.stream().map(e -> new JSONObject().set("CARTON", e))
                .collect(Collectors.toList());

	    return PORequest.builder()
	            .data(data)
	            .busId("ASCM0088")
	            .receiver(receiver)
	            .poInvokeEnum(POInvokeEnum.SI_ASCM0088_Syn_Out)
	            .build();
    }

    private boolean ignoreCheck(String waybillCode) {
        if (!enableCheckBoxCount) {
            log.info("暂未开启检查箱子数量检测");
            return true;
        }

        List<String> lgortList = new LambdaQueryChainWrapper<>(baseWarehouseMapper)
                .in(BaseWarehouse::getWarehouseType, Arrays.asList(WarehouseTypeEnum.WMS.getValue(), WarehouseTypeEnum.XL_WMS.getValue()))
                .select(BaseWarehouse::getLgort)
                .list().stream().map(BaseWarehouse::getLgort)
                .collect(Collectors.toList());

        log.info("waybillCode: {}, wms仓库编码: {}", waybillCode, lgortList);
        BzWaybill waybill = CollUtil.getFirst(bzWaybillService.lambdaQuery()
                .eq(BzWaybill::getWaybillCode, waybillCode).list());

        // 只有WMS仓库发出的运单才检验
        if (!lgortList.contains(waybill.getLgort())) {
            log.info("不是WMS仓库发出的运单，不需要检验箱数量 lgort: {}", waybill.getLgort());
            return true;
        }
        return false;
    }

}
