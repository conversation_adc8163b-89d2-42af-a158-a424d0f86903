package com.xiaopeng.halley.ascm.boot.service.weChat;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xiaopeng.halley.ascm.boot.utils.JsonUtil;
import com.xiaopeng.halley.ascm.boot.utils.OkHttpTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 微信相关调用
 *
 * <AUTHOR>
 * @Date 2022/10/8 10:01 AM
 */
@SuppressWarnings("rawtypes")
@Slf4j
@Service
public class WeChatService {

    @Resource
    private OkHttpTools okHttpTools;
    @Value("${com.xiaopeng.ascm.wechat.appId}")
    private String appId;
    @Value("${com.xiaopeng.ascm.wechat.appSecret}")
    private String appSecret;

    /**
     * 获取微信AccessToken
     *
     * @return
     */
    public String getAccessToken() {
        Map<String, String> queries = new HashMap<>();
        queries.put("grant_type", "client_credential");
        queries.put("appid", appId);
        queries.put("secret", appSecret);
        String result = okHttpTools.get("https://api.weixin.qq.com/cgi-bin/token", queries);
        if (StrUtil.isEmpty(result)) {
            log.error("getAccessToken 微信获取AccessToken,获取失败，result->{}", result);
            return "";
        }
        log.info("getAccessToken 微信获取AccessToken,成功返回result->{}", result);

        Map resultMap = JsonUtil.parseObject(result, Map.class);
        if (null == resultMap) {
            log.error("getAccessToken 微信获取AccessToken,返回体解析失败，result->{}", result);
            return "";
        }
        return Optional.ofNullable(resultMap.get("access_token")).map(String::valueOf).orElse("");
    }

    /**
     * 通过Code获取手机号码
     *
     * @param code
     * @param accessToken
     * @return
     * @throws IOException
     */
    public Map getPhoneNumber(String code, String accessToken) throws IOException {
        log.error("getPhoneNumber getPhoneNumber");
        log.error("getPhoneNumber code" + code);
        log.error("getPhoneNumber accessToken" + accessToken);

        if (StrUtil.isEmpty(code)) {
            log.error("getPhoneNumber 微信获取手机号失败，code为空");
            return null;
        }

        if (StrUtil.isEmpty(accessToken)) {
            log.error("getPhoneNumber 微信获取手机号失败，accessToken为空");
            return null;
        }

        Map<String, String> body = new HashMap<>();
        body.put("code", code);

        String result = okHttpTools.post("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken, body);
        Map resultMap = JsonUtil.parseObject(result, Map.class);
        if (null == resultMap) {
            log.error("getPhoneNumber 微信获取手机号失败,返回体解析失败，result->{}", result);
            return null;
        }

        log.error("getPhoneNumber resultMap", JSONObject.toJSONString(resultMap));
        return resultMap;

    }
}
