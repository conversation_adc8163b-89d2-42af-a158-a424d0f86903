package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AssertUtils {

	/**
	 * 校验是否为数字且大于0
	 */
	public void isNumberAndPositive(String str, String name) {
		if (StrUtil.isBlank(str)) {
			throw new RuntimeException(name + "不能为空");
		}
		try {
			double val = Double.parseDouble(str);
			Assert.isTrue(val >= 0, name + "不能为负数");
		} catch (NumberFormatException e) {
			throw new RuntimeException(name + "必须是数值类型");
		}
	}

}
