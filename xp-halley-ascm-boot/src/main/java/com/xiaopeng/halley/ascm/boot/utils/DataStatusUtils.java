package com.xiaopeng.halley.ascm.boot.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 数据状态工具
 *
 * <AUTHOR>
 * @Date 2022/4/8 6:06 PM
 */
@Slf4j
@Component
public class DataStatusUtils {

    /**
     * 将状态数字翻译为中文字符
     *
     * @param o
     * @return
     */
    public String translate(Optional<Object> o) {
        if (o.orElse("").equals(1)) {
            return "启用";
        } else if (o.orElse("").equals(0)) {
            return "停用";
        } else {
            log.error("状态转换失败，status->{}", o);
            return "-";
        }
    }

    /**
     * 将状态数字翻译为中文字符，并状态倒转
     *
     * @param o
     * @return
     */
    public String translateReverse(Optional<Object> o) {
        if (o.orElse("").equals(1)) {
            return "停用";
        } else if (o.orElse("").equals(0)) {
            return "启用";
        } else {
            log.error("状态转换失败，status->{}", o);
            return "-";
        }
    }

    /**
     * 将状态数字翻译为中文字符，并重置状态
     *
     * @param o
     * @return
     */
    public String translateTF(Optional<Object> o) {
        if (o.orElse("").equals(1)) {
            return "是";
        } else if (o.orElse("").equals(0)) {
            return "否";
        } else {
            log.info("状态转换失败，o->{}", o);
            return "-";
        }
    }
}
