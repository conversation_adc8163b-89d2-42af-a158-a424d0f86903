package com.xiaopeng.halley.ascm.boot.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

public class DateUtils {

    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyyMMdd HHmmss");

    public DateUtils() {
    }

    public static LocalDate getCurrentDate() {
        return LocalDate.now();
    }

    public static LocalDateTime getLocalDateTime() {
        return LocalDateTime.now();
    }

    public static String format(Date date, DateUtils.DateTimeFormatterEnum pattern) {
        return format(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()), pattern);
    }

    public static String format(LocalDateTime localDateTime, DateUtils.DateTimeFormatterEnum pattern) {
        return localDateTime.format(pattern.formatter);
    }

    public static Date parseDate(String date, DateUtils.DateTimeFormatterEnum pattern) {
        return Date.from(parseLocalDateTime(date, pattern).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDateTime parseLocalDateTime(String date, DateUtils.DateTimeFormatterEnum pattern) {
        return LocalDateTime.parse(date, pattern.formatter);
    }

    public static Date todayBegin() {
        return Date.from(LocalDateTime.of(getCurrentDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date todayEnd() {
        return Date.from(LocalDateTime.of(getCurrentDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date yesterdayBegin() {
        return Date.from(LocalDateTime.of(getCurrentDate().plusDays(-1L), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date yesterdayEnd() {
        return Date.from(LocalDateTime.of(getCurrentDate().plusDays(-1L), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date tomorrowBegin() {
        return Date.from(LocalDateTime.of(getCurrentDate().plusDays(1L), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date tomorrowEnd() {
        return Date.from(LocalDateTime.of(getCurrentDate().plusDays(1L), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date startTime(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toLocalDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date endTime(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toLocalDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date monthBegin() {
        return Date.from(LocalDateTime.of(getCurrentDate().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date monthEnd() {
        return Date.from(LocalDateTime.of(getCurrentDate().with(TemporalAdjusters.lastDayOfMonth()), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date theMonthBegin(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).with(TemporalAdjusters.firstDayOfMonth()).toLocalDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date theMonthEnd(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).with(TemporalAdjusters.lastDayOfMonth()).toLocalDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date theYearBegin(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).with(TemporalAdjusters.firstDayOfYear()).toLocalDate(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date theYearEnd(Date date) {
        return Date.from(LocalDateTime.of(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).with(TemporalAdjusters.lastDayOfYear()).toLocalDate(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Boolean checkIsToday(Date date) {
        return getCurrentDate().equals(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toLocalDate());
    }

    public static Duration between(Date beginTime, Date endTime) {
        LocalDateTime beginLocalDateTime = LocalDateTime.ofInstant(beginTime.toInstant(), ZoneId.systemDefault());
        LocalDateTime endLocalDateTime = LocalDateTime.ofInstant(endTime.toInstant(), ZoneId.systemDefault());
        return Duration.between(beginLocalDateTime, endLocalDateTime);
    }

    public static Date theDayBefor(Date date, long num) {
        return Date.from(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).minusDays(num).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date theDayLater(Date date, long num) {
        return Date.from(LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).plusDays(num).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Integer dateBetween(Date beginTime, Date endTime) {
        long times = endTime.getTime() - beginTime.getTime();

        double hours = (double) times / (60 * 60 * 1000);

        BigDecimal a = BigDecimal.valueOf(hours);

        int i = a.setScale(0, RoundingMode.HALF_UP).intValue();
        return i;
    }

    /**
     * 拼接时间和日期
     *
     * @param date 日期
     * @param time 时间
     * @return Date
     */
    public static Date joinDateAndTime(String date, String time) throws ParseException {
        String combined = date + " " + time;
        return SIMPLE_DATE_FORMAT.parse(combined);
    }

    public enum DateTimeFormatterEnum {
        FORMAT_DATE_TIME_STYLE_1("yyyy-MM-dd HH:mm:ss", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
        FORMAT_DATE_TIME_STYLE_2("yyyy-MM-dd", DateTimeFormatter.ofPattern("yyyy-MM-dd")),
        FORMAT_DATE_TIME_STYLE_3("yyyy-MM-dd HH:mm", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
        FORMAT_DATE_TIME_STYLE_4("ss mm HH dd MM ? yyyy", DateTimeFormatter.ofPattern("ss mm HH dd MM ? yyyy"));

        private final String value;
        private final DateTimeFormatter formatter;

        DateTimeFormatterEnum(String value, DateTimeFormatter formatter) {
            this.value = value;
            this.formatter = formatter;
        }

        public String getValue() {
            return this.value;
        }

        public DateTimeFormatter getFormatter() {
            return this.formatter;
        }
    }
}