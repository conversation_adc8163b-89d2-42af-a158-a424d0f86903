package com.xiaopeng.halley.ascm.boot.utils;

import lombok.experimental.UtilityClass;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@UtilityClass
public class HttpUtils {
	public String getCurrentUrl() {
		// 获取request对象
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return "No Url";
		}
		HttpServletRequest request = attributes.getRequest();
		return request.getRequestURI();
	}
}
