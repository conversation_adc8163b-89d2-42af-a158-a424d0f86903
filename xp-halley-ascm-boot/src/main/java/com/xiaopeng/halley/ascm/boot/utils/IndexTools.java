package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xiaopeng.halley.ascm.boot.common.redis.RedisKeyManager;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 编号工具类
 *
 * <AUTHOR>
 * @Date 2022/6/15 3:38 PM
 */
@Service
public class IndexTools {

    static String pre = "ascm";

    @Resource
    private RedissonClient redisson;

    /**
     * 生成编号数字
     *
     * @return
     */
    public Long getTodayIndex() {
        Date currentDate = new Date();
        String today = DateUtil.format(currentDate, "yyyyMMdd");
        String key = RedisKeyManager.CREATE_TODAY_INDEX.buildKey(today);
        if (ObjectUtil.isNull(redisson.getBucket(key))) {
            redisson.getBucket(key).set(key, 1, TimeUnit.DAYS);
        }
        RAtomicLong returnVO = redisson.getAtomicLong(key);
        returnVO.expire(1, TimeUnit.DAYS);
        return returnVO.incrementAndGet();
    }

    /**
     * 生成编号数字
     *
     * @return
     */
    public Long getTodayASCMIndex() {
        Date currentDate = new Date();
        String today = DateUtil.format(currentDate, "yyyyMMdd");
        String key = RedisKeyManager.CREATE_TODAY_INDEX.buildKey(today);
        if (ObjectUtil.isNull(redisson.getBucket(key))) {
            redisson.getBucket(key).set(key, 1, TimeUnit.DAYS);
        }
        RAtomicLong returnVO = redisson.getAtomicLong(key);
        returnVO.expire(1, TimeUnit.DAYS);
        return returnVO.incrementAndGet();
    }

    /**
     * 生成基于维修点的数字编号
     *
     * @return
     */
    public Long getTodayRepairIndex(String repairCenterNum) {
        Date currentDate = new Date();
        String today = DateUtil.format(currentDate, "yyyyMMdd");
        String key = RedisKeyManager.CREATE_TODAY_REPAIR_INDEX.buildKey(today, repairCenterNum);
        if (ObjectUtil.isNull(redisson.getBucket(key))) {
            redisson.getBucket(key).set(key, 1, TimeUnit.DAYS);
        }
        RAtomicLong returnVO = redisson.getAtomicLong(key);
        returnVO.expire(1, TimeUnit.DAYS);
        return returnVO.incrementAndGet();
    }

    /**
     * 生成预约编号后缀
     *
     * @return
     */
    public Long getAppointmentTodayIndex() {
        Date currentDate = new Date();
        String today = DateUtil.format(currentDate, "yyyyMMdd");
        String key = RedisKeyManager.CREATE_APPOINTMENT_TODAY_INDEX.buildKey(today);
        if (ObjectUtil.isNull(redisson.getBucket(key))) {
            redisson.getBucket(key).set(key, 1, TimeUnit.DAYS);
        }
        RAtomicLong returnVO = redisson.getAtomicLong(key);
        returnVO.expire(1, TimeUnit.DAYS);
        return returnVO.incrementAndGet();
    }
}
