package com.xiaopeng.halley.ascm.boot.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.Map;

/**
 * Json工具类
 *
 * <AUTHOR>
 * @Date 2022/4/12 8:16 PM
 */
public class JsonUtil {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private JsonUtil() {
    }

    /**
     * @param obj 准备转换对象
     * @return
     * @description 通过Jackson把对象转换成json字符串
     */
    public static String toJsonStr(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将object转换成bean
     *
     * @param obj
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T parseObject(Object obj, Class<T> clazz) {
        try {
            String json = toJsonStr(obj);
            return parseObject(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseArray(Object obj, TypeReference<T> typeReference) {
        try {
            String json = toJsonStr(obj);
            if ("{}".equalsIgnoreCase(json.trim())) {
                json = "[]";
            }
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseArray(String str, TypeReference<T> typeReference) {
        try {
            if ("{}".equalsIgnoreCase(str.trim())) {
                str = "[]";
            }
            return OBJECT_MAPPER.readValue(str, typeReference);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 判断是否为字符串
     *
     * @param string json字符串
     * @return
     */
    public static boolean isJSON(String string) {
        boolean result = false;
        try {
            Map<String, Object> map = JsonUtil.parseObject(string, Map.class);
            result = true;
        } catch (Exception e) {
            result = false;
        }

        return result;
    }
}
