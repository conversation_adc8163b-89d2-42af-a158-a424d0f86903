package com.xiaopeng.halley.ascm.boot.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * Object工具
 *
 * <AUTHOR>
 * @Date 2022/7/15 7:45 PM
 */
public class ObjectTools {

    public static <T> List<T> objToList(Object obj, Class<T> cla) {
        List<T> list = new ArrayList<T>();
        if (obj instanceof ArrayList<?>) {
            for (Object o : (List<?>) obj) {
                list.add(cla.cast(o));
            }
            return list;
        }
        return null;
    }
}
