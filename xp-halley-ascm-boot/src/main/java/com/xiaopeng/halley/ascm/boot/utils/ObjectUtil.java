package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Field;
import java.util.List;

public class ObjectUtil {
    /**
     * 判断对象中部分属性值是否不为空
     *
     * @param object            对象
     * @param excludeFieldNames 选择忽略校验的属性名称List集合
     * @return
     */
    public static boolean checkObjFieldsIsNotNull(Object object, List<String> excludeFieldNames) {
        if (null == object) {
            return false;
        }
        //反射获取成员变量，判断必填项是否有值。
        try {
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                //先判断是不是需要排除的字段，是排除的字段是，返回false,结束本次循环，不是排除字段，返回true,进入判断是否是null或者” “;
                if (!excludeFieldNames.contains(f.getName())) {
                    //null和‘ ’字符串都不满足，则结束本次循环，进入下次循环
                    if (f.get(object) != null && StrUtil.isNotBlank(f.get(object).toString())) {
                        continue;
                    } else {
                        return false;
                    }
                } else {
                    continue;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }
}