package com.xiaopeng.halley.ascm.boot.utils;

import okhttp3.*;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * 请求公共类
 *
 * <AUTHOR>
 * @Date 2022/8/15 6:26 PM
 */
@SuppressWarnings({"DuplicatedCode", "rawtypes"})
@Service
public class OkHttpTools {

    public static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    //新建一个OkHttpClient对象
    OkHttpClient client = new OkHttpClient();

    /**
     * get
     *
     * @param url     请求的url
     * @param queries 请求的参数，在浏览器？后面的数据，没有可以传null
     * @return
     */
    public String get(String url, Map<String, String> queries) {
        String responseBody = "";
        StringBuilder sb = new StringBuilder(url);
        if (queries != null && queries.keySet().size() > 0) {
            boolean firstFlag = true;
            for (Map.Entry entry : queries.entrySet()) {
                if (firstFlag) {
                    sb.append("?").append(entry.getKey()).append("=").append(entry.getValue());
                    firstFlag = false;
                } else {
                    sb.append("&").append(entry.getKey()).append("=").append(entry.getValue());
                }
            }
        }
        Request request = new Request
                .Builder()
                .url(sb.toString())
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
            int status = response.code();
            if (status == 200) {
                return Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
//            logger.error("okhttp put error >> ex = {}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return responseBody;
    }

    /**
     * get请求异步
     *
     * @param url     请求的url
     * @param queries 请求的参数，在浏览器？后面的数据，没有可以传null
     * @return
     */
    public void getAsy(String url, Map<String, String> queries) {
        //创建请求体
        Request request = new Request.Builder().url(url).get().build();
        Call newCall = client.newCall(request);
        //调用异步执行
        newCall.enqueue(new Callback() {

            //在获取服务器响应结果
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String result = Objects.requireNonNull(response.body()).string();
                    System.out.println(result);
                }
            }

            //失败结果
            @Override
            public void onFailure(Call call, IOException ex) {
                //这里处理错误
            }
        });
    }

    /**
     * post
     *
     * @param url    请求的url
     * @param params post form 提交的参数
     * @return
     */
    public String post(String url, Object params) throws IOException {

//        RequestBody body = RequestBody.create(JSON, json);
        RequestBody body = RequestBody.create(JSON, JsonUtil.toJsonStr(params));

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            return Objects.requireNonNull(response.body()).string();
        }
    }

    /**
     * post
     *
     * @param url    请求的url
     * @param params post form 提交的参数
     * @return
     */
    public void postAsy(String url, Object params) {
        //创建请求体
        RequestBody body = RequestBody.create(JSON, JsonUtil.toJsonStr(params));

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        Call newCall = client.newCall(request);
        //调用异步执行
        newCall.enqueue(new Callback() {

            //在获取服务器响应结果
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String result = Objects.requireNonNull(response.body()).string();
                    System.out.println(result);
                }
            }

            //失败结果
            @Override
            public void onFailure(Call call, IOException ex) {
                //这里处理错误
            }
        });
    }

    /**
     * post
     *
     * @param url    请求的url
     * @param params post form 提交的参数
     * @return
     */
    public String postForm(String url, Map<String, String> params) {
        String responseBody = "";
        FormBody.Builder builder = new FormBody.Builder();
        //添加参数
        if (params != null && params.keySet().size() > 0) {
            for (String key : params.keySet()) {
                builder.add(key, params.get(key));
            }
        }
        Request request = new Request
                .Builder()
                .url(url)
                .post(builder.build())
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
            int status = response.code();
            if (status == 200) {
                return Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
//            logger.error("okhttp post error >> ex = {}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return responseBody;
    }

    /**
     * post请求异步
     *
     * @param url    请求的url
     * @param params post form 提交的参数
     * @return
     */
    public void postFormAsy(String url, Map<String, String> params) {
        //创建上传参数
        FormBody.Builder builder = new FormBody.Builder();
        //添加参数
        if (params != null && params.keySet().size() > 0) {
            for (String key : params.keySet()) {
                builder.add(key, params.get(key));
            }
        }

        RequestBody requestBody = builder.build();
        Request request = new Request.Builder().url(url).post(requestBody)
                .build();
        Call newCall = client.newCall(request);
        newCall.enqueue(new Callback() {

            //返回结果
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String string = Objects.requireNonNull(response.body()).string();
                    System.out.println(string);
                }
            }

            //失败
            @Override
            public void onFailure(Call call, IOException ex) {
                //这里处理错误
            }
        });
    }

    /**
     * post 上传文件
     *
     * @param url
     * @param params
     * @param fileType
     * @return
     */
    public String postFile(String url, Map<String, Object> params, String fileType) {
        String responseBody = "";
        MultipartBody.Builder builder = new MultipartBody.Builder();
        //添加参数
        if (params != null && params.keySet().size() > 0) {
            for (String key : params.keySet()) {
                if (params.get(key) instanceof File) {
                    File file = (File) params.get(key);
                    builder.addFormDataPart(key, file.getName(), RequestBody.create(MediaType.parse(fileType), file));
                    continue;
                }
                builder.addFormDataPart(key, params.get(key).toString());
            }
        }
        Request request = new Request
                .Builder()
                .url(url)
                .post(builder.build())
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
            int status = response.code();
            if (status == 200) {
                return Objects.requireNonNull(response.body()).string();
            }
        } catch (Exception e) {
//            logger.error("okhttp postFile error >> ex = {}", ExceptionUtils.getStackTrace(e));
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return responseBody;
    }
}
