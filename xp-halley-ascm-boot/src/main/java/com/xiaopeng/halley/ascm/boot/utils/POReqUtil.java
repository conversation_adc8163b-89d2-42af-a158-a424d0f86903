package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;

import java.util.Date;

public class POReqUtil {

    /**
     * 请求HEADER
     */
    public static JSONObject getHeadParams(String Receiver, String BusId) {
        JSONObject headParams = new JSONObject();
        //发送方
        headParams.put("SENDER", "ASCM");
        //接收方
        headParams.put("RECEIVER", Receiver);
        //发送时间
        headParams.put("DTSEND", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        //接口ID
        headParams.put("BUSID", BusId);
        //消息ID
        headParams.put("RECID", IdUtil.fastSimpleUUID());
        return headParams;
    }

    /**
     * 请求HEADER
     *
     * @param Sender
     * @param Receiver
     * @param BusId
     * @param sendId
     * @return
     */
    public static JSONObject getHeadParams(String Sender, String Receiver, String BusId, String sendId) {
        JSONObject headParams = new JSONObject();
        //发送方
        headParams.put("SENDER", Sender);
        //接收方
        headParams.put("RECEIVER", Receiver);
        //发送时间
        headParams.put("DTSEND", DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        //接口ID
        headParams.put("BUSID", BusId);
        //消息ID
        headParams.put("RECID", sendId);
        return headParams;
    }
}
