package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.LinkedHashMap;
import java.util.Map;

public class PageQuery<T> {
    protected long page = 1L;
    protected long size = 10L;
    protected long total = 0L;
    protected String keyword;
    protected Map<String, Boolean> orders = new LinkedHashMap();
    protected T param;

    public PageQuery() {
    }

    public <A> Page<A> toPage() {
        Page<A> pageRequest = new Page<>();
        pageRequest.setCurrent(this.page);
        pageRequest.setSize(this.size);
        if (!CollUtil.isEmpty(this.orders)) {
            this.orders.forEach((key, value) -> {
                OrderItem orderItem = new OrderItem();
                orderItem.setColumn(StrUtil.toUnderlineCase(key));
                orderItem.setAsc(value);
                pageRequest.addOrder(orderItem);
            });
        }
        return pageRequest;
    }

    public Page convertPage() {
        Page pageRequest = new Page();
        pageRequest.setCurrent(this.page);
        pageRequest.setSize(this.size);
        if (!CollUtil.isEmpty(this.orders)) {
            this.orders.forEach((key, value) -> {
                OrderItem orderItem = new OrderItem();
                orderItem.setColumn(StrUtil.toUnderlineCase(key));
                orderItem.setAsc(value);
                pageRequest.addOrder(orderItem);
            });
        }

        return pageRequest;
    }

    public long getPage() {
        return this.page;
    }

    public void setPage(final long page) {
        this.page = page;
    }

    public long getSize() {
        return this.size;
    }

    public void setSize(final long size) {
        this.size = size;
    }

    public String getKeyword() {
        return this.keyword;
    }

    public void setKeyword(final String keyword) {
        this.keyword = keyword;
    }

    public Map<String, Boolean> getOrders() {
        return this.orders;
    }

    public void setOrders(final Map<String, Boolean> orders) {
        this.orders = orders;
    }

    public T getParam() {
        return this.param;
    }

    public void setParam(final T param) {
        this.param = param;
    }

    @Override
    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof PageQuery)) {
            return false;
        } else {
            PageQuery<?> other = (PageQuery) o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.getPage() != other.getPage()) {
                return false;
            } else if (this.getSize() != other.getSize()) {
                return false;
            } else {
                label52:
                {
                    Object this$keyword = this.getKeyword();
                    Object other$keyword = other.getKeyword();
                    if (this$keyword == null) {
                        if (other$keyword == null) {
                            break label52;
                        }
                    } else if (this$keyword.equals(other$keyword)) {
                        break label52;
                    }

                    return false;
                }

                Object this$orders = this.getOrders();
                Object other$orders = other.getOrders();
                if (this$orders == null) {
                    if (other$orders != null) {
                        return false;
                    }
                } else if (!this$orders.equals(other$orders)) {
                    return false;
                }

                Object this$param = this.getParam();
                Object other$param = other.getParam();
                if (this$param == null) {
                    return other$param == null;
                } else {
                    return this$param.equals(other$param);
                }
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof PageQuery;
    }

    @Override
    public int hashCode() {
        //TODO:why?
//        int PRIME = true;
        boolean PRIME = true;
        int result = 1;
        long $page = this.getPage();
//        int result = result * 59 + (int)($page >>> 32 ^ $page);
        result = result * 59 + (int) ($page >>> 32 ^ $page);
        long $size = this.getSize();
        result = result * 59 + (int) ($size >>> 32 ^ $size);
        Object $keyword = this.getKeyword();
        result = result * 59 + ($keyword == null ? 43 : $keyword.hashCode());
        Object $orders = this.getOrders();
        result = result * 59 + ($orders == null ? 43 : $orders.hashCode());
        Object $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : $param.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "PageQuery(page=" + this.getPage() + ", size=" + this.getSize() + ", keyword=" + this.getKeyword() + ", orders=" + this.getOrders() + ", param=" + this.getParam() + ")";
    }

    public long getTotal() {
        return this.total;
    }

    public void setTotal(final long total) {
        this.total = total;
    }

}