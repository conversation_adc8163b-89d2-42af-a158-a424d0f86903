package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.experimental.UtilityClass;

import java.util.List;

@UtilityClass
public class PageUtils {
	public <T> Page<T> of(Page<?> page, List<T> data) {
		Page<T> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
		return result.setRecords(data);
	}

	public <T> Page<T> copyToPage(Page<?> page, Class<T> clazz) {
		List<T> list = BeanUtil.copyToList(page.getRecords(), clazz);
		return of(page, list);
	}
}
