package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.io.IoUtil;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.utils.PdfMerger;
import com.itextpdf.layout.Document;
import com.xiaopeng.halley.ascm.boot.printPDF.PageNumberEventHandler;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;

@Slf4j
@UtilityClass
public class PdfHelper {
	public byte[] FONT_DATA;
	private ConverterProperties converterProperties;

	static {
		try {
			// 加载字体资源支持中文
			DefaultFontProvider fontProvider = new DefaultFontProvider();
			FONT_DATA = IoUtil.readBytes(new ClassPathResource("fonts/msyh.ttf").getInputStream());
			fontProvider.addFont(FONT_DATA);
			converterProperties = new ConverterProperties();
			converterProperties.setFontProvider(fontProvider);
		} catch (Exception e) {
			log.error("PdfHelper init", e);
		}
	}

	public byte[] create(String html) throws Exception {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		PdfDocument pdfDocument = new PdfDocument(new PdfWriter(outputStream));
		PageNumberEventHandler eventHandler = new PageNumberEventHandler();
		pdfDocument.addEventHandler(PdfDocumentEvent.END_PAGE, eventHandler);

		Document document = HtmlConverter.convertToDocument(html, pdfDocument, converterProperties);
		eventHandler.writeTotalPages(pdfDocument);
		document.close(); // 确保文档关闭
		return outputStream.toByteArray();
	}

	public void merge(String html1, String html2, OutputStream outputStream) throws Exception {
		// 创建第一个PDF
		byte[] pdf1Bytes = create(html1);
		// 创建第二个PDF
		byte[] pdf2Bytes = create(html2);

		// 创建最终的PDF文档
		PdfWriter writer = new PdfWriter(outputStream);
		PdfDocument finalDoc = new PdfDocument(writer);

		// 读取源文档
		PdfDocument sourceDoc1 = new PdfDocument(new PdfReader(new ByteArrayInputStream(pdf1Bytes)));
		PdfDocument sourceDoc2 = new PdfDocument(new PdfReader(new ByteArrayInputStream(pdf2Bytes)));

		// 合并文档
		PdfMerger merger = new PdfMerger(finalDoc);
		merger.merge(sourceDoc1, 1, sourceDoc1.getNumberOfPages());
		merger.merge(sourceDoc2, 1, sourceDoc2.getNumberOfPages());
		// 关闭所有文档
		sourceDoc1.close();
		sourceDoc2.close();
		finalDoc.close();
	}
}
