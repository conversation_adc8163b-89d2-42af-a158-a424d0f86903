package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.util.StrUtil;

import java.util.Optional;

/**
 * 字符串工具
 *
 * <AUTHOR>
 * @Date 2022/4/11 11:55 PM
 */
public class StringTools {

    /**
     * 详情字符串内容校验，为空返回"-"
     *
     * @param str
     * @return
     */
    public static String infoDisplay(String str) {
        return Optional.ofNullable(str)
                .map(String::valueOf)
                .filter(StrUtil::isNotBlank)
                .orElse("-");
    }

    /**
     * 详情字符串内容校验，为空返回"-"
     *
     * @param o
     * @return
     */
    public static String infoDisplay(Optional o) {
        return Optional.of(o.orElse(""))
                .map(String::valueOf)
                .filter(StrUtil::isNotBlank)
                .orElse("-");
    }

}
