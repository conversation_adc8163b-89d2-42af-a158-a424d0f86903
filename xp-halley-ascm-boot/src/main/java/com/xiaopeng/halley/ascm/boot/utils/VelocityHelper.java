package com.xiaopeng.halley.ascm.boot.utils;

import lombok.experimental.UtilityClass;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.StringWriter;
import java.util.Map;

@UtilityClass
public class VelocityHelper {
	private final VelocityEngine velocityEngine;

	static {
		// 加载html模版
		velocityEngine = new VelocityEngine();
		velocityEngine.setProperty("resource.loader", "classpath");
		velocityEngine.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
	}

	public String render(String templateName, Map<String, Object> bindingMap) {
		StringWriter writer = new StringWriter();
		Template template = velocityEngine.getTemplate(templateName, "UTF-8");
		template.merge(new VelocityContext(bindingMap), writer);
		return writer.toString();
	}
}
