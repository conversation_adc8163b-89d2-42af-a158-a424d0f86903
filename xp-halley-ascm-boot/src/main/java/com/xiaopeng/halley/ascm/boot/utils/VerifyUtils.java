package com.xiaopeng.halley.ascm.boot.utils;

import java.util.regex.Pattern;

/**
 * 校验工具类
 *
 * <AUTHOR>
 * @Date 2022/4/13 9:50 PM
 */
public class VerifyUtils {
    /**
     * 正则表达式：验证手机号
     */
    public static final String REGEX_MOBILE = "^1(?:(?:3[\\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[25-7])|(?:7[0-8])|(?:8[\\d])|(?:9[\\d]))\\d{8}$";

    /**
     * 正则表达式：验证手机号长度
     */
    public static final String REGEX_MOBILE_LENGTH = "^[1][0-9]{10}$";

    /**
     * 正则表达式：验证邮箱
     */
    public static final String REGEX_EMAIL = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    /**
     * 正则表达式：验证汉字
     */
    public static final String REGEX_CHINESE = "^[\u4e00-\u9fa5],{0,}$";

    /**
     * 正则表达式：验证身份证
     */
    public static final String REGEX_ID_CARD = "(^\\d{18}$)|(^\\d{15}$)";

    /**
     * 正则表达式：车牌号
     */
    public static final String REGEX_CAR_PLATE = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
    /**
     * 正则表达式：中英文
     */
    public static final String REGEX_CHINESE_AND_ENGLISH = "^[\u4E00-\u9FA5A-Za-z]+$";
    /**
     * 正则表达式：正整数
     */
    public static final String REGEX_POSITIVE_INTEGER = "^[+]{0,1}(\\d+)$";

    /**
     * 正则表达式：通用邮箱
     */
    public static final String REGEX_NORMAL_EMAIL = "^[a-z0-9A-Z]+[- | a-z0-9A-Z . _]+@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-z]{2,}$";

    /**
     * 正则表达式：只有英文和数字
     */
    public static final String REGEX_ENGLISH_NUMBER = "^[A-Za-z0-9]+$";

    /**
     * 校验车牌号
     *
     * @param carPlate
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isCarPlate(String carPlate) {
        return Pattern.matches(REGEX_CAR_PLATE, carPlate);
    }

    /**
     * 校验通用邮箱
     *
     * @param email
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isNormalEmail(String email) {
        return Pattern.matches(REGEX_NORMAL_EMAIL, email);
    }

    /**
     * 校验正整数
     *
     * @param positiveInteger
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isPositiveInteger(String positiveInteger) {
        return Pattern.matches(REGEX_POSITIVE_INTEGER, positiveInteger);
    }

    /**
     * 校验手机号
     *
     * @param mobile
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isMobile(String mobile) {
        return Pattern.matches(REGEX_MOBILE, mobile);
    }

    /**
     * 校验中英文
     *
     * @param name
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isChineseAndEnglish(String name) {
        return Pattern.matches(REGEX_CHINESE_AND_ENGLISH, name);
    }

    /**
     * 校验邮箱
     *
     * @param email
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isEmail(String email) {
        return Pattern.matches(REGEX_EMAIL, email);
    }

    /**
     * 校验汉字
     *
     * @param chinese
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isChinese(String chinese) {
        return Pattern.matches(REGEX_CHINESE, chinese);
    }

    /**
     * 校验身份证
     *
     * @param idCard
     * @return 校验通过返回true，否则返回false
     */
    public static boolean isIDCard(String idCard) {
        return Pattern.matches(REGEX_ID_CARD, idCard);
    }

    public static boolean isEnglishOrNumber(String englishNumber) {
        return Pattern.matches(REGEX_ENGLISH_NUMBER, englishNumber);
    }

}
