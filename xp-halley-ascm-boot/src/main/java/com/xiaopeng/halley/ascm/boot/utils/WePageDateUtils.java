package com.xiaopeng.halley.ascm.boot.utils;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Date;

@Slf4j
public class WePageDateUtils {

    public static final String FORMAT_CLASSICAL_DATETIME = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_CLASSICAL_DATE_MINUTE = "yyyy/MM/dd HH:mm";
    public static final String FORMAT_CLASSICAL_DATE = "yyyy-MM-dd";
    public static final String FORMAT_CLASSICAL_TIME = "HH:mm:ss";
    public static final String FORMAT_CLASSICAL_DATETIME_ZH = "yyyy年MM月dd日 HH时mm分ss秒";
    public static final String FORMAT_CLASSICAL_DATE_ZH = "yyyy年MM月dd日";

    public static String translate(String formatClassicalDatetime, Object theDate) {

        if (theDate instanceof LocalDateTime) {
            String formarDateString = DateUtil.format((LocalDateTime) theDate, formatClassicalDatetime);
            return formarDateString;
        } else if (theDate instanceof Date) {
            String formarDateString = DateUtil.format((Date) theDate, formatClassicalDatetime);
            return formarDateString;
        } else {
            log.info("时间解析出错 [{}]", theDate.getClass());
            return "时间解析出错";
        }
    }

}