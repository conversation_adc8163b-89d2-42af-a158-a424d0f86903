package com.xiaopeng.halley.ascm.boot.utils.excel;

import com.xpeng.athena.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
public class CheckSizeUtil {

    public void checkExcelSize(MultipartFile file) {
        long fileSize = file.getSize();
        //检验导入大小
        if (5242880 < fileSize) {
            log.error("文件大于5MB");
            throw new BusinessException("文件大于5MB");
        }
        String originalFileName = file.getOriginalFilename();
        //获取最后一个.的位置
        int lastIndexOf = originalFileName.lastIndexOf(".");
        //获取文件的后缀名
        String suffix = originalFileName.substring(lastIndexOf);
//        String prefix = originalFileName.substring(0, lastIndexOf);
        //校验导入文件类型
        if (!".xls".equals(suffix) && !".xlsx".equals(suffix)) {
            log.error("文件类型不匹配==》{}", suffix);
            throw new BusinessException("文件类型不匹配==>" + suffix);
        }
    }
}
