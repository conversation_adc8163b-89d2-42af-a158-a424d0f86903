package com.xiaopeng.halley.ascm.boot.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 * @Date 2022/6/8 8:10 PM
 */
@Slf4j
@Service
public class ExcelTools {

    /**
     * 导入Excel
     *
     * @param multipartFile
     * @param clazz
     */
    public List<Object> importExcel(MultipartFile multipartFile, Class<?> clazz) {
        List<Object> excelData = new LinkedList<>();//将读取到的数据统一放到该List中

        try {
            ExcelListener listener = new ExcelListener();//**注意：每次读sheet要新new一个监听，否则会重复读取之前读过的sheet的数据
            EasyExcel.read(multipartFile.getInputStream(), clazz, listener).sheet(0).doRead();
            excelData.addAll(listener.getDatas());
        } catch (Exception e) {
            e.printStackTrace();
            log.error("ExcelUtils importExcel，Excel读取失败，e->{}", e);
        }

        return excelData;
    }

    /**
     * 导出Excel
     *
     * @param response
     * @param data
     * @param clazz
     */
    public void exportExcel(HttpServletResponse response, List<?> data, Class<?> clazz) {
        try {
            String fileName = URLEncoder.encode("Script_" + System.currentTimeMillis(), "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), clazz).excelType(ExcelTypeEnum.XLSX).sheet("sheet").doWrite(data);
//            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
//            WriteSheet writeSheet = EasyExcel.writerSheet(0, "运输司机").head(clazz).build();
//
//            excelWriter.write(data, writeSheet);
//            excelWriter.finish();

        } catch (IOException e) {
            e.getStackTrace();
        }
    }

}
