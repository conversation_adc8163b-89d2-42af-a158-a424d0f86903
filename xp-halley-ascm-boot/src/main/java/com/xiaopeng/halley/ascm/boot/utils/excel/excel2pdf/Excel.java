package com.xiaopeng.halley.ascm.boot.utils.excel.excel2pdf;

import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.IOException;
import java.io.InputStream;

/**
 * Excel 类用于处理 Excel 文件，提供对工作簿和工作表的访问。
 */
public class Excel {

    /**
     * 当前 Excel 文件的工作簿。
     */
    protected Workbook wb;

    /**
     * 当前 Excel 文件的工作表。
     */
    protected Sheet sheet;

    /**
     * 构造函数，通过输入流初始化 Excel 对象。
     *
     * @param is 包含 Excel 文件数据的输入流
     */
    public Excel(InputStream is) {
        try {
            // 创建工作簿
            this.wb = WorkbookFactory.create(is);
            // 获取活动工作表
            this.sheet = wb.getSheetAt(wb.getActiveSheetIndex());
        } catch (IOException e) {
            // 处理 IO 异常
            e.printStackTrace();
        }
    }

    /**
     * 获取当前工作表。
     *
     * @return 当前工作表对象
     */
    public Sheet getSheet() {
        return sheet;
    }

    /**
     * 获取当前工作簿。
     *
     * @return 当前工作簿对象
     */
    public Workbook getWorkbook() {
        return wb;
    }
}
