package com.xiaopeng.halley.ascm.boot.utils.excel.excel2pdf;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;

import java.io.IOException;
import java.io.OutputStream;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.List;

public class Excel2Pdf extends PdfTool {
    protected List<ExcelObject> objects = new ArrayList<ExcelObject>();

    /**
     * 导出单项PDF，不包含目录。
     *
     * @param object 需要导出的Excel对象
     * @param os     输出流，用于写入PDF文件
     */
    public Excel2Pdf(ExcelObject object, OutputStream os) {
        this.objects.add(object);
        this.os = os;
    }

    /**
     * 导出多项PDF，包含目录。
     *
     * @param objects 需要导出的Excel对象列表
     * @param os      输出流，用于写入PDF文件
     */
    public Excel2Pdf(List<ExcelObject> objects, OutputStream os) {
        this.objects = objects;
        this.os = os;
    }

    /**
     * 执行Excel到PDF的转换操作。
     *
     * @throws DocumentException     文档处理异常
     * @throws MalformedURLException URL格式异常
     * @throws IOException           IO异常
     */
    public void convert() throws DocumentException, MalformedURLException, IOException {
        getDocument().setPageSize(PageSize.A4.rotate());
        PdfWriter writer = PdfWriter.getInstance(getDocument(), os);
        writer.setPageEvent(new PDFPageEvent());
        getDocument().open();

        // 处理单个Excel对象
        if (this.objects.size() <= 1) {
            PdfPTable table = this.toCreatePdfTable(this.objects.get(0), getDocument(), writer);
            getDocument().add(table);
        }

        // 处理多个Excel对象
        if (this.objects.size() > 1) {
            toCreateContentIndexes(writer, this.getDocument(), this.objects);
            for (int i = 0; i < this.objects.size(); i++) {
                PdfPTable table = this.toCreatePdfTable(this.objects.get(i), getDocument(), writer);
                getDocument().add(table);
            }
        }

        getDocument().close();
    }

    /**
     * 创建PDF表格。
     *
     * @param object   Excel对象
     * @param document PDF文档对象
     * @param writer   PDF写入器
     * @return 生成的PDF表格
     * @throws IOException       IO异常
     * @throws DocumentException 文档处理异常
     */
    protected PdfPTable toCreatePdfTable(ExcelObject object, Document document, PdfWriter writer) throws IOException, DocumentException {
        PdfPTable table = new PdfTableExcel(object).getTable();
        table.setKeepTogether(true);
        table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        return table;
    }

    /**
     * 创建内容索引。
     *
     * @param writer   PDF写入器
     * @param document PDF文档对象
     * @param objects  Excel对象列表
     * @throws DocumentException 文档处理异常
     */
    protected void toCreateContentIndexes(PdfWriter writer, Document document, List<ExcelObject> objects) throws DocumentException {
        PdfPTable table = new PdfPTable(1);
        table.setKeepTogether(true);
        table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);

        Font font = new Font(Resource.BASE_FONT_CHINESE, 12, Font.NORMAL);
        font.setColor(new BaseColor(0, 0, 255));

        for (int i = 0; i < objects.size(); i++) {
            ExcelObject o = objects.get(i);
            String text = o.getAnchorName();
            Anchor anchor = new Anchor(text, font);
            anchor.setReference("#" + o.getAnchorName());

            PdfPCell cell = new PdfPCell(anchor);
            cell.setBorder(0);
            table.addCell(cell);
        }

        document.add(table);
    }

    /**
     * PDF页面事件处理类，用于控制页码。
     */
    private static class PDFPageEvent extends PdfPageEventHelper {
        public BaseFont baseFont;
        protected PdfTemplate template;

        @Override
        public void onStartPage(PdfWriter writer, Document document) {
            try {
                this.template = writer.getDirectContent().createTemplate(100, 100);
                this.baseFont = new Font(Resource.BASE_FONT_CHINESE, 8, Font.NORMAL).getBaseFont();
            } catch (Exception e) {
                throw new ExceptionConverter(e);
            }
        }

        @Override
        public void onEndPage(PdfWriter writer, Document document) {
            PdfContentByte byteContent = writer.getDirectContent();
            String text = "第" + writer.getPageNumber() + "页";
            float textWidth = this.baseFont.getWidthPoint(text, 8);
            float realWidth = document.right() - textWidth;

            byteContent.beginText();
            byteContent.setFontAndSize(this.baseFont, 10);
            byteContent.setTextMatrix(realWidth, document.bottom());
            byteContent.showText(text);
            byteContent.endText();
            byteContent.addTemplate(this.template, realWidth, document.bottom());
        }
    }
}
