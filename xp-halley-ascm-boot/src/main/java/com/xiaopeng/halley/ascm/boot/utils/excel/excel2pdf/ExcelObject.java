package com.xiaopeng.halley.ascm.boot.utils.excel.excel2pdf;

import java.io.InputStream;

public class ExcelObject {
    // POI Excel对象
    private final Excel excel;
    // 锚名称，用于标识Excel对象
    private String anchorName;
    // Excel文件的输入流
    private InputStream inputStream;

    /**
     * 构造函数，通过输入流初始化Excel对象
     *
     * @param inputStream Excel文件的输入流
     */
    public ExcelObject(InputStream inputStream) {
        this.inputStream = inputStream;
        this.excel = new Excel(this.inputStream);
    }

    /**
     * 构造函数，通过锚名称和输入流初始化Excel对象
     *
     * @param anchorName  锚名称
     * @param inputStream Excel文件的输入流
     */
    public ExcelObject(String anchorName, InputStream inputStream) {
        this.anchorName = anchorName;
        this.inputStream = inputStream;
        this.excel = new Excel(this.inputStream);
    }

    /**
     * 获取锚名称
     *
     * @return 锚名称
     */
    public String getAnchorName() {
        return anchorName;
    }

    /**
     * 设置锚名称
     *
     * @param anchorName 锚名称
     */
    public void setAnchorName(String anchorName) {
        this.anchorName = anchorName;
    }

    /**
     * 获取输入流
     *
     * @return Excel文件的输入流
     */
    public InputStream getInputStream() {
        return this.inputStream;
    }

    /**
     * 设置输入流
     *
     * @param inputStream Excel文件的输入流
     */
    public void setInputStream(InputStream inputStream) {
        this.inputStream = inputStream;
    }

    /**
     * 获取Excel对象
     *
     * @return POI Excel对象
     */
    Excel getExcel() {
        return excel;
    }
}
