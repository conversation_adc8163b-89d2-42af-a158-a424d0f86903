package com.xiaopeng.halley.ascm.boot.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.ANNOTATION_TYPE;
import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Documented
@Constraint(validatedBy = DatePatternCheckValidator.class)
@Target({FIELD, ANNOTATION_TYPE})
@Retention(RUNTIME)
public @interface DatePatternCheck {
	/**
	 * 字段名称
	 */
	String value() default "";

	String pattern() default "yyyy-MM-dd HH:mm:ss";

	Class<?>[] groups() default {};

	Class<? extends Payload>[] payload() default {};
}