package com.xiaopeng.halley.ascm.boot.validator;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class DatePatternCheckValidator implements ConstraintValidator<DatePatternCheck, String> {
	private String name;
	private String pattern;
	public static final String EMPTY_TEMPLATE = "{}不能为空";
	public static final String PARSE_TEMPLATE = "{}日期格式错误, 正确格式: {}";

	@Override
	public void initialize(DatePatternCheck datePattern) {
		this.name = datePattern.value();
		this.pattern = datePattern.pattern();
	}

	@Override
	public boolean isValid(String value, ConstraintValidatorContext context) {
		context.disableDefaultConstraintViolation();
		if (StrUtil.isBlank(value)) {
			return error(context, EMPTY_TEMPLATE, name);
		}
		try {
			DateUtil.parse(value, pattern);
			return true;
		} catch (Exception e) {
			return error(context, PARSE_TEMPLATE, name, pattern);
		}
	}

	public boolean error(ConstraintValidatorContext context, String template, Object... args) {
		context.buildConstraintViolationWithTemplate(StrUtil.format(template, args))
				.addConstraintViolation();
		return false;
	}
}

