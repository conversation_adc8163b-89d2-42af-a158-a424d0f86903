package com.xiaopeng.halley.ascm.boot.vo.miniProgram;

import lombok.Data;

import java.util.Date;

/**
 * 运单轨迹历史记录VO类
 */
@Data
public class BzWayBillTrackHistoryVO {

    /**
     * 运单编码
     */
    private String waybillCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 状态(0-初始点，1-转交，2-在途点，3-交付）
     */
    private String trackStatus;

    /**
     * 司机名称
     */
    private String driverName;

    /**
     * 司机手机号
     */
    private String driverPhone;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 运单创建时间
     */
    private Date waybillCreateTime;

}

