package com.xiaopeng.halley.ascm.boot.vo.miniProgram;

import lombok.Data;

/**
 * BzWayImageVO 类用于表示小程序中的图片信息。
 */
@Data
public class BzWayImageVO {

    /**
     * 文件唯一标识符，用于在系统中唯一标识该图片文件。
     */
    private String fileId;

    /**
     * 文件的展示URL，用于在前端展示该图片。
     */
    private String fileShowURL;

    /**
     * 上传类型，表示图片的上传方式（如：手动上传、自动上传等）。
     */
    private String uploadType;

    /**
     * 上传类型的展示文本，用于在前端展示上传类型的具体描述。
     */
    private String uploadTypeShow;

}
