package com.xiaopeng.halley.ascm.boot.vo.miniProgram;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 运单门店信息视图对象，用于表示运单相关的门店及仓库信息
 */
@Data
public class WayBillShopVO {
    /**
     * 运单号，唯一标识一个运单
     */
    private String waybillCode;

    /**
     * 总箱数量，表示该运单包含的箱子总数
     */
    private String totalBox;

    /**
     * 总体积，表示该运单所有箱子的总体积，单位通常为立方米
     */
    private String totalVolume;

    /**
     * 箱号列表，包含该运单下所有箱子的详细信息
     */
    private List<BoxVO> boxList;

    /**
     * 应送达日期，表示该运单计划送达的时间，格式为字符串
     */
    private String deliveryTime;

    /**
     * 门店名称，表示该运单对应的门店名称
     */
    private String shopName;

    /**
     * 门店省份，表示该运单对应的门店所在省份
     */
    private String shopProvince;

    /**
     * 门店城市，表示该运单对应的门店所在城市
     */
    private String shopCity;

    /**
     * 门店地址，表示该运单对应的门店详细地址
     */
    private String shopAddress;

    /**
     * 门店联系人，表示该运单对应的门店联系人姓名
     */
    private String shopContactPerson;

    /**
     * 门店联系电话，表示该运单对应的门店联系电话
     */
    private String shopContactNum;

    /**
     * 运单状态，表示该运单的当前状态
     */
    private String waybillStatus;

    /**
     * 送达时间，表示该运单实际送达的时间，格式为日期类型
     */
    private Date signTime;

    /**
     * 送达时间（字符串格式），用于展示或解析
     */
    private String parseSignTime;

    /**
     * 仓库名称，表示该运单对应的仓库名称
     */
    private String lgobe;

    /**
     * 仓库省份，表示该运单对应的仓库所在省份
     */
    private String warehouseProvince;

    /**
     * 仓库城市，表示该运单对应的仓库所在城市
     */
    private String warehouseCity;

    /**
     * 仓库地址，表示该运单对应的仓库详细地址
     */
    private String warehouseAddress;

    /**
     * 创建时间，表示该运单的创建时间，格式为日期类型
     */
    private Date createTime;

    /**
     * 图片信息列表，包含该运单相关的图片信息
     */
    private List<String> pictures;
}
