package com.xiaopeng.halley.ascm.boot.xxlJob;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.xiaopeng.halley.ascm.boot.common.enums.BaseEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.PrintTaskStatusEnum;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintOutTask;
import com.xiaopeng.halley.ascm.boot.service.BasePrintOutTaskService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class BasePrintTaskJob implements JobParamSupport {
	@Resource
	private BasePrintOutTaskService basePrintOutTaskService;

	@XxlJob("RefreshPrintTaskStatus")
	public void refreshPrintTaskStatus() {
		List<String> waybillCodes = this.getParamList(String.class);

		List<Integer> statusList = Arrays.asList(PrintTaskStatusEnum.FAIL.getValue(), PrintTaskStatusEnum.PRINTING.getValue());
		List<BasePrintOutTask> basePrintOutTasks = basePrintOutTaskService.lambdaQuery()
				.in(!waybillCodes.isEmpty(), BasePrintOutTask::getWaybillCode, waybillCodes)
				.in(BasePrintOutTask::getStatus, statusList)
				.isNotNull(BasePrintOutTask::getPrinterName)
				.lt(BasePrintOutTask::getRetryCount, 3)
				.list();

		Date now = new Date();
		for (BasePrintOutTask basePrintOutTask : basePrintOutTasks) {
			if (Objects.equals(basePrintOutTask.getStatus(), PrintTaskStatusEnum.FAIL.getValue()) || (
					Objects.equals(basePrintOutTask.getStatus(), PrintTaskStatusEnum.PRINTING.getValue())) && DateUtil.between(now, basePrintOutTask.getUpdateTime(), DateUnit.MINUTE) >= 15
			) {
				PrintTaskStatusEnum printTaskStatusEnum = BaseEnum.valueOf(PrintTaskStatusEnum.class, basePrintOutTask.getStatus());
				XxlJobHelper.log("打印任务状态刷新 {} -> 待打印, 运单号: {}" + printTaskStatusEnum.getLabel());
				basePrintOutTask.setRetryCount(basePrintOutTask.getRetryCount() + 1);
				basePrintOutTask.setStatus(PrintTaskStatusEnum.UN_PRINTED.getValue());
				basePrintOutTaskService.updateById(basePrintOutTask);
			}
		}
	}
}
