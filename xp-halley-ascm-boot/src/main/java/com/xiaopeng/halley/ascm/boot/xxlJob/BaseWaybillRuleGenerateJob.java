package com.xiaopeng.halley.ascm.boot.xxlJob;

import com.xiaopeng.halley.ascm.boot.service.AscmLockHelper;
import com.xiaopeng.halley.ascm.boot.service.BaseWaybillRulesService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class BaseWaybillRuleGenerateJob {

    @Resource
    private AscmLockHelper ascmLockHelper;
    @Resource
    private BaseWaybillRulesService baseWaybillRulesService;

    @XxlJob("BaseWaybillRuleGenerateJob")
    public void run() {
        log.info("BaseWaybillRuleGenerateJob run 开始刷新规则 ");

        //大锁
        String buildKey = "ascm:base:waybill:rule:66667777";
        if (ascmLockHelper.tryLockComboKey(buildKey)) {
            try {
                baseWaybillRulesService.generateRules();
            } finally {
                ascmLockHelper.unlockComboKey(buildKey);
            }

        }
    }
}
