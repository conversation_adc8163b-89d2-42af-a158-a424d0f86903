package com.xiaopeng.halley.ascm.boot.xxlJob;

import com.xiaopeng.halley.ascm.boot.service.BzAsyncExportLogService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class BzAsyncExportJob {

    @Resource
    private BzAsyncExportLogService bzAsyncExportLogService;

    @XxlJob("BzAsyncExportJob")
    public void run() {
        log.info("BzAsyncExportJob run 开始异步导出");

        bzAsyncExportLogService.export();
    }
}
