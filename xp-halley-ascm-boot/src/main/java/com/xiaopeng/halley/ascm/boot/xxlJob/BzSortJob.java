package com.xiaopeng.halley.ascm.boot.xxlJob;

import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class BzSortJob {

    @Resource
    private BzWaybillService bzWaybillService;

    @XxlJob("BzSortJob")
    public void run() {
        log.info("BzSortJob run 开始异步导出");

        bzWaybillService.updateSort();
    }
}
