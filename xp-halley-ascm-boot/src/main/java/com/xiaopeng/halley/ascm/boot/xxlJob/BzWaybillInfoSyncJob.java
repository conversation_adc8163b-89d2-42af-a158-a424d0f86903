package com.xiaopeng.halley.ascm.boot.xxlJob;

import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xiaopeng.halley.ascm.boot.dto.AsmWayBillReq;
import com.xiaopeng.halley.ascm.boot.dto.AsmWaybillFreshReq;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.feign.AssApiFeign;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillInfoSyncService;
import com.xiaopeng.halley.ascm.boot.service.third.DragonService;
import com.xpeng.athena.common.core.domain.Result;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.xxlJob
 * @Date 2024/7/4 16:17
 */
@Slf4j
@Component
public class BzWaybillInfoSyncJob {
	@Resource
	private AssApiFeign assApiFeign;
	@Resource
	private DragonService dragonService;
	@Resource
	private BzWaybillInfoSyncService bzWaybillInfoSyncService;

	/**
	 * 同步运单信息到骁龙
	 */
	@XxlJob("SyncWaybill")
	public void syncWaybill() {
		String jobParam = XxlJobHelper.getJobParam();
		List<String> filterWaybillCodes = StrUtil.isNotBlank(jobParam) ? JSON.parseArray(jobParam, String.class) : new ArrayList<>();

		XxlJobHelper.log("Start syncWaybill...");
		TimeInterval timeInterval = new TimeInterval().restart();
		Map<String, List<AsmWayBillReq>> waybillMap = dragonService.queryStoreWaybill(filterWaybillCodes);
		waybillMap.forEach((deliveryCode, list) -> {
			XxlJobHelper.log("syncWaybill请求: {}", JSON.toJSONString(list));
			log.info("syncWaybill请求: {}", JSON.toJSONString(list));
			Result<String> result = assApiFeign.syncWaybill(list);
			if (result.getCode() != 200) {
				XxlJobHelper.log("同步运单信息到骁龙失败！运单号: {}, 错误信息: {}", list.get(0).getWaybillCode(), result.getMsg());
				return;
			}
			// 修改运单状态为不需要同步
			dragonService.updateSync(list.stream().map(AsmWayBillReq::getWaybillCode).collect(Collectors.toList()));
		});
		XxlJobHelper.log("syncWaybill, 耗时: {}", timeInterval.intervalPretty());
	}

	/**
	 * 同步运单物流信息到骁龙
	 */
	@XxlJob("FreshWaybill")
	public void freshWaybill() {
		String jobParam = XxlJobHelper.getJobParam();
		List<String> filterWaybillCodes = StrUtil.isNotBlank(jobParam) ? JSON.parseArray(jobParam, String.class) : new ArrayList<>();

		XxlJobHelper.log("Start freshWaybill...");
		TimeInterval timeInterval = new TimeInterval().restart();
		for (AsmWaybillFreshReq asmWaybillFreshReq : dragonService.queryFreshWaybill(filterWaybillCodes)) {
			XxlJobHelper.log("freshWaybill请求: {}", JSON.toJSONString(asmWaybillFreshReq));
			log.info("freshWaybill请求: {}", JSON.toJSONString(asmWaybillFreshReq));
			Result<String> result = assApiFeign.freshWaybill(asmWaybillFreshReq);
			if (result.getCode() != 200) {
				XxlJobHelper.log("刷新运单信息到骁龙失败！运单号: {}, 错误信息: {}", asmWaybillFreshReq.getWayBillCode(), result.getMsg());
				continue;
			}
			// 修改运单状态为不需要同步
			dragonService.updateTrackSync(Collections.singleton(asmWaybillFreshReq.getWayBillCode()), false);
		}
		XxlJobHelper.log("freshWaybill, 耗时: {}", timeInterval.intervalPretty());
	}

	/**
	 * 实际发运时间同步erp
	 */
	@XxlJob("DispatchTimeSyncJob")
	public void dispatchTimeSyncRun() throws Exception {
		XxlJobHelper.log("Start Sync DispatchTime ...");
		try {
			bzWaybillInfoSyncService.dispatchTimeSync();
		} catch (Exception e) {
			log.error("同步实际发运时间异常 {}", e.getMessage());
			XxlJobHelper.log("Sync DispatchTime FAIL ... {}", e.getMessage());
			throw e;
		}
		XxlJobHelper.log("Sync DispatchTime OK ...");
	}

	/**
	 * 实际签收时间同步erp
	 */
	@XxlJob("SignedTimeSyncJob")
	public void signedTimeSyncRun() throws Exception {
		XxlJobHelper.log("Start Sync SignedTime ...");
		try {
			List<BzWaybill> bzWaybills = bzWaybillInfoSyncService.selectSyncData(2);
			bzWaybillInfoSyncService.signedTimeSync(bzWaybills);
		} catch (Exception e) {
			log.error("同步实际签收时间异常 {}", e.getMessage());
			XxlJobHelper.log("Sync SignedTime FAIL ... {}", e.getMessage());
			throw e;
		}
		XxlJobHelper.log("Sync SignedTime OK ...");
	}

}
