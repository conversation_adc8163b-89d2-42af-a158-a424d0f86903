package com.xiaopeng.halley.ascm.boot.xxlJob;

import com.xiaopeng.halley.ascm.boot.service.BzWaybillRetryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Package com.xiaopeng.halley.ascm.boot.xxlJob
 * @Date 2024/5/7 16:56
 */
@Slf4j
@Component
public class CleanOutdatedDataJob {

    @Resource
    private BzWaybillRetryService bzWaybillRetryService;

    /**
     * 1、简单任务示例（Bean模式）
     */
    @XxlJob("CleanOutdatedDataJob")
    public void demoJobHandler() throws Exception {
        XxlJobHelper.log("Start Clean ...");
        String jobParam = XxlJobHelper.getJobParam();
        try {
            bzWaybillRetryService.cleanOutdatedData(jobParam);
        } catch (Exception e) {
            XxlJobHelper.log("Clean FAIL ...");
        }
        XxlJobHelper.log("Clean OK ...");
    }
}
