package com.xiaopeng.halley.ascm.boot.xxlJob;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybill;
import com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillMapper;
import com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper;
import com.xiaopeng.halley.ascm.boot.service.AscmRedisHelper;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author：huqizhi
 * @Date：2023/8/16 11:38
 */
@Slf4j
@Service
public class GetTheTrueDepartureTimeJob {

    @Resource
    private AscmRedisHelper ascmRedisHelper;
    @Resource
    private BzWaybillMapper bzWaybillMapper;
    @Resource
    private BzWaybillTrackHistoryMapper bzWaybillTrackHistoryMapper;

    /**
     * 更新正确的运单发运时间
     *
     * @throws ResultException
     */
    @XxlJob("GetTheTrueDepartureTimeJob")
    public void getTrueTime() throws ResultException {
        log.info("GetTheTrueDepartureTimeJob getTrueTime 开始更新发车时间");

        //获取运输方式为快递，运输状态为运输中，有物流单号的运单
        LambdaQueryWrapper<BzWaybill> bwQueryWrapper = new LambdaQueryWrapper<>();
        bwQueryWrapper.eq(BzWaybill::getTransportType, "快递").eq(BzWaybill::getUpdateStatus, 0)
                .ne(BzWaybill::getLogisticsCode, "");
        Set<String> waybillCodes = bzWaybillMapper.selectList(bwQueryWrapper).stream().map(BzWaybill::getWaybillCode).collect(Collectors.toSet());
        log.info("GetTheTrueDepartureTimeJob getTrueTime waybillCodes:{}", JSON.toJSONString(waybillCodes));
        //获取快递的信息轨迹信息
        for (String waybillCode : waybillCodes) {
            //先查询出发货时间（第一条快递100回调的记录）
            LambdaQueryWrapper<BzWaybillTrackHistory> bwthQueryWrapper = new LambdaQueryWrapper<>();
            bwthQueryWrapper.eq(BzWaybillTrackHistory::getWaybillCode, waybillCode).orderByAsc(BzWaybillTrackHistory::getCreateTime).last("limit 1");
            List<BzWaybillTrackHistory> bzWaybillTrackHistories = bzWaybillTrackHistoryMapper.selectList(bwthQueryWrapper);
            //更新运单的实际发货时间
            log.info("GetTheTrueDepartureTimeJob getTrueTime list:{}", JSON.toJSONString(bzWaybillTrackHistories));
            if (CollUtil.isNotEmpty(bzWaybillTrackHistories)) {
                LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                //TODO  是否还需要更新一下过期时间
                updateWrapper.set(BzWaybill::getDepartureTime, bzWaybillTrackHistories.get(0).getCreateTime())
                        .set(BzWaybill::getUpdateStatus, 1)
                        .eq(BzWaybill::getWaybillCode, waybillCode);
                int update = bzWaybillMapper.update(null, updateWrapper);
                //如果更新成功，记录日志
                log.info("GetTheTrueDepartureTimeJob getTrueTime result:{}", update);
            }

        }

    }

    /**
     * 更新正确的运单签收时间
     */
    @XxlJob("UpdateactualArrivalTime")
    public void updateactualArrivalTime() {
        log.info("GetTheTrueDepartureTimeJob updateactualArrivalTime 开始更新实际到达时间");
        // 获取运输方式为快递，更新状态为1，是已完成的有物流单号的的有运单
        LambdaQueryWrapper<BzWaybill> bwQueryWrapper = new LambdaQueryWrapper<>();
        bwQueryWrapper.eq(BzWaybill::getTransportType, "快递")
                .eq(BzWaybill::getUpdateStatus, 1)
                .eq(BzWaybill::getStatus, "已完成")
                .ne(BzWaybill::getLogisticsCode, "");
        Set<String> waybillCodes = bzWaybillMapper.selectList(bwQueryWrapper).stream().map(BzWaybill::getWaybillCode).collect(Collectors.toSet());
        // 获取运单的轨迹信息
        for (String waybillCode : waybillCodes) {
            //先查询出收货时间（最后条快递100回调的记录）
            LambdaQueryWrapper<BzWaybillTrackHistory> bwthQueryWrapper = new LambdaQueryWrapper<>();
            bwthQueryWrapper.eq(BzWaybillTrackHistory::getWaybillCode, waybillCode).orderByDesc(BzWaybillTrackHistory::getCreateTime).last("limit 1");
            BzWaybillTrackHistory bzWaybillTrackHistory = bzWaybillTrackHistoryMapper.selectOne(bwthQueryWrapper);
            //更新运单的实际发货时间
            log.info("GetTheTrueDepartureTimeJob updateactualArrivalTime list:{}", JSON.toJSONString(bzWaybillTrackHistory));
            if (BeanUtil.isNotEmpty(bzWaybillTrackHistory)) {
                LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                //TODO  是否还需要更新一下过期时间
                updateWrapper.set(BzWaybill::getSignTime, bzWaybillTrackHistory.getCreateTime())
                        .set(BzWaybill::getUpdateStatus, 2)
                        .eq(BzWaybill::getWaybillCode, waybillCode);
                int update = bzWaybillMapper.update(null, updateWrapper);
                //如果更新成功，日志记录一下
                log.info("GetTheTrueDepartureTimeJob updateactualArrivalTime result:{}", update);
            }
        }

    }
}
