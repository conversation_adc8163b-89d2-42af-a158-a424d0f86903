package com.xiaopeng.halley.ascm.boot.xxlJob;

import cn.hutool.core.lang.Opt;
import cn.hutool.json.JSONUtil;
import com.xxl.job.core.context.XxlJobHelper;

import java.util.ArrayList;
import java.util.List;

public interface JobParamSupport {

	default <T> T getParam(Class<T> clazz) {
		return Opt.ofTry(() -> JSONUtil.toBean(XxlJobHelper.getJobParam(), clazz)).get();
	}

	default <T> T getParam(Class<T> clazz, T defaultValue) {
		return Opt.ofTry(() -> JSONUtil.toBean(XxlJobHelper.getJobParam(), clazz)).exceptionOrElse(defaultValue);
	}

	default <T> List<T> getParamList(Class<T> clazz) {
		return Opt.ofTry(() -> JSONUtil.toList(XxlJobHelper.getJobParam(), clazz)).exceptionOrElse(new ArrayList<>());
	}
}
