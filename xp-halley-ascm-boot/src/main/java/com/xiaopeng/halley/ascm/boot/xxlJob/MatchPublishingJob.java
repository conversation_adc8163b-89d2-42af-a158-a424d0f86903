package com.xiaopeng.halley.ascm.boot.xxlJob;
import java.io.InputStream;
import java.util.Date;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xiaopeng.halley.ascm.boot.common.enums.POInvokeEnum;
import com.xiaopeng.halley.ascm.boot.common.enums.PrintTaskStatusEnum;
import com.xiaopeng.halley.ascm.boot.config.PrintConfig;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.service.*;
import com.xiaopeng.halley.ascm.boot.utils.DateUtils;
import com.xiaopeng.halley.ascm.boot.utils.POReqUtil;
import com.xpeng.athena.common.core.domain.Result;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MatchPublishingJob {

    @Resource
    private BzWaybillMapper bzWaybillMapper;

    @Resource
    private BasePrintOutTaskMapper basePrintOutTaskMapper;

    @Resource
    private AscmLockHelper ascmLockHelper;

    @Resource
    private BzWaybillBoxRelMapper bzWaybillBoxRelMapper;

    @Resource
    private BzErpReqBoxMapper bzErpReqBoxMapper;

    @Resource
    private BzErpReqMaterialRelMapper bzErpReqMaterialRelMapper;

    @Resource
    private PoRequestHelp egressGatewayFeign;

    @Resource
    private PrintConfig printConfig;
    @Resource
    private BzWaybillService bzWaybillService;
	@Resource
	private BasePrintConfigService basePrintConfigService;
	@Resource
	private ImageService imageService;
	@Resource
	private BasePrintOutTaskService basePrintOutTaskService;

    /**
     * 运单自动匹配（未发布-->已发布）
     */
    @XxlJob("MatchPublishingJob")
    public void run() {
        log.info("MatchPublishingJob 开始执行");
        Map result = bzWaybillService.publishAllWaybill();
        if (result.get("total").equals(0)) {
            log.info("xxl-job自动发布失败，无符合条件的运单进行发布");
        } else if (!Objects.equals(result.get("total"), result.get("succeed"))) {
            log.info("xxl-job自动发布失败，共" + result.get("total") + "条，发布" + result.get("succeed") + "条。");
        } else {
            log.info("xxl-job自动发布成功，共" + result.get("total") + "条，成功发布" + result.get("succeed") + "条。");
        }
    }

    public List<BasePrintOutTask> getNeedPrintTask(List<BasePrintOutTask> list) {
        // 查询总箱数为0的运单
        Set<String> waybillCodes = bzWaybillService.lambdaQuery()
                .in(BzWaybill::getWaybillCode, list.stream().map(BasePrintOutTask::getWaybillCode).collect(Collectors.toSet()))
                .le(BzWaybill::getTotalBox, 0)
                .list()
                .stream().map(BzWaybill::getWaybillCode).collect(Collectors.toSet());
        Map<Boolean, List<BasePrintOutTask>> map = list.stream().collect(Collectors.partitioningBy(e -> !waybillCodes.contains(e.getWaybillCode())));

        // 修改为无需打印
        List<Long> ids = map.get(false).stream().map(BasePrintOutTask::getId).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            basePrintOutTaskService.lambdaUpdate().in(BasePrintOutTask::getId, ids)
                    .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.NO_NEED_PRINT.getValue())
                    .update();
        }
        return map.get(true);
    }

    /**
     * 定时扫描打印
     */
    @XxlJob("PrintTaskJob")
    public void printTask() {
        log.info(" BzWaybillSchedueld 启动,printTask-->开始查询需要打印的运单！");
        //查询出待打印的运单
        List<BasePrintOutTask> list = basePrintOutTaskMapper.selectList(new LambdaQueryWrapper<BasePrintOutTask>().eq(BasePrintOutTask::getStatus, 0)
                .eq(BasePrintOutTask::getIsDelete, 0));
        if (list.isEmpty()) {
            log.info(" BzWaybillSchedueld 启动,printTask-->暂无需要打印的运单！");
            return;
        }
        list = getNeedPrintTask(list);
        int updateCount = 0;
        for (BasePrintOutTask basePrintOutTask : list) {
            if (printConfig.getWarehouseTypes().contains(basePrintOutTask.getWarehouseType())) {
                continue;
            }
            int update = basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                    .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.PRINTING.getValue())
                    .eq(BasePrintOutTask::getIsDelete, 0)
                    .eq(BasePrintOutTask::getId, basePrintOutTask.getId()));
            updateCount += update;
        }
        if (updateCount != list.size()) {
            log.error(" BzWaybillSchedueld 启动,printTask-->出现错误，存在打印记录状态变更失败！");
        }
        for (BasePrintOutTask basePrintOutTask : list) {
            if (printConfig.getWarehouseTypes().contains(basePrintOutTask.getWarehouseType())) {
                if (basePrintOutTask.getBaseFileId() == null) {
                    createPdfToOss(basePrintOutTask);
                }
                continue;
            }
            try {
                //运单的合计件数
                Integer totalCount = 0;
                //打印设置
                String printSetting = "30";
                log.info(" BzWaybillSchedueld 启动,printTask-->开始执行运单：" + basePrintOutTask.getWaybillCode() + "的打印任务！");
                //搜集打印信息
                //运单信息
                Map mapBzWaybill = bzWaybillMapper.printBzWaybillMsg(basePrintOutTask.getWaybillCode());
                if (ObjectUtil.isEmpty(mapBzWaybill)) {
                    basePrintOutTask.setStatus(3);//打印失败
                    basePrintOutTaskMapper.updateById(basePrintOutTask);
                    continue;
                }
                mapBzWaybill.put("ZYL01", printConfig.getUrl() + mapBzWaybill.get("ZYDBH"));
                if (StrUtil.isAllNotBlank(basePrintOutTask.getUpdateUserId(), basePrintOutTask.getUpdateUserName())) {
                    mapBzWaybill.put("ZYL02", basePrintOutTask.getUpdateUserId());
                    mapBzWaybill.put("ZYL03", basePrintOutTask.getUpdateUserName());
                } else {
                    mapBzWaybill.put("ZYL02", basePrintOutTask.getCreateUserId());
                    mapBzWaybill.put("ZYL03", basePrintOutTask.getCreateUserName());
                }

                log.info("printUrl->{}", printConfig.getUrl());
                //箱号信息
                //查询出该运单下现有箱号
                List<BzWaybillBoxRel> bzWaybillBoxRels = bzWaybillBoxRelMapper.selectList(new LambdaQueryWrapper<BzWaybillBoxRel>()
                        .eq(BzWaybillBoxRel::getWaybillCode, basePrintOutTask.getWaybillCode())
                        .eq(BzWaybillBoxRel::getIsDevanning, 0)
                        .eq(BzWaybillBoxRel::getIsDelete, 0));
                if (bzWaybillBoxRels.size() <= 0) {
                    log.info(" BzWaybillSchedueld 启动,printTask-->运单：" + basePrintOutTask.getWaybillCode() + "下无箱子，该运单打印任务取消");
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.FAIL.getValue())
                            .eq(BasePrintOutTask::getIsDelete, 0)
                            .eq(BasePrintOutTask::getId, basePrintOutTask.getId()));
                    System.out.println(basePrintOutTask.getWaybillCode() + "下无箱子1，该运单打印任务取消");
                    continue;
                }
                List<String> boxCodeList = bzWaybillBoxRels.stream().map(BzWaybillBoxRel::getBoxCode).collect(Collectors.toList());
                //查询箱号信息
                List<Map> mapBox = bzErpReqBoxMapper.printBoxMsg(boxCodeList);
                if (mapBox.size() <= 0) {
                    log.info(" BzWaybillSchedueld 启动,printTask-->运单：" + basePrintOutTask.getWaybillCode() + "下无箱子，该运单打印任务取消");
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.SUCCESS.getValue())
                            .eq(BasePrintOutTask::getIsDelete, 0)
                            .eq(BasePrintOutTask::getId, basePrintOutTask.getId()));
                    System.out.println(basePrintOutTask.getWaybillCode() + "下无箱子2，该运单打印任务取消");
                    continue;
                }
                for (Map box : mapBox) {
                    String boxCode = box.get("CARTON").toString();
                    //物料信息
                    List<Map> mapMaterialRel = bzErpReqMaterialRelMapper.printMaterialRelMsg(boxCode);
                    if (mapMaterialRel.size() <= 0) {
                        log.info(" BzWaybillSchedueld 启动,printTask-->运单：" + basePrintOutTask.getWaybillCode() + "下无物料，该运单打印任务取消");
                        basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                                .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.SUCCESS.getValue())
                                .eq(BasePrintOutTask::getIsDelete, 0)
                                .eq(BasePrintOutTask::getId, basePrintOutTask.getId()));
                        System.out.println(basePrintOutTask.getWaybillCode() + "下无物料，该运单打印任务取消");
                        continue;
                    }
                    List<Integer> integers = mapMaterialRel.stream().map(e -> Integer.parseInt(e.get("QTY").toString())).collect(Collectors.toList());
                    for (Integer integer : integers) {
                        totalCount += integer;
                    }
                    box.put("DELIVERY_ORDER", mapMaterialRel);
                }
                mapBzWaybill.put("ZJS", String.valueOf(totalCount));
                mapBzWaybill.put("ZDYSZ", printSetting);
                mapBzWaybill.put("CARTON", mapBox);

                ArrayList<Object> arrayList = new ArrayList<>();
                arrayList.add(mapBzWaybill);

                //进行数据封装
                String sendId = UUID.fastUUID().toString(true);

                // 这个地方做下区分，如果是2021捷富恺的，Receiver走SMGL
                //  lgort 为2021的时候更换
                JSONObject headParams = null;

                if ("2021".equals(mapBzWaybill.get("LGORT"))) {
                    headParams = POReqUtil.getHeadParams("ASCM", "SMGL", "ASCM0040", sendId);
                } else {
                    headParams = POReqUtil.getHeadParams("ASCM", "WMS", "ASCM0040", sendId);
                }

                JSONObject params = new JSONObject();
                params.put("HEADER", headParams);
                params.put("DATA", arrayList);

                //TODO:调用WMS接口
                EgressGatewayRequest request = new EgressGatewayRequest();
                request.setAppId("xp-halley-ascm");
                request.setApiCode(POInvokeEnum.SI_ASCM0040_Syn_Out.getApiCode());
                request.setData(params);
                log.info("打印接口传输数据！运单" + basePrintOutTask.getWaybillCode() + "数据" + params);
                Result result = null;
                try {
                    result = egressGatewayFeign.invoke(request);
                } catch (Exception e) {
                    log.error("打印接口异常！运单" + basePrintOutTask.getWaybillCode() + "状态回退。");
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.UN_PRINTED.getValue()).
                            eq(BasePrintOutTask::getIsDelete, 0).
                            eq(BasePrintOutTask::getWaybillCode, basePrintOutTask.getWaybillCode()));
                    System.out.println(basePrintOutTask.getWaybillCode() + "超时，该运单打印任务取消");
                    e.printStackTrace();
                    continue;
                }

                if (ObjectUtil.isNull(result)) {
                    log.info("运单：" + basePrintOutTask.getWaybillCode() + "打印失败！");
                    //将打印记录修改成失败
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.FAIL.getValue())
//                            .set(BasePrintOutTask::getCount, basePrintOutTask.getCount()+1)
                            .eq(BasePrintOutTask::getIsDelete, 0)
                            .eq(BasePrintOutTask::getWaybillCode, basePrintOutTask.getWaybillCode()));
                    log.error("运单[{}]打印任务失败", basePrintOutTask.getWaybillCode());
                    continue;
                }

                String dataString = JSON.toJSONString(result.getData());
                JSONObject jsonObject = JSONObject.parseObject(dataString);
                JSONArray jsonArray = jsonObject.getJSONArray("DATA");
//            Map MapReturn=new HashMap();
                Boolean flag = true;
                if (jsonArray != null) {
                    for (Object o : jsonArray) {
                        Map map = (Map) o;
                        if (!"S".equals(map.get("ZSTATUS"))) {
                            flag = false;
                        }
                    }
                }
                System.out.println("dataString" + dataString);
//            SynReturnDTo returnResult = JSONObject.parseObject(String.valueOf(jsonArray), SynReturnDTo.class);

//            if (ObjectUtil.isNotNull(returnResult)) {
                if (!flag) {
                    log.error("运单：" + basePrintOutTask.getWaybillCode() + "打印传回状态“E”,失败！");
                    //将打印记录修改成失败
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.FAIL.getValue()).
                            eq(BasePrintOutTask::getIsDelete, 0).
                            eq(BasePrintOutTask::getWaybillCode, basePrintOutTask.getWaybillCode()));
                    System.out.println(basePrintOutTask.getWaybillCode() + "回传E，该运单打印任务失败");
                    continue;

                } else {
                    log.info("运单：" + basePrintOutTask.getWaybillCode() + "打印成功！");
                    //将打印记录修改成完成
                    basePrintOutTaskMapper.update(null, new LambdaUpdateWrapper<BasePrintOutTask>()
                            .set(BasePrintOutTask::getStatus, PrintTaskStatusEnum.SUCCESS.getValue()).set(BasePrintOutTask::getCount, basePrintOutTask.getCount() + 1).
                            eq(BasePrintOutTask::getIsDelete, 0).eq(BasePrintOutTask::getWaybillCode, basePrintOutTask.getWaybillCode()));
                    continue;
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("运单：" + basePrintOutTask.getWaybillCode() + "失败！");
                continue;
            }
        }
    }

    public void createPdfToOss(BasePrintOutTask basePrintOutTask) {
	    try {
            String printerName = basePrintConfigService.getPrinterName(basePrintOutTask.getLgort(), basePrintOutTask.getRoute());
            if (printerName == null) {
                return;
            }
            basePrintOutTask.setPrinterName(printerName);
            // 生成pdf文件
            InputStream inputStream = basePrintOutTaskService.createPdf(basePrintOutTask.getWaybillCode(), basePrintOutTask.getUpdateTime());
            // 上传到oss
            BaseFile baseFile = imageService.putFileToServer(StrUtil.format("运输单-{}.pdf", basePrintOutTask.getWaybillCode()), inputStream);
            basePrintOutTask.setBaseFileId(baseFile.getId());
            basePrintOutTaskService.updateById(basePrintOutTask);
        } catch (Exception e) {
		    log.error(StrUtil.format("打印任务生成pdf异常, waybillCode: {}", basePrintOutTask.getWaybillCode()), e);
	    }
    }

    /**
     * 定时扫描运单 逻辑删除没有箱号信息的运单信息
     */
    @XxlJob("SelectBoxInfoJob")
    public void selectBoxInfo() {
        log.info(" BzWaybillSchedueld 启动,selectBoxInfo-->开始查询没有箱号信息的运单信息！");
        //查询过滤所有包装完成且没有总箱数的运单信息
        QueryWrapper<BzWaybill> queryWrapperWaybill = new QueryWrapper<>();
        queryWrapperWaybill.eq("is_complete_packing", 1);
        queryWrapperWaybill.eq("total_box", 0);
        List<BzWaybill> selectBox = bzWaybillMapper.selectList(queryWrapperWaybill);
        log.info(" BzWaybillSchedueld 启动,selectBoxInfo-->开始查询所有包装完成且没有总箱数的运单信息！" + selectBox);
//        List<BzWaybill> selectBox1 = bzWaybillMapper.selectList(null).stream().filter(e -> e.getIsCompletePacking().equals(1) && e.getTotalBox() == 0).collect(Collectors.toList());
//        List<BzWaybill> selectBox = bzWaybillAll.stream().filter(e -> e.getIsCompletePacking().equals(1) && e.getTotalBox() == 0).collect(Collectors.toList());
        //查询运单箱号关联下是否有箱子
        for (int i = 0; i < selectBox.size(); i++) {
//            QueryWrapper<BzWaybillBoxRel> queryWrapper = new QueryWrapper<>();
//            queryWrapper.eq("waybill_code", selectBox.get(i).getWaybillCode());
//            //运单号查询运单箱号关联信息
//            List<BzWaybillBoxRel> bzBoxExist = bzWaybillBoxRelMapper.selectList(queryWrapper);
//            //找出拆箱完成的运单
//            List<BzWaybillBoxRel> deleteDate = bzBoxExist.stream().filter(e -> e.getIsDevanning()==1).collect(Collectors.toList());
//            if(CollUtil.isNotEmpty(deleteDate)){
//                UpdateWrapper<BzWaybill> updateWrapper = new UpdateWrapper<>();
//                updateWrapper.set("is_delete", 1)
//                        .eq("waybill_code", deleteDate.get(0).getWaybillCode());
//                bzWaybillMapper.update(null,updateWrapper);
//            }
            LambdaQueryWrapper<BzWaybillBoxRel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BzWaybillBoxRel::getWaybillCode, selectBox.get(i).getWaybillCode())
                    .eq(BzWaybillBoxRel::getIsDevanning, 0)
                    .eq(BzWaybillBoxRel::getIsDelete, 0);
            List<BzWaybillBoxRel> bzBoxExist = bzWaybillBoxRelMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(bzBoxExist)) {//无脏数据
                UpdateWrapper<BzWaybill> updateWrapper = new UpdateWrapper<>();
                updateWrapper.set("is_delete", 1)
                        .eq("waybill_code", selectBox.get(i).getWaybillCode());
                bzWaybillMapper.update(null, updateWrapper);
            }
        }
    }

    /**
     * 定时扫描任务是否完成
     */
    @XxlJob("CheckBzWaybillExpiryTimeJob")
    public void checkBzWaybillExpiryTime() {
        log.info(" BzWaybillSchedueld 启动,开始查看运单是否超时:");
        LambdaQueryWrapper<BzWaybill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BzWaybill::getStatus, "运输中").eq(BzWaybill::getIsDelete, 0);
        List<BzWaybill> bzWaybills = bzWaybillMapper.selectList(queryWrapper);
        if (bzWaybills.size() > 0) {
            log.info(" BzWaybillSchedueld 运行中,发现超时运单:{}", bzWaybills);
            Date date = new Date();
            bzWaybills.forEach(e -> {
                Date expiryDate = e.getExpiryTime();
                if (expiryDate == null || "1970-01-01 08:00:00".equals(DateUtils.format(expiryDate, DateUtils.DateTimeFormatterEnum.FORMAT_DATE_TIME_STYLE_1))) {
                    log.info(" BzWaybillSchedueld 运行中,发现运单:" + e.getWaybillCode() + "过期时间为空");
                } else {
                    //超时，状态改为异常
                    if (date.getTime() > expiryDate.getTime()) {
                        LambdaUpdateWrapper<BzWaybill> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.set(BzWaybill::getStatus, "异常中")
                                .set(BzWaybill::getAbnormalCause, "运送超时")
                                .set(BzWaybill::getIsAbnormal, 1)
                                .eq(BzWaybill::getId, e.getId())
                                // 添加乐观锁，防止并发问题
                                .eq(BzWaybill::getStatus, e.getStatus());
                        bzWaybillMapper.update(null, updateWrapper);
                    }
                }
            });
        } else {
            log.info(" BzWaybillSchedueld 运行中,未发现超时运单");
        }
    }

}
