package com.xpeng.athena.cloud.message.sdk.notify.support;

import cn.hutool.core.lang.UUID;
import cn.hutool.json.JSONUtil;
import com.xpeng.athena.cloud.message.sdk.dto.AppProperties;
import com.xpeng.athena.cloud.message.sdk.dto.MessageProperties;
import com.xpeng.athena.cloud.message.sdk.feign.MessageFeign;
import com.xpeng.athena.cloud.message.sdk.vo.ExceptionMessageVo;
import com.xpeng.athena.cloud.message.sdk.vo.MessageVo;
import com.xpeng.athena.cloud.message.sdk.vo.NotifyMessageVo;
import com.xpeng.athena.common.core.domain.ResultEnums;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
public class MessageHelper {
    private static final Logger log = LoggerFactory.getLogger(MessageHelper.class);
    @Resource
    private AppProperties appProperties;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private MessageProperties messageProperties;

    public MessageHelper() {
    }

    @Async
    public void sendExceptionMessage(Exception e) {
        e.printStackTrace();
    }

    @Async
    public void send(String messageName, Map<String, Object> messageData) {

    }

    @Async
    public void edit(String messageName, Map<String, Object> messageData) {

    }
}
