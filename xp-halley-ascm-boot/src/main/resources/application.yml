spring:
  application:
    name: xp-halley-ascm-boot
  cache:
    type: redis
    redis:
      key-prefix: "halley-ascm:cache:"
      time-to-live: 1000000
      cache-null-values: true
  profiles:
    include: dev
eureka:
  client:
    enabled: true
    register-with-eureka: true
    fetch-registry: true
    service-url:
      defaultZone: http://logan:<EMAIL>/eureka
    registry-fetch-interval-seconds: 10
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 5
    lease-expiration-duration-in-seconds: 20

#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.log4j2.Log4j2Impl  # 指定日志输出实现类
#    log-level: debug  # 设置日志级别
#
#logging:
#  level:
#    root: debug  # 设置全局日志级别
#    org:
#      apache:
#        ibatis: debug  # 设置 Mybatis 日志级别
logging:
  level:
    com.xiaopeng.halley.ascm.boot.mapper: DEBUG

mbp:
  svc:
    url: http://mbp-sit.test.xiaopeng.com
  secure:
    white-list:
      - /ssoAuthSdk/**

xiaopeng:
  xtiger:
    sso:
      appid: halley-mbp
      frontendHost: http://halley-v2.test.xiaopeng.local
      serverHost: http://halley-v2.test.xiaopeng.local/mqi-halley-api/xp-halley-ascm-boot
      issuer: https://sso.test.xiaopeng.com/sso
      webauth:
        enabled: true
      appauth:
        enabled: true
      feignClient:
        host: https://sso.test.xiaopeng.com/sso
      logoutUrl: https://sso.test.xiaopeng.com/sso_login_page/logout.html

export:
  pageSize: 1000

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl