spring:
#  autoconfigure:
#    exclude:
#      - com.xpeng.athena.cloud.message.sdk.MessageNotifyConfig
#      - com.xpeng.athena.cloud.message.sdk.MessageMonitorConfig
  servlet:
    multipart:
      max-file-size: 5000MB  #单个文件的最大大小
      max-request-size: 5000MB
  main:
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  application:
    name: xp-ascm-boot
  datasource:
    #    url: **************************************************************************************************** #本地测试
    url: ************************************************************************************************** #部署使用
    username: xp_ascm
    password: AoH0pAh9xhTlD9h_
  redis:
    host: r-bp1cvsz9n3peubt7krpd.redis.rds.aliyuncs.com # 连接地址
    port: 6379
    password: PVygR8peP6Mc1FPj #密码
    timeout: 15000
    database: 3
  zipkin:
    enabled: false
  kafka:
    bootstrap-servers: ***********:9092,***********:9092,***********:9092
    consumer:
      enable-auto-commit: false
      auto-commit-interval: 100ms
      auto-offset-reset: latest
      max-poll-records: 10
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      group-id: CID_alikafka_ascm
      properties:
        session.timeout.ms: 50000
        max.poll.interval.ms: 600000
    listener:
      concurrency: 3
      type: single
      ack-mode: manual_immediate
      poll-timeout: 1000
    producer:
      acks: all
      retries: 0
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      properties:
        interceptor.classes: com.xiaopeng.halley.ascm.boot.kafka.KafkaProducerInterceptor
        linger.ms: 1
        session.timeout.ms: 50000
    template:
      default-topic: topic-ascm-sync-event-data
management:
  health:
    elasticsearch:
      enabled: false


# https://spre.oss-cn-hangzhou.aliyuncs.com/halley/module/ascm/%E4%B8%8B%E8%BD%BD%20(1).jpeg
cos:
  #阿里cos
  ali:
    endpoint: oss-cn-hangzhou.aliyuncs.com
    bucket: hd1-halley-static-test
    path: dev/module/ascm/
    stsEndpoint: sts.cn-hangzhou.aliyuncs.com
    roleArn: acs:ram::1484537100187225:role/oss-hd1-halley-static-test
    roleSessionName: oss-hd1-halley-static-test
    durationSeconds: 1800
    accessKeyId: LTAI5tGUyE4t7nkvHQwiWrT8
    accessKeySecret: ******************************

po:
  requestPrefixUrl: https://podev.xiaopeng.com/RESTAdapter
  userName: testuser
  password: Po123456


log:
  logExcludeList:

com:
  xiaopeng:
    ascm:
      wechat:
        appId: wx41db8d39d2d1886a
        appSecret: e373787e6a12533cbdc656dfc532a224
      localTest: false
      kafka:
        enable-sync-event-listener: true
        bootstrap-servers: 10.0.13.24:6667,10.0.13.25:6667,10.0.13.26:666
        #        bootstrap-servers: k8s.3d.xiaopeng.local:32092, k8s.3d.xiaopeng.local:32093, k8s.3d.xiaopeng.local:32094
        group-id: CID_alikafka_ascm
        concurrency: 1
        pollRecords: 1
        pollTimeOut: 1000
        executor-thread-pool: 8
        linger_ms: 1000

fileTemp:
  #导入物流信息模板
  waybill:
    tempFileId: 507e6a7cb8af4b49a3cf8f2924c21854
    tempDeliveryFileId: 5f8c5bdbc4b04c2caa6da179ba501fc2
  wabillRules:
    tempFileId: 840edf1015c94e97b91c350f19fb060a
  shop:
    tempFileId: 3ce63cd254324d6c9fa1d3b0ad790d23
  TransportTimeliness:
    tempFileId: f6aab49f648e4993b087081fce095cbc
  actualTime:
    tempFileId: f2844a6b5d49485192c203551f0aeef0
  BaseBatteryProject:
    tempFileId: f2a4d287b66c4effaeb4c7bc489b80e9
  BaseBatteryRouteCost:
    tempFileId: da4a298e3bc547bca56520beda63a911
  shopBattery:
    tempFileId: 7976a4f847e841cea48e47743bf7430c
  BatteryWayBill:
    tempFileId: 07e93ece8b6b42d9af4e647f0aa379ef
  BaseCheckStandard:
    tempFileId: 5628c1cc7a3e4266914443a1ced8925a
  BaseMaterials:
    rulesTempFileId: 1276f506f65b40cda96ca56794e47f51
    packageTempFileId: 8ef72b6b67ea41fdae14ef3830e4a3d7

send:
  warehouseList: 2,4,5

waybillRule:
  orderType: 紧急=快递, 常规=零担

---
spring:
  profiles:
    active: local
  datasource:
    url: **************************************************************************************************** #本地测试
    #    url: ************************************************************************************************** #部署使用
    username: xp_ascm

print:
  warehouseTypes:
    - 5
  url: https://magicnote.cn/ascm/gop?waybillCode=

xiaopeng:
  xtiger:
    ssoauthsdk:
      replaceRequestFilter:
        enabled: false

xxl:
  job:
    accessToken:
    admin:
      addresses: http://xxljob.test.xiaopeng.local/xxl-job-admin
    executor:
      address:
      appname: xp-halley-ascm-boot
      ip:
      logretentiondays: 30
      logpath: ./logs/

delivery:
  drivePhone: 17855105701
  hddrivePhone: 18811944722
  whDrivePhone: 15827011995
  callbackUrl: https://halley.deploy-test.xiaopeng.com/mqi-halley-api/xp-halley-ascm-boot/halley/bzWaybill/callBackMethod
  selfCallbackUrl: https://halley.deploy-test.xiaopeng.com/mqi-halley-api/xp-halley-ascm-boot/halley/bzSelfWaybill/callBackMethod
  key: UhuTROtS4176
  customer: 8C8CCC3F166BFF62D3082B4282EEA4C0

address:
  gdKey: 5e71ebec1bbb77781e97d4a752b78a49

# 配置消息中心
athena:
  # 开放授权配置，请求头必须加入Client-Type为openid
  open-auth:
    list:
      - appId: qwertyuygfdsdfgy
        pathFilter: false
        paths:
          - /路径a
          - /路径b
          - /路径c
  # 白名单请求配置 Client-Type为feign
  white-auth:
    list:
      - ip: 127.0.0.1
        pathFilter: false
        paths:
          - /路径a
          - /路径b
          - /路径c
      - ip: 127.33.*
        pathFilter: false
        paths:
          - /路径a
          - /路径b
          - /路径c
      - ip: *************
        pathFilter: false
        paths:
          - /路径a
          - /路径b
          - /路径c
  message:
    api-check-switch: true
    exception-rule-name: "ascm_exception_collect_rule"
    message-name: "po_exception_message"
    kafka:
      producer:
        bootstrap-servers: ***********:9092,***********:9092,***********:9092
        acks: all
        retries: 0
        key-serializer: org.apache.kafka.common.serialization.StringSerializer
        value-serializer: org.apache.kafka.common.serialization.StringSerializer
        properties:
          interceptor.classes: com.xiaopeng.halley.ascm.boot.kafka.KafkaProducerInterceptor
          linger.ms: 1
          session.timeout.ms: 50000
      template:
        default-topic: CID_alikafka_message_monitor
  # 配置自动填充
  login:
    local: false
    user:
      userName: "admin"
      name: "本地管理员"

car-type-list: 4.2m,6.8m,9.6m,12.5m,16.5m

dragon:
  appName: xp-ass-part-cn-boot

shop-code:
  enable: true
  mapper-infos:
    - shop-city: 武汉市
      origin-shop-code: 2204
      mapper-shop-code: W2204

waybill:
  enableCheckBoxCount: true
wechat:
  auth:
    white-list:
      - /**/miniProgram/**

logging:
  level:
    com.itextpdf.layout.renderer: OFF