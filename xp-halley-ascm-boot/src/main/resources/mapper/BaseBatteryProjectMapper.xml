<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseBatteryProjectMapper">

    <select id="getPageTotal" resultType="long">
        select count(1) from base_battery_project
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseBatteryProjectVo">
        select * from base_battery_project
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            is_delete = 0
            <if test="param.packNums != null and param.packNums.size()>1">
                AND pack_num in
                <foreach item="item" collection="param.packNums" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.packNums != null and param.packNums.size()==1">
                AND pack_num like CONCAT('%',#{param.packNums[0]},'%')
            </if>

            <if test="param.packTypeNums != null and param.packTypeNums.size()>1">
                AND pack_type_num in
                <foreach item="item" collection="param.packTypeNums" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.packTypeNums != null and param.packTypeNums.size()==1">
                AND pack_type_num like CONCAT('%',#{param.packTypeNums[0]},'%')
            </if>

            <if test="param.projects != null and param.projects.size()>1">
                AND project in
                <foreach item="item" collection="param.projects" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.projects != null and param.projects.size()==1">
                AND project like CONCAT('%',#{param.projects[0]},'%')
            </if>
        </where>
    </sql>

</mapper>