<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseBatteryRouteCostMapper">

    <select id="getPageTotal" resultType="long">
        select count(1) from base_battery_route_cost
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseBatteryRouteCostVo">
        select * from base_battery_route_cost
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            is_delete = 0
            <if test="param.dispatchCities != null and param.dispatchCities.size()>1">
                AND dispatch_city in
                <foreach item="item" collection="param.dispatchCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.dispatchCities != null and param.dispatchCities.size()==1">
                AND dispatch_city like CONCAT('%',#{param.dispatchCities[0]},'%')
            </if>

            <if test="param.arrivalCities != null and param.arrivalCities.size()>1">
                AND arrival_city in
                <foreach item="item" collection="param.arrivalCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.arrivalCities != null and param.arrivalCities.size()==1">
                AND arrival_city like CONCAT('%',#{param.arrivalCities[0]},'%')
            </if>

            <if test="param.logisticsProviders != null and param.logisticsProviders.size()>1">
                AND logistics_provider in
                <foreach item="item" collection="param.logisticsProviders" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.logisticsProviders != null and param.logisticsProviders.size()==1">
                AND logistics_provider like CONCAT('%',#{param.logisticsProviders[0]},'%')
            </if>

            <if test="param.logisticsType != null and param.logisticsType != ''">
                AND logistics_type like CONCAT('%',#{param.logisticsType},'%')
            </if>
        </where>
    </sql>

    <select id="getLogisticsProvider" resultType="string">
        select distinct logistics_provider
        from base_battery_route_cost
        where arrival_city = #{arrivalCity}
          and dispatch_city = #{dispatchCity}
          and repair_center_num = #{repairCenterNum}
          and is_delete = 0;
    </select>

</mapper>