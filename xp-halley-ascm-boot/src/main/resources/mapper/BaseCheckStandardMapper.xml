<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseCheckStandardMapper">

    <select id="getPageTotal" resultType="long">
        select count(*)
        from base_check_standard as bcs
        <include refid="queryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseCheckStandardVo">
        select id,check_code,check_desc,check_docs,update_user_name,update_time
        from base_check_standard as bcs
        <include refid="queryCriteria"/>
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="queryCriteria">
        <where>
            <if test="param.checkCodes != null and param.checkCodes.size()>1">
                AND bcs.check_code in
                <foreach item="item" collection="param.checkCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.checkCodes != null and param.checkCodes.size()==1">
                AND bcs.check_code like CONCAT('%',#{param.checkCodes[0]},'%')
            </if>

            <if test="param.checkDesc != null and param.checkDesc != ''">
                AND bcs.check_desc like CONCAT('%',#{param.checkDesc},'%')
            </if>
        </where>
    </sql>

</mapper>