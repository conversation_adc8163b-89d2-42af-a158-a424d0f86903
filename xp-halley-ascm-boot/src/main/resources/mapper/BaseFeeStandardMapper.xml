<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseFeeStandardMapper">

    <select id="pageSummary"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardQuery"
            resultType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardSummary"
    >
        SELECT b.contract_code,
        operation_warehouse                                         as operation_warehouse,
        any_value(lgort)                                            as lgort,
        any_value(lgobe)                                            as lgobe,
        any_value(warehouse_city)                                   as warehouse_city,
        shop_code                                                   as shop_code,
        any_value(shop_city)                                        as shop_city,
        any_value(shop_name)                                        as shop_name,
        group_concat(DISTINCT a.waybill_code)                       as waybill_code,
        group_concat(DISTINCT bermr.delivery_order_code)            as delivery_order_code,
        DATE(a.departure_time)                                      as departure_time,
        any_value(transport_type)                                   as transport_type,
        any_value(c.min_fee)                                        as min_fee,
        any_value(c.unit_price)                                     as unit_price,
        any_value(c.first_weight_fee)                               as first_weight_fee,
        any_value(c.extra_weight_price)                             as extra_weight_price
        FROM bz_waybill a
        INNER JOIN
        bz_contract b
        ON a.lgort = b.operation_warehouse
        AND b.effective_time &lt;= DATE(a.departure_time)
        AND b.end_time >= DATE(a.departure_time)
        AND b.is_delete = 0
        LEFT JOIN
        base_fee_standard c
        ON b.contract_code = c.contract_code
        AND c.is_delete = 0
        AND a.warehouse_city = c.origin_city
        AND a.shop_city = c.destination_city
        AND c.type = #{param.type}
        <if test="param.type == 2">
            AND a.car_type = c.car_type
        </if>
        LEFT JOIN bz_waybill_box_rel bwbr ON a.waybill_code = bwbr.waybill_code
        LEFT JOIN bz_erp_req_material_rel bermr ON bwbr.box_code = bermr.box_code
        WHERE a.status in ('异常中', '运输中', '已完成') and a.is_delete = 0
        <if test="param.type == 1">
            and a.transport_type IN ('零担', '快递')
        </if>
        <if test="param.type == 2">
            and a.transport_type = '专车'
        </if>
        <if test="param.shopCodeList != null and param.shopCodeList.size() > 0">
            AND a.shop_code IN
            <foreach item="item" collection="param.shopCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.shopCode != null and param.shopCode != ''">
            AND a.shop_code LIKE CONCAT('%', #{param.shopCode}, '%')
        </if>
        <if test="param.contractCode != null and param.contractCode != ''">
            AND b.contract_code LIKE CONCAT('%', #{param.contractCode}, '%')
        </if>
        <if test="param.partners != null and param.partners != ''">
            AND b.partners LIKE CONCAT('%', #{param.partners}, '%')
        </if>
        <if test="param.startTime != null and param.endTime != null">
            AND a.departure_time BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.waybillCodes != null and param.waybillCodes.size() > 0">
            AND a.waybill_code IN
            <foreach item="item" collection="param.waybillCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.waybillCode != null and param.waybillCode != ''">
            AND a.waybill_code LIKE CONCAT('%', #{param.waybillCode}, '%')
        </if>
        <if test="param.deliveryOrderCodes != null and param.deliveryOrderCodes.size() > 0">
            AND bermr.delivery_order_code IN
            <foreach item="item" collection="param.deliveryOrderCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.transportType != null and param.transportType != ''">
            AND a.transport_type LIKE CONCAT('%', #{param.transportType}, '%')
        </if>
        <if test="param.transportTypes != null and param.transportTypes.size() > 0">
            AND a.transport_type IN
            <foreach item="item" collection="param.transportTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.lgort != null and param.lgort != ''">
            AND a.lgort = #{param.lgort}
        </if>
        <if test="param.operationWarehouse != null and param.operationWarehouse != ''">
            AND a.lgobe LIKE CONCAT('%', #{param.operationWarehouse}, '%')
        </if>
        <if test="param.operationWarehouses != null and param.operationWarehouses.size() > 0">
            AND a.lgort in
            <foreach item="item" collection="param.operationWarehouses" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.originCities != null and param.originCities.size() > 0">
            AND a.warehouse_city IN
            <foreach item="item" collection="param.originCities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.originCity != null and param.originCity != ''">
            AND a.warehouse_city LIKE CONCAT('%', #{param.originCity}, '%')
        </if>
        <if test="param.destinationCities != null and param.destinationCities.size() > 0">
            AND a.shop_city IN
            <foreach item="item" collection="param.destinationCities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.destinationCity != null and param.destinationCity != ''">
            AND a.shop_city LIKE CONCAT('%', #{param.destinationCity}, '%')
        </if>
        GROUP BY DATE(a.departure_time), b.contract_code, operation_warehouse, shop_code, transport_type
        ORDER BY departure_time DESC;
    </select>

    <select id="page"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardQuery"
            resultType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardVO"
    >
        SELECT
        bfs.id,
        bfs.contract_code,
        bfs.origin_city,
        bfs.destination_city,
        bfs.is_main_area,
        bfs.min_fee,
        bfs.unit_price,
        bfs.first_weight_fee,
        bfs.extra_weight_price,
        bc.operation_warehouse,
        bc.partners,
        bc.effective_time,
        bc.end_time,
        bfs.car_type,
        bfs.distance
        FROM
        base_fee_standard bfs
        LEFT JOIN
        bz_contract bc ON bfs.contract_code = bc.contract_code
        LEFT JOIN
        base_warehouse bw ON bc.operation_warehouse = bw.lgort and bw.is_delete = 0
        WHERE
        bfs.is_delete = 0
        AND bc.is_delete = 0
        <if test="param.type != null">
            and bfs.type = #{param.type}
        </if>
        <!-- 合同编号模糊查询 -->
        <if test="param.contractCode != null and param.contractCode != ''">
            AND bfs.contract_code LIKE CONCAT('%', #{param.contractCode}, '%')
        </if>
        <!-- 合作伙伴模糊查询 -->
        <if test="param.partners != null and param.partners != ''">
            AND bc.partners LIKE CONCAT('%', #{param.partners}, '%')
        </if>
        <!-- 合同日期筛选 -->
        <if test="param.startTime != null and param.endTime != null">
            AND effective_time &lt;= DATE_FORMAT(#{param.endTime},'%Y-%m-%d')
            AND end_time &gt;= DATE_FORMAT(#{param.startTime},'%Y-%m-%d')
        </if>
        <if test="param.startTime != null and param.endTime == null">
            AND effective_time &lt;= DATE_FORMAT(#{param.startTime},'%Y-%m-%d')
            AND end_time &gt;= DATE_FORMAT(#{param.startTime},'%Y-%m-%d')
        </if>
        <if test="param.startTime == null and param.endTime != null">
            AND effective_time &lt;= DATE_FORMAT(#{param.endTime},'%Y-%m-%d')
            AND end_time &gt;= DATE_FORMAT(#{param.endTime},'%Y-%m-%d')
        </if>
        <!-- 运营仓库模糊查询 -->
        <if test="param.operationWarehouse != null and param.operationWarehouse != ''">
            AND bw.lgobe LIKE CONCAT('%', #{param.operationWarehouse}, '%')
        </if>
        <if test="param.operationWarehouses != null and param.operationWarehouses.size() > 0">
            AND bw.lgort in
            <foreach item="item" collection="param.operationWarehouses" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.originCity != null and param.originCity != ''">
            AND bfs.origin_city LIKE CONCAT('%', #{param.originCity}, '%')
        </if>
        <if test="param.destinationCity != null and param.destinationCity != ''">
            AND bfs.destination_city LIKE CONCAT('%', #{param.destinationCity}, '%')
        </if>
        <!-- 发出城市/目标城市模糊查询+多选 -->
        <if test="param.originCities != null and param.originCities.size() > 0">
            AND bfs.origin_city IN
            <foreach collection="param.originCities" item="city" open="(" separator="," close=")">
                #{city}
            </foreach>
        </if>
        <if test="param.destinationCities != null and param.destinationCities.size() > 0">
            AND bfs.destination_city IN
            <foreach collection="param.destinationCities" item="city" open="(" separator="," close=")">
                #{city}
            </foreach>
        </if>
        <if test="param.cityKeyword != null and param.cityKeyword != ''">
            AND (bfs.origin_city LIKE CONCAT('%', #{param.cityKeyword}, '%')
            OR bfs.destination_city LIKE CONCAT('%', #{param.cityKeyword}, '%'))
        </if>
        ORDER BY
        bfs.id DESC
    </select>

    <select id="pageView"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardQuery"
            resultType="com.xiaopeng.halley.ascm.boot.dto.BaseFeeStandardView"
    >
        SELECT
        a.waybill_code,
        b.contract_code,
        a.lgort,
        a.lgobe,
        a.warehouse_city,
        a.shop_code,
        a.shop_city,
        a.shop_name,
        a.departure_time,
        a.transport_type,
        a.order_type,
        a.logistics_code,
        a.total_volume,
        a.total_weight,
        c.min_fee,
        c.unit_price,
        c.first_weight_fee,
        c.extra_weight_price,
        a.car_type,
        c.distance,
        a.driver_name,
        a.driver_phone,
        a.car_plate,
        IF(a.total_volume = 0, 0, GREATEST(a.total_volume * c.unit_price, c.min_fee)) as fee1,
        (c.first_weight_fee + (IF(a.total_weight &lt; 1, 1, a.total_weight) - 1) * c.extra_weight_price) as fee2,
        c.unit_price as fee3
        FROM
        bz_waybill a
        INNER JOIN
        bz_contract b ON a.lgort = b.operation_warehouse
        AND b.effective_time &lt;= DATE_FORMAT(a.departure_time,'%Y-%m-%d')
        AND b.end_time >= DATE_FORMAT(a.departure_time,'%Y-%m-%d')
        AND b.is_delete = 0
        LEFT JOIN
        base_fee_standard c ON b.contract_code = c.contract_code and c.is_delete = 0
        AND a.warehouse_city = c.origin_city
        AND a.shop_city = c.destination_city
        AND c.type = #{param.type}
        <if test="param.type == 2">
            AND a.car_type = c.car_type
        </if>
        <if test="param.deliveryOrderCodes != null and param.deliveryOrderCodes.size() > 0">
        LEFT JOIN
        bz_waybill_box_rel bwbr ON a.waybill_code = bwbr.waybill_code
        LEFT JOIN
        bz_erp_req_material_rel bermr ON bwbr.box_code = bermr.box_code
        </if>
        WHERE a.status in ('异常中', '运输中', '已完成') and a.is_delete = 0
        <if test="param.type == 1">
            and a.transport_type IN ('零担', '快递')
        </if>
        <if test="param.type == 2">
            and a.transport_type = '专车'
        </if>
        <if test="param.shopCodeList != null and param.shopCodeList.size() > 0">
            AND a.shop_code IN
            <foreach item="item" collection="param.shopCodeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.shopCode != null and param.shopCode != ''">
            AND a.shop_code LIKE CONCAT('%', #{param.shopCode}, '%')
        </if>
        <if test="param.contractCode != null and param.contractCode != ''">
            AND b.contract_code LIKE CONCAT('%', #{param.contractCode}, '%')
        </if>
        <if test="param.partners != null and param.partners != ''">
            AND b.partners LIKE CONCAT('%', #{param.partners}, '%')
        </if>
        <if test="param.startTime != null and param.endTime != null">
            AND a.departure_time BETWEEN #{param.startTime} AND #{param.endTime}
        </if>
        <if test="param.waybillCodes != null and param.waybillCodes.size() > 0">
            AND a.waybill_code IN
            <foreach item="item" collection="param.waybillCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.waybillCode != null and param.waybillCode != ''">
            AND a.waybill_code LIKE CONCAT('%', #{param.waybillCode}, '%')
        </if>
        <if test="param.deliveryOrderCodes != null and param.deliveryOrderCodes.size() > 0">
            AND bermr.delivery_order_code IN
            <foreach item="item" collection="param.deliveryOrderCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.transportType != null and param.transportType != ''">
            AND a.transport_type = #{param.transportType}
        </if>
        <if test="param.transportTypes != null and param.transportTypes.size() > 0">
            AND a.transport_type IN
            <foreach item="item" collection="param.transportTypes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.lgort != null and param.lgort != ''">
            AND a.lgort = #{param.lgort}
        </if>
        <if test="param.operationWarehouse != null and param.operationWarehouse != ''">
            AND a.lgobe LIKE CONCAT('%', #{param.operationWarehouse}, '%')
        </if>
        <if test="param.operationWarehouses != null and param.operationWarehouses.size() > 0">
            AND a.lgort in
            <foreach item="item" collection="param.operationWarehouses" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.originCities != null and param.originCities.size() > 0">
            AND a.warehouse_city IN
            <foreach item="item" collection="param.originCities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.originCity != null and param.originCity != ''">
            AND a.warehouse_city LIKE CONCAT('%', #{param.originCity}, '%')
        </if>
        <if test="param.destinationCities != null and param.destinationCities.size() > 0">
            AND a.shop_city IN
            <foreach item="item" collection="param.destinationCities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.destinationCity != null and param.destinationCity != ''">
            AND a.shop_city LIKE CONCAT('%', #{param.destinationCity}, '%')
        </if>
        order by departure_time desc
    </select>

</mapper>