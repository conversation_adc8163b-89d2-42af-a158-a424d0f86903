<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseFileMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.xiaopeng.halley.ascm.boot.entity.BaseFile" id="baseFileMap">
        <result property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="fileUuid" column="file_uuid"/>
        <result property="project" column="project"/>
        <result property="projectPath" column="project_path"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, file_name, file_uuid, project, project_path, create_user_id, create_user_name, update_user_id,
        update_user_name, is_delete, create_time, update_time
    </sql>


</mapper>