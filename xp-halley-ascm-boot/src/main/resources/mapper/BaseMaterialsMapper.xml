<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseMaterialsMapper">

    <resultMap id="BaseResultMap" type="com.xiaopeng.halley.ascm.boot.entity.BaseMaterials">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
        <result property="materialDesc" column="material_desc" jdbcType="VARCHAR"/>
        <result property="picLeft" column="pic_left" jdbcType="VARCHAR"/>
        <result property="picRight" column="pic_right" jdbcType="VARCHAR"/>
        <result property="picStamp" column="pic_Stamp" jdbcType="VARCHAR"/>
        <result property="picBefore" column="pic_before" jdbcType="VARCHAR"/>
        <result property="picAfter" column="pic_after" jdbcType="VARCHAR"/>
        <result property="picOther" column="pic_other" jdbcType="VARCHAR"/>
        <result property="picComplete" column="pic_complete" jdbcType="VARCHAR"/>
        <result property="unitPackage" column="unit_package" jdbcType="TINYINT"/>
        <result property="supplierPackage" column="supplier_package" jdbcType="VARCHAR"/>
        <result property="materialLength" column="material_length" jdbcType="VARCHAR"/>
        <result property="materialWidth" column="material_width" jdbcType="VARCHAR"/>
        <result property="materialHeight" column="material_height" jdbcType="VARCHAR"/>
        <result property="materialVolume" column="material_volume" jdbcType="VARCHAR"/>
        <result property="checkCodeId" column="check_code_id" jdbcType="TINYINT"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="version" column="version" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.GetMaterialPageResonseVo">
        select distinct a.id,
        a.material_code,
        a.material_desc,
        a.pic_left,
        a.pic_right,
        a.pic_Stamp,
        a.pic_before,
        a.pic_after,
        a.pic_other,
        a.pic_complete,
        case a.unit_package when 0 then '否' when '1' then '是' end as unit_package_str,
        a.supplier_package,
        f.check_docs,
        a.update_time,
        a.update_user_name
        from base_materials as a
        left join base_check_standard as f on a.check_code_id = f.id
        <include refid="QueryCriteriaJoinTable"/>
        where 1=1
        <include refid="QueryCriteria"/>
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>
    <select id="getPageTotal" resultType="long">
        select count(distinct material_code)
        from base_materials as a
        <include refid="QueryCriteriaJoinTable"/>
        where 1=1
        <include refid="QueryCriteria"/>
    </select>


    <sql id="QueryCriteriaJoinTable">

        <if test="(param.asnCodes != null and param.asnCodes.size()>1)||(param.asnCodes != null and param.asnCodes.size()==1)||(param.deliveryOrderCodes != null and param.deliveryOrderCodes.size()>1)||(param.deliveryOrderCodes != null and param.deliveryOrderCodes.size()==1)">
            left join bz_delivery_order_material as c on c.material_id = a.id
            left join bz_delivery_orders as e on e.id = c.order_id
        </if>


        <if test="(param.supplierCodes != null and param.supplierCodes.size()>1)||(param.supplierCodes != null and param.supplierCodes.size()==1)||(param.supplierName != null and param.supplierName != '')">
            left join bz_supplier_material as b on a.id = b.material_id
            left join base_suppliers as d on d.id = b.supplier_id
        </if>


    </sql>

    <sql id="QueryCriteria">
        <if test="param.materialCodes != null and param.materialCodes.size()>1">
            AND a.material_code in
            <foreach item="item" collection="param.materialCodes" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.materialCodes != null and param.materialCodes.size()==1">
            AND a.material_code like CONCAT('%',#{param.materialCodes[0]},'%')
        </if>

        <if test="param.asnCodes != null and param.asnCodes.size()>1">
            AND e.asn_code in
            <foreach item="item" collection="param.asnCodes" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.asnCodes != null and param.asnCodes.size()==1">
            AND e.asn_code like CONCAT('%',#{param.asnCodes[0]},'%')
        </if>

        <if test="param.deliveryOrderCodes != null and param.deliveryOrderCodes.size()>1">
            AND e.delivery_order_code in
            <foreach item="item" collection="param.deliveryOrderCodes" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.deliveryOrderCodes != null and param.deliveryOrderCodes.size()==1">
            AND e.delivery_order_code like CONCAT('%',#{param.deliveryOrderCodes[0]},'%')
        </if>

        <if test="param.supplierCodes != null and param.supplierCodes.size()>1">
            AND d.supplier_code in
            <foreach item="item" collection="param.supplierCodes" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.supplierCodes != null and param.supplierCodes.size()==1">
            AND d.supplier_code like CONCAT('%',#{param.supplierCodes[0]},'%')
        </if>

        <if test="param.materialDesc != null and param.materialDesc != ''">
            AND a.material_desc like CONCAT('%',#{param.materialDesc},'%')
        </if>

        <if test="param.supplierName != null and param.supplierName != ''">
            AND d.supplier_name like CONCAT('%',#{param.supplierName},'%')
        </if>
    </sql>

    <select id="getCheckPageTotal" resultType="long">
        select count(*)
        from base_materials as bm
        left join base_check_standard as bcs on bm.check_code_id = bcs.id
        <include refid="checkPageCondition"/>
    </select>

    <select id="getCheckPage" resultType="com.xiaopeng.halley.ascm.boot.dto.GetMaterialCheckPageResponseVo">
        select bm.id,
        bm.material_code,
        bm.material_desc,
        bcs.check_code,
        bcs.check_desc,
        case bm.unit_package when 0 then '否' when '1' then '是' end as unit_package_str,
        bcs.check_docs,
        bm.update_time,
        bm.update_user_name
        from base_materials as bm
        left join base_check_standard as bcs on bm.check_code_id = bcs.id
        <include refid="checkPageCondition"/>
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="checkPageCondition">
        <where>
            <if test="param.materialCodes != null and param.materialCodes.size()>1">
                AND bm.material_code in
                <foreach item="item" collection="param.materialCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.materialCodes != null and param.materialCodes.size()==1">
                AND bm.material_code like CONCAT('%',#{param.materialCodes[0]},'%')
            </if>
            <if test="param.materialDesc != null and param.materialDesc != ''">
                AND bm.material_desc like CONCAT('%',#{param.materialDesc},'%')
            </if>


            <if test="param.checkCodes != null and param.checkCodes.size()>1">
                AND bcs.check_code in
                <foreach item="item" collection="param.checkCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.checkCodes != null and param.checkCodes.size()==1">
                AND bcs.check_code like CONCAT('%',#{param.checkCodes[0]},'%')
            </if>

            <if test="param.checkDesc != null and param.checkDesc != ''">
                AND bcs.check_desc like CONCAT('%',#{param.checkDesc},'%')
            </if>

            <if test="param.checkCodeId != null and param.checkCodeId != 0">
                AND bm.check_code_id = #{param.checkCodeId}
            </if>
            <if test="param.checkCodeId != null and param.checkCodeId == 0">
                AND bm.check_code_id != -1
            </if>
        </where>
    </sql>

</mapper>
