<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseRepairCenterMapper">

    <select id="getPageTotal" resultType="long">
        select count(1) from base_repair_center
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseRepairCenterVo">
        select * from base_repair_center
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            is_delete = 0
            <if test="param.repairCenterNums != null and param.repairCenterNums.size()>1">
                AND repair_center_num in
                <foreach item="item" collection="param.repairCenterNums" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.repairCenterNums != null and param.repairCenterNums.size()==1">
                AND repair_center_num like CONCAT('%',#{param.repairCenterNums[0]},'%')
            </if>

            <if test="param.repairCenterNames != null and param.repairCenterNames.size()>1">
                AND repair_center_name in
                <foreach item="item" collection="param.repairCenterNames" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.repairCenterNames != null and param.repairCenterNames.size()==1">
                AND repair_center_name like CONCAT('%',#{param.repairCenterNames[0]},'%')
            </if>

            <if test="param.repairCenterProvinces != null and param.repairCenterProvinces.size()>1">
                AND repair_center_province in
                <foreach item="item" collection="param.repairCenterProvinces" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.repairCenterProvinces != null and param.repairCenterProvinces.size()==1">
                AND repair_center_province like CONCAT('%',#{param.repairCenterProvinces[0]},'%')
            </if>

            <if test="param.repairCenterCities != null and param.repairCenterCities.size()>1">
                AND repair_center_city in
                <foreach item="item" collection="param.repairCenterCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.repairCenterCities != null and param.repairCenterCities.size()==1">
                AND repair_center_city like CONCAT('%',#{param.repairCenterCities[0]},'%')
            </if>

            <if test="param.contactNums != null and param.contactNums.size()>1">
                AND contact_num in
                <foreach item="item" collection="param.contactNums" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.contactNums != null and param.contactNums.size()==1">
                AND contact_num like CONCAT('%',#{param.contactNums[0]},'%')
            </if>

            <if test="param.contactPeople != null and param.contactPeople.size()>1">
                AND contact_person in
                <foreach item="item" collection="param.contactPeople" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.contactPeople != null and param.contactPeople.size()==1">
                AND contact_person like CONCAT('%',#{param.contactPeople[0]},'%')
            </if>

            <if test="param.status != null and param.status != ''">
                AND status = #{param.status}
            </if>

            <if test="param.statrCreateTime != null and param.endCreateTime != null">
                and create_time between #{param.statrCreateTime} and #{param.endCreateTime}
            </if>
        </where>
    </sql>

</mapper>