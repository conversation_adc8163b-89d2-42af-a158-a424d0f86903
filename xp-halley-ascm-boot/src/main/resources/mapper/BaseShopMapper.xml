<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseShopMapper">

    <select id="selectByShopCity" resultType="java.util.Map">
        select shop_code, shop_province, shop_name, shop_address
        from base_shop
        where shop_city = #{city}
    </select>

    <select id="getPageTotal" resultType="long">
        select COUNT(1)
        from base_shop
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseShopVo">
        select id,xl_shop_code,xl_shop_remark,shop_code,shop_name,shop_remark,
        shop_province,shop_city,shop_address,contact_num,contact_person,
        lng,lat,fence_range,status,create_user_name,update_user_name,
        create_time,update_time,dynamic_update,cargo_contact,cargo_contact_phone,region from base_shop
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            is_delete = 0
            <if test="param.shopCodes != null and param.shopCodes.size()>1">
                AND shop_code in
                <foreach item="item" collection="param.shopCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodes != null and param.shopCodes.size()==1">
                AND shop_code like CONCAT('%',#{param.shopCodes[0]},'%')
            </if>

            <if test="param.shopNames != null and param.shopNames.size()>1">
                AND shop_name in
                <foreach item="item" collection="param.shopNames" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopNames != null and param.shopNames.size()==1">
                AND shop_name like CONCAT('%',#{param.shopNames[0]},'%')
            </if>

            <if test="param.shopProvinces != null and param.shopProvinces.size()>1">
                AND shop_province in
                <foreach item="item" collection="param.shopProvinces" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopProvinces != null and param.shopProvinces.size()==1">
                AND shop_province like CONCAT('%',#{param.shopProvinces[0]},'%')
            </if>

            <if test="param.shopCities != null and param.shopCities.size()>1">
                AND shop_city in
                <foreach item="item" collection="param.shopCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCities != null and param.shopCities.size()==1">
                AND shop_city like CONCAT('%',#{param.shopCities[0]},'%')
            </if>

            <if test="param.contactPeople != null and param.contactPeople.size()>1">
                AND contact_person in
                <foreach item="item" collection="param.contactPeople" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.contactPeople != null and param.contactPeople.size()==1">
                AND contact_person like CONCAT('%',#{param.contactPeople[0]},'%')
            </if>

            <if test="param.contactNum != null and param.contactNum != ''">
                AND contact_num like CONCAT('%',#{param.contactNum},'%')
            </if>

            <if test="param.xlShopCodes != null and param.xlShopCodes.size()>1">
                AND xl_shop_code in
                <foreach item="item" collection="param.xlShopCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.xlShopCodes != null and param.xlShopCodes.size()==1">
                AND xl_shop_code like CONCAT('%',#{param.xlShopCodes[0]},'%')
            </if>

            <if test="param.xlShopRemarks != null and param.xlShopRemarks.size()>1">
                AND xl_shop_remark in
                <foreach item="item" collection="param.xlShopRemarks" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.xlShopRemarks != null and param.xlShopRemarks.size()==1">
                AND xl_shop_remark like CONCAT('%',#{param.xlShopRemarks[0]},'%')
            </if>


            <if test="param.cargoContactPhone != null and param.cargoContactPhone != ''">
                AND cargo_contact_phone like CONCAT('%',#{param.cargoContactPhone},'%')
            </if>

            <if test="param.region != null and param.region != ''">
                AND region like CONCAT('%',#{param.region},'%')
            </if>

            <if test="param.cargoContact != null and param.cargoContact != ''">
                AND cargo_contact like CONCAT('%',#{param.cargoContact},'%')
            </if>

            <if test="param.fenceRanges != null and param.fenceRanges.size()>1">
                AND fence_range in
                <foreach item="item" collection="param.fenceRanges" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.fenceRanges != null and param.fenceRanges.size()==1">
                AND fence_range like CONCAT('%',#{param.fenceRanges[0]},'%')
            </if>

            <if test="param.startCreateTime != null and param.endCreateTime != null">
                and create_time between #{param.startCreateTime} and #{param.endCreateTime}
            </if>
        </where>
    </sql>
</mapper>