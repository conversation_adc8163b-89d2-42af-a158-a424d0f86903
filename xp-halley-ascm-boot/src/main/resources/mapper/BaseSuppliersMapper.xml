<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseSuppliersMapper">

    <resultMap id="BaseResultMap" type="com.xiaopeng.halley.ascm.boot.entity.BaseSuppliers">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
        <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="TINYINT"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_code,supplier_name,
        create_user_id,create_user_name,update_user_id,
        update_user_name,version,is_delete,
        create_time,update_time
    </sql>
</mapper>
