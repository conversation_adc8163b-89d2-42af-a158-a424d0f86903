<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseTransportTimelinessMapper">

    <select id="getPageTotal" resultType="long">
        select count(1) from base_transport_timeliness
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseTransportTimelinessVo">
        select id, warehouse_city, shop_city, transport_type, transport_time, route_name, update_user_id,
        update_user_name, update_time from base_transport_timeliness
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            is_delete = 0
            <if test="param.warehouseCities != null and param.warehouseCities.size()>1">
                AND warehouse_city in
                <foreach item="item" collection="param.warehouseCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.warehouseCity != null and param.warehouseCity != ''">
                AND warehouse_city = #{param.warehouseCity}
            </if>

            <if test="param.warehouseCities != null and param.warehouseCities.size()==1">
                AND warehouse_city like CONCAT('%',#{param.warehouseCities[0]},'%')
            </if>

            <if test="param.shopCities != null and param.shopCities.size()>1">
                AND shop_city in
                <foreach item="item" collection="param.shopCities" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCities != null and param.shopCities.size()==1">
                AND shop_city like CONCAT('%',#{param.shopCities[0]},'%')
            </if>

            <if test="param.transportType != null and param.transportType !=''">
                AND transport_type = #{param.transportType}
            </if>
        </where>
    </sql>

</mapper>