<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseWarehouseMapper">

    <select id="selectWarehouseTypeByWaybill" resultType="java.lang.Integer">
        select b.warehouse_type
        from bz_waybill a
                 left join base_warehouse b on a.lgort = b.lgort and b.is_delete = 0
        where waybill_code = #{waybillCode}
    </select>

</mapper>