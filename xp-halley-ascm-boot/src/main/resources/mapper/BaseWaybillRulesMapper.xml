<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BaseWaybillRulesMapper">


    <sql id="Base_Column_List">
        id, shop_code, path_remark, path, path_expiry, order_type, transport_type, carrier_code,
        status, is_specially, create_user_id, create_user_name, update_user_id,
        update_user_name, is_delete, create_time, update_time, lgort, shop_city, shop_province
    </sql>
    <insert id="insertBatch">

        INSERT INTO base_waybill_rules (path, shop_code, order_type, transport_type, lgort, shop_city,
        shop_province,path_expiry)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.path}
            ,#{item.shopCode}
            ,#{item.orderType}
            ,#{item.transportType}
            ,#{item.lgort}
            ,#{item.shopCity}
            ,#{item.shopProvince}
            ,#{item.pathExpiry}
            )
        </foreach>
    </insert>
    <delete id="deleteAllNotSpecially">
        delete
        from base_waybill_rules
        where is_specially = 0
    </delete>
    <delete id="deleteByIds">
        delete from base_waybill_rules where id in
        <foreach item="id" collection="list" index="index" open="(" separator=","
                 close=")">
            #{id}
        </foreach>

    </delete>
    <select id="getPageTotal" resultType="java.lang.Long">
        select count(1)
        FROM base_waybill_rules a
        LEFT JOIN base_shop b ON a.shop_code = b.shop_code
        LEFT JOIN base_warehouse c ON a.`lgort` = c.`lgort`
        LEFT JOIN base_carrier d ON a.`carrier_code` = d.`carrier_code`
        <include refid="page_condition"/>
    </select>
    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BaseWaybillRulesDTO">

        SELECT a.`id`,
        a.`path_remark`,
        a.`path`,
        a.`path_expiry`,
        a.`order_type`,
        a.`transport_type`,
        a.`carrier_code`,
        a.`status`,
        a.`create_user_name`,
        a.`create_time`,
        a.`update_user_name`,
        a.`update_time`,
        a.is_specially,
        a.`shop_code`,
        a.`lgort`,
        a.`shop_province`,
        a.`shop_city`,
        b.`shop_name`,
        b.`shop_address`,
        c.`lgobe`,
        c.`warehouse_province`,
        c.`warehouse_city`,
        c.`warehouse_address`,
        d.`carrier_name`
        FROM base_waybill_rules a
        LEFT JOIN base_shop b ON a.shop_code = b.`shop_code`
        LEFT JOIN base_warehouse c ON a.`lgort` = c.`lgort`
        LEFT JOIN base_carrier d ON a.`carrier_code` = d.`carrier_code`
        <include refid="page_condition"/>
        ORDER BY create_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="page_condition">
        <where>
            a.is_delete = 0
            <if test="param.shopCodeList!= null and param.shopCodeList.size() > 1">
                and b.shop_code in
                <foreach item="shopCode" collection="param.shopCodeList" index="index" open="(" separator=","
                         close=")">
                    #{shopCode}
                </foreach>
            </if>
            <if test="param.shopCodeList != null and param.shopCodeList.size()==1">
                AND b.shop_code like CONCAT('%',#{param.shopCodeList[0]},'%')
            </if>

            <if test="param.shopName != '' and param.shopName != null">
                and b.shop_name like CONCAT('%',#{param.shopName},'%')
            </if>

            <if test="param.orderType != '' and param.orderType != null">
                and a.order_type = #{param.orderType}
            </if>

            <if test="param.transportType != '' and param.transportType != null">
                and a.transport_type = #{param.transportType}
            </if>

            <if test="param.carrierCodeList!= null and param.carrierCodeList.size() > 1">
                and d.carrier_code in
                <foreach item="carrierCode" collection="param.carrierCodeList" index="index" open="(" separator=","
                         close=")">
                    #{carrierCode}
                </foreach>
            </if>
            <if test="param.carrierCodeList != null and param.carrierCodeList.size()==1">
                AND d.carrier_code like CONCAT('%',#{param.carrierCodeList[0]},'%')
            </if>

            <if test="param.carrierName != '' and param.carrierName != null">
                and d.carrier_name like CONCAT('%',#{param.carrierName},'%')
            </if>

        </where>

    </sql>

    <select id="selectRules" resultType="string">
        select a.id
        from base_waybill_rules a
                 left join base_warehouse b on a.lgort = b.lgort
        where a.shop_city = #{shopCity}
          and b.warehouse_city = #{warehouseCity}
          and a.transport_type = #{transportType}
          and a.is_specially = 0;
    </select>

</mapper>