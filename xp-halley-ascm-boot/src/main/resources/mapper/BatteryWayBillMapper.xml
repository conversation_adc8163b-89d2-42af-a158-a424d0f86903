<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BatteryWayBillMapper">

    <select id="getPageTotal" resultType="long">
        select count(1)
        from battery_way_bill a
        left join base_repair_center b on a.repair_center_num = b.repair_center_num
        left join base_shop c on a.xl_shop_code = c.xl_shop_code
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BatteryWayBillVo">
        select a.id,
        a.repair_center_num,
        b.repair_center_name,
        repair_date,
        a.pack_num,
        waybill_code,
        waybill_status,
        c.shop_code,
        a.xl_shop_code,
        c.xl_shop_remark as shop_remark,
        c.region,
        vin_code,
        a.project,
        distance_travelled,
        fault_summary,
        fault_classification,
        dispatch_city,
        arrival_city,
        transportation_type,
        logistics_type,
        case logistics_arrangement_time when '1970-01-01 08:00:00' then null else logistics_arrangement_time end as
        logistics_arrangement_time,
        is_same_package,
        package_vin,
        logistics_provider,
        car_type,
        case pickup_date when '1970-01-01 08:00:00' then null else pickup_date end as pickup_date,
        driver_name,
        driver_phone,
        plate_number,
        response_time_tolerance,
        transportation_time_tolerance,
        actual_payment_amount,
        original_waybill,
        route_name,
        route_delivery_time,
        kilometers_num,
        case a.delivery_time when '1970-01-01 08:00:00' then null else a.delivery_time end as delivery_time,
        expense,
        a.dispatch_address,
        a.loading_contact,
        a.loading_phone,
        a.arrival_address,
        a.unloading_contact,
        a.unloading_phone,
        a.update_user_name,
        a.update_time,
        a.remark
        from battery_way_bill a
        left join base_repair_center b on a.repair_center_num = b.repair_center_num
        left join base_shop c on a.xl_shop_code = c.xl_shop_code
        <include refid="QueryCriteria"/>
        order by update_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            a.is_delete = 0 and c.is_delete = 0
            <if test="param.waybillCodes != null and param.waybillCodes.size()>1">
                AND waybill_code in
                <foreach item="item" collection="param.waybillCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.waybillCodes != null and param.waybillCodes.size()==1">
                AND waybill_code like CONCAT('%',#{param.waybillCodes[0]},'%')
            </if>

            <if test="param.xlShopCodes != null and param.xlShopCodes.size()>1">
                AND a.xl_shop_code in
                <foreach item="item" collection="param.xlShopCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.xlShopCodes != null and param.xlShopCodes.size()==1">
                AND a.xl_shop_code like CONCAT('%',#{param.xlShopCodes[0]},'%')
            </if>

            <if test="param.regions != null and param.regions.size()>1">
                AND c.region in
                <foreach item="item" collection="param.regions" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.regions != null and param.regions.size()==1">
                AND c.region like CONCAT('%',#{param.regions[0]},'%')
            </if>

            <if test="param.packNums != null and param.packNums.size()>1">
                AND a.pack_num in
                <foreach item="item" collection="param.packNums" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.packNums != null and param.packNums.size()==1">
                AND a.pack_num like CONCAT('%',#{param.packNums[0]},'%')
            </if>

            <if test="param.vinCodes != null and param.vinCodes.size()>1">
                AND vin_code in
                <foreach item="item" collection="param.vinCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.vinCodes != null and param.vinCodes.size()==1">
                AND vin_code like CONCAT('%',#{param.vinCodes[0]},'%')
            </if>

            <if test="param.waybillStatus != null">
                AND waybill_status = #{param.waybillStatus}
            </if>

            <if test="param.startRepairDate != null and param.endRepairDate != null">
                and repair_date between #{param.startRepairDate} and #{param.endRepairDate}
            </if>

            <if test="param.startLogisticsArrangementTime != null and param.endLogisticsArrangementTime != null">
                and logistics_arrangement_time between #{param.startLogisticsArrangementTime} and
                #{param.endLogisticsArrangementTime}
            </if>
        </where>
    </sql>
</mapper>