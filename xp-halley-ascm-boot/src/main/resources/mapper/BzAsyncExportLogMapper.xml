<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzAsyncExportLogMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.xiaopeng.halley.ascm.boot.entity.BzAsyncExportLog" id="bzAsyncExportLogMap">
        <result property="id" column="id"/>
        <result property="lgort" column="lgort"/>
        <result property="operationCode" column="operation_code"/>
        <result property="methodPath" column="method_path"/>
        <result property="params" column="params"/>
        <result property="name" column="name"/>
        <result property="state" column="state"/>
        <result property="isDelete" column="is_delete"/>
        <result property="ownerOrgId" column="owner_org_id"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="optimisticLock" column="optimistic_lock"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, lgort, name, operation_code, method_path, params, state, is_delete, owner_org_id, create_user_id,
        create_user_name, create_time, update_user_id, update_user_name, update_time, optimistic_lock
    </sql>


</mapper>