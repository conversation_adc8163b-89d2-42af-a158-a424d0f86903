<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzContractFileMapper">

    <select id="selectByTime" resultType="java.util.Map">
        select bcf.time,
               group_concat(bcf.id) as ids
        from bz_contract_file bcf
                 left join base_file bf on bcf.base_file_id = bf.id
        left join bz_contract bc on bcf.contract_id = bc.id
        where bcf.contract_id = #{param.contractId}
        <if test="param.effectiveTime != null">
            AND DATE_FORMAT(bcf.time, '%Y-%m') >= DATE_FORMAT(#{param.effectiveTime}, '%Y-%m')
        </if>
        <if test="param.endTime != null">
            AND DATE_FORMAT(bcf.time, '%Y-%m') &lt;= DATE_FORMAT(#{param.endTime}, '%Y-%m')
        </if>
        group by bcf.time
        order by bcf.time desc
    </select>
</mapper>