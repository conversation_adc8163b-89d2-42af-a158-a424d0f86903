<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzDriverMapper">


    <select id="findByBzDriverName" resultType="java.lang.String">
        select driver_name
        from bz_driver
        where driver_phone = #{driverPhone}
    </select>
</mapper>