<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzErpReqBoxMapper">


    <select id="getListByWayBillCode" resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.BoxVO">
        select
        bwbr.box_code,
        (case
        bwbr.status
        when 0 then '未扫描'
        when 1 then '已扫描'
        end) status,
        (case
        berb.package_type
        when 0 then '单独包装'
        when 1 then '混合包装'
        when 2 then '虚拟包装'
        end) packageType
        from
        bz_waybill_box_rel bwbr
        inner join bz_erp_req_box berb on
        bwbr.box_code = berb.box_code
        where
        bwbr.is_delete = 0
        and bwbr.waybill_code = #{billCode}
        and bwbr.is_devanning = 0
        and bwbr.box_code in(
        select
        distinct bwbr.box_code
        from
        bz_waybill_box_rel bwbr
        inner join bz_waybill bw on
        bwbr.waybill_code = bw.waybill_code
        and bw.is_delete = 0
        where
        bw.waybill_code =
        #{billCode}
        )


        <!-- select box_code,
               (case status
                    when 0 then '未扫描'
                    when 1 then '已扫描'
                    when 1 then '已拆离'
                   end) status,
               (case package_type
                    when 0 then '单独包装'
                    when 1 then '混合包装'
                   end) packageType
        from bz_erp_req_box
        where is_delete =0 and  box_code in
              (
                  select box_code
                  from bz_erp_req_box_rel
                  where is_delete =0 and ascm_code in
                        (
                            select erp.ascm_code
                            from bz_erp_req erp
                            where is_delete =0 and erp.id in
                                  (select br.erp_req_id
                                   from bz_waybill_req_rel br
                                   where is_delete =0 and br.waybill_id =
                                         (select bw.id from bz_waybill bw where is_delete =0 and bw.waybill_code = #{billCode}))
                        )
              ) -->

    </select>

    <select id="getBoxInfoByWaybillId" resultType="java.util.Map">
        SELECT
        bz_erp_req.delivery_order_code,
        bz_erp_req_box.box_code,
        bz_erp_req_box.package_type,
        bz_erp_req_box.box_long,
        bz_erp_req_box.box_width,
        bz_erp_req_box.box_height,
        bz_erp_req_box.actual_volume,
        bz_erp_req_box.status,
        bz_erp_req_box.update_user_name,
        bz_erp_req_box.update_time
        FROM
        bz_waybill_req_rel
        LEFT JOIN
        bz_erp_req ON bz_erp_req.id = bz_waybill_req_rel.erp_req_id
        LEFT JOIN
        bz_erp_req_box_rel ON bz_erp_req.ascm_code = bz_erp_req_box_rel.ascm_code
        LEFT JOIN
        bz_erp_req_box ON bz_erp_req_box_rel.box_code = bz_erp_req_box.box_code
        WHERE
        bz_waybill_req_rel.waybill_id = #{waybillId}
        and bz_erp_req_box.waybill_code = #{waybillCode}
        <if test="boxCode != null and boxCode != ''">
            and bz_erp_req_box.box_code like concat('%',#{boxCode},'%')
        </if>
        <if test="status != null and status != ''">
            and bz_erp_req_box.status = #{status}
        </if>
        AND bz_waybill_req_rel.is_delete = 0
        AND bz_erp_req.is_delete = 0
        AND bz_erp_req_box_rel.is_delete = 0
        AND bz_erp_req_box.is_delete = 0;
    </select>
    <select id="printBoxMsg" resultType="java.util.Map">
        select
        bb.box_code as 'CARTON',
        bb.box_long as 'ZCD',
        bb.box_width as 'ZKD',
        bb.box_height as 'ZGD',
        bb.actual_volume as 'ZSJTJ',
        bb.box_weight as 'ZDXZL',
        bb.package_code as 'PMAT',
        (case
        bb.package_type
        when 0 then '单独包装'
        when 1 then '混合包装'
        when 2 then '虚拟包装'
        end) as 'ZBXLB'
        from
        bz_erp_req_box bb
        where
        bb.box_code in (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
        and bb.is_delete = 0
    </select>

    <select id="getBoxesByWaybillCodePagePDA" resultType="com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto">
        select
        a.waybill_code waybillCode,
        b.id boxId,
        b.box_code boxCode,
        SUBSTRING(b.box_code,3) as box_code_double,
        b.box_long boxLong,
        b.box_width boxWidth,
        b.box_height boxHeight,
        b.package_type packageType,
        b.actual_volume actualVolume,
        (case
        c.status
        when 0 then '未扫描'
        when 1 then '已扫描'
        end) status
        ,
        c.update_user_name updateUserName
        ,
        c.update_time updateTime,
        b.package_code packageCode,
        b.package_time packageTime,
        b.box_volume boxVolume
        from
        bz_waybill a
        left join bz_waybill_box_rel c on
        a.waybill_code = c.waybill_code
        left join bz_erp_req_box b on
        b.box_code = c.box_code
        where
        a.is_delete = 0
        and a.waybill_code = #{waybillCode}
        and c.is_delete = 0
        and c.is_devanning = 0
        <if test="type == 1">
            and c.status != 2
        </if>
        order by c.status asc ,box_code_double asc
    </select>
</mapper>