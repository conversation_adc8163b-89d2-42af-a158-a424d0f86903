<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzErpReqMaterialRelMapper">

    <select id="findAllByBoxCode" resultType="com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqMaterialRelDto">
        select delivery_order_code             deliveryOrderCode,
               matnr                           matnr,
               maktx                           maktx,
               material_long                   materialLong,
               material_width                  materialWidth,
               material_height                 materialHeight,
               material_volume * package_count materialVolume,
               material_weight * package_count materialWeight,
               order_count                     orderCount,
               package_count                   packageCount
        from bz_erp_req_material_rel
        where box_code = #{boxCode}
          and is_delete = 0
    </select>

    <select id="countBoxPackageCount" resultType="java.lang.Integer">
        select sum(package_count) packageCount
        from bz_erp_req_material_rel a
        where a.is_delete = 0
        <if test="boxCodeList != null and boxCodeList.size()>0">
            AND a.box_code in
            <foreach item="item" collection="boxCodeList" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectWaybillByDeliveryCode" resultType="java.lang.String">
        select
        distinct bwbr.waybill_code
        from
        bz_erp_req_material_rel mr
        left join
        bz_waybill_box_rel bwbr on
        mr.box_code = bwbr.box_code
        where
        mr.delivery_order_code in
        <foreach collection="deliveryOrderCode" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>
    <select id="printMaterialRelMsg" resultType="java.util.Map">
        select br.delivery_order_code as 'ZZVBELN',
               br.matnr               as 'PRODUCTNO',
               br.maktx               as 'MAKTX',
               sum(br.package_count)  as 'QTY'
        from bz_erp_req_material_rel br
        where br.box_code = #{boxCode}
          and br.is_delete = 0
        group by br.delivery_order_code, br.matnr, br.maktx
    </select>

    <select id="deliveryOrderPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWaybillBoxInformationVO">
        select bermr.delivery_order_code, bermr.purchase_time, bermr.delivery_time, bermr.box_code,
        berb.package_type, berb.box_long, berb.box_width, berb.box_height,
        berb.box_volume, bwbr.status, bwbr.is_devanning
        from bz_erp_req_material_rel bermr
        left join bz_waybill_box_rel bwbr on bermr.box_code = bwbr.box_code
        left join bz_erp_req_box berb on bermr.box_code = berb.box_code
        <include refid="theSearch"/>

        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <select id="getdeliveryOrderPageTotal" resultType="java.lang.Long">
        select count(1)
        from bz_erp_req_material_rel bermr
        left join bz_waybill_box_rel bwbr on bermr.box_code = bwbr.box_code
        left join bz_erp_req_box berb on bermr.box_code = berb.box_code
        <include refid="theSearch"/>
    </select>

    <sql id="theSearch">
        <where>
            <if test="param.status != null and param.status != ''">
                and bwbr.status = #{param.status}
            </if>
            <if test="param.isDevanning != null and param.isDevanning != ''">
                and bwbr.is_devanning = #{param.isDevanning}
            </if>
            <if test="param.boxCode != null and param.boxCode != ''">
                and bwbr.box_code = #{param.boxCode}
            </if>
            <if test="param.waybillCode != null and param.waybillCode != ''">
                and bwbr.waybill_code = #{param.waybillCode}
            </if>
        </where>
    </sql>

    <select id="selectMaterialList" resultType="com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel">
        select any_value(delivery_order_code) as delivery_order_code,
               any_value(origin_delivery_order_code) as origin_delivery_order_code,
               any_value(matnr)               as matnr,
               any_value(maktx)               as maktx,
               sum(quantity)                  as quantity
        from bz_erp_req_material_rel
        where is_delete = 0
          and box_code = #{boxCode}
          and quantity > 0
        group by delivery_order_code, matnr
    </select>
</mapper>