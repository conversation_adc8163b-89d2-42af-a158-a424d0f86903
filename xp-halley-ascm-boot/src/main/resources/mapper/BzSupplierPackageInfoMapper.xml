<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzSupplierPackageInfoMapper">

    <resultMap id="BaseResultMap" type="com.xiaopeng.halley.ascm.boot.entity.BzSupplierPackageInfo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
        <result property="materialId" column="material_id" jdbcType="BIGINT"/>
        <result property="supplierSnp" column="supplier_snp" jdbcType="VARCHAR"/>
        <result property="materialLength" column="material_length" jdbcType="VARCHAR"/>
        <result property="materialWidth" column="material_width" jdbcType="VARCHAR"/>
        <result property="materialHeight" column="material_height" jdbcType="VARCHAR"/>
        <result property="stuff" column="stuff" jdbcType="VARCHAR"/>
        <result property="stuffName" column="stuff_name" jdbcType="VARCHAR"/>
        <result property="dosage" column="dosage" jdbcType="VARCHAR"/>
        <result property="recycle" column="recycle" jdbcType="TINYINT"/>
        <result property="createUserId" column="create_user_id" jdbcType="VARCHAR"/>
        <result property="createUserName" column="create_user_name" jdbcType="VARCHAR"/>
        <result property="updateUserId" column="update_user_id" jdbcType="VARCHAR"/>
        <result property="updateUserName" column="update_user_name" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="TINYINT"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,supplier_id,material_id,
        supplier_snp,material_length,material_width,
        material_height,stuff,stuff_name,
        dosage,recycle,create_user_id,
        create_user_name,update_user_id,update_user_name,
        version,is_delete,create_time,
        update_time
    </sql>
</mapper>
