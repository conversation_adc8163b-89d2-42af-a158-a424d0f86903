<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzWaybillBoxRelMapper">


    <insert id="insertBatch">
        insert into
        bz_waybill_box_rel(
        waybill_code,
        box_code,
        status,
        is_devanning,
        is_delete,
        create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.waybillCode},
            #{item.boxCode},
            #{item.status},
            #{item.isDevanning},
            #{item.isDelete},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <select id="getBoxesByWaybillCodePage" resultType="com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto">
        select
        a.waybill_code waybillCode,
        d.delivery_order_code deliveryOrderCode,
        b.id boxId,
        b.box_code boxCode,
        b.box_long boxLong,
        b.box_width boxWidth,
        b.box_height boxHeight,
        b.package_type packageType,
        b.actual_volume actualVolume,
        (case
        c.status
        when 0 then '未扫描'
        when 1 then '已扫描'
        end) status
        ,
        c.update_user_name updateUserName
        ,
        c.update_time updateTime,
        b.package_code packageCode,
        b.package_time packageTime,
        b.box_volume boxVolume
        from
        bz_waybill a
        left join bz_waybill_box_rel c on
        a.waybill_code = c.waybill_code
        left join bz_erp_req_box b on
        b.box_code = c.box_code
        left join bz_erp_req_material_rel d on
        d.box_code = b.box_code
        where
        a.is_delete = 0
        and a.waybill_code = #{waybillCode}
        and c.is_delete = 0
        and c.is_devanning = 0
        <if test="type == 1">
            and c.status != 2
        </if>
    </select>

    <select id="getBoxes" resultType="com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto">
        select a.id boxId,
               a.box_code,
               c.package_code,
               c.package_type,
               c.box_long,
               c.box_width,
               c.box_height,
               c.box_volume,
               c.box_weight,
               a.status,
               c.package_time,
               a.update_user_name,
               c.update_time,
               b.waybill_code,
               c.actual_volume
        from bz_waybill_box_rel a
                 left join bz_waybill b on a.waybill_code = b.waybill_code
                 left join bz_erp_req_box c on a.box_code = c.box_code
        where b.is_delete = 0
          and b.waybill_code = #{waybillCode}
          and c.is_delete = 0
          and a.is_devanning = 0
    </select>


</mapper>