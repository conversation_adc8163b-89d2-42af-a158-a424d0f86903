<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzWaybillExportRecordMapper">
    <insert id="insertWaybill" useGeneratedKeys="true" keyProperty="id">
        insert into bz_waybill_export_record(`status`, `create_user_id`, `create_user_name`)
        values ('0', '1', '2000-11-12')
    </insert>

    <update id="updateStatus">
        update
            bz_waybill_export_record
        set `status` = 1,
            `file_id`=#{fileId}
        where id = #{id}
    </update>

    <select id="findAll" resultType="com.xiaopeng.halley.ascm.boot.entity.BzWaybillExportRecord">
        select *
        from bz_waybill_export_record
    </select>
</mapper>