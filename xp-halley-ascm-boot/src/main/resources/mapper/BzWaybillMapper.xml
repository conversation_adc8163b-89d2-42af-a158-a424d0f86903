<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzWaybillMapper">

    <select id="queryStoreWaybill" resultType="com.xiaopeng.halley.ascm.boot.dto.AsmWayBillReq">
        SELECT GROUP_CONCAT(
                       CONCAT(c.delivery_order_code, '@', b.box_code, '@', c.matnr, '@', c.package_count)
                       ORDER BY c.delivery_order_code, b.box_code
                       SEPARATOR ','
               )                                                                       AS concat_str,
               a.transport_type                                                        as transType,
               IF(a.departure_time = '1970-01-01 08:00:00', null, a.departure_time)    as carrierPickupTime,
               IF(a.sign_time = '1970-01-01 08:00:00', null, a.sign_time)         as carrierArriveShopTime,
               IF(a.transport_type = '专车', driver_name, null)  as driverName,
               IF(a.transport_type = '专车', driver_phone, null) as driverPhone,
               IF(a.status = '异常中', '运输中', a.status)         as logisticsState,
               a.logistics_code                                                           as logisticsCode,
               a.waybill_code,
               IF(a.pda_shipping_time = '1970-01-01 08:00:00', null, a.pda_shipping_time) as outScanTime
        FROM bz_waybill a
                 left join bz_waybill_box_rel b on a.waybill_code = b.waybill_code and b.is_delete = 0
                 left join bz_erp_req_material_rel c on b.box_code = c.box_code and c.is_delete = 0
        WHERE a.is_delete = 0
          and a.status in ('运输中', '已完成', '异常中')
          and NOT EXISTS (SELECT 1
                          FROM base_warehouse bw
                          WHERE bw.lgort = a.shop_code
                            AND bw.warehouse_city = a.shop_city
                            and bw.is_delete = 0
                            and bw.status = 0)
          and a.sync = 1
        <if test="waybillCodes != null and waybillCodes.size() > 0">
            AND a.waybill_code in
            <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by a.waybill_code
    </select>

    <select id="queryFreshWaybill" resultType="com.xiaopeng.halley.ascm.boot.dto.AsmWaybillFreshReq">
        SELECT a.waybill_code,
               IF(a.sign_time = '1970-01-01 08:00:00', null, a.sign_time)         as carrierArriveShopTime,
               a.logistics_code as logisticsCode,
               IF(a.status = '异常中', '运输中', a.status)         as logisticsState
        FROM bz_waybill a
        WHERE a.is_delete = 0
          and NOT EXISTS (SELECT 1
                          FROM base_warehouse bw
                          WHERE bw.lgort = a.shop_code
                            AND bw.warehouse_city = a.shop_city
                            and bw.is_delete = 0
                            and bw.status = 0)
          and a.sync = 0
          and a.track_sync = 1
        <if test="waybillCodes != null and waybillCodes.size() > 0">
            AND a.waybill_code in
            <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="calVolumeWeight" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillDetailDto">
        SELECT c.id,
               a.waybill_code,
               sum(actual_volume) as total_volume,
               sum(box_weight) as total_weight
        FROM bz_waybill_box_rel a
                 left join bz_erp_req_box b on a.box_code = b.box_code and b.is_delete = 0
                 left join bz_waybill c on a.waybill_code = c.waybill_code and c.is_delete = 0
        WHERE a.is_delete = 0
            and a.is_devanning = 0
            and a.waybill_code in
        <foreach collection="waybillCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY waybill_code
    </select>

    <update id="updateByWaybillCode">
        UPDATE bz_waybill
        SET
        <if test="departureTime != null">
            departure_time = #{departureTime}
        </if>
        <if test="updateTime != null">
            ,update_time = #{updateTime}
        </if>
        <if test="updateUserId != null and updateUserId != ''">
            ,update_user_id = #{updateUserId}
        </if>
        <if test="updateUserName != null and updateUserName !=''">
            ,update_user_name = #{updateUserName}
        </if>
        <if test="pdaShippingTime != null">
            ,pda_shipping_time = #{pdaShippingTime}
        </if>
        WHERE waybill_code = #{waybillCode}
    </update>

    <select id="getAscmCodesById" parameterType="long" resultType="string">
        select b.ascm_code
        from bz_waybill_req_rel a
                 left join bz_erp_req b on a.erp_req_id = b.id
        where a.waybill_id = #{id}
    </select>

    <select id="export" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillExportDto"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillExportDto">
        select
        `bz_waybill`.`waybill_code` as waybillCode,
        `bm`.delivery_order_code deliveryOrderCode,
        `bz_waybill`.`total_box` totalBox,
        `bz_waybill`.`status` waybillStatus,
        `bz_waybill`.order_type orderType,
        `bz_waybill`.transport_type transportType ,
        `bz_waybill`.lgort lgort,
        `bz_waybill`.lgobe lgobe ,
        `base_carrier`.carrier_name carrierName,
        `bz_waybill`.shop_province shopProvince ,
        `bz_waybill`.shop_city shopCity,
        `bz_waybill`.shop_code shopCode,
        `bz_waybill`.shop_name shopName,
        `bz_waybill`.logistics_company logisticsCompany,
        `bz_waybill`.logistics_code logisticsCode,
        `bz_waybill`.shop_address shopAddress,
        `bz_waybill`.`path` path ,
        `bz_waybill`.path_expiry pathExpiry,
        `bz_waybill`.expiry_time expiryTime,
        `bz_waybill`.departure_time departureTime,
        `bz_waybill`.sign_time signTime,
        `bz_waybill`.create_time waybillCreateTime,
        `bz_erp_req_box`.`box_code` boxCode,
        `bz_erp_req_box`.`actual_volume` actualVolume,
        (case `bz_waybill_box_rel`.`status`
        when 0 then '未扫描'
        when 1 then '已扫描' end) status,
        `bz_waybill_box_rel`.`is_devanning` isDevanning,
        (case `bz_erp_req_box`.package_type
        when 0 then '单独包装'
        when 1 then '混合包装'
        when 2 then '虚拟包装' end) packageType,
        `bz_erp_req_box`.package_code packageCode,
        `bz_erp_req_box`.box_long boxLong,
        `bz_erp_req_box`.box_width boxWidth,
        `bz_erp_req_box`.box_height boxHeight ,
        `bz_erp_req_box`.box_volume boxVolume,
        `bz_erp_req_box`.box_weight boxWeight
        from
        `bz_waybill`
        left join `base_carrier` on
        `bz_waybill`.`carrier_code` = `base_carrier`.`carrier_code`
        left join `bz_waybill_box_rel` on
        `bz_waybill`.`waybill_code` = `bz_waybill_box_rel`.`waybill_code`
        left join `bz_erp_req_box` on
        `bz_waybill_box_rel`.`box_code` = `bz_erp_req_box`.`box_code`
        left join (
        select
        group_concat(distinct delivery_order_code) as delivery_order_code,
        group_concat(distinct box_code) as box_code,
        CONCAT(delivery_order_code, box_code) as deliveryBox
        from
        bz_erp_req_material_rel
        group by
        deliveryBox) as bm on
        `bz_erp_req_box`.`box_code` = bm.`box_code`
        where
        `bz_waybill`.`is_delete` = 0
        <!--       <if test="waybillCodeList!=null and waybillCodeList!=''">-->
        <!--           AND `bz_waybill`.`waybill_code` in (-->
        <!--           <foreach collection="waybillCodeList" separator="," item="waybillCode">-->
        <!--               #{waybillCode}-->
        <!--           </foreach>-->
        <!--           )-->
        <!--       </if>-->
        <!--                <if test="waybillCode != null and waybillCode != ''">-->
        <!--                    AND `bz_waybill`.`waybill_code` LIKE #{waybillCode}-->
        <!--                </if>-->
        <!--        <if test="status != null and status != ''">-->
        <!--            AND `bz_waybill`.`status` = #{status}-->
        <!--        </if>-->
        <!--        <if test="orderType != null and orderType != ''">-->
        <!--            AND `bz_waybill`.`order_type` = #{orderType}-->
        <!--        </if>-->
        <!--        AND `bz_waybill`.`lgort` in (-->
        <!--        <foreach collection="lgortList" separator="," item="lgort">-->
        <!--            #{lgort}-->
        <!--        </foreach>-->
        <!--        )-->
        <!--        <if test="lgort != null and lgort != ''">-->
        <!--            AND `bz_waybill`.`lgort` = #{lgort}-->
        <!--        </if>-->
        <!--        AND `bz_waybill`.`shop_code` in (-->
        <!--        <foreach collection="shopCodeList" separator="," item="shopCode">-->
        <!--            #{shopCode}-->
        <!--        </foreach>-->
        <!--        )-->
        <!--        <if test="shopCode != null and shopCode != ''">-->
        <!--            AND `bz_waybill`.`shop_code` LIKE #{shopCode}-->
        <!--        </if>-->
        <!--        <if test="logisticsCodeList != null">-->
        <!--            AND `bz_waybill`.`logistics_code` in (-->
        <!--            <foreach collection="logisticsCodeList" separator="," item="logisticsCode">-->
        <!--                #{logisticsCode}-->
        <!--            </foreach>-->
        <!--            )-->
        <!--        </if>-->
        <!--        <if test="logisticsCode != null and logisticsCode != ''">-->
        <!--            AND `bz_waybill`.`logistics_code` = #{logisticsCode}-->
        <!--        </if>-->
        <if test="createTimeStart != null">
            AND `bz_waybill`.`create_time` &gt;= #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            AND `bz_waybill`.`create_time` &lt; #{createTimeEnd}
        </if>
        <!--        <if test="shopCityList != null">-->
        <!--            AND `base_shop`.`shop_city` in (-->
        <!--            <foreach collection="shopCityList" separator="," item="shopCity">-->
        <!--                #{shopCity}-->
        <!--            </foreach>-->
        <!--            )-->
        <!--        </if>-->

        <!--        <if test="null != shopCity and shopCity != ''">-->
        <!--            AND `base_shop`.`shop_city` = #{shopCity}-->
        <!--        </if>-->
        <!--        <if test="null != shopProvince and shopProvince != ''">-->
        <!--            AND `base_shop`.`shop_province` = #{shopProvince}-->
        <!--        </if>-->
    </select>

    <select id="getAllWaybillStatusCount" resultType="java.util.Map">
        SELECT status, count(id) as count
        FROM bz_waybill
        Where `bz_waybill`.`is_delete` = 0
        <if test="null != lgort and lgort != ''">
            AND `bz_waybill`.`lgort` = #{lgort}
        </if>
        <if test="null != carrierCode and carrierCode != ''">
            AND `bz_waybill`.`carrier_code` = #{carrierCode}
        </if>
        <if test="null != shopCode and shopCode != ''">
            AND `bz_waybill`.`shop_code` = #{shopCode}
        </if>
        and total_box > 0
        group by status;
    </select>

    <select id="statusNum" resultType="map">
        select bw.status
        ,count(*) num
        from bz_waybill bw
        where bw.is_delete = 0
        and bw.total_box > 0
        and (bw.status = '待提货' or bw.status = '已发布' or bw.status = '待运输')
        and bw.is_hang_up = 0
        <if test="lgort != null and lgort != ''">
            and bw.lgort = #{lgort}
        </if>
        <if test="carrierCode != null and carrierCode != ''">
            and bw.carrier_code = #{carrierCode}
        </if>
        group by bw.status
    </select>

    <select id="getDetailById" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillDetailDto"
            parameterType="String">
        select a.id id
        , a.create_user_name createUserName
        , a.create_time createTime
        , a.update_user_name updateUserName
        , a.update_time updateTime
        , a.status status
        , a.order_type orderType
        , a.transport_type transportType
        , a.waybill_code waybillCode
        , a.carrier_code carrierCode
        , d.carrier_name carrierName
        , a.`path` `path`
        , a.path_expiry pathExpiry
        , a.logistics_company logisticsCompany
        , a.logistics_code logisticsCode
        , a.driver_name driverName
        , a.driver_phone driverPhone
        , a.lgort lgort
        , a.lgobe lgobe
        , a.warehouse_province warehouseProvince
        , a.warehouse_city warehouseCity
        , a.warehouse_contact_num contactNum
        , a.warehouse_contact_person contactPerson
        , a.warehouse_address warehouseAddress
        , a.compensate_type compensateType
        , a.compensate_reason compensateReason
        , a.shop_code shopCode
        , a.shop_name shopName
        , a.shop_province shopProvince
        , a.shop_city shopCity
        , a.shop_contact_num shopContactNum
        , a.shop_contact_person shopContactPerson
        , a.shop_address shopAddress
        , a.car_plate carPlate
        , a.car_type
        , a.total_volume
        , a.total_weight
        from bz_waybill a
        left join base_carrier d on a.carrier_code = d.carrier_code and d.is_delete = 0
        where a.is_delete = 0
        and a.waybill_code = #{waybillCode}
        <if test="lgort != null and lgort != ''">
            and a.lgort = #{lgort}
        </if>
        <if test="carrierCode != null and carrierCode != ''">
            and a.carrier_code = #{carrierCode}
        </if>
    </select>
    <select id="getBoxAllByCode" resultType="com.xiaopeng.halley.ascm.boot.dto.erp.BzErpReqBoxRelDto">
        select
        f.waybill_code waybillCode,
        b.box_code boxCode,
        b.box_long boxLong,
        b.box_width boxWidth,
        b.box_height boxHeight,
        b.actual_volume actualVolume,
        c.status status,
        b.update_user_name updateUserName,
        b.update_time updateTime
        from
        bz_waybill_box_rel c
        left join bz_waybill f on
        c.waybill_code = f.waybill_code
        left join bz_erp_req_box b on
        b.box_code = c.box_code
        where
        c.is_delete = 0
        and f.is_delete = 0
        and b.is_delete = 0
        and c.status != 2
        and f.waybill_code = (
        select
        fc.waybill_code waybillCode
        from
        bz_waybill_box_rel wbr
        left join bz_waybill fc on
        wbr.waybill_code = fc.waybill_code
        where
        wbr.is_delete = 0
        and wbr.status != 2
        and wbr.is_devanning = 0
        and wbr.is_delete = 0
        and wbr.box_code = #{boxCode}
        <if test="lgort != null and lgort != ''">
            and fc.lgort = #{lgort}
        </if>
        <if test="carrierCode != null and carrierCode != ''">
            and fc.carrier_code = #{carrierCode}
        </if>
        limit 1)
    </select>

    <select id="getPDAWaybillList" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillListDto"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillListDto">
        select
        bw.waybill_code WaybillCode,
        substring(bw.waybill_code,1,6) waybillCodeDoubleDay,
        substring(bw.waybill_code,7,3) waybillCodeDoubleCount,
        length(bw.waybill_code) - length(replace(bw.waybill_code,'-','')) demolitioncount,
        bw.shop_code shopCode,
        bw.shop_city shopCity,
        bw.order_type orderType,
        bw.warehouse_city warehouseCity,
        bw.abnormal_cause abnormalCause,
        bw.total_box totalBox,
        bw.driver_phone driverPhone,
        bw.shop_name shopName,
        bw.lgobe lgobe,
        bw.transport_type transportType,
        bw.is_complete_packing isCompletePacking,
        bw.create_time createTime,
        count(bwbr.status = 0 or null) unscannedBox,
        count(bwbr.status = 1 or null) scannedBox
        from
        bz_waybill bw
        left join bz_waybill_box_rel bwbr on
        bwbr.waybill_code = bw.waybill_code
        where
        bw.total_box > 0
        and bwbr.is_devanning = 0
        and bw.is_hang_up = 0
        and bw.is_delete = 0
        and bwbr.is_delete = 0
        <if test="paramData.city != null and paramData.city != ''">
            and bw.shop_city =
            #{paramData.city}
        </if>
        <if test="paramData.lgort != null and paramData.lgort != ''">
            and bw.lgort =
            #{paramData.lgort}
        </if>
        <if test="paramData.status != null and paramData.status != ''">
            and bw.status =
            #{paramData.status}
        </if>
        <if test="paramData.shopName != null and paramData.shopName != ''">
            and bw.shop_name like CONCAT('%', #{paramData.shopName},'%')
        </if>
        <if test="paramData.searchKey != null and paramData.searchKey != ''">
            and (bw.shop_name like CONCAT('%', #{paramData.searchKey},'%') or
                 bw.waybill_code like CONCAT('%', #{paramData.searchKey},'%'))
        </if>
        <if test="paramData.pageType == 1 and paramData.shopCodeList.size != 0">
            and bw.shop_code in
            <foreach collection="paramData.shopCodeList" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        group by bw.waybill_code
        order by waybillCodeDoubleDay desc,waybillCodeDoubleCount asc,demolitioncount asc,bw.create_time asc
        <if test="startIndex != null and pageSize != ''">
            limit #{startIndex},#{pageSize}
        </if>
    </select>

    <select id="getPADWaybillPageTotal" resultType="int">
        SELECT
        COUNT(1)
        FROM
        (SELECT
        bw.id
        FROM
        bz_waybill bw
        LEFT JOIN base_shop bs ON bw.shop_code = bs.shop_code
        LEFT JOIN bz_waybill_box_rel bwbr ON bwbr.waybill_code = bw.waybill_code
        LEFT JOIN bz_erp_req_box berb ON bwbr.box_code = berb.box_code
        LEFT JOIN base_warehouse bw2 ON bw.lgort = bw2.lgort
        where bw.total_box > 0
        and bwbr.is_delete = 0
        and bw.is_delete = 0
        and bw.is_hang_up = 0
        and bwbr.is_devanning = 0
        <if test="paramData.city != null and paramData.city != ''">
            and bs.shop_city = #{paramData.city}
        </if>
        <if test="paramData.lgort != null and paramData.lgort != ''">
            and bw.lgort = #{paramData.lgort}
        </if>
        <if test="paramData.status != null and paramData.status != ''">
            and bw.status = #{paramData.status}
        </if>
        <if test="paramData.shopName != null and paramData.shopName != ''">
            and bs.shop_name like CONCAT('%',#{paramData.shopName},'%')
        </if>
        <if test="paramData.searchKey != null and paramData.searchKey != ''">
            and (bw.shop_name like CONCAT('%', #{paramData.searchKey},'%') or
            bw.waybill_code like CONCAT('%', #{paramData.searchKey},'%'))
        </if>
        <if test="paramData.pageType == 1 and paramData.shopCodeList.size != 0">
            and bw.shop_code in
            <foreach collection="paramData.shopCodeList" item="value" separator="," open="(" close=")">
                #{value}
            </foreach>
        </if>
        GROUP BY bw.waybill_code) a;
    </select>
    <select id="getWaybillStatusByNo" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillStatusDto">
        select bw.status         as status,
               bw.waybill_code   as waybillCode,
               bw.driver_phone   as driverPhone,
               bw.car_plate      as carPlate,
               bw.driver_name    as driverName,
               bw.expiry_time    as expiryTime,
               bw.transport_type as transportType,
               bw.path_expiry    as pathExpiry
        from bz_waybill bw
        where bw.is_delete = 0
          and bw.waybill_code = #{waybillCode}
    </select>

    <select id="getWaybillStatus" resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.WaybillStatusDto">
        select fc.status status,
               fc.waybill_code
        from bz_waybill_box_rel bwbr
                 left join bz_waybill fc on
            bwbr.waybill_code = fc.waybill_code
        where fc.is_delete = 0
          and bwbr.is_delete = 0
          and bwbr.is_devanning = 0
          and bwbr.box_code = #{boxCode}
          and (fc.status = '待提货'
            or fc.status = '已发布'
            or fc.status = '待运输')
        group by fc.waybill_code
        limit 1
    </select>
    <select id="verifyStatus" resultType="map">
        select
        fc.path,
        bwbr.status status,
        fc.status waybillStatus
        from
        bz_waybill fc
        left join bz_waybill_box_rel bwbr on
        fc.waybill_code = bwbr.waybill_code
        where
        fc.is_delete = 0
        and bwbr.status != 2
        and bwbr.is_delete = 0
        and bwbr.is_devanning = 0
        <if test="waybillCode != null and waybillCode != ''">
            and fc.waybill_code = #{waybillCode}
        </if>
        and bwbr.box_code = #{boxCode}
        <if test="lgort != null and lgort != ''">
            and fc.lgort = #{lgort}
        </if>
        <if test="carrierCode != null and carrierCode != ''">
            and fc.carrier_code =#{carrierCode}
        </if>
    </select>
    <select id="getCountByCode" resultType="int">
        select count(1)
        from bz_waybill_box_rel bwbr
                 left join bz_waybill fc on
            bwbr.waybill_code = fc.waybill_code
        where fc.is_delete = 0
          and bwbr.is_delete = 0
          and bwbr.status = 0
          and bwbr.is_devanning = 0
          and fc.waybill_code = #{waybillCode}
    </select>

    <select id="getCodeByTransportType" resultType="java.lang.Integer">
        select count(1)
        from bz_waybill
        where is_delete = 0
          and (status != '已取消' and status != '已完成')
          and transport_type = #{transportType}
    </select>

    <select id="findStateNum" resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.StatusVO">
        select count(a.status = '运输中' or null) as transitNum,
               count(a.status = '已完成' or null) as finishedNum,
               count(a.status = '异常中' or null) as exceptionNum
        from (select bw.*
              from bz_waybill bw
                       left join bz_waybill_box_rel bwbr on
                  bwbr.waybill_code = bw.waybill_code
              where bw.is_delete = 0
                and bw.is_hang_up = 0
                and bw.total_box > 0
                and bwbr.is_delete = 0
                and bwbr.is_devanning = 0
                and bw.driver_phone = #{driverPhone}
              group by bw.waybill_code) a
    </select>
    <select id="findWaybill" resultType="java.lang.String">
        select bz_waybill.status
        from bz_waybill
        where is_delete = 0
          and waybill_code = #{waybill}
    </select>
    <select id="checkWaybillCode" resultType="java.lang.Integer">
        select count(1)
        from bz_waybill
        where waybill_code = #{waybill}
    </select>
    <select id="getWayBillListByCode" resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.WayBillShopVO">
        select
        b.waybill_code as waybillCode,
        b.status as waybillStatus,
        b.total_box as totalBox,
        b.total_volume as totalVolume,
        b.create_time as createTime,
        b.sign_time as signTime,
        b.expiry_time as deliveryTime,
        b.lgobe as lgobe,
        b.shop_province as shopProvince,
        b.shop_city as shopCity,
        b.lgobe as lgobe,
        b.lgobe as lgobe,
        b.warehouse_city as warehouseCity,
        b.warehouse_address as warehouseAddress,
        b.shop_name as shopName,
        b.shop_contact_person as shopContactPerson,
        b.shop_contact_num as shopContactNum,
        b.shop_address as shopAddress,
        b.warehouse_province as warehouseProvince
        from bz_waybill b
        where b.is_delete =0 and
        b.waybill_code in
        <foreach collection="billCode" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </select>

    <select id="printBzWaybillMsg" resultType="java.util.Map">
        select b.waybill_code             as 'ZYDBH',
               b.shop_code                as 'KUNNR',
               b.shop_name                as 'NAME_ORG1',
               b.shop_city                as 'ZSDCTY',
               b.shop_address             as 'ZSCADD',
               b.shop_contact_person      as 'ZJSR',
               b.shop_contact_num         as 'ZJCRNUM',
               b.lgobe                    as 'LGOBE',
               b.warehouse_address        as 'ZFHADD',
               b.warehouse_contact_person as 'ZFHR',
               b.warehouse_contact_num    as 'ZFHNUM',
               b.total_box                as 'ZXZS',
               b.total_volume             as 'ZTJ',
               b.transport_type           as 'ZYSFS',
               b.lgort                    as 'LGORT'

        from bz_waybill b
        where b.waybill_code = #{waybillCode}
          and b.is_delete = 0
    </select>

    <select id="boxesByWaybill" resultType="com.xiaopeng.halley.ascm.boot.dto.BoxParamDto">
        select
        any_value(`bz_waybill`.`id`) as id,
        any_value(case
        `bz_waybill_box_rel`.`status`
        when 0 then '未扫描'
        when 1 then '已扫描'
        end) status,
        (case
        `bz_waybill_box_rel`.`is_devanning`
        when 0 then '未拆离'
        when 1 then '已拆离'
        end) isDevanning,
        any_value(`bz_waybill_box_rel`.`update_user_name`) as update_user_name,
        any_value(`bz_waybill_box_rel`.`update_time`) as update_time_get,
        `bz_erp_req_box`.`box_code`,
        any_value(`bz_erp_req_box`.`box_long`) as box_long,
        any_value(`bz_erp_req_box`.`box_width`) as box_width,
        any_value(`bz_erp_req_box`.`box_height`) as box_height,
        any_value(case
        `bz_erp_req_box`.`package_type`
        when 0 then '单独包装'
        when 1 then '混合包装'
        when 2 then '虚拟包装'
        end) packageType,
        any_value(package_code) as package_code,
        any_value(`bz_erp_req_box`.`actual_volume`) as actual_volume,
        any_value(`bz_erp_req_box`.`waybill_code`) as waybill_code,
        group_concat(distinct `bz_erp_req_material_rel`.`delivery_order_code`) as delivery_order_code
        from
        `bz_waybill`
        left join `bz_waybill_box_rel` on
        `bz_waybill`.`waybill_code` = `bz_waybill_box_rel`.`waybill_code`
        left join `bz_erp_req_box` on
        `bz_waybill_box_rel`.`box_code` = `bz_erp_req_box`.`box_code`
        left join `bz_erp_req_material_rel` on
        `bz_erp_req_box`.`box_code` = `bz_erp_req_material_rel`.`box_code`
        where
        `bz_waybill`.`id` = #{vo.bzWaybillId}
        and `bz_waybill_box_rel`.`is_delete` = #{isDelete}
        <if test="vo.boxStatus != null">
            and `bz_waybill_box_rel`.`status` = #{vo.boxStatus}
        </if>
        <if test="vo.isDevanning != null">
            and `bz_waybill_box_rel`.`is_devanning` = #{vo.isDevanning}
        </if>
        <if test="vo.boxCode != null">
            and `bz_erp_req_box`.`box_code` like CONCAT('%',#{vo.boxCode},'%')
        </if>
        group by `bz_erp_req_box`.`box_code`,`bz_waybill_box_rel`.`is_devanning`
    </select>
    <select id="getMiniProgramWaybillList"
            resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillListDto">
        select
        bw.waybill_code WaybillCode,
        bw.shop_code shopCode,
        bw.shop_city shopCity,
        bw.order_type orderType,
        bw.warehouse_city warehouseCity,
        bw.total_box totalBox,
        bw.shop_name shopName,
        bw.lgobe lgobe,
        bw.is_complete_packing isCompletePacking,
        bw.create_time createTime,
        bw.update_time updateTime,
        count(bwbr.status = 0 or null) unscannedBox,
        count(bwbr.status = 1 or null) scannedBox
        from
        bz_waybill bw
        left join bz_waybill_box_rel bwbr on
        bwbr.waybill_code = bw.waybill_code
        where
        bw.total_box > 0
        and bwbr.is_devanning = 0
        and bw.is_hang_up = 0
        and bw.is_delete = 0
        and bwbr.is_delete = 0
        and bw.waybill_code in
        <foreach collection="waybillCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by WaybillCode
        order by bw.update_time desc,bw.create_time desc
    </select>
    <select id="getDeliveryOrderCode" resultType="java.lang.String">

        select group_concat(distinct c.delivery_order_code)
        from bz_waybill a
                 left join bz_waybill_box_rel b on a.waybill_code = b.waybill_code
                 left join bz_erp_req_material_rel c on b.box_code = c.box_code
        where a.waybill_code = #{wayBillCode}
    </select>
    <select id="getMiniProgramWaybills"
            resultType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillListDto"
            parameterType="com.xiaopeng.halley.ascm.boot.dto.waybill.BzWaybillListDto">
        select
        bw.waybill_code WaybillCode,
        bw.shop_code shopCode,
        bw.shop_city shopCity,
        bw.order_type orderType,
        bw.warehouse_city warehouseCity,
        bw.abnormal_cause abnormalCause,
        bw.total_box totalBox,
        bw.driver_phone driverPhone,
        bw.shop_name shopName,
        bw.lgobe lgobe,
        bw.is_complete_packing isCompletePacking,
        bw.create_time createTime,
        count(bwbr.status = 0 or null) unscannedBox,
        count(bwbr.status = 1 or null) scannedBox
        from
        bz_waybill bw
        left join bz_waybill_box_rel bwbr on
        bwbr.waybill_code = bw.waybill_code
        where
        bw.total_box > 0
        and bwbr.is_devanning = 0
        and bw.is_hang_up = 0
        and bw.driver_phone = #{mobile}
        and bw.is_delete = 0
        and bwbr.is_delete = 0
        <if test="paramData.city != null and paramData.city != ''">
            and bw.shop_city =
            #{paramData.city}
        </if>
        <if test="paramData.lgort != null and paramData.lgort != ''">
            and bw.lgort =
            #{paramData.lgort}
        </if>
        <if test="paramData.status != null and paramData.status != ''">
            and bw.status =
            #{paramData.status}
        </if>
        <if test="paramData.shopName != null and paramData.shopName != ''">
            and bw.shop_name like CONCAT('%', #{paramData.shopName},'%')
        </if>
        <if test="paramData.shopCode != null and paramData.shopCode != ''">
            and bw.shop_code like CONCAT('%', #{paramData.shopCode},'%')
        </if>
        group by bw.waybill_code
        order by bw.update_time desc,bw.id desc
        <if test="startIndex != null and pageSize != ''">
            limit #{startIndex},#{pageSize}
        </if>
    </select>
    <select id="getMiniProgramWaybillPageTotal" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        (SELECT
        bw.id
        FROM
        bz_waybill bw
        LEFT JOIN bz_waybill_box_rel bwbr ON bwbr.waybill_code = bw.waybill_code
        LEFT JOIN bz_erp_req_box berb ON bwbr.box_code = berb.box_code
        where bw.total_box > 0
        and bw.driver_phone = #{mobile}
        and bw.is_delete = 0
        and bwbr.is_delete = 0
        and bwbr.is_devanning = 0
        <if test="paramData.city != null and paramData.city != ''">
            and bw.shop_city = #{paramData.city}
        </if>
        <if test="paramData.lgort != null and paramData.lgort != ''">
            and bw.lgort = #{paramData.lgort}
        </if>
        <if test="paramData.status != null and paramData.status != ''">
            and bw.status = #{paramData.status}
        </if>
        <if test="paramData.shopName != null and paramData.shopName != ''">
            and bw.shop_name like CONCAT('%',#{paramData.shopName},'%')
        </if>
        <if test="paramData.shopCode != null and paramData.shopCode != ''">
            and bw.shop_code like CONCAT('%',#{paramData.shopCode},'%')
        </if>
        GROUP BY bw.waybill_code) a;
    </select>

    <select id="getConditionPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWayBillPageVo">
        select
        bw.id,
        bw.order_type,
        bw.waybill_code,
        bw.transport_type,
        bw.total_volume,
        bw.status,
        bw.lgort,
        bw.lgobe,
        bw.shop_code,
        bw.shop_city,
        bw.shop_name,
        bw.is_complete_packing,
        case bw.status when '运输中' then 1 else 0 end as statusgroup,
        case bw.circulation_time when '1970-01-01 08:00:00' then null else bw.circulation_time end as circulation_time,
        case bw.departure_time when '1970-01-01 08:00:00' then null else bw.departure_time end as departure_time,
        case bw.expiry_time when '1970-01-01 08:00:00' then null else bw.expiry_time end as expiry_time,
        case bw.sign_time when '1970-01-01 08:00:00' then null else bw.sign_time end as sign_time,
        case bw.waybill_create_time when '1970-01-01 08:00:00' then null else bw.waybill_create_time end as
        waybill_create_time,
        case bw.received_time when '1970-01-01 08:00:00' then null else bw.received_time end as received_time,
        bw.total_box,
        bw.logistics_code,
        bw.local_info,
        bw.logistics_company,
        bw.is_hang_up,
        bw.shop_province,
        concat(bw.lgobe,'-',bw.shop_city) route,
        case when bw.expiry_time > bw.sign_time then '未超时' else '超时' end as is_timeout
        <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>1">
            ,ANY_VALUE(bermr.delivery_order_code)
        </if>
        <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>1">
            ,ANY_VALUE(bermr.origin_delivery_order_code)
        </if>
        from bz_waybill bw

        <include refid="where_condition"/>

        <include refid="page_condition"/>
        group by bw.waybill_code
        order by bw.sort_num desc,bw.received_time desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="page_condition">
        <where>
            bw.is_delete = 0 and bw.total_box > 0
            <if test="param.statusList != null and param.statusList.size()>=1">
                AND bw.status in
                <foreach item="item" collection="param.statusList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.startReceivedTime != null and param.endReceivedTime != null">
                and bw.received_time between #{param.startReceivedTime} and #{param.endReceivedTime}
            </if>
            <if test="param.startCirculationTime != null and param.endCirculationTime != null">
                and bw.circulation_time between #{param.startCirculationTime} and #{param.endCirculationTime}
            </if>
            <if test="param.startDepartureTime != null and param.endDepartureTime != null">
                and bw.departure_time between #{param.startDepartureTime} and #{param.endDepartureTime}
            </if>
            <if test="param.startSignTime != null and param.endSignTime != null">
                and bw.sign_time between #{param.startSignTime} and #{param.endSignTime}
            </if>

            <if test="param.waybillCodeList != null and param.waybillCodeList.size()>1">
                AND bw.waybill_code in
                <foreach item="item" collection="param.waybillCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()>1">
                AND bw.shop_code in
                <foreach item="item" collection="param.shopCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.waybillCodeList != null and param.waybillCodeList.size()==1">
                AND bw.waybill_code like CONCAT('%',#{param.waybillCodeList[0]},'%')
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()==1">
                AND bw.shop_code like CONCAT('%',#{param.shopCodeList[0]},'%')
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()==1">
                AND bw.shop_city like CONCAT('%',#{param.shopCityList[0]},'%')
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()>1">
                AND bw.shop_city in
                <foreach item="item" collection="param.shopCityList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.lgortList != null and param.lgortList.size()>1">
                AND bw.lgort in
                <foreach item="item" collection="param.lgortList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.lgortList != null and param.lgortList.size()==1">
                AND bw.lgort like CONCAT('%',#{param.lgortList[0]},'%')
            </if>

            <if test="param.orderType != null and param.orderType != ''">
                and bw.order_type like concat('%',#{param.orderType},'%')
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()>1">
                AND bw.logistics_code in
                <foreach item="item" collection="param.logisticsCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()==1">
                AND bw.logistics_code like CONCAT('%',#{param.logisticsCodeList[0]},'%')
            </if>

            <if test="param.transportType != null and param.transportType != ''">
                and bw.transport_type like concat('%',#{param.transportType},'%')
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()>1">
                AND bw.shop_province in
                <foreach item="item" collection="param.shopProvinceList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()==1">
                AND bw.shop_province like CONCAT('%',#{param.shopProvinceList[0]},'%')
            </if>


            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>1">
                AND bermr.delivery_order_code in
                <foreach item="item" collection="param.deliveryOrderCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()==1">
                AND bermr.delivery_order_code like CONCAT('%',#{param.deliveryOrderCodeList[0]},'%')
            </if>

            <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>1">
                AND bermr.origin_delivery_order_code in
                <foreach item="item" collection="param.originDeliveryOrderCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()==1">
                AND bermr.origin_delivery_order_code like CONCAT('%',#{param.originDeliveryOrderCodeList[0]},'%')
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()>1">
                AND berb.box_code in
                <foreach item="item" collection="param.boxCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()==1">
                AND berb.box_code like CONCAT('%',#{param.boxCodeList[0]},'%')
            </if>

        </where>
    </sql>

    <select id="getDeliveryOrderCodeList" resultType="java.util.Map">
        select bermr.delivery_order_code as deliveryOrderCode, berb.receive_state as receiveState
        from bz_waybill bw
                 left join bz_waybill_box_rel bwbr on bw.waybill_code = bwbr.waybill_code
                 left join bz_erp_req_material_rel bermr on bermr.box_code = bwbr.box_code
                 left join bz_erp_req_box berb on bwbr.box_code = berb.box_code
        where bw.waybill_code = #{waybillCode}
    </select>

    <select id="warehouseAllocate" resultType="com.xiaopeng.halley.ascm.boot.dto.WarehouseAllocateVo">
        select bw.status, bw.order_type, bw.transport_type, bw.waybill_code, bw.total_box,
        bw.received_time, bw.circulation_time, bw.sign_time, bw.local_info, bw.local_update_time,
        bw.shop_code, bw.shop_name,bw.expiry_time, bw.lgort, bw.lgobe, concat(bw.lgobe,'-',bw.shop_city) route
        from bz_waybill bw left join base_shop bs on bw.shop_code = bs.shop_code
        <where>
            bw.status in('已完成','运输中','异常中') and bs.shop_code is not null
            <if test="param.startReceivedTime != null and param.endReceivedTime != null">
                and bw.received_time between #{param.startReceivedTime} and #{param.endReceivedTime}
            </if>
            <if test="param.startCirculationTime != null and param.endCirculationTime != null">
                and bw.circulation_time between #{param.startCirculationTime} and #{param.endCirculationTime}
            </if>
            <if test="param.startDepartureTime != null and param.endDepartureTime != null">
                and bw.departure_time between #{param.startDepartureTime} and #{param.endDepartureTime}
            </if>
            <if test="param.startSignTime != null and param.endSignTime != null">
                and bw.sign_time between #{param.startSignTime} and #{param.endSignTime}
            </if>
            <if test="param.waybillCode != null and param.waybillCode != ''">
                <foreach collection="param.waybillCode" item="waybillCode" open="or bw.waybill_code like"
                         separator="or bw.waybill_code like">
                    concat('%',#{waybillCode},'%')
                </foreach>
            </if>
            <if test="param.deliveryOrderCode != null and param.deliveryOrderCode != ''">
                <foreach collection="param.deliveryOrderCode" item="deliveryOrderCode"
                         open="or bermr.delivery_order_code like" separator="or bermr.delivery_order_code like">
                    concat('%',#{deliveryOrderCode},'%')
                </foreach>
            </if>
            <if test="param.shopCode != null and param.shopCode != ''">
                <foreach collection="param.shopCode" item="shopCode" open="or bw.shop_code like"
                         separator="or bw.shop_code like">
                    concat('%',#{shopCode},'%')
                </foreach>
            </if>
            <if test="param.shopCity != null and param.shopCity != ''">
                <foreach collection="param.shopCity" item="shopCity" open="or bw.shop_city like"
                         separator="or bw.shop_city like">
                    concat('%',#{shopCity},'%')
                </foreach>
            </if>
            <if test="param.shopProvince != null and param.shopProvince != ''">
                <foreach collection="param.shopProvince" item="shopProvince" open="or bw.shop_province like"
                         separator="or bw.shop_province like">
                    concat('%',#{shopProvince},'%')
                </foreach>
            </if>
            <if test="param.lgort != null and param.lgort != ''">
                <foreach collection="param.lgort" item="lgort" open="or bw.lgort like" separator="or bw.lgort like">
                    concat('%',#{lgort},'%')
                </foreach>
            </if>
            <if test="param.orderType != null and param.orderType != ''">
                and bw.order_type like concat('%',#{param.orderType},'%')
            </if>
            <if test="param.logisticsCode != null and param.logisticsCode != ''">
                and bw.logistics_code like concat('%',#{param.logisticsCode},'%')
            </if>
            <if test="param.transportType != null and param.transportType != ''">
                and bw.transport_type like concat('%',#{param.transportType},'%')
            </if>
            <if test="param.boxCode != null and param.boxCode != ''">
                <foreach collection="param.boxCode" item="boxCode" open="or berb.box_code like "
                         separator="or berb.box_code like">
                    concat('%',#{boxCode},'%')
                </foreach>
            </if>
        </where>

        <if test="param.receivedTime != null and param.receivedTime != ''">
            order by bw.received_time #{param.receivedTime}
        </if>
        <if test="page != null and size != null">
            limit #{page},#{size}
        </if>

    </select>

    <select id="getPageTotal" resultType="java.lang.Long">
        select count(1) from (
        select
        bw.id
        from bz_waybill bw

        <include refid="where_condition"/>

        <include refid="page_condition"/>

        group by bw.waybill_code
        ) as theList
    </select>

    <select id="getWarehouseAllocatePageTotal" resultType="java.lang.Long">
        select count(1) from bz_waybill bw left join base_shop bs on bw.shop_code = bs.shop_code
        <where>
            bw.status in('已完成','运输中','异常中') and bs.shop_code is not null
            <if test="param.startReceivedTime != null and param.endReceivedTime != null">
                and bw.received_time between #{param.startReceivedTime} and #{param.endReceivedTime}
            </if>
            <if test="param.startCirculationTime != null and param.endCirculationTime != null">
                and bw.circulation_time between #{param.startCirculationTime} and #{param.endCirculationTime}
            </if>
            <if test="param.startDepartureTime != null and param.endDepartureTime != null">
                and bw.departure_time between #{param.startDepartureTime} and #{param.endDepartureTime}
            </if>
            <if test="param.startSignTime != null and param.endSignTime != null">
                and bw.sign_time between #{param.startSignTime} and #{param.endSignTime}
            </if>
            <if test="param.waybillCode != null and param.waybillCode != ''">
                <foreach collection="param.waybillCode" item="waybillCode" open="or bw.waybill_code like"
                         separator="or bw.waybill_code like">
                    concat('%',#{waybillCode},'%')
                </foreach>
            </if>
            <if test="param.deliveryOrderCode != null and param.deliveryOrderCode != ''">
                <foreach collection="param.deliveryOrderCode" item="deliveryOrderCode"
                         open="or bermr.delivery_order_code like" separator="or bermr.delivery_order_code like">
                    concat('%',#{deliveryOrderCode},'%')
                </foreach>
            </if>
            <if test="param.shopCode != null and param.shopCode != ''">
                <foreach collection="param.shopCode" item="shopCode" open="or bw.shop_code like"
                         separator="or bw.shop_code like">
                    concat('%',#{shopCode},'%')
                </foreach>
            </if>
            <if test="param.shopCity != null and param.shopCity != ''">
                <foreach collection="param.shopCity" item="shopCity" open="or bw.shop_city like"
                         separator="or bw.shop_city like">
                    concat('%',#{shopCity},'%')
                </foreach>
            </if>
            <if test="param.shopProvince != null and param.shopProvince != ''">
                <foreach collection="param.shopProvince" item="shopProvince" open="or bw.shop_province like"
                         separator="or bw.shop_province like">
                    concat('%',#{shopProvince},'%')
                </foreach>
            </if>
            <if test="param.lgort != null and param.lgort != ''">
                <foreach collection="param.lgort" item="lgort" open="or bw.lgort like" separator="or bw.lgort like">
                    concat('%',#{lgort},'%')
                </foreach>
            </if>
            <if test="param.orderType != null and param.orderType != ''">
                and bw.order_type like concat('%',#{param.orderType},'%')
            </if>
            <if test="param.logisticsCode != null and param.logisticsCode != ''">
                and bw.logistics_code like concat('%',#{param.logisticsCode},'%')
            </if>
            <if test="param.transportType != null and param.transportType != ''">
                and bw.transport_type like concat('%',#{param.transportType},'%')
            </if>
            <if test="param.boxCode != null and param.boxCode != ''">
                <foreach collection="param.boxCode" item="boxCode" open="or berb.box_code like "
                         separator="or berb.box_code like">
                    concat('%',#{boxCode},'%')
                </foreach>
            </if>
        </where>
    </select>

    <select id="getMaterialDetailPageTotal" resultType="long">
        select count(1) from (select d.id, d.order_type, d.transport_type, d.status, d.waybill_code, d.total_box,
        d.car_plate,
        d.driver_name,
        a.box_code,
        a.delivery_order_code,
        a.matnr, a.maktx, a.package_count, a.receive_count, (a.package_count - a.receive_count) as shortfall_count,
        d.logistics_code, d.local_info, a.is_delete, c.is_devanning,
        case d.local_update_time when '1970-01-01 08:00:00' then null else d.local_update_time end as local_update_time,
        case d.received_time when '1970-01-01 08:00:00' then null else d.received_time end as received_time,
        case d.circulation_time when '1970-01-01 08:00:00' then null else d.circulation_time end as circulation_time,
        case d.departure_time when '1970-01-01 08:00:00' then null else d.departure_time end as departure_time,
        case d.sign_time when '1970-01-01 08:00:00' then null else d.sign_time end as sign_time,
        case d.expiry_time when '1970-01-01 08:00:00' then null else d.expiry_time end as expiry_time,
        d.shop_code, d.shop_name, d.warehouse_contact_num, d.lgort, d.lgobe,
        case when d.expiry_time > d.sign_time then '否' else '是' end as whether_timeout,
        concat(d.lgobe,'-',d.shop_city) route, d.shop_province, d.shop_city, d.is_delete as waybill_is_delete
        from bz_waybill d
        left join bz_waybill_box_rel c on c.waybill_code = d.waybill_code
        left join bz_erp_req_material_rel a on a.box_code = c.box_code) as a
        <include refid="paginationCriteria"/>
    </select>

    <select id="getMaterialDetailPageV2" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWaybillMaterialDetailPageVo">
        SELECT
        a.id AS erp_material_id,
        d.id,
        d.order_type,
        d.transport_type,
        d.status,
        d.waybill_code,
        d.total_box,
        a.box_code,
        d.car_plate,
        d.car_type,
        d.driver_name,
        a.delivery_order_code,
        a.origin_delivery_order_code,
        a.matnr,
        a.maktx,
        a.package_count,
        a.receive_count,
        (a.package_count - a.receive_count) AS shortfall_count,
        a.material_long,
        a.material_width,
        a.material_height,
        a.material_volume * a.package_count AS material_volume,
        a.material_weight * a.package_count AS material_weight,
        b.box_long,
        b.box_width,
        b.box_height,
        b.actual_volume,
        b.box_weight,
        d.logistics_code,
        d.local_info,
        a.is_delete,
        c.is_devanning,
        CASE d.local_update_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.local_update_time END AS local_update_time,
        CASE d.received_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.received_time END AS received_time,
        CASE d.circulation_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.circulation_time END AS circulation_time,
        CASE d.departure_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.departure_time END AS departure_time,
        CASE d.sign_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.sign_time END AS sign_time,
        CASE d.expiry_time WHEN '1970-01-01 08:00:00' THEN NULL ELSE d.expiry_time END AS expiry_time,
        d.shop_code,
        d.shop_name,
        d.warehouse_contact_num,
        d.lgort,
        d.lgobe,
        CASE WHEN d.expiry_time > d.sign_time THEN '否' ELSE '是' END AS whether_timeout,
        d.path AS route,
        d.shop_province,
        d.shop_city,
        d.is_delete AS waybill_is_delete
        FROM bz_waybill d
        LEFT JOIN bz_waybill_box_rel c ON c.waybill_code = d.waybill_code AND c.is_delete = 0
        LEFT JOIN bz_erp_req_box b ON b.box_code = c.box_code
        LEFT JOIN bz_erp_req_material_rel a ON a.box_code = c.box_code
        <where>
            a.is_delete = 0
            AND c.is_devanning = 0
            AND d.is_delete = 0
            AND d.total_box > 0
            <if test="param.lgortList != null and param.lgortList.size() == 1">
                AND d.lgort LIKE CONCAT('%', #{param.lgortList[0]}, '%')
            </if>

            <if test="param.lgortList != null and param.lgortList.size() > 1">
                AND d.lgort IN
                <foreach item="item" collection="param.lgortList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.driverName != null and param.driverName != ''">
                AND d.driver_name = #{param.driverName}
            </if>

            <if test="param.carPlate != null and param.carPlate != ''">
                AND d.car_plate LIKE CONCAT('%', #{param.carPlate}, '%')
            </if>

            <if test="param.waybillCodeList != null and param.waybillCodeList.size()>1">
                AND d.waybill_code IN
                <foreach item="item" collection="param.waybillCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.waybillCodeList != null and param.waybillCodeList.size()==1">
                AND d.waybill_code LIKE CONCAT('%', #{param.waybillCodeList[0]}, '%')
            </if>

            <if test="param.statusList != null and param.statusList.size()>1">
                AND d.status IN
                <foreach item="item" collection="param.statusList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.statusList != null and param.statusList.size()==1">
                AND d.status LIKE CONCAT('%', #{param.statusList[0]}, '%')
            </if>

            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>1">
                AND a.delivery_order_code IN
                <foreach item="item" collection="param.deliveryOrderCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()==1">
                AND a.delivery_order_code LIKE CONCAT('%', #{param.deliveryOrderCodeList[0]}, '%')
            </if>

            <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size == 1">
                AND a.origin_delivery_order_code like CONCAT('%',#{param.originDeliveryOrderCodeList[0]},'%')
            </if>

            <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>1">
                AND a.origin_delivery_order_code in
                <foreach item="item" collection="param.originDeliveryOrderCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()>1">
                AND d.shop_code IN
                <foreach item="item" collection="param.shopCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()==1">
                AND d.shop_code LIKE CONCAT('%', #{param.shopCodeList[0]}, '%')
            </if>

            <if test="param.matnrList != null and param.matnrList.size()>1">
                AND a.matnr IN
                <foreach item="item" collection="param.matnrList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.matnrList != null and param.matnrList.size()==1">
                AND a.matnr LIKE CONCAT('%', #{param.matnrList[0]}, '%')
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()>1">
                AND d.shop_city IN
                <foreach item="item" collection="param.shopCityList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()==1">
                AND d.shop_city LIKE CONCAT('%', #{param.shopCityList[0]}, '%')
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()>1">
                AND d.shop_province IN
                <foreach item="item" collection="param.shopProvinceList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()==1">
                AND d.shop_province LIKE CONCAT('%', #{param.shopProvinceList[0]}, '%')
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()>1">
                AND a.box_code IN
                <foreach item="item" collection="param.boxCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()==1">
                AND a.box_code LIKE CONCAT('%', #{param.boxCodeList[0]}, '%')
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()>1">
                AND d.logistics_code IN
                <foreach item="item" collection="param.logisticsCodeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()==1">
                AND d.logistics_code LIKE CONCAT('%', #{param.logisticsCodeList[0]}, '%')
            </if>

            <if test="param.orderType != null and param.orderType != ''">
                AND d.order_type = #{param.orderType}
            </if>

            <if test="param.whetherTimeout != null and param.whetherTimeout != ''">
                AND (CASE WHEN d.expiry_time > d.sign_time THEN 0 ELSE 1 END) = #{param.whetherTimeout}
            </if>

            <if test="param.transportType != null and param.transportType != ''">
                AND d.transport_type = #{param.transportType}
            </if>

            <if test="param.shortfallCount != null and param.shortfallCount == 0">
                AND (a.package_count - a.receive_count) = #{param.shortfallCount}
            </if>

            <if test="param.shortfallCount != null and param.shortfallCount == 1">
                AND (a.package_count - a.receive_count) >= #{param.shortfallCount}
            </if>

            <if test="param.startReceivedTime != null and param.endReceivedTime != null">
                AND d.received_time BETWEEN #{param.startReceivedTime} AND #{param.endReceivedTime}
            </if>

            <if test="param.startCirculationTime != null and param.endCirculationTime != null">
                AND d.circulation_time BETWEEN #{param.startCirculationTime} AND #{param.endCirculationTime}
            </if>

            <if test="param.startDepartureTime != null and param.endDepartureTime != null">
                AND d.departure_time BETWEEN #{param.startDepartureTime} AND #{param.endDepartureTime}
            </if>

            <if test="param.startSignTime != null and param.endSignTime != null">
                AND d.sign_time BETWEEN #{param.startSignTime} AND #{param.endSignTime}
            </if>
        </where>
    </select>

    <select id="getMaterialDetailPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWaybillMaterialDetailPageVo">
        select * from (select a.id as erp_material_id, d.id, d.order_type, d.transport_type, d.status, d.waybill_code, d.total_box, a.box_code,
        d.car_plate,
        d.car_type,
        d.driver_name,
        a.delivery_order_code,
        a.matnr, a.maktx, a.package_count, a.receive_count, (a.package_count - a.receive_count) as shortfall_count,
        a.material_long, a.material_width, a.material_height,
        a.material_volume * a.package_count as material_volume, a.material_weight * a.package_count as material_weight,
        b.box_long, b.box_width, b.box_height, b.actual_volume, b.box_weight,
        d.logistics_code, d.local_info, a.is_delete, c.is_devanning,
        case d.local_update_time when '1970-01-01 08:00:00' then null else d.local_update_time end as local_update_time,
        case d.received_time when '1970-01-01 08:00:00' then null else d.received_time end as received_time,
        case d.circulation_time when '1970-01-01 08:00:00' then null else d.circulation_time end as circulation_time,
        case d.departure_time when '1970-01-01 08:00:00' then null else d.departure_time end as departure_time,
        case d.sign_time when '1970-01-01 08:00:00' then null else d.sign_time end as sign_time,
        case d.expiry_time when '1970-01-01 08:00:00' then null else d.expiry_time end as expiry_time,
        d.shop_code, d.shop_name, d.warehouse_contact_num, d.lgort, d.lgobe,
        case when d.expiry_time > d.sign_time then '否' else '是' end as whether_timeout,
        d.path route, d.shop_province, d.shop_city, d.is_delete as waybill_is_delete
        from bz_waybill d
        left join bz_waybill_box_rel c on c.waybill_code = d.waybill_code
        left join bz_erp_req_box b on b.box_code = c.box_code
        left join bz_erp_req_material_rel a on a.box_code = c.box_code) as a
        <include refid="paginationCriteria"/>
        <if test="startIndex != null and size != null">
            limit #{startIndex},#{size}
        </if>

    </select>

    <sql id="paginationCriteria">
        <where>
            a.is_delete = 0 and a.is_devanning = 0 and a.waybill_is_delete = 0
            <if test="param.lgortList != null and param.lgortList.size() == 1">
                AND lgort like CONCAT('%',#{param.lgortList[0]},'%')
            </if>

            <if test="param.lgortList != null and param.lgortList.size()>1">
                AND lgort in
                <foreach item="item" collection="param.lgortList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.driverName != null and param.driverName != ''">
                AND driver_name = #{param.driverName}
            </if>
            <if test="param.carPlate != null and param.carPlate != ''">
                AND car_plate like CONCAT('%',#{param.carPlate},'%')
            </if>
            <if test="param.waybillCodeList != null and param.waybillCodeList.size()>1">
                AND a.waybill_code in
                <foreach item="item" collection="param.waybillCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.waybillCodeList != null and param.waybillCodeList.size()==1">
                AND a.waybill_code like CONCAT('%',#{param.waybillCodeList[0]},'%')
            </if>

            <if test="param.statusList != null and param.statusList.size()>1">
                AND a.status in
                <foreach item="item" collection="param.statusList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.statusList != null and param.statusList.size()==1">
                AND a.status like CONCAT('%',#{param.statusList[0]},'%')
            </if>

            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>1">
                AND a.delivery_order_code in
                <foreach item="item" collection="param.deliveryOrderCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()==1">
                AND a.delivery_order_code like CONCAT('%',#{param.deliveryOrderCodeList[0]},'%')
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()>1">
                AND a.shop_code in
                <foreach item="item" collection="param.shopCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodeList != null and param.shopCodeList.size()==1">
                AND a.shop_code like CONCAT('%',#{param.shopCodeList[0]},'%')
            </if>

            <if test="param.matnrList != null and param.matnrList.size()>1">
                AND a.matnr in
                <foreach item="item" collection="param.matnrList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.matnrList != null and param.matnrList.size()==1">
                AND a.matnr like CONCAT('%',#{param.matnrList[0]},'%')
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()>1">
                AND a.shop_city in
                <foreach item="item" collection="param.shopCityList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCityList != null and param.shopCityList.size()==1">
                AND a.shop_city like CONCAT('%',#{param.shopCityList[0]},'%')
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()>1">
                AND a.shop_province in
                <foreach item="item" collection="param.shopProvinceList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopProvinceList != null and param.shopProvinceList.size()==1">
                AND a.shop_province like CONCAT('%',#{param.shopProvinceList[0]},'%')
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()>1">
                AND a.box_code in
                <foreach item="item" collection="param.boxCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.boxCodeList != null and param.boxCodeList.size()==1">
                AND a.box_code like CONCAT('%',#{param.boxCodeList[0]},'%')
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()>1">
                AND a.logistics_code in
                <foreach item="item" collection="param.logisticsCodeList" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()==1">
                AND a.logistics_code like CONCAT('%',#{param.logisticsCodeList[0]},'%')
            </if>

            <if test="param.orderType != null and param.orderType != ''">
                and a.order_type = #{param.orderType}
            </if>

            <if test="param.whetherTimeout != null and param.whetherTimeout != ''">
                and a.whether_timeout = #{param.whetherTimeout}
            </if>

            <if test="param.transportType != null and param.transportType != ''">
                and a.transport_type = #{param.transportType}
            </if>

            <if test="param.shortfallCount != null and param.shortfallCount == 0">
                and a.shortfall_count = #{param.shortfallCount}
            </if>

            <if test="param.shortfallCount != null and param.shortfallCount == 1">
                and a.shortfall_count >= #{param.shortfallCount}
            </if>

            <if test="param.startReceivedTime != null and param.endReceivedTime != null">
                and a.received_time between #{param.startReceivedTime} and #{param.endReceivedTime}
            </if>
            <if test="param.startCirculationTime != null and param.endCirculationTime != null">
                and a.circulation_time between #{param.startCirculationTime} and #{param.endCirculationTime}
            </if>
            <if test="param.startDepartureTime != null and param.endDepartureTime != null">
                and a.departure_time between #{param.startDepartureTime} and #{param.endDepartureTime}
            </if>
            <if test="param.startSignTime != null and param.endSignTime != null">
                and a.sign_time between #{param.startSignTime} and #{param.endSignTime}
            </if>
        </where>
    </sql>

    <select id="getDeliveryOrderCodes" resultType="java.util.Map">
        select bw.waybill_code as waybillCode, group_concat(distinct bermr.delivery_order_code) as deliveryOrderCode,
        group_concat(distinct bermr.origin_delivery_order_code) as originDeliveryOrderCode,
        group_concat(distinct berb.receive_state) as receiveState,
        group_concat(distinct bermr.delivery_order_type) as deliveryOrderType,
        min(bermr.plan_shipping_time) as planShippingTime from bz_waybill bw
        left join bz_waybill_box_rel bwbr on bw.waybill_code = bwbr.waybill_code
        left join bz_erp_req_material_rel bermr on bermr.box_code = bwbr.box_code
        left join bz_erp_req_box berb on bwbr.box_code = berb.box_code
        where bwbr.is_devanning =0 and bw.waybill_code in
        <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by bw.waybill_code
    </select>

    <select id="getDeliveryOrderCodeMap" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWayBillNewPageVo">
        select bw.waybill_code as waybillCode, group_concat(distinct bermr.delivery_order_code) as deliveryOrderCode
        from bz_waybill bw
        left join bz_waybill_box_rel bwbr on bw.waybill_code = bwbr.waybill_code
        left join bz_erp_req_material_rel bermr on bermr.box_code = bwbr.box_code
        where bwbr.is_devanning = 0 and bw.waybill_code in
        <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by bw.waybill_code
    </select>

    <sql id="where_condition">
        <if test="(param.boxCodeList != null and param.boxCodeList.size()>=1) and !(param.shopProvinceList != null and param.shopProvinceList.size()>=1) and !(param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>=1)">
            left join bz_erp_req_box berb on bw.waybill_code = berb.waybill_code
        </if>

        <if test="param.shopProvinceList != null and param.shopProvinceList.size()>=1">
            left join bz_erp_req_box berb on bw.waybill_code = berb.waybill_code
            left join bz_erp_req_material_rel bermr on berb.box_code =bermr.box_code
        </if>

        <if test="(param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>=1) or (param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>=1)">
            left join bz_erp_req_box berb on bw.waybill_code = berb.waybill_code
            left join bz_erp_req_material_rel bermr on berb.box_code =bermr.box_code
        </if>
    </sql>

    <resultMap id="syncWaybillState" type="com.xiaopeng.halley.ascm.boot.dto.SyncWaybillInfoDto">
        <result property="waybillCode" column="waybillCode"/>
        <collection property="boxList" ofType="com.xiaopeng.halley.ascm.boot.dto.BoxListInfoDto">
            <result property="boxCode" column="boxCode"/>
            <collection property="deliveryOrderCodeList" ofType="java.lang.String">
                <result property="deliveryOrderCode" column="deliveryOrderCode"/>
            </collection>
        </collection>
    </resultMap>

    <select id="syncWaybillTransportInfo" resultMap="syncWaybillState">
        select bw.waybill_code as waybillCode, bwbr.box_code as boxCode, bermr.delivery_order_code as deliveryOrderCode
        from bz_waybill bw,
             bz_waybill_box_rel bwbr,
             bz_erp_req_box berb,
             bz_erp_req_material_rel bermr
        where bw.waybill_code = bwbr.waybill_code
          and bwbr.box_code = berb.box_code
          and berb.box_code = bermr.box_code
          and bw.waybill_code = #{waybillCode}
          and bwbr.is_devanning = 0
          and bwbr.is_delete = 0
    </select>

    <select id="getNewPageItem" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWayBillNewPageVo">
        SELECT any_value(bw.id) as id,
        bw.car_type,
        bw.total_weight,
        bw.waybill_code,
        bw.status,
        bw.total_box,
        any_value(bw.is_hang_up) as isHangUp,
        bw.order_type,
        bw.transport_type,
        bw.total_volume,
        bw.werks as factoryCode,
        bw.factory_desc as factoryName,
        bw.shop_code,
        bw.is_devanning,
        bw.combine_count,
        bw.lgort,
        bw.lgobe,
        bw.car_plate,
        bw.is_complete_packing,
        bw.demolition_count,
        bw.warehouse_province,
        bw.warehouse_city,
        bw.warehouse_address,
        bw.warehouse_contact_num as warehouseContactNum,
        bw.warehouse_contact_person as warehouseContactPerson,
        case bw.departure_time when '1970-01-01 08:00:00' then null else bw.departure_time end as departureTime,
        bw.abnormal_cause,
        case bw.expiry_time when '1970-01-01 08:00:00' then null else bw.expiry_time end as expiryTime,
        case bw.received_time when '1970-01-01 08:00:00' then null else bw.received_time end as receivedTime,
        case bw.circulation_time when '1970-01-01 08:00:00' then null else bw.circulation_time end as circulationTime,
        case bw.sign_time when '1970-01-01 08:00:00' then null else bw.sign_time end as signTime,
        case bw.pda_shipping_time when '1970-01-01 08:00:00' then null else bw.pda_shipping_time end as pdaShippingTime,
        bw.is_abnormal,
        bw.is_ontime,
        bw.local_info,
        case bw.local_update_time when '1970-01-01 08:00:00' then null else bw.local_update_time end as localUpdateTime,
        bw.carrier_code,
        bc.carrier_name as carrierName,
        bc.contact_person as carrierContactPerson,
        bc.contact_num as carrierContactNum,
        bw.driver_name,
        bw.driver_phone,
        bw.logistics_code,
        bw.logistics_company,
        any_value(bw.is_temp) as isTemp,
        bw.create_time,
        any_value(bw.update_time) as updateTime,
        any_value(bw.hang_up_time) as hangUpTime,
        bw.path as route,
        case when bw.expiry_time > bw.sign_time then '未超时' else '超时' end as is_timeout
        FROM bz_waybill bw
        <include refid="join_criteria"/>
        WHERE bw.is_delete = 0
        <include refid="query_criteria"/>
        GROUP BY bw.waybill_code
        ORDER BY isHangUp DESC, hangUpTime ASC, isTemp DESC, updateTime DESC, id DESC
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <select id="getNewPageTotal" resultType="long">
        select COUNT(1) from (SELECT bw.id as id
        FROM bz_waybill bw
        <include refid="join_criteria"/>
        WHERE bw.is_delete = 0
        <include refid="query_criteria"/>
        GROUP BY bw.waybill_code) as a;
    </select>

    <sql id="join_criteria">
        LEFT JOIN base_carrier bc ON bw.carrier_code = bc.carrier_code
        <if test="(param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>=1) or (param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>=1) or (param.boxCodeList != null and param.boxCodeList.size() >= 1) or (param.planShippingTimeStart != null or param.planShippingTimeEnd != null)">
            LEFT JOIN bz_waybill_box_rel bwbr ON bw.waybill_code = bwbr.waybill_code
            LEFT JOIN bz_erp_req_material_rel bermr ON bwbr.box_code = bermr.box_code
        </if>
        <if test="(param.shopCityList != null and param.shopCityList.size() >= 1) or (param.shopProvinceList != null and param.shopProvinceList.size() >= 1) ">
            LEFT JOIN base_shop bs ON bw.shop_code = bs.shop_code
        </if>
    </sql>

    <sql id="query_criteria">
        and bw.total_box > 0
        <!-- 计划发运时间范围查询 -->
        <if test="param.planShippingTimeStart != null">
            AND bermr.plan_shipping_time >= #{param.planShippingTimeStart}
        </if>
        <if test="param.planShippingTimeEnd != null">
            AND bermr.plan_shipping_time &lt;= #{param.planShippingTimeEnd}
        </if>

        <!-- 计划到达时间范围查询 -->
        <if test="param.expiryTimeStart != null">
            AND bw.expiry_time >= #{param.expiryTimeStart}
        </if>
        <if test="param.expiryTimeEnd != null">
            AND bw.expiry_time &lt;= #{param.expiryTimeEnd}
        </if>

        <if test="param.waybillCodeList != null and param.waybillCodeList.size()>1">
            AND bw.waybill_code in
            <foreach item="item" collection="param.waybillCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.waybillCodeList != null and param.waybillCodeList.size()==1">
            AND bw.waybill_code like CONCAT('%',#{param.waybillCodeList[0]},'%')
        </if>

        <if test="param.orderType != null and param.orderType != ''">
            AND bw.order_type = #{param.orderType}
        </if>

        <if test="param.statusList != null and param.statusList != ''">
            AND bw.status = #{param.statusList[0]}
        </if>

        <if test="param.statusList != null and param.statusList.size()>1">
            AND bw.status in
            <foreach item="item" collection="param.statusList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>


        <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size == 1">
            AND bermr.delivery_order_code like CONCAT('%',#{param.deliveryOrderCodeList[0]},'%')
        </if>

        <if test="param.deliveryOrderCodeList != null and param.deliveryOrderCodeList.size()>1">
            AND bermr.delivery_order_code in
            <foreach item="item" collection="param.deliveryOrderCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size == 1">
            AND bermr.origin_delivery_order_code like CONCAT('%',#{param.originDeliveryOrderCodeList[0]},'%')
        </if>

        <if test="param.originDeliveryOrderCodeList != null and param.originDeliveryOrderCodeList.size()>1">
            AND bermr.origin_delivery_order_code in
            <foreach item="item" collection="param.originDeliveryOrderCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.shopCityList != null and param.shopCityList.size() == 1">
            AND bs.shop_city like CONCAT('%',#{param.shopCityList[0]},'%')
        </if>

        <if test="param.shopCityList != null and param.shopCityList.size()>1">
            AND bs.shop_city in
            <foreach item="item" collection="param.shopCityList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.shopCodeList != null and param.shopCodeList.size() == 1">
            AND bw.shop_code like CONCAT('%',#{param.shopCodeList[0]},'%')
        </if>

        <if test="param.shopCodeList != null and param.shopCodeList.size()>1">
            AND bw.shop_code in
            <foreach item="item" collection="param.shopCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.shopProvinceList != null and param.shopProvinceList.size() == 1">
            AND bs.shop_province like CONCAT('%',#{param.shopProvinceList[0]},'%')
        </if>

        <if test="param.shopProvinceList != null and param.shopProvinceList.size()>1">
            AND bw.shop_province in
            <foreach item="item" collection="param.shopProvinceList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.lgortList != null and param.lgortList.size() == 1">
            AND bw.lgort like CONCAT('%',#{param.lgortList[0]},'%')
        </if>

        <if test="param.lgortList != null and param.lgortList.size()>1">
            AND bw.lgort in
            <foreach item="item" collection="param.lgortList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.logisticsCodeList != null and param.logisticsCodeList.size == 1">
            AND bw.logistics_code like CONCAT('%',#{param.logisticsCodeList[0]},'%')
        </if>

        <if test="param.logisticsCodeList != null and param.logisticsCodeList.size()>1">
            AND bw.logistics_code in
            <foreach item="item" collection="param.logisticsCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.transportType != null and param.transportType != ''">
            AND bw.transport_type = #{param.transportType}
        </if>

        <if test="param.transportType != null and param.transportType != ''">
            AND bw.transport_type = #{param.transportType}
        </if>

        <if test="param.boxCodeList != null and param.boxCodeList.size() == 1">
            AND bermr.box_code like CONCAT('%',#{param.boxCodeList[0]},'%')
        </if>
        <if test="param.boxCodeList != null and param.boxCodeList.size()>1">
            AND bermr.box_code in
            <foreach item="item" collection="param.boxCodeList" index="index" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.startReceivedTime != null and param.endReceivedTime != null">
            AND bw.received_time BETWEEN #{param.startReceivedTime} AND #{param.endReceivedTime}
        </if>
        <if test="param.startCirculationTime != null and param.endCirculationTime != null">
            AND bw.circulation_time BETWEEN #{param.startCirculationTime} AND #{param.endCirculationTime}
        </if>
        <if test="param.startDepartureTime != null and param.endDepartureTime != null">
            AND bw.departure_time BETWEEN #{param.startDepartureTime} AND #{param.endDepartureTime}
        </if>
        <if test="param.startSignTime != null and param.endSignTime != null">
            AND bw.sign_time BETWEEN #{param.startSignTime} AND #{param.endSignTime}
        </if>
    </sql>

    <select id="matchTransport" resultType="map">
        SELECT bw.*, bs.shop_city
        FROM bz_waybill as bw
        LEFT JOIN base_shop as bs ON bw.shop_code = bs.shop_code
        WHERE bw.id IN
        <foreach item="item" collection="ids" index="index" open="(" separator=","
                 close=")">
            #{item}
        </foreach>
    </select>

    <select id="waybillJoinList" resultType="map">
        SELECT bw.waybill_code   as waybillCode,
               bw.total_box      as totalBox,
               bw.total_volume   as totalVolume,
               bw.order_type     as orderType,
               bw.transport_type as transportType,
               bw.shop_code      as shopCode,
               bw.shop_name      as shopName,
               bw.id             as id,
               bw.lgort          as lgort,
               bs.contact_num    as shopContactNum,
               bs.contact_person as shopContactPerson
        FROM bz_waybill as bw
                 LEFT JOIN base_shop as bs ON bw.shop_code = bs.shop_code
        WHERE bw.`is_delete` = 0
          AND bw.`is_hang_up` = 0
          AND bw.`is_temp` = 0
          AND bw.`total_box` > 0
          AND bs.`is_delete` = 0
          AND bw.`lgort` = #{bzWaybill.lgort}
          AND bw.`shop_code` = #{bzWaybill.shopCode}
          AND bw.`order_type` = #{bzWaybill.orderType}
          AND bw.`transport_type` = #{bzWaybill.transportType}
          AND bw.`status` = #{bzWaybill.status}
          AND bw.`id` != #{bzWaybill.id}
        LIMIT #{startIndex},#{size}
    </select>

    <select id="waybillJoinListTotal" resultType="long">
        SELECT count(1)
        FROM bz_waybill as bw
                 LEFT JOIN base_shop as bs ON bw.shop_code = bs.shop_code
        WHERE bw.`is_delete` = 0
          AND bw.`is_hang_up` = 0
          AND bw.`is_temp` = 0
          AND bw.`total_box` > 0
          AND bs.`is_delete` = 0
          AND bw.`lgort` = #{bzWaybill.lgort}
          AND bw.`shop_code` = #{bzWaybill.shopCode}
          AND bw.`order_type` = #{bzWaybill.orderType}
          AND bw.`transport_type` = #{bzWaybill.transportType}
          AND bw.`status` = #{bzWaybill.status}
          AND bw.`id` != #{bzWaybill.id}
    </select>

    <resultMap id="syncWaybillInfo" type="com.xiaopeng.halley.ascm.boot.dto.WaybillInfoSyncErpVO">
        <result property="waybillCode" column="waybillCode"/>
        <result property="departureTime" column="departureTime"/>
        <result property="signTime" column="signTime"/>
        <collection property="boxList" ofType="com.xiaopeng.halley.ascm.boot.dto.WaybillBoxListInfoDto">
            <result property="boxCode" column="boxCode"/>
            <collection property="deliveryOrderCodeList" ofType="java.lang.String">
                <result property="deliveryOrderCode" column="deliveryOrderCode"/>
            </collection>
        </collection>
    </resultMap>

    <select id="waybillTimeInfoSyncErp" resultMap="syncWaybillInfo">
        select bw.waybill_code                                                        as waybillCode,
               IF(bw.departure_time = '1970-01-01 08:00:00', null, bw.departure_time) as departureTime,
               IF(bw.sign_time = '1970-01-01 08:00:00', null, bw.sign_time)           as signTime,
               bwbr.box_code                                                          as boxCode,
               bermr.delivery_order_code                                              as deliveryOrderCode
        from bz_waybill bw,
             bz_waybill_box_rel bwbr,
             bz_erp_req_box berb,
             bz_erp_req_material_rel bermr
        where bw.waybill_code = bwbr.waybill_code
          and bwbr.box_code = berb.box_code
          and berb.box_code = bermr.box_code
          and bw.waybill_code = #{waybillCode}
          and bwbr.is_devanning = 0
          and bwbr.is_delete = 0
    </select>

    <select id="selectSyncData" resultType="com.xiaopeng.halley.ascm.boot.entity.BzWaybill">
        select bw.id as id ,bw.waybill_code as waybillCode,bw.shop_code as shopCode
        from bz_waybill as bw
        left join bz_waybill_info_sync as bwis on bw.id = bwis.waybill_id and bw.is_delete = 0 and bw.total_box > 0
        <where>
            <if test="waybillCodeList != null and waybillCodeList.size()>0">
                AND bw.waybill_code in
                <foreach item="item" collection="waybillCodeList" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="type == 1">
                and bwis.dispatch_time_sync = 0 and bw.status in ('运输中','异常中')
            </if>
            <if test="type == 2">
                and bwis.signed_time_sync = 0 and bw.status = '已完成'
            </if>
        </where>
    </select>

</mapper>