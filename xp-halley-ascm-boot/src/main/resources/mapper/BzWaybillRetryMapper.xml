<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzWaybillRetryMapper">


    <select id="getPageTotal" resultType="long">
        select count(1)
        from bz_waybill_retry bwr
        <include refid="QueryCriteria"/>
    </select>

    <select id="getPage" resultType="com.xiaopeng.halley.ascm.boot.dto.BzWaybillRetryVo">
        select bwr.id,
        bwr.waybill_code,
        bwr.warehouse_number,
        bwr.interface_number,
        bwr.interface_describe,
        bwr.shop_code,
        bwr.shop_describe,
        bwr.target_system,
        bwr.first_send_date,
        bwr.first_send_state,
        bwr.last_send_date,
        bwr.last_send_name,
        bwr.retry_return_msg,
        bwr.update_user_id,
        bwr.update_user_name,
        bwr.update_time
        from bz_waybill_retry bwr
        <include refid="QueryCriteria"/>
        order by first_send_date desc, waybill_code desc
        <if test="startIndex != null and size != ''">
            limit #{startIndex},#{size}
        </if>
    </select>

    <sql id="QueryCriteria">
        <where>
            bwr.is_show = 1
            <if test="param.interfaceNumbers != null and param.interfaceNumbers.size()>1">
                AND bwr.interface_number in
                <foreach item="item" collection="param.interfaceNumbers" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.interfaceNumbers != null and param.interfaceNumbers.size()==1">
                AND bwr.interface_number like CONCAT('%',#{param.interfaceNumbers[0]},'%')
            </if>

            <if test="param.waybillCodes != null and param.waybillCodes.size()>1">
                AND bwr.waybill_code in
                <foreach item="item" collection="param.waybillCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.waybillCodes != null and param.waybillCodes.size()==1">
                AND bwr.waybill_code like CONCAT('%',#{param.waybillCodes[0]},'%')
            </if>

            <if test="param.shopCodes != null and param.shopCodes.size()>1">
                AND bwr.shop_code in
                <foreach item="item" collection="param.shopCodes" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.shopCodes != null and param.shopCodes.size()==1">
                AND bwr.shop_code like CONCAT('%',#{param.shopCodes[0]},'%')
            </if>

            <if test="param.firstSendDateStart != null and param.firstSendDateEnd != null">
                and bwr.first_send_date between #{param.firstSendDateStart} and
                #{param.firstSendDateEnd}
            </if>
        </where>
    </sql>

</mapper>