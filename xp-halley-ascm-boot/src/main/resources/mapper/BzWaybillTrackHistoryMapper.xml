<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xiaopeng.halley.ascm.boot.mapper.BzWaybillTrackHistoryMapper">

    <select id="getAddressInfo" resultType="com.xiaopeng.halley.ascm.boot.dto.AddressInfo">
        select wth.waybill_code, bf.id as file_id, wth.address, wth.update_time
        from bz_waybill_track_history wth
        left join bz_waybill_file wf on wf.waybill_code = wth.waybill_code and wf.is_delete = 0
        left join base_file bf on wf.file_Id = bf.file_uuid and bf.is_delete = 0
        where wth.is_delete = 0
        <if test="waybillCodes != null and waybillCodes.size() > 0">
            AND wth.waybill_code in
            <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="insertList">
        insert
        into
        bz_waybill_track_history(waybill_code,
        province,
        city,
        address,
        latitude,
        longitude,
        driver_phone,
        track_status,
        update_time,
        create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.waybillCode},
            #{item.province},
            #{item.city},
            #{item.address},
            #{item.latitude},
            #{item.longitude},
            #{item.driverPhone},
            #{item.trackStatus},
            #{item.updateTime},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <select id="getMapDataByWaybillCode" resultType="java.util.Map">
        select waybill_code                            waybillCode,
               city,
               date_format(create_time, '%Y-%m-%d') as transportTime
        from bz_waybill_track_history
        where waybill_code = #{waywillCode}
          and is_delete = 0
    </select>

    <select id="getWaybillTransportationList"
            resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO">
        select waybill_code as waybillCode,
        city,
        date_format(update_time, '%Y-%m-%d %H:%i:%s') updateTime,
        address as address
        from bz_waybill_track_history
        where waybill_code IN
        <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and (city != '' or waybill_code LIKE 'JD%')
        and is_delete = 0
        order by update_time desc
    </select>

    <select id="getWaybillTransportationDTO"
            resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayBillTrackHistoryVO">
        select waybill_code as                               waybillCode,
               city,
               date_format(update_time, '%Y-%m-%d %H:%i:%s') updateTime,
               (case track_status
                    when 0 then '初始点'
                    when 1 then '转交'
                    when 2 then '在途点'
                    when 3 then '交付'
                    when 4 then '快递'
                   end)
                                                             trackStatus,
               driver_name                                   driverName,
               driver_phone                                  driverPhone,
               latitude                                      latitude,
               longitude                                     longitude,
               address                                       address
        from bz_waybill_track_history
        where waybill_code = #{waybillCode}
          and is_delete = 0
        order by update_time desc
    </select>

    <select id="getWaybillImage" resultType="com.xiaopeng.halley.ascm.boot.vo.miniProgram.BzWayImageVO">
        select file_id     as fileId,
               upload_type as uploadType,
               (case upload_type
                    when 1 then '签收'
                    when 2 then '异常'
                   end)    as uploadTypeShow
        from bz_waybill_file
        where waybill_code = #{waybillCode}
          and is_delete = 0
        order by update_time asc
    </select>

    <select id="getAddress" resultType="com.xiaopeng.halley.ascm.boot.entity.BzWaybillTrackHistory">
        SELECT b.id as id, b.waybill_code as waybill_code, b.update_time as create_time, b.address as address
        FROM bz_waybill_track_history b
        WHERE b.is_delete = 0 and b.waybill_code IN
        <foreach item="item" collection="waybillCodes" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by b.update_time desc
    </select>


</mapper>