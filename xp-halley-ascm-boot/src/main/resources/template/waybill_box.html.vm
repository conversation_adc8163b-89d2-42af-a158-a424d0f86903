## @vtlvariable name="logoData" type="java.lang.String"
## @vtlvariable name="totalVolume" type="java.lang.Double"
## @vtlvariable name="imageData" type="java.lang.String"
## @vtlvariable name="total" type="java.lang.Integer"
## @vtlvariable name="printTime" type="java.lang.String"
## @vtlvariable name="waybill" type="com.xiaopeng.halley.ascm.boot.dto.BzWayBillPageVo"
## @vtlvariable name="boxList" type="java.util.List"
## @vtlvariable name="boxItem" type="com.xiaopeng.halley.ascm.boot.dto.BoxParamDto"
## @vtlvariable name="materialList" type="java.util.List"
## @vtlvariable name="materialItem" type="com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel"
## @vtlvariable name="DateUtil" type="cn.hutool.core.date.DateUtil"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客户装箱单</title>
</head>
<body>

<div class="header">
    客户装箱单
</div>
<table class="info-table" style="margin: 15px 0">
    <tr>
        <td>客户</td>
        <td colspan="5" class="col-span-auto">${waybill.shopName}</td>
        <td>日期</td>
        <td>${printTime}</td>
    </tr>
    <tr>
        <td>地址</td>
        <td colspan="5" class="col-span-auto">${waybill.shopAddress}</td>
        <td>客户号</td>
        <td>${waybill.shopCode}</td>
    </tr>
    <tr>
        <td>总箱数</td>
        <td colspan="1" class="col-span-auto">${waybill.totalBox}</td>
        <td>合计件数</td>
        <td colspan="1" class="col-span-auto">${total}</td>
        <td>总体积</td>
        <td colspan="1" class="col-span-auto">${waybill.totalVolume}m³</td>
        <td>运输方式</td>
        <td colspan="1" class="col-span-auto">${waybill.transportType}</td>
    </tr>
</table>
<table class="order-details-table">
    <tr>
        <th>序号</th>
        <th>箱号</th>
        <th>交货单号</th>
        <th>物料号</th>
        <th>中文名</th>
        <th>装箱类别</th>
        <th>数量</th>
        <th>备注</th>
    </tr>

    #foreach (${materialItem} in ${materialList})
        <tr>
            <td style="font-size: 12px">${velocityCount}</td>
            <td>${materialItem.boxCode}</td>
            <td>${materialItem.deliveryOrderCode}</td>
            <td>${materialItem.matnr}</td>
            <td>${materialItem.maktx}</td>
            <td>${materialItem.packageType}</td>
            <td>${materialItem.quantity}</td>
            <td></td>
        </tr>
    #end
</table>
<div></div>
</body>

<style>
    body {
        font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
        font-size: 10.5pt; /* 调整基础字体大小 */
        margin: 20px auto;
        max-width: 800px; /* 限制页面最大宽度，模拟A4纸效果 */
        line-height: 1.5;
        color: #333;
    }

    .header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }

    .qrCode {
        width: 120px;
        height: 120px;
    }

    .logo {
        position: absolute; /* 设置绝对定位 */
        right: 0; /* 右对齐 */
        top: 0; /* 顶部对齐 */
        width: 77px;
        height: 40px;
        margin-bottom: 20px;
    }

    .waybill-info {
        display: flex;
        margin-bottom: 10px;
        font-weight: bold;
    }

    .freight-type {
        font-size: 17px;
        float: right;
        font-weight: normal;
    }

    .section-title {
        font-weight: bold;
        margin-top: 15px;
        margin-bottom: 5px;
        padding-bottom: 3px;
        font-size: 15px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
        font-size: 10pt; /* 表格内字体大小 */
    }

    table, th, td {
        border: 1px solid #000;
    }

    th, td {
        padding: 6px 8px; /* 调整内边距 */
        text-align: left;
        vertical-align: middle; /* 垂直居中 */
    }

    th {
        background-color: #f0f0f0; /* 表头背景色 */
        font-weight: bold;
        text-align: center; /* 表头文字居中 */
        white-space: nowrap; /* 防止表头换行 */
    }

    td {
        white-space: nowrap; /* 防止单元格内容换行，根据内容调整 */
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .info-table {
        table-layout: fixed;
        width: 100%;
    }

    /* 特殊表格布局 */
    .info-table td:nth-child(odd) { /* 键（左侧）列 */
        width: 40px; /* 示例宽度 */
        background-color: #f8f8f8; /* 浅灰背景，与图片一致 */
        font-weight: bold;
        text-align: left; /* 键文本右对齐 */
    }

    .test {
        width: 40px !important;
    }

    .info-table td:nth-child(even) { /* 值（右侧）列 */
        background-color: #ffffff;
        font-weight: normal;
    }

    .order-details-table th, .order-details-table td {
        text-align: center; /* 订单详情表格内容居中 */
    }

    .order-details-table td, .info-table td {
        white-space: normal; /* 确保允许换行 */
        word-wrap: break-word; /* IE 兼容性较好 */
        word-break: break-word;
        font-size: 10px;
        font-family: 'Microsoft YaHei', serif;
    }

    .total-row td {
        font-weight: bold;
        background-color: #f0f0f0;
    }

    .total-row td:first-child {
        text-align: center;
    }

    .total-row td:nth-child(5) { /* 合计后面那个单元格，让"合计"跨列到这里 */
        border-right: none; /* 合计单元格右边框去除 */
    }

    .total-row td:nth-child(6) { /* 体积合计 */
        text-align: center;
    }

    .signature-table {
        border: none; /* 签收信息表格整体无边框 */
    }

    .signature-table td {
        border: 1px solid #000; /* 单元格有边框 */
        height: 20px; /* 增加高度以便签名 */
        width: 33.33%; /* 三等分 */
        vertical-align: bottom; /* 文本靠下 */
        padding: 5px;
    }

    .signature-table tr:last-child td {
        height: 25px; /* 异常备注行高度 */
        vertical-align: top; /* 异常备注文本靠上 */
    }

    /* 打印时页码的样式，通常用CSS @page规则结合生成PDF的工具 */
    .page-footer {
        page-break-before: always;
    }
</style>
</html>