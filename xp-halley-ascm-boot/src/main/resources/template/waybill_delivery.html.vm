## @vtlvariable name="logoData" type="java.lang.String"
## @vtlvariable name="totalVolume" type="java.lang.Double"
## @vtlvariable name="imageData" type="java.lang.String"
## @vtlvariable name="total" type="java.lang.Integer"
## @vtlvariable name="printTime" type="java.lang.String"
## @vtlvariable name="waybill" type="com.xiaopeng.halley.ascm.boot.dto.BzWayBillPageVo"
## @vtlvariable name="boxList" type="java.util.List"
## @vtlvariable name="boxItem" type="com.xiaopeng.halley.ascm.boot.dto.BoxParamDto"
## @vtlvariable name="materialList" type="java.util.List"
## @vtlvariable name="materialItem" type="com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel"
## @vtlvariable name="DateUtil" type="cn.hutool.core.date.DateUtil"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运输交接单</title>
</head>
<body>

<div class="header">
    运输交接单
    <img class="logo" src="${logoData}" alt=""/>
</div>

<img class="qrCode" src="${imageData}" alt=""/>

<div class="freight-type">${waybill.transportType}</div>
<div class="waybill-info">
    <span>运单编号: ${waybill.waybillCode}</span>
</div>

<div style="float: right">日期 <span class="date-time">${printTime}</span></div>
<div class="section-title">收方信息</div>
<table class="info-table">
    <tr>
        <td>客户名称</td>
        <td colspan="3" class="col-span-auto">${waybill.shopName}</td>
        <td>客户代码</td>
        <td>${waybill.shopCode}</td>
    </tr>
    <tr>
        <td>收件信息</td>
        <td colspan="5" class="col-span-auto">${waybill.shopAddress}</td>
    </tr>
    <tr>
        <td>目的地</td>
        <td class="col-span-auto">${waybill.shopCity}</td>
        <td>联系人</td>
        <td class="col-span-auto">${waybill.shopContactPerson}</td>
        <td>电话</td>
        <td class="col-span-auto">${waybill.shopContactNum}</td>
    </tr>
</table>

<div class="section-title">寄方信息</div>
<table class="info-table">
    <tr>
        <td>发运仓库</td>
        <td colspan="5" class="col-span-auto">${waybill.lgobe}</td>
    </tr>
    <tr>
        <td>寄方信息</td>
        <td colspan="5" class="col-span-auto">${waybill.warehouseAddress}</td>
    </tr>
    <tr>
        <td>联系人</td>
        <td colspan="3" class="col-span-auto">${waybill.warehouseContactPerson}</td>
        <td>电话</td>
        <td colspan="1" class="col-span-auto">${waybill.warehouseContactNum}</td>
    </tr>
</table>

<div class="section-title">订单信息</div>
<table class="order-details-table">
    <tr>
        <td>交货单</td>
        <td colspan="8" style="text-align: left"> ${waybill.deliveryOrderCode}</td>
    </tr>

    <tr>
        <th>序号</th>
        <th style="width: 120px">箱号</th>
        <th>箱编码</th>
        <th>长(mm)</th>
        <th>宽(mm)</th>
        <th>高(mm)</th>
        <th>体积(m³)</th>
        <th>折合重</th>
        <th>装箱类</th>
        <th>备注</th>
    </tr>
    #foreach (${boxItem} in ${boxList})
        <tr>
            <td>${velocityCount}</td>
            <td>${boxItem.boxCode}</td>
            <td>${boxItem.packageCode}</td>
            <td>${boxItem.boxLong}</td>
            <td>${boxItem.boxWidth}</td>
            <td>${boxItem.boxHeight}</td>
            <td>${boxItem.actualVolume}</td>
            <td>${boxItem.boxWeight}</td>
            <td>${boxItem.packageType}</td>
            <td></td>
        </tr>
    #end
    <tr class="total-row">
        <td colspan="1" style="text-align: center; border-right: 1px solid #000;">合计</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td>${totalVolume}</td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
</table>

<div class="section-title">签收信息</div>
<table class="signature-table">
    <tr>
        <td style="text-align: center">发运仓库确认</td>
        <td style="text-align: center">物流运输确认</td>
        <td style="text-align: center">门店签收确认</td>
    </tr>
    <tr>
        <td style="height: 30px"></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td colspan="3" style="text-align: left; padding-top: 10px;">异常备注:</td>
    </tr>
</table>
<div></div>
</body>

<style>
    body {
        font-family: 'Microsoft YaHei', 'SimSun', sans-serif;
        font-size: 10.5pt; /* 调整基础字体大小 */
        margin: 20px auto;
        max-width: 800px; /* 限制页面最大宽度，模拟A4纸效果 */
        line-height: 1.5;
        color: #333;
    }

    .header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
    }

    .qrCode {
        width: 120px;
        height: 120px;
    }

    .logo {
        position: absolute; /* 设置绝对定位 */
        right: 0; /* 右对齐 */
        top: 0; /* 顶部对齐 */
        width: 77px;
        height: 40px;
        margin-bottom: 20px;
    }

    .waybill-info {
        display: flex;
        margin-bottom: 10px;
        font-weight: bold;
    }

    .freight-type {
        font-size: 17px;
        float: right;
        font-weight: normal;
    }

    .section-title {
        font-weight: bold;
        margin-top: 15px;
        margin-bottom: 5px;
        padding-bottom: 3px;
        font-size: 15px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
        font-size: 10pt; /* 表格内字体大小 */
    }

    table, th, td {
        border: 1px solid #000;
    }

    th, td {
        padding: 6px 8px; /* 调整内边距 */
        text-align: left;
        vertical-align: middle; /* 垂直居中 */
    }

    th {
        background-color: #f0f0f0; /* 表头背景色 */
        font-weight: bold;
        text-align: center; /* 表头文字居中 */
        white-space: nowrap; /* 防止表头换行 */
    }

    td {
        white-space: nowrap; /* 防止单元格内容换行，根据内容调整 */
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .info-table {
        table-layout: fixed;
        width: 100%;
    }

    /* 特殊表格布局 */
    .info-table td:nth-child(odd) { /* 键（左侧）列 */
        width: 45px; /* 示例宽度 */
        background-color: #f8f8f8; /* 浅灰背景，与图片一致 */
        font-weight: bold;
        text-align: left; /* 键文本右对齐 */
    }

    .info-table td:nth-child(even) { /* 值（右侧）列 */
        background-color: #ffffff;
        font-weight: normal;
    }

    .order-details-table th, .order-details-table td {
        text-align: center; /* 订单详情表格内容居中 */
    }

    .order-details-table td, .info-table td {
        white-space: normal; /* 确保允许换行 */
        word-wrap: break-word; /* IE 兼容性较好 */
        word-break: break-word;
        font-size: 10px;
        font-family: 'Microsoft YaHei', serif;
    }

    .total-row td {
        font-weight: bold;
        background-color: #f0f0f0;
    }

    .total-row td:first-child {
        text-align: center;
    }

    .total-row td:nth-child(5) { /* 合计后面那个单元格，让"合计"跨列到这里 */
        border-right: none; /* 合计单元格右边框去除 */
    }

    .total-row td:nth-child(6) { /* 体积合计 */
        text-align: center;
    }

    .signature-table {
        border: none; /* 签收信息表格整体无边框 */
    }

    .signature-table td {
        border: 1px solid #000; /* 单元格有边框 */
        height: 20px; /* 增加高度以便签名 */
        width: 33.33%; /* 三等分 */
        vertical-align: bottom; /* 文本靠下 */
        padding: 5px;
    }

    .signature-table tr:last-child td {
        height: 25px; /* 异常备注行高度 */
        vertical-align: top; /* 异常备注文本靠上 */
    }

    /* 打印时页码的样式，通常用CSS @page规则结合生成PDF的工具 */
    .page-footer {
        page-break-before: always;
    }
</style>
</html>