package com.xiaopeng.halley.ascm.boot.service;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xiaopeng.halley.ascm.boot.dto.*;
import com.xiaopeng.halley.ascm.boot.entity.BaseMaterials;
import com.xiaopeng.halley.ascm.boot.entity.BaseSuppliers;
import com.xiaopeng.halley.ascm.boot.entity.BzSupplierMaterial;
import com.xiaopeng.halley.ascm.boot.entity.BzSupplierPackageInfo;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.utils.PageQuery;
import com.xpeng.athena.common.core.domain.Result;
import com.xpeng.athena.common.core.exception.ResultException;
import com.xpeng.athena.common.core.util.ResultUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.instancio.Instancio;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringRunner.class)
public class BaseMaterialsServiceTest {

    @Spy
    protected BaseSuppliersMapper mockBaseSuppliersMapper;
    @Spy
    protected BzSupplierMaterialMapper mockBzSupplierMaterialMapper;
    @Spy
    protected BzSupplierPackageInfoMapper mockBzSupplierPackageInfoMapper;
    @Spy
    @InjectMocks
    protected ImageService mockImageService;
    @Spy
    protected BaseMaterialsMapper mockBaseMaterialsMapper;
    @Spy
    protected BzDeliveryOrderMaterialMapper mockBzDeliveryOrderMaterialMapper;
    @Spy
    protected BzDeliveryOrdersMapper mockBzDeliveryOrdersMapper;

    @InjectMocks
    private BaseMaterialsService baseMaterialsServiceUnderTest;

    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), BaseMaterials.class);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetPage() throws ResultException {
        // Setup
        final PageQuery<GetMaterialPageRequestDto> page = new PageQuery<GetMaterialPageRequestDto>();
        page.setParam(new GetMaterialPageRequestDto());
        page.setPage(1);
        page.setSize(20);

        doReturn(100L).when(mockBaseMaterialsMapper).getPageTotal(any());

        // Configure BaseMaterialsMapper.getPage(...).
        final GetMaterialPageResonseVo getMaterialPageResonseVo = Instancio.create(GetMaterialPageResonseVo.class);
        final List<GetMaterialPageResonseVo> materialPageResonseVos = Arrays.asList(getMaterialPageResonseVo);
        doReturn(materialPageResonseVos).when(mockBaseMaterialsMapper).getPage(new GetMaterialPageRequestDto(), 0, 20);

        doReturn("picLeft").when(mockImageService).getOssUrl(any());

        // Run the test
        final Page<GetMaterialPageResonseVo> result = baseMaterialsServiceUnderTest.getPage(page);
        System.out.println(result.getRecords());
        System.out.println(result.getTotal());
        // Verify the results
    }

    @Test
    public void testGetPage_BaseMaterialsMapperGetPageReturnsNoItems() throws ResultException {
        // Setup
        final PageQuery<GetMaterialPageRequestDto> page = new PageQuery<>();

        doReturn(0L).when(mockBaseMaterialsMapper).getPageTotal(any());
        doReturn(Collections.emptyList()).when(mockBaseMaterialsMapper).getPage(new GetMaterialPageRequestDto(), 0, 20);

        // Run the test
        final Page<GetMaterialPageResonseVo> result = baseMaterialsServiceUnderTest.getPage(page);
        System.out.println(result.getRecords());
        // Verify the results
    }

    @Test
    public void testDetail() throws ResultException {
        // Setup
        final GetMaterialDetailRequestVo param = new GetMaterialDetailRequestVo(0L);
        final Result<GetMaterialDetailResponseVo> expectedResult = ResultUtil.success();

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn("picLeft").when(mockImageService).getOssUrl(any());

        // Configure BzSupplierMaterialMapper.selectList(...).
        final BzSupplierMaterial bzSupplierMaterial = Instancio.create(BzSupplierMaterial.class);
        final List<BzSupplierMaterial> bzSupplierMaterials = Arrays.asList(bzSupplierMaterial);
        doReturn(bzSupplierMaterials).when(mockBzSupplierMaterialMapper).selectList(any());

        // Configure BaseSuppliersMapper.selectList(...).
        final BaseSuppliers baseSuppliers1 = Instancio.create(BaseSuppliers.class);
        final List<BaseSuppliers> baseSuppliers = Arrays.asList(baseSuppliers1);
        doReturn(baseSuppliers).when(mockBaseSuppliersMapper).selectList(any());

        // Run the test
        final Result<GetMaterialDetailResponseVo> result = baseMaterialsServiceUnderTest.detail(param);
        System.out.println(result.getData());
        // Verify the results
    }

    @Test
    public void testDetail_BzSupplierMaterialMapperReturnsNoItems() throws ResultException {
        // Setup
        final GetMaterialDetailRequestVo param = new GetMaterialDetailRequestVo(0L);
        final Result<GetMaterialDetailResponseVo> expectedResult = ResultUtil.success();

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn("picLeft").when(mockImageService).getOssUrl(any());
        doReturn(Collections.emptyList()).when(mockBzSupplierMaterialMapper).selectList(any());

        // Run the test
        final Result<GetMaterialDetailResponseVo> result = baseMaterialsServiceUnderTest.detail(param);
        System.out.println(result.getData());
    }

    @Test
    public void testDetail_BaseSuppliersMapperReturnsNoItems() throws ResultException {
        // Setup
        final GetMaterialDetailRequestVo param = new GetMaterialDetailRequestVo(0L);
        final Result<GetMaterialDetailResponseVo> expectedResult = ResultUtil.success();

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn("picLeft").when(mockImageService).getOssUrl(any());

        // Configure BzSupplierMaterialMapper.selectList(...).
        final BzSupplierMaterial bzSupplierMaterial = Instancio.create(BzSupplierMaterial.class);
        final List<BzSupplierMaterial> bzSupplierMaterials = Arrays.asList(bzSupplierMaterial);
        doReturn(bzSupplierMaterials).when(mockBzSupplierMaterialMapper).selectList(any());

        doReturn(Collections.emptyList()).when(mockBaseSuppliersMapper).selectList(any());

        // Run the test
        final Result<GetMaterialDetailResponseVo> result = baseMaterialsServiceUnderTest.detail(param);
        System.out.println(result.getData());
    }

    @Test
    public void testQuerySupplierPackage() {
        // Setup
        final QuerySupplierPackageDto param = new QuerySupplierPackageDto("supplierCode", 0L);
        final Result<List<PackageInfoVo>> expectedResult = ResultUtil.success();

        // Configure BaseSuppliersMapper.selectOne(...).
        final BaseSuppliers baseSuppliers = Instancio.create(BaseSuppliers.class);
        doReturn(baseSuppliers).when(mockBaseSuppliersMapper).selectOne(any());

        // Configure BzSupplierPackageInfoMapper.selectList(...).
        final BzSupplierPackageInfo bzSupplierPackageInfo = Instancio.create(BzSupplierPackageInfo.class);
        final List<BzSupplierPackageInfo> bzSupplierPackageInfos = Arrays.asList(bzSupplierPackageInfo);
        doReturn(bzSupplierPackageInfos).when(mockBzSupplierPackageInfoMapper).selectList(any());

        // Run the test
        final Result<List<PackageInfoVo>> result = baseMaterialsServiceUnderTest.querySupplierPackage(param);
        System.out.println(result.getData());
    }

    @Test
    public void testQuerySupplierPackage_BzSupplierPackageInfoMapperReturnsNoItems() {
        // Setup
        final QuerySupplierPackageDto param = new QuerySupplierPackageDto("supplierCode", 0L);
        final Result<List<PackageInfoVo>> expectedResult = ResultUtil.success();

        // Configure BaseSuppliersMapper.selectOne(...).
        final BaseSuppliers baseSuppliers = Instancio.create(BaseSuppliers.class);
        doReturn(baseSuppliers).when(mockBaseSuppliersMapper).selectOne(any());

        doReturn(Collections.emptyList()).when(mockBzSupplierPackageInfoMapper).selectList(any());

        // Run the test
        final Result<List<PackageInfoVo>> result = baseMaterialsServiceUnderTest.querySupplierPackage(param);
        System.out.println(result.getData());
    }

    @Test
    public void testImportMaterialPicture() {
        // Setup
        final MaterialPictureFile param = new MaterialPictureFile(
                Arrays.asList(new MaterialPictureItemVo("333@前", "http://www.aaa.com/url")));
        final Result<String> expectedResult = ResultUtil.success();

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn(1).when(mockBaseMaterialsMapper).update(any(), any());

        // Run the test
        final Result<String> result = baseMaterialsServiceUnderTest.importMaterialPicture(param);
        assertEquals(expectedResult.getCode(), result.getCode());
    }

    @Test
    public void testSubmit() throws MalformedURLException, UnsupportedEncodingException {
        final Result<String> expectedResult = ResultUtil.success();
        // Setup
        final SaveMaterialCodeDto param = Instancio.create(SaveMaterialCodeDto.class);
        param.setPicLeft("http://www.aaa.com/url1");
        param.setPicRight("http://www.aaa.com/url2");
        param.setPicBefore("http://www.aaa.com/url3");
        param.setPicAfter("http://www.aaa.com/url4");
        param.setPicOther("http://www.aaa.com/url5");
        param.setPicStamp("http://www.aaa.com/url6");
        // Run the test
        final Result<String> result = baseMaterialsServiceUnderTest.submit(param);
        assertEquals(expectedResult.getCode(), result.getCode());
    }

    @Test
    public void testGetUrlPath() throws Exception {
        assertEquals("url", baseMaterialsServiceUnderTest.getUrlPath("http://www.aaa.com/url"));
        assertThrows(MalformedURLException.class, () -> baseMaterialsServiceUnderTest.getUrlPath("url"));
    }

    @Test
    public void testDeletePics() {
        // Setup
        final DeletePicsDto param = new DeletePicsDto(Arrays.asList("value"));
        final Result<String> expectedResult = ResultUtil.success();

        // Run the test
        final Result<String> result = baseMaterialsServiceUnderTest.deletePics(param);
        assertEquals(expectedResult.getCode(), result.getCode());
    }
}
