package com.xiaopeng.halley.ascm.boot.service.sync;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.dto.syn.SynReturnDTo;
import com.xiaopeng.halley.ascm.boot.entity.BzErpReqMaterialRel;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import com.xiaopeng.halley.ascm.boot.service.AscmLockHelper;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillInfoSyncService;
import com.xiaopeng.halley.ascm.boot.service.BzWaybillService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AscmErpWmsSyncNewServiceTest {

    private MockMvc mockMvc;

    private String prefix;

    @Mock
    private ApplicationContext mockContext;
    @Mock
    private BzWaybillMapper mockBzWaybillMapper;
    @Mock
    private BzErpReqMaterialRelMapper mockBzErpReqMaterialRelMapper;
    @Mock
    private BzWaybillService mockWaybillService;
    @Mock
    private BaseWarehouseMapper mockBaseWarehouseMapper;
    @Mock
    private BzWaybillBoxRelMapper mockBzWaybillBoxRelMapper;
    @Mock
    private BzErpReqBoxMapper mockBzErpReqBoxMapper;
    @Mock
    private BaseShopMapper mockBaseShopMapper;
    @Mock
    private AscmLockHelper mockAscmLockHelper;
    @Mock
    private BzWaybillInfoSyncService mockBzWaybillInfoSyncService;
    @Mock
    private AscmEventKafkaProducer mockAscmEventKafkaProducer;

    @InjectMocks
    private AscmErpWmsSyncNewService ascmErpWmsSyncNewServiceUnderTest;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders.standaloneSetup(ascmErpWmsSyncNewServiceUnderTest).build();

        this.prefix = StringUtils.replace("", " ", "");
    }

    @Test
    public void testPTAndDTSync() {
        // Setup
        final JSONObject params = new JSONObject(0, false);
        final SynReturnDTo expectedResult = new SynReturnDTo("RECID", "S", "ZMESSAGE");

        // Configure BzErpReqMaterialRelMapper.selectList(...).
        final BzErpReqMaterialRel bzErpReqMaterialRel = new BzErpReqMaterialRel();
        bzErpReqMaterialRel.setPurchaseDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        bzErpReqMaterialRel.setDeliveryDateTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        bzErpReqMaterialRel.setPlanShippingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        bzErpReqMaterialRel.setPurchaseDate("20240726");
        bzErpReqMaterialRel.setPurchaseTime("102536");
        bzErpReqMaterialRel.setDeliveryDate("20240726");
        bzErpReqMaterialRel.setDeliveryTime("102536");
        bzErpReqMaterialRel.setDeliveryOrderType("deliveryOrderType");
        bzErpReqMaterialRel.setId(0L);
        bzErpReqMaterialRel.setDeliveryOrderCode("deliveryOrderCode");
        bzErpReqMaterialRel.setLineNumber("ASCM012589");
        bzErpReqMaterialRel.setBoxCode("N24072610086");
        bzErpReqMaterialRel.setMatnr("matnr");
        bzErpReqMaterialRel.setMaktx("maktx");
        bzErpReqMaterialRel.setQuantity(0);
        bzErpReqMaterialRel.setUnit("m");
        bzErpReqMaterialRel.setEstimatedVolume(0.0);
        bzErpReqMaterialRel.setOrderCount(0);
        bzErpReqMaterialRel.setPackageCount(0);
        bzErpReqMaterialRel.setMaterialLong("2.5");
        bzErpReqMaterialRel.setMaterialWidth("2.5");
        bzErpReqMaterialRel.setMaterialHeight("2.5");
        bzErpReqMaterialRel.setMaterialVolume(0.0);
        final List<BzErpReqMaterialRel> bzErpReqMaterialRels = Arrays.asList(bzErpReqMaterialRel);
        when(mockBzErpReqMaterialRelMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(bzErpReqMaterialRels);

        when(mockBzErpReqMaterialRelMapper.updateById(any(BzErpReqMaterialRel.class))).thenReturn(0);

        // Run the test

        // Verify the results

    }

}
