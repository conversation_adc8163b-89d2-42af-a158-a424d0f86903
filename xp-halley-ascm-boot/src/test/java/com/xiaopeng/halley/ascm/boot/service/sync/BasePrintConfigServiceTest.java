package com.xiaopeng.halley.ascm.boot.service.sync;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.xiaopeng.halley.ascm.boot.entity.BasePrintConfig;
import com.xiaopeng.halley.ascm.boot.mapper.BasePrintConfigMapper;
import com.xiaopeng.halley.ascm.boot.service.BasePrintConfigService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class BasePrintConfigServiceTest {
	@Mock
	private BasePrintConfigMapper basePrintConfigMapper;
	@Spy
	@InjectMocks
	private BasePrintConfigService basePrintConfigService;
	@Mock
	private LambdaQueryChainWrapper<BasePrintConfig> queryChainWrapper;

	private static final String DEFAULT_PRINTER_NAME = "DefaultPrinter";
	private static final String SPECIFIC_PRINTER_NAME = "SpecificPrinter";
	private static final String Lgort_2021 = "2021";
	private static final String Lgort_2022 = "2022";
	private static final String ROUTE_HUANAN = "华南";
	private static final String ROUTE_HUABEI = "华北";

	private BasePrintConfig createConfig(String lgort, String route, String printerName) {
		BasePrintConfig config = new BasePrintConfig();
		config.setLgort(lgort);
		config.setRoute(route);
		config.setPrinterName(printerName);
		return config;
	}

	@Test
	@DisplayName("测试正常情况: 找到匹配仓库和路线的打印机")
	void getPrinterName_shouldReturnSpecificPrinterName() {
		BasePrintConfig config1 = createConfig(Lgort_2021, ROUTE_HUANAN, SPECIFIC_PRINTER_NAME);
		BasePrintConfig config2 = createConfig(Lgort_2021, ROUTE_HUABEI, "AnotherPrinter");
		BasePrintConfig config3 = createConfig(Lgort_2022, ROUTE_HUANAN, SPECIFIC_PRINTER_NAME);
		List<BasePrintConfig> mockConfigs = Arrays.asList(config1, config2, config3);

		when(basePrintConfigService.lambdaQuery()).thenReturn(queryChainWrapper);
		when(queryChainWrapper.eq(any(), eq(Lgort_2021))).thenReturn(queryChainWrapper);
		when(queryChainWrapper.list()).thenReturn(mockConfigs);

		String printerName = basePrintConfigService.getPrinterName(Lgort_2021, ROUTE_HUANAN);
		assertNotNull(printerName, "打印机名称不应为 null");
		assertEquals(SPECIFIC_PRINTER_NAME, printerName, "应返回与路线匹配的打印机名称");
	}

	@Test
	@DisplayName("测试特殊情况: 只有1个配置且route为空")
	void getPrinterName_shouldReturnPrinterNameWhenSingleConfigAndNullRoute() {
		BasePrintConfig singleConfigWithNullRoute = createConfig(Lgort_2021, null, DEFAULT_PRINTER_NAME);
		List<BasePrintConfig> mockConfigs = Collections.singletonList(singleConfigWithNullRoute);

		when(basePrintConfigService.lambdaQuery()).thenReturn(queryChainWrapper);
		when(queryChainWrapper.eq(any(), eq(Lgort_2021))).thenReturn(queryChainWrapper);
		when(queryChainWrapper.list()).thenReturn(mockConfigs);

		String printerName = basePrintConfigService.getPrinterName(Lgort_2021, "任意路线");

		assertNotNull(printerName, "打印机名称不应为 null");
		assertEquals(DEFAULT_PRINTER_NAME, printerName, "应返回唯一且路线为空的配置的打印机名称");
	}

	@Test
	@DisplayName("测试无匹配配置: 未找到任何配置")
	void getPrinterName_shouldReturnNullWhenNoConfigsFound() {
		List<BasePrintConfig> mockConfigs = Collections.emptyList();

		when(basePrintConfigService.lambdaQuery()).thenReturn(queryChainWrapper);
		when(queryChainWrapper.eq(any(), eq("NonExistentLgort"))).thenReturn(queryChainWrapper);
		when(queryChainWrapper.list()).thenReturn(mockConfigs);

		String printerName = basePrintConfigService.getPrinterName("NonExistentLgort", ROUTE_HUANAN);

		assertNull(printerName, "未找到配置时，打印机名称应为 null");
	}

	@Test
	@DisplayName("测试无匹配配置: 找到仓库但无匹配路线")
	void getPrinterName_shouldReturnNullWhenLgortMatchButNoRouteMatch() {
		BasePrintConfig config1 = createConfig(Lgort_2021, ROUTE_HUANAN, SPECIFIC_PRINTER_NAME);
		BasePrintConfig config2 = createConfig(Lgort_2021, ROUTE_HUABEI, "AnotherPrinter");
		List<BasePrintConfig> mockConfigs = Arrays.asList(config1, config2);

		when(basePrintConfigService.lambdaQuery()).thenReturn(queryChainWrapper);
		when(queryChainWrapper.eq(any(), eq(Lgort_2021))).thenReturn(queryChainWrapper);
		when(queryChainWrapper.list()).thenReturn(mockConfigs);

		String printerName = basePrintConfigService.getPrinterName(Lgort_2021, "不存在的路线");

		assertNull(printerName, "找到仓库但无匹配路线时，打印机名称应为 null");
	}
}