package com.xiaopeng.halley.ascm.boot.service.sync;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.xiaopeng.halley.ascm.boot.common.kafka.AscmEventKafkaProducer;
import com.xiaopeng.halley.ascm.boot.dto.erp.MaterialDeliveryNoteDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.MaterialInfoDto;
import com.xiaopeng.halley.ascm.boot.dto.erp.SyncWaybillResultDto;
import com.xiaopeng.halley.ascm.boot.dto.syn.SynReturnDTo;
import com.xiaopeng.halley.ascm.boot.entity.*;
import com.xiaopeng.halley.ascm.boot.mapper.*;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.instancio.Instancio;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringRunner.class)
public class ThirdPartySyncWaybillServiceTest {

    @Spy
    protected BzWaybillMapper mockBzWaybillMapper;
    @Spy
    protected BzErpReqBoxMapper mockBzErpReqBoxMapper;
    @Spy
    protected BzErpReqMaterialRelMapper mockBzErpReqMaterialRelMapper;
    @Spy
    @InjectMocks
    protected AscmEventKafkaProducer mockAscmEventKafkaProducer;
    @Spy
    protected BaseMaterialsMapper mockBaseMaterialsMapper;
    @Spy
    protected BaseSuppliersMapper mockBaseSuppliersMapper;
    @Spy
    protected BzSupplierMaterialMapper mockBzSupplierMaterialMapper;
    @Spy
    protected BzSupplierPackageInfoMapper mockBzSupplierPackageInfoMapper;
    @Spy
    protected BzDeliveryOrderMaterialMapper mockBzDeliveryOrderMaterialMapper;
    @Spy
    protected BzDeliveryOrdersMapper mockBzDeliveryOrdersMapper;

    @InjectMocks
    private ThirdPartySyncWaybillService thirdPartySyncWaybillServiceUnderTest;

    @Before
    public void setUp() {
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), BzWaybill.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), BzWaybillTrackHistory.class);
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), BaseMaterials.class);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSaveWaybillData() {
        // Setup
        doReturn(0).when(mockBzWaybillMapper).update(any(), any());

        // Configure BzErpReqBoxMapper.selectOne(...).
        final BzErpReqBox bzErpReqBox = Instancio.create(BzErpReqBox.class);
        doReturn(bzErpReqBox).when(mockBzErpReqBoxMapper).selectOne(any());

        // Configure BzErpReqMaterialRelMapper.selectList(...).
        final BzErpReqMaterialRel bzErpReqMaterialRel = Instancio.create(BzErpReqMaterialRel.class);
        final List<BzErpReqMaterialRel> bzErpReqMaterialRels = Arrays.asList(bzErpReqMaterialRel);
        doReturn(bzErpReqMaterialRels).when(mockBzErpReqMaterialRelMapper).selectList(any());

        doReturn(0).when(mockBzErpReqMaterialRelMapper).updateById(any());
        doReturn(0).when(mockBzErpReqBoxMapper).updateById(any());
        SyncWaybillResultDto syncWaybillResultDto = Instancio.create(SyncWaybillResultDto.class);
        String jsonString = JSON.toJSONString(syncWaybillResultDto);
        // Run the test
        thirdPartySyncWaybillServiceUnderTest.saveWaybillData(jsonString, String.valueOf(System.currentTimeMillis()));

    }

    @Test
    public void testSaveWaybillData_BzErpReqMaterialRelMapperSelectListReturnsNoItems() {
        // Setup
        doReturn(1).when(mockBzWaybillMapper).update(any(), any());

        // Configure BzErpReqBoxMapper.selectOne(...).
        final BzErpReqBox bzErpReqBox = Instancio.create(BzErpReqBox.class);
        doReturn(bzErpReqBox).when(mockBzErpReqBoxMapper).selectOne(any());

        final BzErpReqMaterialRel bzErpReqMaterialRel = Instancio.create(BzErpReqMaterialRel.class);
        final BzErpReqMaterialRel bzErpReqMaterialRel1 = Instancio.create(BzErpReqMaterialRel.class);
        final List<BzErpReqMaterialRel> bzErpReqMaterialRels = Arrays.asList(bzErpReqMaterialRel, bzErpReqMaterialRel1);
        doReturn(bzErpReqMaterialRels).when(mockBzErpReqMaterialRelMapper).selectList(any());
        doReturn(1).when(mockBzErpReqMaterialRelMapper).updateById(any());
        doReturn(1).when(mockBzErpReqBoxMapper).updateById(any());
        SyncWaybillResultDto syncWaybillResultDto = Instancio.create(SyncWaybillResultDto.class);
        String jsonString = JSON.toJSONString(syncWaybillResultDto);
        // Run the test
        thirdPartySyncWaybillServiceUnderTest.saveWaybillData(jsonString, String.valueOf(System.currentTimeMillis()));

    }

    @Test
    public void testMaterialInfo() {
        // Setup
        final MaterialInfoDto materialInfoDto = Instancio.create(MaterialInfoDto.class);

        final SynReturnDTo expectedResult = new SynReturnDTo("Success!", "ZSTATUS", "ZMESSAGE");

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn(1).when(mockBaseMaterialsMapper).insert(any());
        doReturn(1).when(mockBaseMaterialsMapper).updateById(any());

        // Configure BaseSuppliersMapper.selectOne(...).
        final BaseSuppliers baseSuppliers = Instancio.create(BaseSuppliers.class);
        doReturn(baseSuppliers).when(mockBaseSuppliersMapper).selectOne(any());

        doReturn(1).when(mockBaseSuppliersMapper).insert(any());

        // Configure BzSupplierMaterialMapper.selectOne(...).
        final BzSupplierMaterial bzSupplierMaterial = Instancio.create(BzSupplierMaterial.class);
        doReturn(bzSupplierMaterial).when(mockBzSupplierMaterialMapper).selectOne(any());

        doReturn(1).when(mockBzSupplierMaterialMapper).insert(any());
        doReturn(1).when(mockBzSupplierPackageInfoMapper).insert(any());

        // Run the test
        final SynReturnDTo result = thirdPartySyncWaybillServiceUnderTest.materialInfo(materialInfoDto);

        // Verify the results
        assertEquals(expectedResult.getRECID(), result.getRECID());
    }

    @Test
    public void testMaterialDeliveryNote() {
        String str = "{\"VBELN\":\"3330100FA3-00-03\",\"VERUR\":\"AM2580123\",\"ZYL01\":\"500\",\"ZYL02\":\"500\",\"ZYL03\":\"5000\",\"MATNR\":[\"3330100FA3-00-01\",\"3330100FA3-00-02\",\"3330100FA3-00-03\"]}";

        // Setup
        final MaterialDeliveryNoteDto materialDeliveryNoteDto = JSON.parseObject(str, MaterialDeliveryNoteDto.class);
        final SynReturnDTo expectedResult = new SynReturnDTo("Success!", "ZSTATUS", "ZMESSAGE");
        doReturn(1).when(mockBzDeliveryOrdersMapper).insert(any());

        // Configure BaseMaterialsMapper.selectOne(...).
        final BaseMaterials baseMaterials = Instancio.create(BaseMaterials.class);
        doReturn(baseMaterials).when(mockBaseMaterialsMapper).selectOne(any());

        doReturn(1).when(mockBzDeliveryOrderMaterialMapper).insert(any());

        // Run the test
        final SynReturnDTo result = thirdPartySyncWaybillServiceUnderTest.materialDeliveryNote(materialDeliveryNoteDto);

        // Verify the results
        assertEquals(expectedResult.getRECID(), result.getRECID());
    }
}
